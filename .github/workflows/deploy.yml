name: Deployment

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      PROFILE:
        description: ""
        required: true
        default: staging
        type: choice
        options:
          - staging
          - production

jobs:
  deploy:
    name: Deploy to Cloudflare
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 23

      - name: Install dependencies
        run: npm ci

      - name: Set environment variables based on profile
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            PROFILE="${{ github.event.inputs.PROFILE }}"
          else
            PROFILE="staging"
          fi

          if [ "$PROFILE" = "staging" ]; then
            echo "VITE_GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID_STAGING }}" >> $GITHUB_ENV
            echo "VITE_GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET_STAGING }}" >> $GITHUB_ENV
            echo "VITE_ADMIN_BASE_URL=${{ secrets.ADMIN_BASE_URL_STAGING }}" >> $GITHUB_ENV
            echo "DEPLOY_PROFILE=staging" >> $GITHUB_ENV
          elif [ "$PROFILE" = "production" ]; then
            echo "VITE_GOOGLE_CLIENT_ID=${{ secrets.GOOGLE_CLIENT_ID_PROD }}" >> $GITHUB_ENV
            echo "VITE_GOOGLE_CLIENT_SECRET=${{ secrets.GOOGLE_CLIENT_SECRET_PROD }}" >> $GITHUB_ENV
            echo "VITE_ADMIN_BASE_URL=${{ secrets.ADMIN_BASE_URL_PROD }}" >> $GITHUB_ENV
            echo "DEPLOY_PROFILE=production" >> $GITHUB_ENV
          fi

      - name: Build project
        run: npm run build

      - name: Deploy with Wrangler
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          environment: ${{ env.DEPLOY_PROFILE }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        env:
          VITE_GOOGLE_CLIENT_ID: ${{ env.VITE_GOOGLE_CLIENT_ID }}
          VITE_GOOGLE_CLIENT_SECRET: ${{ env.VITE_GOOGLE_CLIENT_SECRET }}
          VITE_ADMIN_BASE_URL: ${{ env.VITE_ADMIN_BASE_URL }}
