{"name": "stablemoney-controlhub", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate:proto": "sh scripts/proto-gen.sh", "deploy": "tsc -b && vite build && npx wrangler deploy"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@react-oauth/google": "^0.12.2", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.81.5", "@tanstack/react-router": "^1.120.13", "@tanstack/router-vite-plugin": "^1.120.13", "antd": "^5.25.4", "array-move": "^4.0.0", "axios": "^1.10.0", "dayjs": "^1.11.13", "immutability-helper": "^3.1.1", "json-edit-react": "^1.29.0", "lodash": "^4.17.21", "react": "^19.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "tailwindcss": "^4.1.8", "use-debounce": "^10.0.5", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.19.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tanstack/react-router-devtools": "^1.120.15", "@types/lodash": "^4.17.20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "ts-proto": "^2.7.2", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}