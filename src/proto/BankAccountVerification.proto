syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

enum BankVerificationType {
  UNKNOWN_TYPE = 0;
  PENNY_DROP = 1;
  REVERSE_PENNY_DROP = 2;
  CANCEL_CHEQUE = 3;
}

enum BankVerificationProvider {
  UNKNOWN_PROVIDER = 0;
  DIGIO_PD = 1;
  SETU_RPD = 2;
  CHEQUE = 3;
  SIGNZY_OCR_READ = 4;
}

message InitiateBankVerificationRequest {
  BankVerificationType type = 1;
  oneof result {
    BankAccount bank_account = 2;
    Cheque cheque = 3;
  }
}

message BankAccount {
  string beneficiary_account_no = 1 ;
  string ifsc_id = 2 ;
  string beneficiary_name = 3 ;
}



message InitiateBankVerificationResponse {
  oneof result{
    InitiatePdResponse initiate_pd_response = 2;
    InitiateRpdResponse initiate_rpd_response = 3;
    InitiateChequeResponse initiate_cheque_response = 4;
  }
}

message InitiatePdResponse {
  bool verified = 1;
}

message InitiateRpdResponse {
  string id = 1;
  string short_url = 2;
  string status = 3;
  string trace_id = 4;
  string upi_bill_id = 5;
  string upi_link = 6;
  string valid_upto = 7;
}

message RpdStatusRequest {
  string request_id = 1;
}

message RpdStatusResponse {
  string id = 1;
  string short_url = 2;
  string status = 3;
  string trace_id = 4;
  string upi_bill_id = 5;
  string upi_link = 6;
  string valid_upto = 7;
  string data = 8;
  AdditionalData additional_data = 9;
  bool verified = 10;
}

message AdditionalData {
  string ref_id = 1;
}

message BankListResponse {
  repeated BankList data = 2;
}

message BankList {
  string bank_name = 1;
  string ifsc = 2;
  string logo = 3;
  bool is_popular = 4;
  string id = 5;
}

message IfscListResponse {
  repeated IfscList data = 2;
}

message IfscList {
  string bank = 1;
  string branch = 2;
  string ifsc = 3;
  string id = 4;
}

message BankStatusResponse {
  bool is_verified = 1;
  string rpd_status = 2;
  string pd_status = 3;
  string cheque_status = 4;
  string metadata = 5;
  bool fuzzy_match_result = 6;
  string account_number = 7;
  string account_holder_name = 8;
  string bank_name = 9;
  string branch_name = 10;
  string ifsc_code = 11;
  string logo = 12;
}

message Cheque {
  string document_id = 1;
}

message InitiateChequeResponse {
}