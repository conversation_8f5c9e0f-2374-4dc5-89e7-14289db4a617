syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;
import "Device.proto";
import "google/protobuf/empty.proto";


enum AlgoStatusType {
  DATA_INGESTED_NEW = 0;
  DATA_INGESTED_INCREMENTAL = 1;
  ALGO360_PROCESS_FINISHED = 2;
  ALGO360_PRIORITY_VARIABLES_PROCESSED = 3;
  ALGO360_GAP_VARIABLES_PROCESSED = 4;
  UNKNOWN_ALGO_STATUS_TYPE = 5;
}