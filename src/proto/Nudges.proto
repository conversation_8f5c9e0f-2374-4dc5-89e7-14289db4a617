syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

message NudgeResponse {
  string nudge = 1;
  string nudge_type = 2;
  string nudge_id = 3;
  int32 priority = 4;
  string name = 5;
}

message UpdateNudgeActionRequest {
  string nudge_type = 1;
  NudgeActionType nudge_action_type = 2;
  string nudge_id = 3;
}

enum NudgeType {
  UNKNOWN_NUDGE_TYPE = 0;
  REFERRAL_NUDGE = 1;
  REFERRAL_TEST_NUDGE = 2;
  FIRST_FD_REWARD_NUDGE = 3;
  FUNDING_ANNOUNCEMENT_NUDGE = 4;
  PROFILE_COMPLETION_NUDGE = 5;
  BANK_NUDGE = 6;
  MATURED_FD_CARDS_NUDGE = 7;
  MATURING_SOON_FD_CARDS_NUDGE = 8;
  GENERIC_EXPRESSION_NUDGE = 10;
}

enum NudgeActionType {
  UNKNOWN_NUDGE_ACTION = 0;
  NUDGE_SUCCESS = 1;
  NUDGE_DISMISS = 2;
}
