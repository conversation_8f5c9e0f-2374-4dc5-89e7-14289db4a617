syntax = "proto3";
import "Common.proto";
import "Campaign.proto";
import "Reward.proto";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

enum ReferralItemStatus {
  UNKNOWN_REFERRAL_STATUS = 0;
  BOOKING_PENDING_REFERRAL_STATUS = 1;
  FD_BOOKED_REFERRAL_STATUS = 2;
  FD_WITHDRAWN_REFERRAL_STATUS = 3;
  FD_BOOKED_INELIGIBLE_STATUS = 4;
}

message ReferralItem {
  string referee_name = 1;
  string referee_dp = 2;
  string referral_level = 3;
  string referral_level_pic_url = 4;
  string referral_status_description = 5;
  ReferralItemStatus referral_status = 6;
  optional double reward_amount = 7;
  optional string days_left = 8;
  string updated_at = 9;
  string share_text = 10;
  string phone_number = 11;
  string cta_text = 12;
  string referee_id = 13;
}

message ReferralSummary {
  double referral_earnings = 1;
  double withdrawable_earnings = 2;
  double maximum_earnings = 3;
  int32 total_referrals = 4;
  int32 successful_referrals = 5;
  string title = 6;
  double current_earnings_percentage = 7;
}

message ReferralDashboardResponse {
  ReferralSummary referralSummary = 1;
  repeated ReferralItem referral_items_list = 2;
}

message ReferrerItem {
  string name = 1;
  string id = 2;
}

message ReferralDashboardResponseV2 {
  ReferralSummary referralSummary = 1;
  repeated ReferralItem referral_items_list = 2;
  optional double potential_earning_amount = 3;
  ReferrerItem referrer = 4;
}

message ReferralLeaderboardItem {
  string name = 1;
  int32 rank = 2;
  int32 referral_count = 3;
  double referral_earning = 4;
  string icon_url = 5;
}

message ReferralLeaderboardResponse {
  repeated ReferralLeaderboardItem referral_items_list = 2;
}

message GetRefereesRequest {
  PaginationFilter paginationFilter = 1;
  string user_id = 2;
  repeated CampaignType campaignTypes = 3;
}

message Referee {
  string user_id = 1;
  string first_name = 2;
  string last_name = 3;
  optional CampaignType campaign_type = 4;
}

message Referer {
  string user_id = 1;
  string first_name = 2;
  string last_name = 3;
  optional CampaignType campaign_type = 4;
}

message GetRefereesResponse {
  repeated Referee referees = 1;
}

message ReferralLinkRequest {
  string user_id = 1;
  CampaignType campaign_type = 2;
}
