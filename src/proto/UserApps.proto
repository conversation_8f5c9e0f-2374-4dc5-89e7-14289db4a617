syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;
import "Device.proto";

message InstalledAppItem{
  string app_name = 1 ;
  string package_name = 2;
  string version_name = 3;
  bool is_system_app = 4;
  DeviceType device_type = 5;
}

message UserInstalledAppsRequest{
  repeated InstalledAppItem installed_apps = 1;
}

message UserInstalledAppsResponse{
}

message AppItem{
  string app_name = 1;
  string package_name = 2;
}

message AppsListResponse{
  repeated AppItem appItemsList = 1;
}