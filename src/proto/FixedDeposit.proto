syntax = "proto3";

package com.stablemoney.api.business;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;

import "BusinessCommon.proto";
import "Common.proto";

enum FdProvider {
  FD_PROVIDER_UNKNOWN = 0;
  UPSWING = 1;
  FINCARE = 2;
  UNITY = 3;
  UJJIVAN = 4;
  INDUSIND = 5;
  IDFC = 6;
  BANDHAN = 7;
}

enum InvestmentStatus {
  // Valid statuses in user investment
  INVESTMENT_STATUS_UNKNOWN = 0;
  FD_INITIATE = 1;
  FD_ACCOUNT_CREATED = 2;
  PRE_PAYMENT_VKYC_MAKER_SUCCESS = 3;
  FD_REVIEW = 4;
  PAYMENT_FAILURE = 5;
  PAYMENT_SUCCESS = 6;
  VKYC_PENDING = 7;
  VKYC_FAILURE = 8;
  VKYC_MAKER_SUCCESS = 9;
  VKYC_ATTEMPTED = 44; // means VKYC was attempted
  VKYC_SUCCESS = 10;
  BOOKING_SUCCESS = 11;
  WITHDRAWN = 12;
  MATURED = 13;
  FD_APPLY = 14;
  REFUND = 49;
  DISCARDED = 50;

  // Not persisted in user investment
  FD_ACCEPT_TERMS = 15;
  FD_CALCULATE = 16;
  FD_VERIFY = 17;
  FD_PAYMENT_INITIATED = 18;
  FD_PAYMENT_FAILED = 19;
  FD_PAYMENT_SUCCESS = 20;
  FD_PERSONAL_FORM = 21;
  FD_PERSONAL_VERIFY = 22;
  FD_VKYC = 23;
  FD_VKYC_INTIATED = 24;
  FD_VKYC_RESULT_PAGE = 25;
  FD_PENDING_AUTHENTICATE = 26;
  FD_WELCOME_BACK = 27;
  FD_ETB_PAYMENT_INITIATED = 28;
  FD_ETB_CALCULATE = 29;
  FD_ETB_PERSONAL_FORM = 30;
  FD_ETB_PERSONAL_VERIFY = 31;
  FD_ETB_FD_CHECK = 32;
  FD_ETB_PAYMENT_FAILED = 33;
  FD_ETB_PAYMENT_SUCCESS = 34;
  FD_ETB_PAYMENT_PENDING = 35;
  FD_ETB_PENDING_AUTHENTICATE = 36;
  FD_ETB_WELCOME_BACK = 37;
  FD_EXISTING_FLOW_MESSAGE = 38;
  FD_APPLY_ETB = 39;
  FD_AMOUNT_BEFORE_PAYMENT_INFO = 40;
  FD_ACC_CREATED_INFO = 41;
  ETB_FD_ACCOUNT_CREATED = 42;
  FD_PAYMENT_SUCCESS_INFO = 43;
  UJJIVAN_MAIN_PAGE = 45;
  VKYC_LINK_COPIED = 46;
  NTB_USER_EXISTS = 47;
  ETB_BY_MISTAKE_USER = 48;
  UTM_INGESTED = 51;
  NTB_AUTH_ERROR = 52;
  ETB_AUTH_ERROR = 53;
  ETB_NO_PENDING_JOURNEY = 54;
  SM_AADHAAR_FAILED = 55;
  NTB_BY_MISTAKE_USER = 56;
}

enum FdEvent {
  FD_EVENT_UNKNOWN = 0;
  HALF_KYC_EVENT = 1;
  FULL_KYC_EVENT = 2;
  FD_INITIATE_EVENT = 3;
  PAYMENT_SUCCESS_EVENT = 4;
  PERSONAL_DETAILS_EVENT = 5;
}

message InitiateFdRequest {
  FdProvider provider = 1;
  string bank_id = 2;
  FdData fd_data = 3;
}

message InitiateFdResponse {
  message UpswingJourney {
    string ici = 1;
    string token = 2;
  }
  message WebRedirectionJourney {
    string journey_id = 1;
    string redirection_url = 2;
    optional FdPrefillData fdPrefillData = 3;
  }
  message StableMoneyJourney {

  }
  oneof result {
    UpswingJourney upswing_journey = 1;
    WebRedirectionJourney web_redirection_journey = 2;
    StableMoneyJourney stable_money_journey = 3;
  }
}

message FincareInvestmentSummary {
  TotalInvestedAmount totalInvestedAmount = 1;
  TotalInterestEarned totalInterestEarned = 2;
  string bank_name = 3;
  string bank_logo = 4;
  optional bool fincare_maximum_amount_check = 5;
  optional bool fincare_vkyc_completed = 6;
  optional int32 active_term_deposit_count = 7;
}


message UnityInvestmentSummary {
  TotalInvestedAmount totalInvestedAmount = 1;
  TotalInterestEarned totalInterestEarned = 2;
  string bank_name = 3;
  string bank_logo = 4;
  string bank_id = 5;
  optional int32 active_term_deposit_count = 6;
}

message UjjivanInvestmentSummary {
  TotalInvestedAmount totalInvestedAmount = 1;
  TotalInterestEarned totalInterestEarned = 2;
  string bank_name = 3;
  string bank_logo = 4;
  string bank_id = 5;
  optional int32 active_term_deposit_count = 6;
}

message IndusindInvestmentSummary {
  TotalInvestedAmount totalInvestedAmount = 1;
  TotalInterestEarned totalInterestEarned = 2;
  string bank_name = 3;
  string bank_logo = 4;
  string bank_id = 5;
  optional int32 active_term_deposit_count = 6;
}

message NetWorthSummaryResponse {
  TotalInvestedAmount totalInvestedAmount = 1;
  TotalInterestEarned totalInterestEarned = 2;
  CurrentAmount currentAmount = 3;
  int32 activeTermDepositCount = 4;
  bool is_invested = 5;
  bool is_pending = 6;
  string pending_event_time = 7;
  string pending_event_type = 8;
  bool is_vkyc_pending = 9;
  bool is_upswing_initiated = 10;
  optional FincareInvestmentSummary fincareInvestmentSummary = 11;
  optional UnityInvestmentSummary unity_investment_summary = 12;
  optional EmergencyFundSummary emergencyFundSummary = 13;
  optional UjjivanInvestmentSummary ujjivan_investment_summary = 14;
  optional IndusindInvestmentSummary indusind_investment_summary = 15;
  double inProgressTotal = 16;
}

message EmergencyFundSummary {
  bool is_emergency_fund_created = 1;
  double target_amount = 2;
  double current_amount = 3;
  double progress_percentage = 4;
  string progress_description = 5;
  bool is_active = 6;
}

message TotalInvestedAmount {
  double amount = 1;
  string currency = 2;
}

message TotalInterestEarned {
  double amount = 1;
  string currency = 2;
}

message CurrentAmount {
  double amount = 1;
  string currency = 2;
}

message FincarePayload {
  string session_id = 1;
  string utm_medium = 2;
  string utm_source = 3;
  string utm_campaign = 4;
  string referral_link = 5;
  optional double fd_rate = 6;
  optional string fd_maturity_date = 7;
  optional double fd_amount = 8;
  string kyc_status = 9;
  string account_created_status = 10;
  optional bool is_senior_citizen = 11;
  optional int32 tenure_in_days = 12;
  optional string interest_payout_type = 13;
  optional string maturity_instruction = 14;
  optional bool fd_active = 15;
  optional string fd_closing_date = 16;
  optional string fd_booking_date = 17;
  optional string prod_type = 18;
}

message TenurePrefillInfo {
  optional string tenure_html_tag = 1;
  optional int32 tenure_year = 2;
  optional int32 tenure_month = 3;
  optional int32 tenure_day = 4;
}

message NomineePrefillInfo {
  optional string nominee_name = 1;
  optional string nominee_relationship = 2;
  optional string nominee_date_of_birth = 3;
}

message BankAccountPrefillInfo {
  optional string account_holder_name = 1;
  optional string bank_account_number = 2;
  optional string ifsc_code = 3;
}

message FdPrefillData {
  double investment_amount = 1;
  string maturity_instruction = 2;
  string interest_payout_type = 3;
  TenurePrefillInfo tenure_prefill_info = 4;
  NomineePrefillInfo nominee_prefill_info = 5;
  BankAccountPrefillInfo bank_account_prefill_info = 6;
  bool is_tax_saving = 7;
  optional string pan_number = 8;
  optional string dob = 9;
}

message FdEventResponse {
  string booking_url = 1;
  string booking_id = 2;
  FdPrefillData fd_prefill_data = 3;
  string fd_id = 4;
  optional bool isBlock = 5;
  optional bool isInput = 6;
}

message EtbResponse {
  bool is_etb_known = 1;
}

message SetEtbRequest {
  string bank_id = 1;
  bool has_account = 2;
  bool has_fd = 3;
}

message GetEtbRequest {
  string bank_id = 1;
}

message PanStatusResponse {
  bool is_pan_needed = 1;
}

message EventMetadata {
}

message FdData {
  string fd_id = 1;
  double investment_amount = 2;
  string interest_payout_type = 3;
  string maturity_instruction = 4;
  optional string pan_number = 5;
  optional string dob = 6;
  optional bool pan_consent = 7;
}

message FdEventRequest {
  string bank_id = 1;
  string event_type = 2;
  optional FdData fd_data = 3;
  optional EventMetadata event_metadata = 4;
  optional string booking_id = 5;
  optional string page_html = 6;
  optional string page_url = 7;
  optional string timestamp = 8;
}

message FdCalculationRequest {
  string fd_id = 1;
  double investment_amount = 2;
  string interest_payout_type = 3;
}

message FdCalculationResponse {
  double maturity_amount = 1;
  string maturity_description = 2;
}

message FdCalculationResponseV2 {
  double maturity_amount = 1;
  double interest_gained = 2;
  string maturity_description = 3;
}

message NearMaturityInvestments {
  message FixedDeposit {
    string bank_id = 1;
    string bank_name = 2;
    string bank_logo_url = 3;
    com.stablemoney.api.identity.BankType bank_type = 4;
    string tenure = 5;
    double interest_rate = 6;
    com.stablemoney.api.identity.RedirectDeeplink redirect_deep_link = 7;
    repeated string description = 8;
  }
  repeated InvestmentItem investment_items = 1;
}

message LastMaturedInvestment {
  InvestmentItem investment_item = 1;
  repeated com.stablemoney.api.identity.DataKey pointers = 3;
  repeated NearMaturityInvestments.FixedDeposit suggested_fds = 5;
}

message NearMaturityInvestmentDetail {
  message Recommendation {
    enum Type {
      UNKNOWN = 0;
      NONE = 1;
      REINVEST = 2;
      WITHDRAW = 3;
    }
    Type type = 1;
    double amount = 2;
    double interest_rate = 3;
    string tenure = 4;
  }
  enum Term {
    SHORT = 0;
    MEDIUM = 1;
    LONG = 2;
  }
  InvestmentItem investment_item = 1;
  Recommendation recommendation = 2;
  repeated NearMaturityInvestments.FixedDeposit suggested_fds = 3;
  Term term = 4;
}

message InvestmentItem {
  double investment_amount = 1;
  string fd_maturity_date = 2;
  string interest_payout_type = 3;
  double current_gains = 4;
  string bank_name = 5;
  string bank_logo_url = 6;
  string tenure = 7;
  string fd_booking_date = 8;
  double interest_rate = 9;
  double maturity_amount = 10;
  string booking_id = 11;
  string investment_status = 12;
  string invested_at = 13;
  string maturity_instruction = 15;
  com.stablemoney.api.identity.RedirectDeeplink redirect_deep_link = 16;
}

message InvestmentResponse {
  string bank_id = 1;
  repeated InvestmentItem pendingInvestedItems = 2;
  repeated InvestmentItem bookedInvestedItems = 3;
  TotalInvestedAmount totalInvestedAmount = 4;
  TotalInterestEarned totalInterestEarned = 5;
}

message FincareSheetRow {
  string hmac = 1;
  FincarePayload fincare_payload = 2;
}

message FincareSheetRequest {
  repeated FincareSheetRow fincare_sheet = 1;
}

message UjjivanPayload {
  string application_id = 1;
  string application_status = 2;
  string creation_date = 3;
  string last_update_date = 4;
  optional double amount = 5;
  optional string user_id = 6;
  optional string investment_id = 7;
}

message UjjivanSheetRow {
  string hmac = 1;
  UjjivanPayload payload = 2;
}

message UjjivanSheetRequest {
  repeated UjjivanSheetRow sheet = 1;
}

message SheetResponse {
  repeated string successes = 1;
  repeated string failures = 2;
  repeated string skipped = 3;
}


message UserFixedDepositSummaryRequest {
  string user_id = 1;
}

message UserFixedDepositSummaryResponse {
  double total_invested_amount = 1;
  double total_interest_earned = 2;
  double current_amount = 3;
  optional EmergencyFundSummary emergency_fund_summary = 4;
  int32 active_term_deposit_count = 5;
  double in_progress_total = 6;
}


message WithdrawalCalculatorRequest {
  string fd_id = 1;
}
message WithdrawalCalculatorResponse {
  double invested_amount = 1;
  double withdrawal_amount = 2;
  double interest_earned = 3;
  double penalty_interest = 4;
  string fd_account_number = 5;
  double applicable_interest_rate = 6;
  double maturity_amount = 7;
  double maturity_interest = 8;
  string fd_active_days = 9;
  string booking_date = 10;
  string fd_booked_tenure = 11;
  string bank_logo = 12;
  optional WithdrawalCreditBankDetails withdrawal_credit_bank_details = 13;
  optional com.stablemoney.api.identity.RedirectDeeplink redirect_deep_link = 14;
  map<string, string> withdrawal_metadata = 15;
}

message WithdrawalCreditBankDetails {
  string bank_name = 1;
  string account_number = 2;
  string payment_mode = 3;
  string credit_expected_date = 4;
}

message WithdrawalRequest {
  string fd_id = 1;
}

message WithdrawalResponse {
  string message = 1;
}

message CompareFdResponse {
  message RateAndTenure {
    double rate = 1;
    int32 tenure_in_days = 2;
    com.stablemoney.api.identity.CompoundingFrequencyType compounding_frequency = 3;
  }
  string bank_id = 1;
  string bank_name = 2;
  string bank_logo = 3;
  RateAndTenure short_term = 4;
  RateAndTenure mid_term = 5;
  RateAndTenure long_term = 6;
  repeated string payout = 7;
  bool bank_account_required = 8;
  bool insured = 9;
  string withdrawal = 10;
}
