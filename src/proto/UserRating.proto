syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;


enum RatingSource {
  UNKNOWN_RATING_SOURCE = 0;
  IN_APP_RATING_SOURCE = 1;
  TD_BOOKED_EMAIL_RATING_SOURCE = 2;
}

message PostRatingRequest {
  optional double rating = 1;
  optional string review = 2;
  optional bool is_review_skipped = 3;
  string user_id = 4;
  bool is_google_play_rating_shown = 6;
}

message PostRatingResponse {
}

message UserRatingRequest {
  string user_id = 1;
}

message UserRatingResponse {
  double rating = 1;
  string review = 2;
  bool is_review_skipped = 3;
  string in_app_review_cohort = 4;
  bool show_in_app_review = 6;
}

message EmailFeedbackRequest {
  string customer_id = 1;
  string booking_id = 2;
  int32 rating = 3;
  repeated string good_experience_option = 4;
  string message = 5;
}

message EmailFeedbackResponse {
  bool status = 1;
}