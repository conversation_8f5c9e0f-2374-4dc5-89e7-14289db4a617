syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;

import "Collection.proto";
import "BusinessCommon.proto";
import "Bank.proto";
import "Common.proto";

message BanksVkycDoneOrNbfcsWithAnInvestment{
  repeated BankInvestmentInfo bank_investment_info = 1;
}

message BankInvestmentInfo{
  FixedDepositResponse fd_response = 1;
  RedirectDeeplink redirect_deeplink = 2;
  bool is_completed = 3;
  double total_investment = 4;
  double target_investment_amount = 5;
  repeated string tags = 6;
  double progress_percentage = 7;
  string progress_title = 8;
  string progress_subtitle = 9;
}

message UserInvestments{
  UserInvestmentSummary user_investment_summary = 1;
  repeated InvestmentsGroupedByStatus investments_grouped_by_status = 2;
  bool show_investments = 3;
  RedirectDeeplink redirect_deeplink = 4;
  string redirect_cta = 5;
}

message UserInvestmentSummary{
  double total_investment = 1;
  double total_gain = 2;
  double maturity_amount = 3;
  double current_value = 4;
  string total_investment_text = 5;
  string total_gain_text = 6;
  string maturity_amount_text = 7;
  string current_value_text = 8;
  string total_investment_color = 9;
  string total_gain_color = 10;
  string maturity_amount_color = 11;
  string current_value_color = 12;
  string icon_url = 13;
  string total_investment_text_color = 14;
  string total_gain_text_color = 15;
  string maturity_amount_text_color = 16;
  string current_value_text_color = 17;
}

message InvestmentsGroupedByStatus{
  BookingStatus investment_status = 1;
  string investment_status_label = 2;
  repeated InvestmentInfo investment_info = 3;
  RedirectDeeplink redirect_deeplink = 4;
  string redirect_cta = 5;
  bool show_view_all = 6;
}

message InvestmentInfo{
  BankResponse bank_response = 1;
  string left_top_value = 2;
  string left_bottom_value = 3;
  string right_top_value = 4;
  string right_bottom_value = 5;
  string subtitle = 6;
  string subtitle_color = 7;
  string message = 8;
  string message_text_color = 9;
  string message_bg_color = 10;
  string message_icon_color = 11;
  RedirectDeeplink redirect_deeplink = 12;
  string cta_text = 13;
  string id = 14;
  BookingStatus booking_status = 15;
  string left_top_text = 16;
  string left_bottom_text = 17;
  string right_top_text = 18;
  string right_bottom_text = 19;
  string left_top_color = 20;
  string left_bottom_color = 21;
  string right_top_color = 22;
  string right_bottom_color = 23;
  string left_top_text_color = 24;
  string left_bottom_text_color = 25;
  string right_top_text_color = 26;
  string right_bottom_text_color = 27;
  string maturity_date = 28;
  string tenure = 29;
  string investment_date = 30;
  optional RedirectDeeplink withdraw_deeplink = 31;
  bool is_tax_saving = 32;
  string booking_date = 33;
  MaturityInstruction maturity_instruction = 35;
  bool is_withdrawal_confirmation_required = 36;
  map<string, string> withdrawal_metadata = 37;
}

message InvestmentNomineeDetails {
  string nominee_name_text = 1;
  string nominee_name_value = 2;
  string nominee_name_color = 3;
  string nominee_relation_text = 4;
  string nominee_relation_value = 5;
  string nominee_relation_color = 6;
  string nominee_dob_text = 7;
  string nominee_dob_value = 8;
  string nominee_dob_color = 9;
  string heading = 10;
}

message WithdrawalBankAccountDetails {
  string account_holder_name_text = 1;
  string account_holder_name_value = 2;
  string account_holder_name_color = 3;
  string account_number_text = 4;
  string account_number_value = 5;
  string account_number_color = 6;
  string ifsc_code_text = 7;
  string ifsc_code_value = 8;
  string ifsc_code_color = 9;
  string heading = 10;
}


message SavingsBankAccountDetails {
  repeated InvestmentKeyValue investment_key_value_details = 1;
  string heading = 2;
}
message InvestmentKeyValue{
  string label_text = 1;
  string label_value = 2;
  string label_value_color = 3;
}

message InvestmentDetailsResponse {
  InvestmentInfo investment_info = 1;
  optional InvestmentNomineeDetails nominee_details = 2;
  optional WithdrawalBankAccountDetails withdrawal_bank_account_details = 3;
  optional InterestPayoutDetails interest_payout_details = 4;
  optional SavingsBankAccountDetails savings_bank_acount_details = 5;
}

message InterestPayoutDetails {
  string heading = 1;
  string payout_type_text = 2;
  string payout_type_value = 3;
  string gains_till_date_text = 4;
  string gains_till_date_value = 5;
  string interest_paid_till_date_text = 6;
  string interest_paid_till_date_value = 7;
  string gains_this_month_text = 8;
  string gains_this_month_value = 9;
}


enum BookingStatus{
  UNKNOWN_INVESTMENT_STATUS = 0;
  ACTIVE_INVESTMENT_STATUS = 1;
  MATURED_INVESTMENT_STATUS = 2;
  WITHDRAWN_INVESTMENT_STATUS = 3;
  IN_PROGRESS_INVESTMENT_STATUS = 4;
  FAILED_INVESTMENT_STATUS = 5;
  RENEWED_INVESTMENT_STATUS = 6;
  CANCELLED_INVESTMENT_STATUS = 7;
}

message PrematureWithdrawalDetailsResponse{
  double interest_gained_till_date = 1;
  double interest_gained_if_withdrawn_today = 2;
  double interest_gained_if_withdrawn_after_some_days = 3;
  int32 days_to_increase_interest = 4;
  RedirectDeeplink redirect_deeplink = 5;
  bool is_withdrawal_possible = 6;
  int32 lock_in_period_in_days = 7;
  string lock_in_period = 8;
  string lock_in_period_end_date = 9;
}

message VkycExpiryResponse {
  string expiry_date = 1;
}

message PostWithdrawalDetail {
  message UserFixedDeposit {
    string id = 1;
    string fd_identifier = 2;
    string journey_id = 4;
    string bank_name = 6;
    string bank_logo_url = 8;
    string tenure = 10;
    double investment_amount = 12;
  }
  message WithdrawalTimeline {
    message TimelineItem {
      com.stablemoney.api.identity.DataKey title = 1;
      com.stablemoney.api.identity.DataKey description = 2;
      bool complete = 3;
    }
    repeated TimelineItem items = 3;
  }

  UserFixedDeposit user_fixed_deposit = 1;
  bool survey_submitted = 2;
  string survey_response = 3;
  int64 withdrawal_timestamp = 4;
  int64 estimated_refund_time = 5;
  bool refund_processed = 8;
  WithdrawalTimeline withdrawal_timeline = 10;
}

message WithdrawalReasonSubmitRequest {
  string user_fixed_deposit_id = 1;
  string reason_code = 2;
}

message WithdrawalReasonSubmitResponse {
}