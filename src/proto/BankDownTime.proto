syntax = "proto3";
import "google/protobuf/empty.proto";
package com.stablemoney.api.business.bankdowntime;
option java_package = "com.stablemoney.internal.api";
option java_multiple_files = true;

enum DownTimeApplicability {
  DOWN_TIME_APPLICABILITY_UNKNOWN = 0;
  NTB_ONLY = 1;
  ETB_ONLY = 2;
  BOTH = 3;
}

message WidgetConfiguration {
  enum ImpactedWidget {
    IMPACTED_WIDGET_UNKNOWN = 0;
    CAROUSEL = 1;
    COLLECTIONS = 2;
  }

  enum PositionType {
    POSITION_TYPE_UNKNOWN = 0;
    LAST = 2;
    DISAPPEAR = 3;
    SPECIFIC = 4;
  }
  message WidgetPosition {
    ImpactedWidget impacted_widget = 1;
    PositionType position_type = 2;
    int32 position = 3;
  }
  repeated WidgetPosition widget_positions = 1;
}

message BankDownTime {
  string id = 1;
  string bank_id = 2;
  DownTimeApplicability applicability = 3;
  int64 down_time_start = 4;
  int64  down_time_end = 6;
  bool is_recurring = 8;
  WidgetConfiguration widget_configuration = 10;
  string down_time_message = 12;
  string down_time_message_for_invested_user = 14;
  string down_time_message_for_non_invested_user = 16;
  string bank_name = 17;
}

message BankDownTimesField {
  string id = 1;
  string bank_id = 2;
  DownTimeApplicability applicability = 3;
  int64 down_time_start = 4;
  int64  down_time_end = 6;
  bool is_recurring = 7;
  WidgetConfiguration widget_configuration = 8;
  string bank_name = 9;
}

message DeleteBankDownTimeRequest {
  string id = 1;
}

message BankDownTimeRequest{
  string id = 1;
}

message BankDownTimeResponse{
  BankDownTime bank_down_time = 1;
}

message BanksDownTimeResponse{
  repeated BankDownTimesField BankDownTimesField = 1;
}

message BankDetail{
  string bank_id = 1;
  string bank_name = 2;
  string fsi = 3;
}
message AllBanksDetailsResponse{
  repeated BankDetail bankDetail = 1;
}

service BankDownTimeAdminService {
  rpc addOrUpdateBankDownTime(BankDownTime) returns (BankDownTime);
  rpc deleteBankDownTime(DeleteBankDownTimeRequest) returns (google.protobuf.Empty);
  rpc getBankDownTime(BankDownTimeRequest) returns (BankDownTimeResponse);
  rpc getBanksDownTime (google.protobuf.Empty) returns (BanksDownTimeResponse);
  rpc getAllBanksDetails (google.protobuf.Empty) returns (AllBanksDetailsResponse);
}