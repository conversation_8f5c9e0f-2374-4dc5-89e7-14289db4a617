syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;
enum BankType {
  UNKNOWN_BANK_TYPE = 0;
  NBFC_BANK_TYPE = 1;
  PRIVATE_BANK_TYPE = 2;
  PUBLIC_BANK_TYPE = 3;
  COOPERATIVE_BANK_TYPE = 4;
  SMALL_FINANCE_BANK_TYPE = 5;
  POST_OFFICE_SAVINGS_BANK_TYPE = 6;
  FOREIGN_BANK_TYPE = 7;
  PAYMENTS_BANK_TYPE = 8;
}

message RedirectDeeplink{
  string path = 1;
  string path_type = 2;
}




enum InvestorType {
  UNKNOWN_INVESTOR_TYPE = 0;
  GENERAL_PUBLIC_INVESTOR_TYPE = 1;
  SENIOR_CITIZEN_INVESTOR_TYPE = 2;
  WOMEN_INVESTOR_TYPE = 3;
  WOMEN_SENIOR_CITIZEN_INVESTOR_TYPE = 4;
}

enum OfferingStatus {
  UNKNOWN_OFFERING_STATUS = 0;
  ACTIVE_OFFERING_STATUS = 1;
  INACTIVE_OFFERING_STATUS = 2;
  COMING_SOON_OFFERING_STATUS = 3;
  DELETED_OFFERING_STATUS = 4;
}

enum InterestPayoutType{
  UNKNOWN_INTEREST_PAYOUT_TYPE = 0;
  MONTHLY_INTEREST_PAYOUT_TYPE = 1;
  QUARTERLY_INTEREST_PAYOUT_TYPE = 2;
  HALF_YEARLY_INTEREST_PAYOUT_TYPE = 3;
  YEARLY_INTEREST_PAYOUT_TYPE = 4;
  MATURITY_INTEREST_PAYOUT_TYPE = 5;
}

enum CompoundingFrequencyType{
  UNKNOWN_COMPOUNDING_FREQUENCY_TYPE = 0;
  MONTHLY_COMPOUNDING_FREQUENCY_TYPE = 1;
  QUARTERLY_COMPOUNDING_FREQUENCY_TYPE = 2;
  HALF_YEARLY_COMPOUNDING_FREQUENCY_TYPE = 3;
  YEARLY_COMPOUNDING_FREQUENCY_TYPE = 4;
}

enum UiScreenType{
  UNKNOWN_UI_SCREEN = 0;
  HOME_SCREEN = 1;
  EXPLORE_SCREEN = 2;
}

enum InvestabilityStatus {
  UNKNOWN_INVESTABILITY_STATUS = 0;
  ACTIVE_INVESTABILITY_STATUS = 1;
  INACTIVE_INVESTABILITY_STATUS = 2;
  COMING_SOON_INVESTABILITY_STATUS = 3;
}


enum InvestabilityRolloutStatus {
  UNKNOWN_INVESTABILITY_ROLLOUT_STATUS = 0;
  COMPLETE_ROLLOUT_STATUS = 1;
  PARTIALLY_ROLLOUT_STATUS = 2;
}

enum TenureFormatType {
  UNKNOWN_TENURE_FORMAT_TYPE = 0;
  DAYS_TENURE_FORMAT_TYPE = 1;
  YEAR_MONTH_DAY_TENURE_FORMAT_TYPE = 2;
}

enum BusinessProvider{
  UNKNOWN_BUSINESS_PROVIDER = 0;
  TARRAKKI_BUSINESS_PROVIDER = 1;
  UPSWING_BUSINESS_PROVIDER = 2;
}
