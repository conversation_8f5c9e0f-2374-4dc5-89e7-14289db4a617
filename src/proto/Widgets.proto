syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;
import "BusinessCommon.proto";

enum UiWidgetType{
  UNKNOWN_UI_WIDGET_TYPE = 0;
  FD_HORIZONTAL_CARDS_WITH_BUTTON = 1;
  INVESTABLE_BANKS_WIDGET = 2;
  SINGLE_BANNER_WIDGET = 3;
  SAFE_AND_SECURE_WIDGET = 4;
  STATIC_HORIZONTAL_CARDS_WIDGET = 5;
  SMALL_FD_CARD_WITH_BUTTON = 6;
}

message UiWidgetResponse{
  repeated UiWidget ui_widget = 1;
}

message UiWidget{
  UiWidgetType ui_widget_type = 1;
  UiWidgetData ui_widget_data = 2;
}

message UiWidgetData{
  FDHorizontalCardsWithButton fd_horizontal_cards_with_button = 1;
  InvestableBanksWidget investable_banks_widget = 2;
  SingleBannerWidget single_banner_widget = 3;
  SafeAndSecureWidget safe_and_secure_widget = 4;
  StaticHorizontalCardsWidget static_horizontal_cards_widget = 5;
  SmallFDCardWithButton small_fd_card_with_button = 6;
}

message FDHorizontalCardsWithButton{
  string title = 1;
  string description = 2;
  string button_text = 3;
  bool show_rank = 4;
  int32 number_of_items_to_load = 5;
  string collection_id = 6;
}


message InvestableBanksWidget{
  string title = 1;
  string description = 2;
  string button_text = 3;
  int32 number_of_items_to_load = 4;
  string collection_id = 5;
  repeated InvestableBanksWidgetHighlightTags investable_banks_widget_highlight_tags = 6;
  string footer_description = 7;
  string footer_description_icon = 8;
  string bank_item_button_text = 9;
}
message InvestableBanksWidgetHighlightTags{
  string label = 1;
  string icon = 2;
}

enum BannerType{
  UNKNOWN_BANNER_TYPE = 0;
  IMAGE_BANNER = 1;
  LOTTIE_BANNER = 2;
  SVG_BANNER = 3;
  WEBP_BANNER = 4;
}

enum BannerLayoutType{
  UNKNOWN_BANNER_LAYOUT_TYPE = 0;
  CARD_BANNER = 1;
  FLAT_BANNER = 2;
}

message SingleBannerWidget{
  BannerType banner_type = 1;
  BannerLayoutType banner_layout_type = 2;
  string title = 3;
  string description = 4;
  string button_text = 5;
  UiScreenType screen_to_open = 6;
}

message SafeAndSecureWidget{
  UiScreenType screen_to_open = 1;
}

message StaticHorizontalCardsWidget{
  string title = 1;
  string description = 2;
  repeated StaticHorizontalCardsWidgetItem static_horizontal_cards_widget_item = 3;
}

message StaticHorizontalCardsWidgetItem{
  string title = 1;
  string description = 2;
  string icon_url = 3;
  UiScreenType screen_to_open = 4;
}

message SmallFDCardWithButton{
  string title = 1;
  string description = 2;
  string button_text = 3;
  bool show_rank = 4;
  string collection_id = 5;
}