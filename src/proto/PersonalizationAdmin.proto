syntax = "proto3";
import "Common.proto";

package com.stablemoney.api.personalization;
option java_package = "com.stablemoney.api.personalization";
option java_multiple_files = true;

import "google/protobuf/struct.proto";

enum ReferenceType {
  PAGE = 0;
  FRAME = 1;
}

enum BannerType{
  BANK = 0;
  GENERAL = 1;
  BOND = 2;
}

message PageResponse {
  string path = 1;
  string name = 2;
  string id = 3;
  double created_at = 4;
  double updated_at = 5;
  string content = 6;
  string config = 7;
  bool is_public = 8;
  optional string feature_flag = 9;
  optional string variant = 10;
}

message FeatureFlagMappingResponse {
  string feature_flag = 1;
  string widget_id = 2;
  string variant = 3;
  double created_at = 4;
  double updated_at = 5;
}

message PageSummary{
  string path = 1;
  string name = 2;
  string id = 3;
  double created_at = 4;
  double updated_at = 5;
  optional double deleted_at = 6;
  optional string feature_flag = 7;
  optional string variant = 8;
}

message FeatureFlagMappingSummary {
  string id = 1;
  string feature_flag = 2;
  string widget_id = 3;
  string variant = 4;
  ReferenceType reference_type = 5;
  double created_at = 6;
  double updated_at = 7;
  optional double deleted_at = 8;
}

message PagesResponse {
  repeated PageSummary data = 1;
  com.stablemoney.api.identity.PaginationResponse pagination = 2;
}

message PagesRequest {
  com.stablemoney.api.identity.PaginationRequest pagination = 1;
  bool include_deleted = 2;
  optional string  q = 3;
}

message GetAuditLogsRequest {
  string entity = 1;
  string identifier = 2;
  int32 page_number = 3;
  int32 page_size = 4;
}

message GetAuditLogsResponse {
  message AuditLog {
    string field = 1;
    string operation = 2;
    string diff = 3;
    string updated_by = 4;
    int64 created_at = 5;
  }
  repeated AuditLog audit_logs = 1;
}

message CreatePageRequest {
  string path = 1;
  string name = 2;
  string content = 3;
  string config = 4;
  bool is_public = 5;
  optional string feature_flag = 6;
  optional string variant = 7;
  optional string username = 8;
}

message IdRequest {
  string id = 1;
}

message UpdatePageRequest {
  string id = 1;
  string path = 2;
  string name = 3;
  string content = 4;
  string config = 5;
  bool is_public = 6;
  optional string feature_flag = 7;
  optional string variant = 8;
  optional string username = 9;
}

message DeletePageResponse {
  bool success = 1;
}

message UpdateFeatureFlagMappingRequest {
  string id = 1;
  string feature_flag = 2;
  string widget_id = 3;
  string variant = 4;
  string page_id = 5;
}

message StatusResponse {
  bool success = 1;
}

message CreateFeatureFlagMappingRequest {
  string feature_flag = 1;
  string widget_id = 2;
  string type_id = 3;
  string variant = 4;
  ReferenceType reference_type = 5;
}

message DeleteFeatureFlagMappingResponse {
  bool success = 1;
}

message FeatureFlagMappingsRequest{
  string type_id = 1;
  ReferenceType reference_type = 2;
}

message FeatureFlagMappingsResponse{
  repeated FeatureFlagMappingSummary feature_flag_mappings = 1;
}

message FrameSummary{
  string name = 1;
  string id = 2;
  double created_at = 3;
  double updated_at = 4;
  optional double deleted_at = 5;
}

message CreateFrameRequest {
  string name = 1;
  string content = 2;
  string config = 3;
  optional string username = 4;
}

message FrameResponse {
  string name = 1;
  string id = 2;
  double created_at = 3;
  double updated_at = 4;
  string content = 5;
  string config = 6;
}

message UpdateFrameRequest {
  string id = 1;
  string name = 2;
  string content = 3;
  string config = 4;
  optional string username = 5;
}

message DeleteFrameResponse {
  bool success = 1;
}

message FramesRequest {
  com.stablemoney.api.identity.PaginationRequest pagination = 1;
  bool include_deleted = 2;
  optional string  q = 3;
}

message FramesResponse {
   repeated FrameSummary data = 1;
   com.stablemoney.api.identity.PaginationResponse pagination = 2;
}

message BannerSummary{
  string name = 1;
  string id = 2;
  double created_at = 3;
  double updated_at = 4;
  optional bool is_active=5;
}

message BannersRequest{
   com.stablemoney.api.identity.PaginationRequest pagination = 1;
   bool include_deleted = 2;
   optional string  q = 3;
}

message BannersResponse {
   repeated BannerSummary data = 1;
   com.stablemoney.api.identity.PaginationResponse pagination = 2;
}

message CreateBannerRequest{
  string name = 1;
  string content = 2;
  string group_name=3;
  BannerType banner_type=4;
  string entity_identifier = 5;
}

message BannerResponse{
  string name = 1;
  string id = 2;
  double created_at = 3;
  double updated_at = 4;
  string group_name = 5;
  BannerType banner_type=6;
  string content = 7;
  string entity_identifier = 8;
}

message UpdateBannerRequest {
  string id = 1;
  string name = 2;
  string group_name = 3;
  BannerType banner_type=4;
  string content=5;
  string entity_identifier = 6;
}

message DeleteBannerResponse{
  bool status=1;
}

message RuleSummary{
  string page = 1;
  string name = 2;
  string id = 3;
  string feature_flag = 4;
  string expression = 5;
  int32 priority = 6;
  string output_a=7;
  string output_b=8;
  double created_at = 9;
  double updated_at = 10;
  bool is_active = 11;
}

message RulesRequest{
   com.stablemoney.api.identity.PaginationRequest pagination = 1;
   bool include_deleted = 2;
   optional string  q = 3;
}

message RulesResponse {
   repeated RuleSummary data = 1;
   com.stablemoney.api.identity.PaginationResponse pagination = 2;
}

message CreateRuleRequest{
   string page = 1;
   string name = 2;
   string feature_flag = 3;
   string expression = 4;
   int32 priority = 5;
   string output_a = 6;
   string output_b = 7;
}

message RuleResponse{
   string page = 1;
   string name = 2;
   string id = 3;
   string feature_flag = 4;
   string expression = 5;
   int32 priority = 6;
   string output_a=7;
   string output_b=8;
   double created_at = 9;
   double updated_at = 10;
}

message UpdateRuleRequest {
  string page = 1;
  string name = 2;
  string id = 3;
  string feature_flag = 4;
  string expression = 5;
  int32 priority = 6;
  string output_a = 7;
  string output_b = 8;
}

message DeleteRuleResponse{
  bool status = 1;
}

message BannersHeadingRequest{

}

message BannersHeadingResponse{
  repeated string banners = 1;
}

message StorySummary{
  string name = 1;
  string feature_flag = 2;
  string variant = 3;
  string page = 4;
  bool is_shareable = 5;
  bool is_likeable =6;
  int32 priority = 7;
  double created_at = 8;
  double updated_at = 9;
  string id = 10;
  string start_time = 11;
  string end_time = 12;
  string content = 13;
}

message StoriesRequest{
  com.stablemoney.api.identity.PaginationRequest pagination = 1;
  optional string q = 2;
  bool include_deleted = 3;
}

message StoriesResponse{
  repeated StorySummary data = 1;
  com.stablemoney.api.identity.PaginationResponse pagination = 2;
}

message CreateStoryRequest{
    string name = 1;
    string feature_flag = 2;
    string variant = 3;
    string page = 4;
    bool is_shareable = 5;
    bool is_likeable =6;
    int32 priority = 7;
    string start_time = 8;
    string end_time = 9;
    string content = 10;
}

message UpdateStoryRequest{
  string name = 1;
  string feature_flag = 2;
  string variant = 3;
  string page = 4;
  bool is_shareable = 5;
  bool is_likeable =6;
  int32 priority = 7;
  string start_time = 8;
  string end_time = 9;
  string id = 10;
  string content = 11;
}

message DeleteStoryRequest{
  string id = 1;
}

message DeleteStoryResponse{
  bool status = 1;
}

message StoryResponse{
  string name = 1;
  string feature_flag = 2;
  string variant = 3;
  string page = 4;
  bool is_shareable = 5;
  bool is_likeable =6;
  int32 priority = 7;
  string start_time = 8;
  string end_time = 9;
  string id = 10;
  string content = 11;
}