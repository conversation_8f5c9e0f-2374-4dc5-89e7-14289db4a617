syntax = "proto3";
import "Bank.proto";

package com.stablemoney.api.business.recommendation;
option java_package = "com.stablemoney.api.business.recommendation";
option java_multiple_files = true;


message GetInsuredFixedDepositsRecommendation {
  message Investment {
    com.stablemoney.api.bank.Bank bank = 1;
    double invested_amount = 2;
    double max_amount = 3;
  }
  repeated Investment investments = 1;
}

message GetNextBestFixedDepositsRecommendation {
  message FixedDeposit {
    enum DurationType {
      UNKNOWN = 0;
      SHORT_TERM = 1;
      MEDIUM_TERM = 2;
      LONG_TERM = 3;
    }
    message Tenure {
      string tenure = 1;
      int32 days = 2;
      int32 months = 3;
      int32 years = 4;
    }
    com.stablemoney.api.bank.Bank bank = 1;
    Tenure tenure = 2;
    double interest_rate = 3;
    repeated string benefits = 5;
    bool instant_booking = 6;
    string recommendation_reason = 8;
    int32 min_tenure_in_days = 9;
    int32 max_tenure_in_days = 10;
  }
  repeated FixedDeposit fixed_deposits = 1;
}


message OneClickFixedDepositsRecommendation {
  message OneClickInvestmentBank {
    com.stablemoney.api.bank.Bank bank = 1;
    string last_invested_on = 2;
    repeated string tags = 3;
  }
  repeated OneClickInvestmentBank banks = 1;
}