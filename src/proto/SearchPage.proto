syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;
import "Collection.proto";

message SearchPageItem{

  BankResponseWithFdData bank_response_with_fd_data = 1;
  string display_name = 2;
  string operating_bank_id = 3;
}


message SearchPageResponse{

  repeated SearchPageItem search_page_item = 1;
  int32 search_hits = 2;
}