syntax = "proto3";

package com.stablemoney.api.location.city;
option java_package = "com.stablemoney.api.location.city";
option java_multiple_files = true;
import "google/protobuf/empty.proto";

message CityResponse {
  string id = 1;
  string name = 2;
  string state = 3;
  string country = 4;
  bool is_popular = 5;
  repeated string alias_list = 6;
}

message GetCitiesResponse {
  option deprecated = true;
  repeated CityResponse cities = 1;
  repeated CityResponse popular_cities = 2;
}

message GetPopularCitiesResponse {
  repeated CityResponse popular_cities = 2;
}

message SearchCityResponse {
  repeated CityResponse cities = 2;
}
