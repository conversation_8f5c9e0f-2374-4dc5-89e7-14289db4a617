syntax = "proto3";

package com.stablemoney.api.business.collection;
option java_package = "com.stablemoney.api.business.collection";
option java_multiple_files = true;

import "BusinessCommon.proto";
import "Collection.proto";
import "Bank.proto";
import "Common.proto";
import "google/protobuf/empty.proto";

message CollectionItem {
  string fd_id = 1;
  string bank_id = 3;
  double rate = 5;
  com.stablemoney.api.bank.Tenure tenure = 7;
  com.stablemoney.api.bank.TagConfig tag_config = 9;
  com.stablemoney.api.identity.RedirectDeeplink deeplink = 11;
}

message BulkCollectionRequest {
  repeated string collection_names = 1;
  com.stablemoney.api.identity.InvestorType investor_type = 2;
}

message BulkCollectionResponse {
  repeated CollectionResponse collections = 1;
}

message CollectionResponse {
  message Bank {
    string id = 2;
    string name = 4;
    string short_name = 6;
    string logo_url = 8;
    bool is_popular = 12;
    string color = 16;
    string cover_image_url = 20;
    string icon_bg_color = 22;
    com.stablemoney.api.identity.InvestabilityStatus investability_status = 24;
  }
  string id = 1;
  string name = 2;
  string title = 3;
  string description = 5;
  string short_title = 4;
  string short_description = 7;
  string icon_url = 9;
  string background_color = 10;
  repeated CollectionItem collection_items = 11;
  repeated Bank banks = 13;
}

enum CollectionType {
  MANUAL = 0;
  EXPRESSION = 1;
}

message CollectionEntity {
  string id = 1;
  string name = 2;
  string title = 3;
  string description = 4;
  string short_title = 5;
  string short_description = 6;
  string icon_url = 7;
  CollectionType collection_type = 8;
  string mapped_collection_config = 9;
  bool exclude_inactive_banks = 10;
  string expression_where_clause = 11 [deprecated = true];
  string expression_order_by_clause = 12 [deprecated = true];
  double created_at = 13;
  double updated_at = 14;
  string background_color = 15;
  bool is_sort_by_interest_rate = 16;
  bool is_pick_highest_interest_rate = 17;
}

message UpdateCollectionRequestProto {
  string id = 1;
  string name = 2;
  string title = 3;
  string description = 4;
  string short_title = 5;
  string short_description = 6;
  string icon_url = 7;
  CollectionType collection_type = 8;
  string mapped_collection_config = 9;
  bool exclude_inactive_banks = 10;
  string expression_where_clause = 11 [deprecated = true];
  string expression_order_by_clause = 12 [deprecated = true];
  string background_color = 13;
  bool is_sort_by_interest_rate = 14;
  bool is_pick_highest_interest_rate = 15;
}

message CreateCollectionRequestProto {
  string name = 2;
  string title = 3;
  string description = 4;
  string short_title = 5;
  string short_description = 6;
  string icon_url = 7;
  CollectionType collection_type = 8;
  string mapped_collection_config = 9;
  bool exclude_inactive_banks = 10;
  string expression_where_clause = 11 [deprecated = true];
  string expression_order_by_clause = 12 [deprecated = true];
  string background_color = 13;
  bool is_sort_by_interest_rate = 14;
  bool is_pick_highest_interest_rate = 15;
}

message DeleteCollectionRequestProto {
  string id = 1;
}

message CollectionsRequestProto {
  com.stablemoney.api.identity.PaginationRequest pagination = 1;
  optional string  q = 2;
}

message CollectionsResponseProto {
  repeated CollectionEntity collections = 1;
  com.stablemoney.api.identity.PaginationResponse pagination = 2;
}

message StatusResponseProto {
  bool status = 1;
}

message CollectionRequestProto {
  string id = 1;
}

message CollectionResponseProto {
  CollectionEntity collection = 1;
}

message CollectionItemsRequestProto {
  string collection_id = 1;
}


message CollectionItemEntity {
  string id = 1;
  string bank_id = 2;
  string bank_name = 3;
  int32 min_tenure_in_days = 4;
  int32 max_tenure_in_days = 5;
  int32 priority = 6;
  string display_title = 7;
  com.stablemoney.api.bank.TagConfig tag_config = 8;
  com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 9;
  bool is_active = 10;
  double created_at = 11;
  double updated_at = 12;
}

message CollectionItemsResponseProto {
  repeated CollectionItemEntity collection_items = 1;
}

message  CollectionItemRequestProto {
  string id = 1;
}

message  CollectionItemResponseProto {
  CollectionItemEntity collection_item = 1;
}
message DeleteCollectionItemRequestProto {
  string id = 1;
}

message  UpdateCollectionItemRequestProto {
  string id = 1;
  string collection_id = 2;
  string bank_id = 3;
  int32 min_tenure_in_days = 4;
  int32 max_tenure_in_days = 5;
  int32 priority = 6;
  string display_title = 7;
  com.stablemoney.api.bank.TagConfig tag_config = 8;
  com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 9;
}

message  CreateCollectionItemRequestProto {
  string collection_id = 1;
  string bank_id = 2;
  int32 min_tenure_in_days = 3;
  int32 max_tenure_in_days = 4;
  int32 priority = 5;
  string display_title = 6;
  com.stablemoney.api.bank.TagConfig tag_config = 7;
  com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 8;
}

message BankTenureRequest {
  google.protobuf.Empty empty = 1;
}

message BankTenureEntity {
  string bankName = 1;
  string bankId = 2;
  repeated TenureRange tenures = 3;
  message TenureRange {
    int32 minTenureInDays = 2;
    int32 maxTenureInDays = 3;
  }
}

message BankTenureResponse {
  repeated BankTenureEntity bankTenureList = 1;
}

message BulkUpdateFdItemPriorityRequest {
  message Item {
    string id = 1;
    int32 new_priority = 2;
  }
  repeated Item items = 1;
}

message BulkUpdateFdItemPriorityResponse {
}