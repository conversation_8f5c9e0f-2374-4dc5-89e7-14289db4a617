syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;
import "BusinessCommon.proto";

enum CollectionDataType {
  UNKNOWN_COLLECTION_DATA_TYPE = 0;
  AGGREGATED_BANK_DATA = 1;
  HIGHEST_INTEREST_RATE = 2;
  HIGHEST_INTEREST_RATE_SHORT_TERM = 3;
  HIGHEST_INTEREST_RATE_MEDIUM_TERM = 4;
  HIGHEST_INTEREST_RATE_LONG_TERM = 5;
  HIGHEST_INTEREST_RATE_TAX_SAVING = 6;
  HIGHEST_INTEREST_RATE_1_YEAR_WITH_DELTA = 7;
  HIGHEST_INTEREST_RATE_2_YEAR_WITH_DELTA = 8;
  HIGHEST_INTEREST_RATE_3_YEAR_WITH_DELTA = 9;
  HIGHEST_INTEREST_RATE_4_YEAR_WITH_DELTA = 10;
  HIGHEST_INTEREST_RATE_5_YEAR_WITH_DELTA = 11;
  HIGHEST_INTEREST_RATE_1_YEAR_WITHOUT_DELTA = 12;
  HIGHEST_INTEREST_RATE_2_YEAR_WITHOUT_DELTA = 13;
  HIGHEST_INTEREST_RATE_3_YEAR_WITHOUT_DELTA = 14;
  HIGHEST_INTEREST_RATE_4_YEAR_WITHOUT_DELTA = 15;
  HIGHEST_INTEREST_RATE_5_YEAR_WITHOUT_DELTA = 16;
  HIGHEST_INTEREST_SPECIAL_RATE_FD = 17;
  HIGHEST_INTEREST_ZERO_LOCK_IN_PERIOD_FD = 18;
  HIGHEST_INTEREST_RATE_FOR_WOMEN_FD = 19;
  HIGHEST_INTEREST_RATE_FOR_SENIOR_CITIZEN_FD = 20;
  HIGHEST_INTEREST_RATE_FOR_GENERAL_CITIZEN = 21;
}



message CollectionResponse {
  string id = 1;
  string title = 2;
  string description = 3;
  string short_title = 4;
  string short_description = 5;
  string icon_url = 6;
  repeated CollectionItem collection_item = 7;
  int32 total_count = 8;
  repeated SortOption sort_options = 9;
  repeated FilterByInstitutionTypeOption filter_by_institution_type_options = 10;
  CollectionWidgetType collection_widget_type = 11;
}

message SortOption{
  string display_text = 1;
  bool is_selected = 2;
  string sort_key = 3;
}

enum SortTypes{
  UNKNOWN_SORT_TYPE = 0;
  INTEREST_RATE_SORT = 1;
  DEFAULT_SORT = 2;
}

message FilterByInstitutionTypeOption{
  string display_text = 1;
  bool is_selected = 2;
  string filter_key = 3;
  int64 count = 4;
}

message CollectionItem {
  string description = 1;
  oneof item {
    FixedDepositResponse fixed_deposit = 2;
    BankResponseWithFdData bank_response_with_fd_data = 3;
  }
  Tag tag = 4 [deprecated = true];
  RedirectDeeplink deeplink = 6;
}

message Tag {
  string name = 1;
  string color = 2;
  string background_color = 3;
  double width = 4;
}


message FixedDepositResponse {
  string fd_id = 1;
  string raw_tenure = 2;
  TenureFormatType tenure_format_type = 3;
  BankResponse bank = 4;
  double rate = 5;
  double annualized_rate = 6;
  double min_deposit = 7;
  double max_deposit = 8;
  InvestorType investor_type = 10;
  int32 lock_in_period_in_days = 11;
  bool is_pre_mature_withdrawal_allowed = 12;
  InterestPayoutType interest_payout_type = 14;
  string breakage_charges_and_description = 15;
  bool is_loan_against_fd_allowed = 16;
  bool is_partial_withdrawal_allowed = 17;
  int32 min_tenure_in_days = 18;
  int32 max_tenure_in_days = 19;
  int32 in_denomination_of = 20;
  bool is_tax_saving = 21;
  string tenure = 22;
  int32 tenure_in_days = 23;
  int32 tenure_in_months = 24;
  int32 tenure_in_years = 25;
  repeated MaturityInstruction maturity_instructions = 26;
  repeated InterestPayoutType interest_payout_types = 27;
  string tag = 28;
}
enum MaturityInstruction{
  UNKNOWN_MATURITY_INSTRUCTION = 0;
  AUTO_RENEWAL_WITH_MATURITY_AMOUNT = 1;
  AUTO_RENEWAL_WITH_PRINCIPAL_AMOUNT = 2;
  CLOSE_ACCOUNT = 3;
}
message BankResponse {
  string id = 1;
  string name = 2;
  string logo_url = 3;
  string website_url = 4;
  bool is_insured = 5;
  BankType bank_type = 6;
  InvestabilityStatus investability_status = 7;
  bool is_popular = 8;
  string operating_bank_id = 9;
  string customer_care_number = 10 ;
  int32 establishment_year = 11 ;
  string rbi_license_url = 12 ;
  string display_name = 13;
  string color = 14;
  TagConfig tag_config = 15;
  string cover_image_url = 16;
  string icon_bg_color = 17;
  InvestabilityRolloutStatus investability_rollout_status = 18;
  map<string, string> properties = 20;
  string short_name = 22;
}

message TagConfig {
  string name = 1;
  string icon_url = 2;
  string color = 3;
  string bg_color = 4;
  string type = 5;
  string shimmer_color = 6;
}

message BankResponseWithFdData{
  BankResponse bank = 1;
  repeated FixedDepositResponse fds_for_bank_card = 2;
  FixedDepositResponse highest_interest_rate_fd = 3;
}

message BulkCollectionsResponse {
  repeated CollectionResponse collection = 1;
}


message AllCollectionsInfo{
  repeated CollectionResponse collection = 1;
}

enum CollectionDataStrategyType{
  UNKNOWN_COLLECTION_DATA_STRATEGY_TYPE = 0;
  SIMPLE_COLLECTION_DATA_STRATEGY = 1;
  AGGREGATED_COLLECTION_DATA_STRATEGY = 2;
}

enum CollectionWidgetType{
  UNKNOWN_WIDGET_TYPE = 0;
  FD_CARD = 1;
  BANK_CARD = 2;
}

message UserOngoingBookingStatusResponse{
  repeated OngoingBookingStatusData ongoing_booking_status = 1;
}

message OngoingBookingStatusData{
  string title = 1;
  string sub_title = 2;
  bool is_agents_active = 3;
  string agent_title = 4;
  string agent_sub_title = 5;
  BankResponse bank = 6;
  string cta = 7;
  RedirectDeeplink redirect_deeplink = 8;
  OngoingBookingStatus state = 9;
  string payment_success_time = 10;
  string latest_status = 11;
  string message = 12;
  double amount = 13;
  double interest_rate = 14;
  string tenure_days = 15;
  string distinct_id = 16;
  bool is_withdrawal_card = 17;
  string withdrawal_date = 18;
}

message OngoingBookingStatusDataV2{
  string title = 1;
  string sub_title = 2;
  bool is_agents_active = 3;
  string agent_title = 4;
  string agent_sub_title = 5;
  BankResponse bank = 6;
  OngoingBookingStatus state = 7;
  string payment_success_time = 8;
  optional string primary_cta = 9;
  optional RedirectDeeplink primary_redirect_deeplink = 10;
  optional string secondary_cta = 11;
  optional RedirectDeeplink secondary_redirect_deeplink = 12;
  optional string message = 13;
  string bank_id = 14;
  double amount = 15;
  double interest_rate = 16;
  string tenure_days = 17;
  string tag = 18;
  string tag_color = 19;
  string distinct_id = 20;
  bool is_withdrawal_card = 21;
  string withdrawal_date = 22;
}

message UserOngoingBookingStatusResponseV2{
  repeated OngoingBookingStatusDataV2 ongoing_booking_status = 1;
}

enum OngoingBookingStatus{
  UNKNOWN_BOOKING_STATUS = 0;
  VKYC_PENDING_STATUS = 1;
  BOOKING_IN_PROGRESS = 2;
  VKYC_SUCCESS_STATUS = 3;
  RESUME_JOURNEY = 4;
}

message PaymentFailureItem {
  string id = 1;
  double amount = 2;
  optional string bank_name = 3;
  optional string bank_id = 4;
}

message PaymentFailureCardResponse {
  repeated PaymentFailureItem paymentFailureItemList = 1;
  bool hasPaymentSuccess = 2;
}

enum UserCancellationAction{
  UNKNOWN_CANCELLATION_ACTION = 0;
  NO_ACTION = 1;
  CANCEL_CANCELLATION = 2;
  DO_NOT_CANCEL_CANCEL_CANCELLATION = 3;
}

message WithdrawalData{
  string id = 1;
  UserCancellationAction user_cancellation_action = 2;
  WithdrawalStatus withdrawal_status = 3;
  string withdrawal_time = 4;
  string withdrawal_cancellation_deadline = 5;
  repeated WithdrawalTimeLineNode withdrawal_time_line = 6;
  string bottom_info_text = 7;
  bool has_money_credited = 8;
  bool has_answered_cancellation_questions = 9;
  string withdrawal_reason = 10;
}

enum WithdrawalStatus{
  UNKNOWN_WITHDRAWAL_STATUS = 0;
  WITHDRAWAL_INITIATED = 1;
  WITHDRAWAL_IN_PROCESSING = 2;
  WITHDRAWAL_PROCESSED = 3;
}
message WithdrawalTimeLineNode{
  string title = 1;
  string sub_title = 2;
  bool is_completed = 3;
}

message SubmitWithdrawalReasonRequest{
  string withdrawal_id = 1;
  string reason = 2;
}

message SubmitWithdrawalReasonResponse {
}

message WithdrawalCancellationRequest{
  option deprecated = true;
  string withdrawal_id = 1;
  repeated WithdrawalCancellationQuestionAndAnswer qa = 2;
}

message UserWithdrawalCancellationActionRequest{
  string withdrawal_id = 1;
  UserCancellationAction user_cancellation_action = 2;
}


message UserWithdrawalCancellationActionResponse{
}
message WithdrawalCancellationQuestionAndAnswer{
  string question = 1;
  string answer = 2;
}

message WithdrawalCancellationResponse{
  option deprecated = true;
}

message BankDetails{
  string bankId = 1;
  string logo = 2;
  string bank_name = 3;
}

message LocationBasedRecommendedFd{
  double rate = 1;
  string tenure = 2;
  BankDetails bank_details = 3;
}