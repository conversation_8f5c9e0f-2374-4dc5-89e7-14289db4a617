syntax = "proto3";

package com.stablemoney.api.investment.withdrawal;
option java_package = "com.stablemoney.api.investment.withdrawal";
option java_multiple_files = true;
import "Common.proto";


message WithdrawalTimeline {
  message TimelineItem {
    com.stablemoney.api.identity.DataKey data_key = 1;
    bool complete = 2;
  }
  repeated TimelineItem items = 3;
}

message WithdrawalSurvey {
  message UserFixedDeposit {
    string id = 1;
    string fd_identifier = 2;
    string journey_id = 4;
    string bank_name = 6;
    string bank_logo_url = 8;
    string tenure = 10;
    double investment_amount = 12;
  }
  UserFixedDeposit user_fixed_deposit = 1;
  bool survey_submitted = 2;
  string survey_response = 3;
  int64 withdrawal_timestamp = 4;
  int64 estimated_refund_time = 5;
  bool refund_processed = 8;
  WithdrawalTimeline withdrawal_timeline = 10;
}
