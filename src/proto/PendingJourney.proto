syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;
import "BusinessCommon.proto";
import "Collection.proto";

message PendingJourney {
  enum JourneyType {
    ACTION_UNKNOWN = 0;
    VKYC_FAILED = 2;
    VKYC_REQUIRED = 4;
    VKYC_RETRY_REQUIRED = 6;
    VKYC_INITIATED = 8;
    VKYC_EXPIRED = 10;
  }

  string id = 1;
  string icon_url = 2;
  repeated RichTextResponse title = 3;
  string timestamp = 4;
  RedirectDeeplink redirect_deeplink = 5;
  PendingJourneyCardType card_type = 6;
  string bank_id = 7;
  JourneyType journey_type = 9;
}

message RichTextResponse{
  repeated RichTextNode title = 1;
  string id = 2;
}

enum PendingJourneyCardType{
  UNKNOWN_PENDING_JOURNEY_CARD_TYPE = 0;
  SIMPLE_PENDING_JOURNEY_CARD_TYPE = 1;
  VKYC_ATTEMPT_CONFIRMATION_PENDING_JOURNEY_CARD_TYPE = 2;
}

message PendingJourneyResponse{
  optional BottomSheetResponse bottom_sheet = 1;
  repeated PendingJourney pending_journeys = 2;
}

message BottomSheetResponse{
  repeated RichTextNode title = 1;
  repeated RichTextNode sub_title = 2;
  RedirectDeeplink redirect_deeplink = 3;
  string icon_url = 4;
  string button_cta = 5;
}

message  VkycAttemptConfirmationResponse{

}

message VkycAttemptConfirmationRequest{
  string id = 1;
  bool was_successful = 2;
}
message RichTextNode{
  string text = 1;
  string color = 2;
}

message VkycSlotSelectionResponse{

}

message VkycSlotSelectionRequest{
  string id = 1;
  string slot_id = 2;
}


message VkycDetailsResponse{
  bool is_expired = 1;
  string id = 2;
  oneof details{
    VkycPendingPageDetails vkyc_pending_page_details = 3;
    VkycFailurePageDetails vkyc_failure_page_details = 4;
  }
  BankResponse bank = 5;
  string faq_category = 6;
}

message VkycPendingPageDetails{
  repeated RichTextNode title = 1;
  string logo_url = 2;
  repeated RichTextNode bottom_text = 3;
  string timestamp = 4;
  VKYCSteps steps = 5;
  VkycSchedule slots = 6;
}

message VkycFailurePageDetails{
  repeated RichTextNode title = 1;
  string logo_url = 2;
  repeated RichTextNode sub_title = 3;
  repeated RichTextNode bottom_text = 4;
  repeated VkycFailureEvents failure_events = 7;
  repeated TipsForNextAttempt tips_for_next_attempt = 8;

}

message TipsForNextAttempt{
  repeated RichTextNode text = 1;
  repeated RichTextNode subtext = 2;
  repeated BankDescription bank_description = 3;
  RedirectDeeplink redirect_deeplink = 4;
}
message BankDescription{
  BankResponse bank = 1;
  string description = 2;
}

message VkycFailureEvents{
  string timestamp = 1;
  string text = 2;
  VkycFailureEventType type = 3;
}
enum VkycFailureEventType{
  UNKNOWN_VKYC_FAILURE_EVENT_TYPE = 0;
  SUCCESS_VKYC_FAILURE_EVENT_TYPE = 1;
  FAILURE_VKYC_FAILURE_EVENT_TYPE = 2;
  NEUTRAL_VKYC_FAILURE_EVENT_TYPE = 3;
}

message VkycSchedule{
  bool is_agent_available = 1;
  VkycBookingStatus booking_status = 2;
  repeated DaySchedule days = 3;
  RedirectDeeplink vkyc_redirect_deeplink = 4;
}

message VkycBookingStatus{
  bool is_slot_booked = 1;
  repeated RichTextNode description = 2;
}

message DaySchedule {
  string day = 1;
  string timestamp = 2;
  repeated VkycSlot slots = 3;
}


message VkycSlot{
  string id = 1;
  string display_text = 2;
  bool is_recommended = 3;
}


message VKYCSteps{
  repeated RichTextNode title = 1;
  repeated VKYCStep steps = 2;
}

message VKYCStep{
  repeated RichTextNode title = 1;
  repeated RichTextNode short_title = 2;
  Media media = 3;
}

message Media{
  string url = 1;
  MediaTypes type = 2;
}

enum MediaTypes{
  UNKNOWN_MEDIA_TYPE = 0;
  IMAGE_MEDIA_TYPE = 1;
  TIMESTAMP_FOR_VKYC_COUNTDOWN_MEDIA_TYPE = 2;
}