syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;
import "Collection.proto";
import "BusinessCommon.proto";


enum QuestionResponseType {
  UNKNOWN_QUESTION_RESPONSE_TYPE = 0;
  SINGLE_RESPONSE_QUESTION = 1;
  MULTIPLE_RESPONSE_QUESTION = 2;
}

enum QuestionnaireType {
  QUESTIONNAIRE_TYPE_UNKNOWN = 0;
  FIND_MY_FD = 1;
}


message FindMyFdResponse {
  repeated Question questionnaire = 1;
}

message Question {
  string id = 1;
  string question = 2;
  string description = 3;
  string submit_button_text = 4;
  bool is_skippable = 5;
  QuestionResponseType question_response_type = 6;
  repeated AnswerResponse answer = 7;
  string parameter_asked = 9;
}

message AnswerResponse {
  string id = 1;
  string answer = 2;
  string description = 3;
  string parameter_value = 5;
  string short_description = 6;
}

message FindMyFdSubmitRequest {
  repeated Response responses = 1;
  bool is_skipped = 2;
  double investment_amount = 3;
  optional int32 number_of_nib_recommendations = 4;
  optional int32 number_of_ib_recommendations = 5;
}

message Response {
  string question_id = 1;
  repeated string answer_id = 2;
}

message FindMyFdSubmitResponse {
  repeated FindMyFdRecommendation recommendations = 1;
  string recommendation_id = 2;
}

message RecommendationReviewRequest{
  bool did_recommendation_helped_you = 1;
  string recommendation_id = 2;
}

message RecommendationReviewResponse{
}


message FindMyFdRecommendation{
  FixedDepositResponse fixed_deposit = 1;
  double investment_amount = 2;
  double returns = 3;
  double maturity_amount = 4;
  RedirectDeeplink redirect_deeplink = 5;
  repeated BenefitsDetails benefitsDetails = 6;
}

message BenefitsDetails{
  string benefit = 1;
  string icon_url = 2;
}
message RecommendationReviewSubmitRequest {
  string recommendation_id = 1;
  bool has_helped_you = 2;
}

message RecommendationReviewSubmitResponse{
}

message InitiateFindMyFdResponse {
  bool is_find_my_fd_initiated = 1;
  repeated FindMyFdRecommendation recommendations = 2;
  bool is_recommendation_feedback_submitted = 3;
  double investment_amount = 4;
  string recommendation_id = 5;
  FindMyFdSubmitRequest find_my_fd_submit_request = 6;
}