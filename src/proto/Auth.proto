syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;
import "Device.proto";
import "google/protobuf/empty.proto";


enum AuthType {
  LOGIN_TYPE_UNKNOWN = 0;
  EMAIL_OTP = 1;
  GOOGLE = 2;
  APPLE = 3;
  MOBILE_OTP = 4;
}

enum AuthProcess {
  AUTH_PROCESS_UNKNOWN = 0;
  LOGIN = 1;
  VERIFY = 2;
}

message InitiateAuthRequest {
  AuthType auth_type = 1;
  UserDevice user_device = 2;
  oneof result {
    string email = 3 ;
    AppleLoginRequest apple_login_request = 4;
    GoogleLoginRequest google_login_request = 5;
    MobileLoginRequest mobile_login_request = 6;
  }
}

message AppleLoginRequest {
  string authorisation_code = 1 ;
  string name = 2;
}

message GoogleLoginRequest {
  string id_token = 1 ;
}

message InitiateAuthResponse {
  string user_id = 1 ;
  oneof result {
    OTPChallenge otp_challenge = 2;
    AuthenticationResult authentication_result = 3;
  }
}

message AuthenticationResult {
  string token = 1;
  string refresh_token = 2;
  string token_type = 3;
  string expires_in = 4;
}

message RespondToAuthChallengeResponse {
  string user_id = 1;
  AuthenticationResult authentication_result = 2;
}


message OTPChallenge {
  string challenge_id = 1;
  int64 expiry = 2;
}

message RespondToAuthChallengeRequest {
  string user_id = 1 ;
  string challenge_id = 2 ;
  string answer = 3 ;
}

message RefreshTokenRequest {
  string token = 1;
}

message RefreshTokenResponse {
  string user_id = 1;
  AuthenticationResult authentication_result = 2;
}

message MobileLoginRequest {
  string mobile = 1;
  string country_code = 2 ;
  string encrypted_mobile = 3;
  string encryption_key = 4;
}

message InitiateVerifyRequest {
  AuthType auth_type = 1;
  oneof result {
    string email = 2 ;
    AppleLoginRequest apple_login_request = 3;
    GoogleLoginRequest google_login_request = 4;
    MobileLoginRequest mobile_login_request = 5;
  }
}

message InitiateVerifyResponse {
  oneof result {
    bool is_email_verified = 1 ;
    OTPChallenge otp_challenge = 2;
  }
}

message RespondToVerifyChallengeRequest {
  string challenge_id = 1 ;
  string answer = 2 ;
}

message RespondToVerifyChallengeResponse {
}

message EmptyProtoResponse{
}

message ZohoTokenGenerationResponse{
  string jwtToken = 1;
}