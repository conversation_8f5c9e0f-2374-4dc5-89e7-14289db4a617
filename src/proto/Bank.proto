syntax = "proto3";

package com.stablemoney.api.bank;
option java_package = "com.stablemoney.api.bank";
option java_multiple_files = true;
import "BusinessCommon.proto";


message Bank {
  string id = 1;
  string name = 2;
  string logo_url = 3;
  com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 4;
}

message AgentAvailabilityDetails{
  bool is_available = 1;
  string availability_timings_description = 2;
  bool is_holiday = 3;
  string holiday_description = 4;
}

message Tenure {
  string raw_tenure = 1;
  string tenure = 2;
  int32 tenure_in_days = 3;
}


message TagConfig {
  string name = 1;
  string icon_url = 2;
  string color = 3;
  string bg_color = 4;
  string type = 5;
}
