syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;
import "Onboarding.proto";
import "BusinessCommon.proto";
import "Device.proto";
import "Collection.proto";
import "Campaign.proto";


enum IncomeRange {
  UNKNOWN_INCOME_RANGE = 0;
  LESS_THAN_1L = 1;
  BETWEEN_1L_AND_5L = 2;
  BETWEEN_5L_AND_10L = 3;
  BETWEEN_10L_AND_25L = 4;
  BETWEEN_25L_AND_ABOVE = 5;
}


message UserProfileInternalResponse {
  UserProfileResponse profile = 1;
  repeated UserDevice user_devices = 2;
}

enum EmploymentType {
  UNKNOWN_EMPLOYMENT_TYPE = 0;
  PRIVATE_SECTOR_SERVICE = 1;
  PUBLIC_SECTOR = 2;
  BUSINESS = 3;
  PROFESSIONAL = 4;
  AGRICULTURIST = 5;
  RETIRED = 6;
  HOUSEWIFE = 7;
  STUDENT = 8;
  FOREX_DEALER = 9;
  GOVERNMENT_SERVICE = 10;
  OTHERS_EMPLOYMENT_TYPE = 11;
  SELF_EMPLOYED = 12;
}

enum Gender {
  UNKNOWN_GENDER = 0;
  MALE = 1;
  FEMALE = 2;
  OTHER_GENDER = 3;
}

enum MaritalStatus {
  UNKNOWN_MARITAL_STATUS = 0;
  SINGLE = 1;
  MARRIED = 2;
}

enum TradingExperience {
  UNKNOWN_TRADING_EXPERIENCE = 0;
  LESS_THAN_1_MONTH = 1;
  BETWEEN_1_MONTH_AND_6_MONTH = 2;
  BETWEEN_6_MONTH_AND_1_YEAR = 3;
  BETWEEN_1_YEAR_AND_ABOVE = 4;
}

message UserData {
  string id = 1;
  string email = 2;
  bool email_verified = 3;
  string mobile = 4;
  bool mobile_verified = 5;
  string name = 6;
  string masked_email = 8;
  string first_name = 9;
  string last_name = 10;
  string social_name = 11;
  string profile_image_url = 12;
  string user_registration_time = 13;

}

message UserProfileResponse {
  UserData data = 2;
  UserProfileData profile_data = 3;
  repeated OnboardingModuleStatusData module_status = 4;
  string life_time_status = 5;
  string current_status = 6;
}

message UserProfileData {
  string id = 1;
  string pan_number = 2;
  string aadhar_number = 3;
  string dob = 4;
  Gender gender = 5;
  IncomeRange income_range = 6;
  EmploymentType employment_type = 7;
  TradingExperience trading_experience = 8;
  MaritalStatus marital_status = 9;
  string father_name = 10;
  string mother_name = 11;
  string e_sign_url = 12;
  string income_tax_department_name = 13;
  string kra_name = 14;
  int32 fd_booking_count = 16;
  bool first_fd_reward_claimed = 17;
  bool is_gold_member = 18;
  bool is_upswing_ticket_raised = 19;
  string in_app_review_cohort = 20 [deprecated = true];
  string ticket_cohort = 21;
  bool has_lifetime_investment = 22;
  string new_user_home_ui_config = 23; // 1. app_home_ui_250_reward  2. app_home_ui_200_reward 3. app_home_ui_no_reward
  string new_user_reward_ui_config = 24; // 1. app_reward_ui_250_reward  2. app_reward_ui_200_reward 3. app_reward_ui_no_reward
  bool show_credit_score = 25;
  string referer_name = 26;
  string rewards_info_string = 27;
  bool has_my_investments = 28;
  bool is_special_gold_member = 29;
  bool is_rating_available = 30;
  bool show_credit_refresh = 31;
  string investment_user_home_ui_config = 33; //app_home_fincare/app_home/gt_home_fincare/gt_home
  double profile_completion_percentage = 34;
  bool is_senior_citizen = 35;
  bool view_senior_citizen_rates = 36;
  bool is_referer_gold_member = 37;
  string appsflyer_referral_link = 38;
  optional CampaignType referral_campaign = 39; // This is the campaign name through which this user was referred
}

message UpdateProfileRequest {
  optional string dob = 1;
  optional IncomeRange income_range = 2 ;
  optional EmploymentType employment_type = 3 ;
  optional TradingExperience trading_experience = 4 ;
  optional MaritalStatus marital_status = 5 ;
  optional string father_name = 6 ;
  optional string mother_name = 7 ;
  optional Gender gender = 8 ;
}

message UpdateProfileResponse {
}

message OnboardingModuleStatusData{
  OnboardingModule name = 1;
  bool status = 2;
}

message UpdateNameResponse {
}

message UpdateMediaSourceRequest {
  string media_source = 1;
}

message UpdateMediaSourceResponse {
}


message UpdateNameRequest {
  string first_name = 1;
  string last_name = 2;
  optional bool update_name = 3;
}

message UpdateAcquisitionParamsRequest {
  map<string, string> params = 1;
}

message UpdateAcquisitionParamsResponse {
  bool params_updated = 1;
}

message DeleteUserRequest {
  string user_id = 1;
  string reason = 2 ;
}

message DeleteUserResponse {
}


message CreditScorePanResponse{
  string pan_number = 1;
}

message CityDataResponse{
  bool is_city_investor_visible = 1;
  string city_name = 2;
  int64 investor_city_count = 3;
}

message PanDetailsResponse{
  string pan_number = 1;
  string dob = 2;
  bool is_pan_available = 3;
}

message UserDetailsResponse {
  string user_id = 1;
  optional string first_name = 2;
  optional string last_name = 3;
  optional string profile_image_url = 4;
}
