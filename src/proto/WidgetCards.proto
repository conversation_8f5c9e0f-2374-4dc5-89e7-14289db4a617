syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

enum WidgetType {
  WIDGET_TYPE_UNKNOWN = 0;
  GLOBAL_WIDGET = 1;
  HOME_PAGE_WIDGET = 2;
}

message HomeMessagingWidgetItem{
  string title = 1;
  string description = 2;
  string icon_url = 3;
  string icon_type = 4;
  double icon_width = 5;
  double icon_height = 6;
}

message HomeMessagingWidgetResponse{
  repeated HomeMessagingWidgetItem home_messaging_widget_item = 1;
}

 message MessageWidgetMappingRequest{
   string message_widget_id = 1;
  repeated string user_id = 2;
 }

message MessageWidgetMappingResponse{
  int32 mappings_added = 1;
  int32 invalid_users = 2;
}