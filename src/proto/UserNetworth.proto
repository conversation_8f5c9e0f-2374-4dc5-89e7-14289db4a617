syntax = "proto3";

package com.stablemoney.internal.api;
option java_package = "com.stablemoney.internal.api";
option java_multiple_files = true;


message GetUserNetWorthHistoryRequest {
  string user_id = 1;
}

message GetUserNetWorthHistoryResponse {
  message UserProfile {
    string name = 1;
    string member_since = 2;
    bool is_new_user = 3;
  }
  message NetWorthLog {
    string date = 1;
    double net_worth = 2;
  }
  UserProfile user_profile = 1;
  double total_value = 2;
  double net_gains = 3;
  repeated NetWorthLog net_worth_logs = 4;
  double percentile = 5;
  int32 active_deposits_count = 6;
}