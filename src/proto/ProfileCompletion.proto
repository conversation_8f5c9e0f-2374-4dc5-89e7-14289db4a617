syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;
import "Device.proto";
import "google/protobuf/empty.proto";
import "Profile.proto";
import "City.proto";


message ProfileCompletionStepsResponse{
  repeated ProfileCompletionStep steps = 1;
  string next_step = 2;
  bool is_completed = 3;
  double completion_percentage = 4;
}

message ProfileCompletionStep{
  string step_name = 1;
  bool is_completed = 2;
  int32 priority = 3;
}

message ProfileCompletionRequest {
  string step_name = 1;
  optional PersonalDetails personal_details = 2;
  optional string dob = 3;
  optional DemographicDetails demographic_details = 4;
  optional EmploymentDetails employment_details = 5;
  repeated UserBank user_bank = 6;
}

message UserBank{
  string bank_id = 1;
  bool has_fd_with_bank = 2;
}

message ProfileCompletionResponse {
  string title = 1;
  string image_url = 2;
  string image_type = 3;
  string description = 4;
  double completion_percentage = 5;
  string button_text = 6;
  string next_step = 7;
}

message PersonalDetails {
  string first_name = 1;
  string last_name = 2;
}

message DemographicDetails {
  Gender gender = 1;
  MaritalStatus marital_status = 2;
  string city_id = 3 ;
}

message EmploymentDetails {
  ProfileCompletionEmploymentType employment_type = 1 ;
  ProfileCompletionIncomeRange monthly_income = 2 ;
}

enum ProfileCompletionIncomeRange {
  UNKNOWN_PROFILE_COMPLETION_INCOME_RANGE = 0;
  LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE = 1;
  BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE = 2;
  BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE = 3;
  BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE = 4;
  BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE = 5;
  ABOVE_5L_PROFILE_COMPLETION_INCOME = 6;
}

enum ProfileCompletionEmploymentType {
  UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 0;
  SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 1;
  SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 2;
  RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 3;
  HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 4;
  OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 5;
}


message GetProfileResponse {
  PersonalDetails personal_details = 1;
  string dob = 2;
  DemographicDetailsResponse demographic_details = 3;
  EmploymentDetails employment_details = 4;
  repeated Bank banks = 5;
  string background_image_url = 6;
}

message DemographicDetailsResponse {
  Gender gender = 1;
  MaritalStatus marital_status = 2;
  com.stablemoney.api.location.city.CityResponse city = 3;
}

message Bank{
  string name = 1;
  string id = 2;
  string icon_url = 3;
  bool has_fd_with_bank = 4;
}

