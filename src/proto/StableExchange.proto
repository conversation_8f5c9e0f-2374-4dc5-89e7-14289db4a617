syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

message QuestionData {
  string question = 1;
  string question_id = 2;
  string perspective = 3;
  string user_name = 4;
  int32 user_age = 5;
  string user_gender = 6;
  string user_city = 7;
  string user_image = 8;
  string response_count = 9;
  string registration_date = 10;
  repeated AnswerList answer_data = 11;
  bool is_reminder_set = 12;
  int32 reminder_count = 13;
  string question_posted_date = 14;
  bool is_question_answered = 15;
  bool is_question_posted = 16;
  string agree_percentage = 17;
  string wel_done_string = 18;
  repeated string responder_images = 19;
  ReminderAskString reminder_ask_string = 20;
  string user_first_name = 21;
  string question_posted_date_string = 22;
  string question_posted_end_date = 23;
}

message TodayQuestionResponse {
  QuestionData question_data = 1;
}

message AnswerList {
  string answer_id = 1;
  string answer = 2;
  double percentage = 3;
}

message AllQuestionResponse {
  repeated QuestionData question_data = 1;
}

message ReminderAskString {
  string reminder_string = 1;
  repeated string ask_string = 2;
  string completion_string = 3;
}

message SubmitAnswerRequest {
  string answer_id = 1;
  string question_id = 2;
}

message SubmitAnswerResponse {
  QuestionData question_data = 1;
}

message SubmitQuestionRequest {
  string question = 1;
}

message SubmitQuestionResponse {
}

message SubmitReminderResponse {
  QuestionData question_data = 1;
}

message SubmitReminderRequest {
  bool is_reminder = 1;
}