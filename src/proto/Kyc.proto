syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

enum KycType {
  KYC_TYPE_UNKNOWN = 0;
  POI = 1;
  KYC = 2;
  POA = 3;
  SELFIE = 4;
  ESIGN = 5;
  BANK_ACCOUNT = 6;
  DEMAT_ACCOUNT = 7;
  NOMINEE = 8;
  WET_SIGNATURE = 9;
  USER_PROFILE = 10;
  PAN_KYC = 11;
  NAME = 12;
  QUESTIONNAIRE = 13;
  EMAIL = 14;
  WHITELIST_CHECK = 15;
}

enum ProofType{
  PROOF_TYPE_UNKNOWN = 0;
  PAN = 1;
  AADHAR = 2;
  PASSPORT = 3;
  DRIVING_LICENSE = 4;
  VOTER_ID = 5;
  GOVT_ID = 6;
  REGULATORY_ID = 7;
  PSU_ID = 8;
  BANK_ID = 9;
  PUBLIC_FINANCIAL_INSTITUTION_ID = 10;
  COLLEGE_ID = 11;
  PROFESSIONAL_BODY_ID = 12;
  CREDIT_CARD = 13;
  OTHER_ID = 16;
  BANK_PASSBOOK = 17;
  BANK_ACCOUNT_STATEMENT = 18;
  RATION_CARD = 19;
  LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE = 20;
  LAND_LINE_TELEPHONE_BILL = 21;
  ELECTRICITY_BILL = 22;
  GAS_BILL = 23;
  FLAT_MAINTENANCE_BILL = 24;
  INSURANCE_COPY = 25;
  SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE = 26;
  POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS = 27;
  POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS = 28;
  POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT = 29;
  POA_ISSUED_BY_PARLIAMENT = 30;
  POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY = 31;
  POA_ISSUED_BY_NOTARY_PUBLIC = 32;
  POA_ISSUED_BY_GAZETTED_OFFICER = 33;
  ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT = 34;
  ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES = 35;
  ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS = 36;
  ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS = 37;
  ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS = 38;
  ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES = 39;
  ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL = 40;
}

enum KycProvider {
  KYC_PROVIDER_UNKNOWN = 0;
  NONE = 1;
  CDSL = 2;
  DIGIO = 3;
  HYPERVERGE = 4;
  TARRAKKI = 5;
}

enum OnBoardingStatus {
  ONBOARDING_STATUS_UNKNOWN = 0;
  PENDING = 1;
  COMPLETE = 2;
  REJECTED = 3;
  SKIPPED = 4;
}

message GenerateTokenRequest {
}

message GenerateTokenResponse {
  string id = 1;
  string access_token = 2;
  string customer_identifier = 3;
  bool is_new = 4;
}

message InitiateKycRequest {
  KycType kyc_type = 1;
  oneof result {
    PanKycRequest pan_kyc_request = 2;
    WetSignatureRequest wet_signature_request = 3;
    SelfieRequest selfie_request = 4;
    KycRequest kyc_request = 5;
    EsignRequest esign_request = 6;

  }
}

message EsignRequest {
  EsignStep step = 1 ;
  oneof result {
    GenerateTokenRequest generate_token_request = 2;
    StatusRequest status_request = 3;
  }
}

enum EsignStep{
  ESIGN_STEP_UNKNOWN = 0;
  ESIGN_STEP_GENERATE_TOKEN = 1;
  ESIGN_STEP_STATUS = 2;
}

message KycRequest {
  KycStep step = 1 ;
  oneof result {
    GenerateTokenRequest generate_token_request = 2;
    StatusRequest status_request = 3;
  }
}


message InitiateKycResponse {
  oneof result{
    PanKycResponse pan_kyc_response = 1;
    WetSignatureResponse wet_signature_response = 2;
    SelfieResponse selfie_response = 3;
    KycResponse kyc_response = 4;
    EsignKycResponse esign_response = 5;
  }
}

message EsignKycResponse {
  oneof result {
    GenerateTokenResponse generate_token_response = 1;
    StatusResponse status_response = 2;
  }
}

message KycResponse {
  oneof result {
    GenerateTokenResponse generate_token_response = 1;
    StatusResponse status_response = 2;
  }
}

message SelfieRequest {
  SelfieKycStep step = 1 ;
  oneof result {
    StartSelfiStepRequest start_selfi_step_request = 2;
    SetSelfiStatusRequest set_selfi_status_request = 3;
  }


}

message StartSelfiStepRequest{}


message SetSelfiStatusRequest{
  string transaction_id = 1;
  string status = 2 ;
}

message SelfieResponse {
  oneof result {
    StartSelfiStepResponse start_selfi_step_response = 1;
    SetSelfiStatusResponse set_selfi_status_response = 2;
  }
}

message StartSelfiStepResponse {
  string selfie = 1;
  string workflow_id = 2;
  string access_token = 3;
  string transaction_id = 4;
}

message SetSelfiStatusResponse {
}

message PanKycResponse{
  PanKycStep step = 1;
  oneof result {
    KycFetchNameByPanResponse kyc_fetch_name_by_pan_response = 2;
    KycValidateNameAndGetPanStatusResponse kyc_validate_name_and_get_pan_status_response = 3;
  }
}

enum PanKycStep {
  UNKNOWN_PAN_STEP = 0;
  NAME_FETCH = 1;
  PAN_STATUS = 2;
}


enum KycStep {
  UNKNOWN_STEP = 0;
  GENERATE_TOKEN = 1;
  GET_STATUS = 2;
}


enum SelfieKycStep {
  UNKNOWN_SELFIE_STEP = 0;
  START_SELFIE_STEP = 1;
  SET_STATUS = 2;
}


message PanKycRequest {
  PanKycStep step = 1;
  oneof result {
    KycFetchNameByPanRequest kyc_fetch_name_by_pan_request = 2;
    KycValidateNameAndGetKycStatusRequest kyc_validate_name_and_get_kyc_status_request = 3;
  }
}

message KycFetchNameByPanResponse {
  string pan = 1;
  string full_name = 2;
}

message KycFetchNameByPanRequest {
  string pan = 1 ;
}

message KycValidateNameAndGetPanStatusRequest{
  bool is_name_match = 1;
  string dob = 2 ;
}

message KycValidateNameAndGetPanStatusResponse {
  bool pan_kyc_status = 1;
}



message StatusRequest {
  string id = 1;
}

message StatusResponse {
  string kyc_status = 1;
  string description = 2;
  string digilocker_status = 3;
}

enum RaaDuration {
  UNKNOWN_RAA_DURATION = 0;
  RAA_60_DAYS = 1;
  RAA_90_DAYS = 2;
}

message WetSignatureRequest{
  RaaDuration raa_duration = 1 ;
  bool is_pep = 2 ;
  bool is_indian_citizen = 3 ;
  string document_id = 4 ;
  bool credit_report_consent = 5 ;
}

message WetSignatureResponse{
}

message KycValidateNameAndGetKycStatusRequest {
  bool is_name_match = 1;
}
