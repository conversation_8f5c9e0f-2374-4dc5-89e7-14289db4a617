syntax = "proto3";

import "google/protobuf/timestamp.proto";
package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

enum VoucherType {
  VOUCHER_TYPE_UNKNOWN = 0;
  AMAZON_PAY_VOUCHER = 1;
  AMAZON_VOUCHER_CODE = 2;
}

enum RewardCode {
  REWARD_CODE_UNKNOWN = 0;
  FIRST_FD_REWARD = 1;
  REFERRAL_REWARD = 2;
  INVESTMENT_BASED_REFERRAL_REWARD = 3;
  REFEREE_FIRST_FD_REWARD = 4;
  EMERGENCY_FUND_REFERRAL_REWARD = 5;
}

enum VoucherStatus {
  VOUCHER_STATUS_UNKNOWN = 0;
  SENT = 1;
  AVAILABLE = 2;
  EXPIRED = 3;
}

enum RewardDeliveryStatus {
  REWARD_PROCESSING_STATUS_UNKNOWN = 0;
  QUEUED = 1;
  FAILED = 2;
  SENT_FROM_BACKEND = 3;
  DELIVERED = 4;
  OPENED = 5;
  REWARD_TO_BE_SENT = 6;
  REWARD_NOT_ELIGIBLE = 7;
}

enum BlacklistUserType {
  BLACKLIST_USER_TYPE_UNKNOWN = 0;
  INTERNAL = 1;
  EXTERNAL = 2;
}

enum ReferralStatus {
  REFERRAL_STATUS_UNKNOWN = 0;
  BOOKING_NOT_INITIATED = 1;
  BOOKING_DONE = 2;
  REWARD_PROCESSED = 3;
  REWARD_NOT_APPLICABLE = 4;
}

message VoucherItem {
  string voucher_code = 1;
  double amount = 2;
  RewardCode reward_code = 3;
  VoucherStatus voucher_status = 4;
  optional google.protobuf.Timestamp expiry_date = 5;
  VoucherType voucher_type = 6;
}

message BulkVoucherRequest {
  repeated VoucherItem voucher_items_list = 1;
}

message AddRewardTransactionRequest {
  string reward_link = 1;
  string phone_number = 2;
  google.protobuf.Timestamp transaction_timestamp = 3;
}

message BulkAddRewardTransactionsRequest {
  repeated AddRewardTransactionRequest transactions = 1;
}

message ManualAddRewardRequest {
  RewardCode reward_code = 1;
  string user_id = 2;
  optional string referrer_user_id = 3;
  double amount = 4;
  string bank_id = 5;
  optional bool consume_booking = 6;
  optional bool override_payment_time_check = 7;
}

message CheckGoldenTicketRequest {
  repeated string userIdList = 1;
}

message RewardItem {
  string reward_link = 1;
  google.protobuf.Timestamp reward_sent_timestamp = 2;
  double reward_amount = 3;
  RewardCode reward_code = 4;
  RewardDeliveryStatus reward_delivery_status = 5;
}

message RewardsDataResponse {
  double total_earnings = 1;
  repeated RewardItem rewards = 2;
}

message RewardTypesResponse {
  repeated RewardType reward_types = 1;
}

message RewardTypesRequest {
  optional RewardCode rewardCode = 1;
}

message RewardType {
  string id = 1;
  double amount = 2;
  uint32 max_count = 3;
  double min_transaction_amount = 4;
  RewardCode reward_code = 5;
  optional uint64 valid_from = 6;
  optional uint64 valid_till = 7;
  optional double instant_reward_tr_amount = 8;
  optional uint32 delivery_delay_in_days = 9;
  optional float referral_percent = 10;
  optional uint32 referral_level = 11;
  optional double referer_min_net_worth = 12;
  optional double golden_ticket_amount = 13;
  optional uint32 randomized_reward_percentage = 14;
  optional uint32 referral_count = 15;
}
