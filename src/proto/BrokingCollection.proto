syntax = "proto3";
option java_package = "com.stablemoney.api.broking.collection";
package com.stablemoney.api.broking;
option java_multiple_files = true;
import "google/protobuf/empty.proto";

enum CollectionType {
    UNKNOWN = 0;
    MANUAL = 1;
    EXPRESSION = 2;
}

message AllCollectionsResponse {
  message Collection {
    string name = 1;
    string title = 2;
    string id = 3;
    CollectionType collection_type = 4;
    bool is_active = 5;
  }
  repeated Collection collections = 1;
}

message TagConfig{
  string name = 1;
  string icon_url = 2;
  string color = 3;
  string bg_color = 4;
  string type = 5;
}

enum DisplayType {
    DISPLAY_TYPE_UNKNOWN = 0;
    DEFAULT = 1;
    MINIMUM_INVESTMENT = 2;
    SHORT_TERM = 4;
    SHORT_TERM_XIRR = 6;
    SELLING_OUT_SOON = 8;
  }

message CreateBondCollectionRequest {
  string description = 1;
  string icon_url = 2;
  bool is_active = 3;
  string name = 4;
  string title = 5;
  string pre_filter_criteria = 8;
  string post_filter_criteria = 9;
  string sort_criteria = 10;
  int32 collection_item_limit = 11;
  CollectionType collection_type = 12;
  DisplayType display_type = 13;
  string excluded_isin = 14;
}

message CreateBondCollectionResponse {
  bool status = 1;
}

message UpdateBondCollectionResponse {
  bool status = 1;
}

message IdRequest {
  string id = 1;
}

message UpdateBondItemPriorityRequest {
  string id = 1;
  int32 new_priority = 2;
}

message UpdateBondItemPriorityResponse {
  bool status = 1;
}


message BondCollectionResponse {
  string description = 1;
  string icon_url = 2;
  bool is_active = 3;
  string name = 4;
  string title = 5;
  string pre_filter_criteria = 8;
  string post_filter_criteria = 9;
  string sort_criteria = 10;
  int32 collection_item_limit = 11;
  CollectionType collection_type = 12;
  string id = 13;
  DisplayType display_type = 15;
  string excluded_isin = 16;
}

message DeleteBondCollectionResponse {
  bool status = 1;
}

message UpdateBondCollectionRequest {
  string id = 1;
  string description = 2;
  string icon_url = 3;
  bool is_active = 4;
  string name = 5;
  string title = 6;
  string pre_filter_criteria = 9;
  string post_filter_criteria = 10;
  string sort_criteria = 11;
  int32 collection_item_limit = 12;
  bool include_sold_out = 13;
  CollectionType collection_type = 14;
  DisplayType display_type = 15;
  string excluded_isin = 16;
}

message BondsCollection {
  string updated_at = 1;
  string description = 2;
  string name = 3;
  string created_at = 4;
  string id = 5;
}

message GetBondsCollectionRequest {
  int32 page = 1;
  int32 size = 2;
  string search_string = 4;
}

message GetBondsCollectionResponse {
  repeated BondsCollection data = 1;
  bool has_next_page = 2;
}

message CreateAndUpdateBondCollectionItemRequest {
  string id = 1;
  string display_title = 2;
  int32 priority = 3;
  string isin_code = 5;
  bool is_active = 6;
  string collection_id = 7;
  string created_at = 8;
  string updated_at = 9;
  string selling_point = 10;
  string button_cta = 11;
  double strucken_yield = 12;
  bool show_tag = 13;
  bool is_dynamic_tag = 14;
}

message BondCollectionItemResponse {
  string id = 1;
  string display_title = 2;
  int32 priority = 3;
  com.stablemoney.api.broking.TagConfig tag_config = 4;
  string isin_code = 5;
  bool is_active = 6;
  string collection_id = 7;
  string created_at = 8;
  string updated_at = 9;
  string selling_point = 10;
  string button_cta = 11;
  double strucken_yield = 12;
  bool show_tag = 13;
  bool is_dynamic_tag = 14;
}

message DeleteBondCollectionItemResponse {
  bool status = 1;
}

message GetBondsCollectionItemRequest {
  string collection_id = 1;
}

message GetBondsCollectionItemResponse {
  repeated BondCollectionItemResponse data = 1;
}

message GetBondDetailsRequest {
 }

message BondDetails{
 string isin_code = 1;
 string display_title = 2;
 string name = 3;
}
 message GetBondDetailsResponse {
   repeated BondDetails data = 1;
 }

message BondsCollectionItemStatusResponse{
  bool status = 1;
}

message BulkUpdateBondItemPriorityRequest {
  message Item {
    string id = 1;
    int32 new_priority = 2;
  }
  repeated Item items = 1;
}

message BulkUpdateBondItemPriorityResponse {
  bool status = 1;
}

message GetAllCollectionExpressionsResponse {
  message CollectionExpression {
    string name = 1;
    string pre_filter_criteria = 2;
    string post_filter_criteria = 3;
    string sort_criteria = 4;
  }
  repeated CollectionExpression collection_expressions = 1;
}

service BrokingCollectionAdminService {
  rpc CreateBondCollection(CreateBondCollectionRequest) returns (CreateBondCollectionResponse);
  rpc UpdateBondCollection(UpdateBondCollectionRequest) returns (UpdateBondCollectionResponse);
  rpc DeleteBondCollection(IdRequest) returns (DeleteBondCollectionResponse);
  rpc GetBondCollection(IdRequest) returns (BondCollectionResponse);
  rpc GetBondsCollection(GetBondsCollectionRequest) returns (GetBondsCollectionResponse);
  rpc CreateAndUpdateBondItemCollection(CreateAndUpdateBondCollectionItemRequest) returns (BondsCollectionItemStatusResponse);
  rpc DeleteBondItemCollection(IdRequest) returns (DeleteBondCollectionItemResponse);
  rpc GetBondItemCollection(IdRequest) returns (BondCollectionItemResponse);
  rpc GetBondsItemCollection(GetBondsCollectionItemRequest) returns (GetBondsCollectionItemResponse);
  rpc GetBondsDetails (GetBondDetailsRequest) returns (GetBondDetailsResponse);
  rpc GetAllBondCollections(google.protobuf.Empty) returns (AllCollectionsResponse);
  rpc UpdateBondItemPriority(UpdateBondItemPriorityRequest) returns (UpdateBondItemPriorityResponse);
  rpc BulkUpdateBondItemPriority(BulkUpdateBondItemPriorityRequest) returns (BulkUpdateBondItemPriorityResponse);
  rpc GetAllCollectionExpressions(google.protobuf.Empty) returns (GetAllCollectionExpressionsResponse);
}