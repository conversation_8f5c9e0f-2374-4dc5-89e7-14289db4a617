syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;
import "BusinessCommon.proto";
import "Collection.proto";
import "Faq.proto";
import "google/protobuf/timestamp.proto";


enum DeviceInvestabilityStatus {
  UNKNOWN = 0;
  ACTIVE = 1;
  INACTIVE = 2;
  COMING_SOON = 3;
  ACTIVE_ON_APP = 4;
}

message FDReturnCalculatorResponse {
  double extra_income = 1;
  double matured_amount = 2;
}

message BankListingPageDataResponse {
  BankResponse bank_response = 1;
  string fd_booking_stats = 2 ;
  string assurance_line = 3 ;
  string assurance_logo_url = 4 ;
  repeated StableMoneyAnalysis stable_money_analysis = 5 ;
  BankListingPageInterestData general_citizen_interest_data = 6 ;
  BankListingPageInterestData senior_citizen_interest_data = 7 ;
  double senior_citizen_roi_max_difference = 10 ;
  string rate_last_updated_at = 11;
  string rates_pdf_url = 12 ;
  string about_bank_info = 13 ;
  repeated BankProminentPersonnel bank_prominent_personnel = 14 ;
  repeated BankAward bank_award = 15 ;
  repeated SafeBank safe_banks = 16 ;
  repeated BankCustomerTestimonial bank_customer_testimonial = 17 ;
  repeated InvestNowMessage invest_now_message = 18 ;
  repeated com.stablemoney.api.business.faq.Faq bank_faq = 19 ;
  RedirectDeeplink redirect_deeplink = 20;
  bool is_first_time_offer_applicable = 21;
}

message BankListingPageInterestData{
  FixedDepositResponse highest_interest_fd = 1;
  repeated FixedDepositResponse fixed_deposits = 2;
  repeated RecommendedFd recommended_fds = 3;
  string disclaimer = 4;
  InvestorType investor_type = 5;
  double roi_max_difference_from_general = 6;
}

message InvestNowMessage {
  string message = 1 ;
}

message StableMoneyAnalysis {
  string description = 1 ;
}

message SafeBank {
  string bank_id = 1 ;
  string logo_url = 2 ;
}

message BankProminentPersonnel {
  string heading = 1 ;
  string name = 2 ;
  string description = 3 ;
  string picture_url = 4 ;
}

message BankAward {
  string title = 1 ;
  string icon_url = 2 ;
  string received_by = 3 ;
}

message BankCustomerTestimonial {
  string comment = 1 ;
  string name = 2 ;
  string designation = 3 ;
  string picture_url = 4 ;
}

message RecommendedFd{
  string description = 1;
  FixedDepositResponse fixed_deposit_response = 2;
  string icon_url = 3;
}


message BankListingV2{
  BankResponse bank_response = 1;
  repeated TopInfoCard top_info_card = 2;
  repeated BankTag bank_tags = 3;
  repeated BankListingPageInterestData interest_data = 4;
  repeated BankSellingPoint bank_selling_points = 5;
  RedirectDeeplink redirect_deeplink = 6;
  string fd_booking_stats = 7;
  repeated com.stablemoney.api.business.faq.Faq bank_faq = 8;
  string rate_last_updated_at = 10;
  string rates_pdf_url = 11;
  string assurance_line = 12 ;
  string assurance_logo_url = 13 ;
  bool is_first_time_offer_applicable = 14;
  bool is_vkyc_required = 15;
  repeated string bank_faq_category_priority_list = 28;
}

message HighestRateKeyValue {
  InvestorType investor_type = 1;
  FixedDepositResponse fixed_deposit = 2;
}

message InvestableBankCompare {
  double interest_rate = 1;
  BankResponse bank_response = 2;
}

message NonInvestableBankCompare {
  message CompareLineItem {
    string title = 1;
    string highest_bank_point = 2;
    string current_bank_point = 3;
    RedirectDeeplink more_details_link = 4;
  }
  repeated string titles = 1 [deprecated = true];
  repeated string highest_bank_details = 2 [deprecated = true];
  repeated string bank_details = 3 [deprecated = true];
  BankResponse highest_rate_bank_response = 4;
  repeated CompareLineItem line_items = 6;
}

message FdWithdrawalCalculation {
  string tenure = 1;
  double withdrawal_amount = 2;
  double rate = 3;
  double original_rate = 4;
}

message FdWithdrawalCalculationDetails {
  double interest_rate = 1;
  double investment_amount = 2;
  string tenure = 3;
}


message BankListingV3{
  enum DeviceInvestabilityStatus {
    UNKNOWN = 0;
    ACTIVE = 1;
    INACTIVE = 2;
    COMING_SOON = 3;
    ACTIVE_ON_APP = 4;
  }
  BankResponse bank_response = 1;
  repeated BankListingPageInterestData interest_data = 4;
  RedirectDeeplink redirect_deeplink = 6;
  repeated com.stablemoney.api.business.faq.Faq bank_faq = 8;
  bool is_vkyc_required = 15;
  repeated HighestRateKeyValue highest_rate_map = 17;
  string investable_bank_down_time_message = 18;
  repeated InvestableBankCompare investable_bank_compare = 19;
  NonInvestableBankCompare non_investable_bank_compare = 20;
  repeated FdWithdrawalCalculation fd_withdrawal_calculation = 21;
  string non_investable_bank_message = 22;
  string bank_image_url = 23;
  FdWithdrawalCalculationDetails fd_withdrawal_calculation_details = 24;
  bool is_repeat_user_to_platform = 25;
  bool is_repeat_user_to_bank = 26;
  repeated string bank_faq_category_priority_list = 28;
  DeviceInvestabilityStatus device_investability_status = 30;
}

message BankListing {
  string id = 1;
  string slug = 2;
  string logo_url = 3;
  string name = 4;
  string keywords = 5;
  bool account_required_to_invest = 6;
  BankType bank_type = 7;
  InvestabilityStatus investability_status = 8;
  repeated com.stablemoney.api.business.faq.Faq faqs = 9;
  repeated WithdrawalOption withdrawal_options = 10;
  repeated FixedDepositInfo fixed_deposits = 11;
  repeated Downtime downtimes = 12;
  RedirectDeeplink invest_link = 13;
  double withdrawal_penalty_rate = 14;
  repeated string bank_faq_category_priority_list = 15;
}

message FixedDepositInfo {
  string id = 1;
  double rate = 2;
  double min_deposit = 3;
  double max_deposit = 4;
  InvestorType investor_type = 5;
  bool is_pre_mature_withdrawal_allowed = 6;
  InterestPayoutType interest_payout_type = 7;
  bool is_loan_against_fd_allowed = 8;
  bool is_partial_withdrawal_allowed = 9;
  string display_tenure = 10;
  uint32 min_tenure_in_days = 11;
  uint32 max_tenure_in_days = 12;
}

message Downtime {
  uint64 start_on = 1;
  uint64 end_on = 2;
}

message WithdrawalOption {
  double rate = 1;
  uint32 min_tenure_in_days = 2;
  uint32 max_tenure_in_days = 3;
  double min_deposit = 4;
  double max_deposit = 5;
  InvestorType investor_type = 6;
  CompoundingFrequencyType compounding_frequency = 7;
}

message TopInfoCard {
  string title = 1;
  string description = 2;
  string icon_url = 3;
  string info_bottom_sheet_title = 4;
  string info_bottom_sheet_description = 5;
}

message BankTag{
  string tag_id = 1;
  string tag_name = 2;
  string icon_url = 3;
}

message BankSellingPoint{
  string title = 1;
  string description = 2;
  string icon_url = 3;
}

message BankFixedDepositStep{
  string title = 1;
  string description = 2;
  string image_url = 3;
}

message BankFixedDepositStepsResponse{
  repeated BankFixedDepositStep bank_fixed_deposit_step = 1;
}

message BankRateNotificationResponse {
  bool status = 1;
}

message BankRateNotificationRequest {
  string bank_id = 1;
  optional bool to_notify = 2;
}

message AllBanksResponse {
  repeated BankListing banks = 1;
}

message BranchLocations {
  repeated BranchLocation branch_locations = 1;
  string city_name = 3;
  int32 total_branches = 5;
  int32 total_city_branches = 7;
}

message BranchLocation {
  message LatLong {
    double latitude = 1;
    double longitude = 2;
  }
  string address = 1;
  string area = 2;
  string city = 3;
  string state = 4;
  string pincode = 5;
  LatLong latLong = 6;
  string phone_number = 7;
}

message MediaItem {
  enum MediaType {
    UNKNOWN = 0;
    IMAGE = 1;
    VIDEO = 2;
  }
  enum ScreenType {
    SCREEN_TYPE_UNKNOWN = 0;
    MOBILE = 1;
    DESKTOP = 2;
  }
  string section = 1;
  MediaType media_type = 2;
  string url = 3;
  ScreenType screen_type = 4;
  optional com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 5;
}

message CompareLineItem {
  string title = 1;
  string highest_bank_point = 2;
  string current_bank_point = 3;
  RedirectDeeplink more_details_link = 4;
}

message BankListingV4 {
  message UserBankRelationship {
    string relationship_type = 1;
    int32 number_of_bookings = 2;
    double total_invested_amount = 3;
    string last_invested_on = 4;
    bool vkyc_completed = 5;
  }
  BankResponse bank_response = 1;
  repeated string marketing_highlights = 18;
  repeated Tenure tenures = 2;
  optional float women_roi_diff = 3;
  bool is_rate_change_notification_enabled = 17;
  RedirectDeeplink redirect_deeplink = 4;
  optional RedirectDeeplink sip_redirect_deeplink = 5;
  optional RedirectDeeplink rd_redirect_deeplink = 6;
  InvestableBankCompareV4 investable_bank_compare = 7;
  NonInvestableBankCompareV4 non_investable_bank_compare = 8;
  FdWithdrawalCalculationDetails fd_withdrawal_calculation_details = 9;
  repeated FdWithdrawalCalculation fd_withdrawal_calculation = 10;
  DeviceInvestabilityStatus user_device_investability_status = 11;
  string share_image_url = 12;
  repeated string highlight_pointers = 13;
  map<string, BankTable> bank_tables = 14;
  map<string, BankPointers> bank_pointers = 15;
  repeated MediaItem media_items = 16;
  string user_bank_relationship_type = 19 [deprecated = true];
  UserBankRelationship user_bank_relationship = 20;
  optional float senior_roi_diff = 21;
}

message BankTable {
  repeated BankTableItem items = 1;
  string footer_text = 2;
}

message BankTableItem {
  string key = 1;
  string value = 2;
  optional string sub_value = 3;
  repeated string tags = 4;
}

message BankPointers {
  repeated BankPointerItem items = 1;
}

message BankPointerItem {
  string pointer = 1;
  repeated string tags = 2;
}

message Tenure {
  string raw_tenure = 1;
  TenureFormatType tenure_format_type = 2;
  map<string, double> rates = 3; // will hold investor type to rate map
  int32 min_tenure_in_days = 4;
  int32 max_tenure_in_days = 5;
  string tenure = 6;
  string tag = 8;
  repeated InterestPayoutToMaturityInstructionMapping interest_payout_to_maturity_instruction_mapping = 9;
  int32 tenure_in_days = 10;
  int32 tenure_in_months = 11;
  int32 tenure_in_years = 12;
  string fd_id = 13;
  map<string, bool> is_highest_for_investor_type = 14;
  map<string, InterestRate> interest_rates = 15;
  InterestPayoutType default_interest_payout_type = 16;
  MaturityInstruction default_maturity_instruction = 17;
  double minimum_deposit = 18;
  double maximum_deposit = 19;
}

message InterestRate {
  string fd_id = 3;
  double rate = 1;
  double xirr = 2;
}

message InterestPayoutToMaturityInstructionMapping {
  InterestPayoutType interest_payout_type = 1;
  repeated MaturityInstruction instructions = 2;
}

message BankSummaryResponse {
  string id = 1;
  string name = 2;
  string logo_url = 3;
  string short_name = 4;
  optional double highest_interest_rate = 5;
}

message InvestableBankCompareV4 {
  repeated BankSummaryResponse compared_banks = 1;
}

message NonInvestableBankCompareV4 {
  BankSummaryResponse current_bank = 1;
  BankSummaryResponse highest_bank = 2;
  repeated CompareLineItem line_items = 3;
}


message SearchBankResponse {
  message Bank {
    string bank_name = 1;
    string bank_logo = 2;
    string bank_id = 3;
    bool is_partner = 4;
  }

  repeated Bank banks = 1;
}