syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;
import "Collection.proto";
import "Investment.proto";
import "BusinessCommon.proto";

enum GoalType {
  UNKNOWN_GOAL_TYPE = 0;
  EMERGENCY_FUND = 1;
}

enum EmergencyFundExpenseCategory {
  HOUSEHOLD_EXPENSES = 0;
  KIDS_EXPENSES = 1;
  TRAVEL_EXPENSES = 2;
  PARENTS_HEALTH_EXPENSES = 3;
  KIDS_EDUCATION_EXPENSES = 4;
  JOB_LOSS_EXPENSES = 5;
  MEDICAL_EXPENSES = 6;
  REPAIRS_EXPENSES = 7;
  OTHER_EXPENSES = 8;
}

enum EmergencyFundUserCohort {
  HAS_KIDS_AND_PARENTS_COHORT = 0;
  HAS_KIDS_COHORT = 1;
  HAS_PARENTS_COHORT = 2;
  MARRIED_COHORT = 3;
  SINGLE_COHORT = 4;
}

message GoalDetails {
  string id = 1;
  double target_amount = 2;
  double current_amount = 3;
  double progress_percentage = 4;
  string progress_description = 5;
  string progress_description_dashboard = 6;
  bool is_reminder_date_selected = 7;
  int32 reminder_date_of_month = 8;
  double gains = 9;
  int32 target_duration_months = 10;
  string achievement_badge = 11;
  string share_text = 12;
  string progress_sub_description_dashboard = 13;
  double fd_goal = 14;
  double savings_account_goal = 15;
  double savings_min_limit = 16;
  double savings_max_limit = 17;
  double savings_account_amount = 18;
  double fd_amount = 19;
}

message GetGoalDetailsResponse {
  bool is_active = 1;
  bool is_goal_created = 2;
  GoalDetails goal_details = 3;
}

message GoalCreateRequest{
  GoalType goal_type = 1 ;
  int32 target_duration_months = 2 ;
  double income = 3 ;

}
message GoalCreateResponse {
  GoalDetails goal_details = 1;
  RecommendedBankForGoalResponse recommended_bank = 2;
}

message RecommendedBankForGoalResponse{
  bool has_dicgc_limit_reached = 1;
  bool is_active = 2;
  repeated RecommendedBankSellingPointsResponse selling_points = 3;
  BankResponse bank = 4;
  FixedDepositResponse highest_interest_rate_fd = 5;
  RedirectDeeplink redirect_deeplink = 6;
  double recommended_amount = 7;
  double current_invested_amount = 8;
}

message RecommendedBankSellingPointsResponse{
  string icon_url = 1;
  string description = 2;
}


message GoalNotificationRequest{
  GoalType goal_type = 1 ;
  int32 date_of_month = 2 ;
}

message GoalNotificationResponse{
  GoalDetails goal_details = 1;
}

message RecommendedBankListResponse{
  repeated RecommendedBankForGoalResponse recommended_banks = 1;
}

message GoalInvestmentListResponse{
  repeated GoalInvestmentInfo investments = 1;
}

message GoalInvestmentInfo{
  BankResponse bank = 1;
  string maturity_date = 2;
  string booking_date = 3;
  double amount = 4;
  double interest_rate = 5;
  string id = 6;
}
message GoalSetInvestmentListRequest{
  GoalType goal_type = 1;
  repeated string investment_ids = 2;
}

message GoalSetInvestmentListResponse{
  GoalDetails goal_details = 1;
}


message SelectBankRequest{
  GoalType goal_type = 1;
  string bank_id = 2;
}
message SelectBankResponse{

}

message UpdateSavingsAmountResponse{

}

message UpdateSavingsAmountRequest{
  double savings_account_amount = 1;
}

message EmergencyFundStepsResponse{
  repeated EmergencyFundSteps steps = 1;
  string next_step = 2;
  bool is_completed = 3;
  double completion_percentage = 4;
}

message EmergencyFundSteps{
  string step_name = 1;
  bool is_completed = 2;
  int32 priority = 3;
}

message EmergencyFundQuestionnaireRequest{
  string step = 1;
  optional CityStep city_step = 2;
  optional FamilyDetailsStep family_details_step = 3;
  optional MonthlyExpenseStep monthly_expense_step = 4;
  optional InsuranceStep insurance_step = 5;
  optional ModifyPlanStep modify_plan_step = 6;
  optional AcceptPlanStep accept_plan_step = 7;
  optional MonthlyPledgeStep monthly_pledge = 8;
  optional GetPlanRecommendation plan_recommendation = 9;
}

message GetEmergencyFundResponse{
  string city_id = 1;
  bool is_paying_emi = 2;
  bool is_paying_rent = 3;
  bool is_married = 4;
  bool has_kids = 5;
  bool do_parents_depend_on_you = 6;
  double monthly_expense = 7;
  bool has_health_insurance = 8;
  bool has_life_insurance = 9;
  double goal_amount = 10;
  double monthly_pledge = 12;
  double recommended_amount = 13;
  bool is_active = 14;
  string city = 15;
}

message EmergencyFundQuestionnaireResponse{
  double goal_amount = 1;
  repeated RecommendedBankForGoalResponse recommended_banks = 2;
  double fd_goal = 3;
  double savings_account_goal = 4;
  double mapped_investments_amount = 5;
  double fd_contribution_amount = 6;
  string savings_recommended_prompt = 7;
  double savings_min_percentage = 8;
  double savings_max_percentage = 9;
  bool has_mappable_investments = 10;
  double highest_fd_return = 12;
  double savings_account_amount = 13;
}

message GetPlanRecommendation{
  double goal_amount = 1;
}

message CityStep{
  string city_id = 1;
  bool is_paying_emi = 2;
  bool is_paying_rent = 3;
}

message FamilyDetailsStep{
  bool is_married = 1;
  bool has_kids = 2;
  bool do_parents_depend_on_you = 3;
}

message MonthlyExpenseStep{
  double monthly_expense = 1;
  double emi_amount = 2;
}

message InsuranceStep{
  bool has_health_insurance = 1;
  bool has_life_insurance = 2;
}

message EmergencyFundGoal {
  optional double goal_amount = 1;
  double emi_amount = 2;
  double monthly_expense = 3;
  bool has_health_insurance = 4;
  bool has_kids = 5;
  bool is_married = 6;
  bool has_dependent_parents = 7;
  string city_id = 8;
}

message EmergencyFundMilestone {
  string name = 1;
  optional double amount = 2;
  bool is_completed = 3;
}

message EmergencyFundResponse {
  EmergencyFundGoal goal = 1;
  double invested_amount = 2;
  repeated EmergencyFundMilestone milestones = 3;
}

message EmergencyFundReferralData {
  string referral_link = 1;
  repeated EmergencyFundReferralReward rewards = 2;
  repeated EmergencyFundReferralProgramMember referees = 3;
  EmergencyFundReferralProgramMember referrer = 4;
}

message EmergencyFundReferralReward {
  int64 referral_count = 1;
  double reward_amount = 2;
}

message EmergencyFundReferralProgramMember {
  string id = 1;
  string first_name = 2;
  string last_name = 3;
  float goal_progress = 4;
  repeated EmergencyFundMilestone milestones = 5;
}

message GetRecommendedGoalAmountResponse {
  double recommended_amount = 1;
}

message ModifyPlanStep {
  double goal_amount = 1;
  double fd_goal = 2;
  double savings_account_goal = 3;
}

message AcceptPlanStep{
  double fd_goal = 1;
  double savings_account_goal = 2;
  bool map_existing_investments = 3;
}

message MonthlyPledgeStep{
  double monthly_pledge_amount = 1;
}

message EmergencyFundDashboardResponse{
  bool has_done_first_investment = 1;
  GetGoalDetailsResponse goal_details = 2;
  PledgeDetail pledge = 3;
  repeated RecommendedBankForGoalResponse recommended_banks = 4;
  repeated RecommendedBankForGoalResponse chosen_banks = 5;
  repeated RecommendedBankForGoalResponse other_banks = 6;
  bool is_new_ef_user = 7;
  string recommended_savings_prompt = 8;
}

message PledgeDetail{
  double monthly_pledge = 1;
  repeated Contribution contributions = 2;
  int64 next_contribution_days = 3;
}
message Contribution{
  string month = 1;
  string year = 2;
  ContributionStatus status = 3;
}
enum ContributionStatus{
  UNKNOWN_CONTRIBUTION_STATUS = 0;
  PENDING_CONTRIBUTION_STATUS = 1;
  COMPLETED_CONTRIBUTION_STATUS = 2;
  MISSED_CONTRIBUTION_STATUS = 3;
}

