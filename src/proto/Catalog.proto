syntax = "proto3";
import "google/protobuf/empty.proto";
import "BrokingCollection.proto";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;

enum BondType {
    BOND_TYPE_UNKNOWN = 0;
    T_BILL = 1;
    GOVT_SECURITIES = 2;
    STATE_DEVELOPMENT_LOAN = 3;
    MUNICIPAL_BONDS = 4;
    CORPORATE_BONDS = 5;
}

message MediaItem {
  enum MediaType {
    UNKNOWN_MEDIA_TYPE = 0;
    IMAGE = 1;
    VIDEO = 2;
  }
  enum ScreenType {
    SCREEN_TYPE_UNKNOWN = 0;
    MOBILE = 1;
    DESKTOP = 2;
    TABLET = 3;
  }
}

enum InventoryAlarmStatus {
  ALARM_STATUS_UNKNOWN = 0;
  NORMAL = 1;
  LEVEL_1 = 2;
}

enum RepaymentFrequency {
  REPAYMENT_FREQUENCY_UNKNOWN = 0;
  MONTHLY = 1;
  QUARTERLY = 2;
  HALF_YEARLY = 3;
  YEARLY = 4;
  CUMULATIVE = 5;
}

enum InvestabilityStatus {
  INVESTABILITY_STATUS_UNKNOWN = 0;
  LIVE = 1;
  SOLD_OUT = 2;
  COMING_SOON = 3;
  INACTIVE = 4;
}

enum RepaymentType {
  REPAYMENT_TYPE_UNKNOWN = 0;
  INTEREST_PAYMENT = 1;
  PRINCIPAL_REPAYMENT = 2;
  PRINCIPAL_REPAYMENT_AND_INTEREST_PAYMENT = 3;
}

message BondIssuingInstitutionResponse {
  string id = 1;
  string name = 2;
  string slug = 3;
  string description = 4;
  string sector = 5;
  string logo_url = 6;
  string color = 7;
  string website_url = 8;
  string created_at = 9;
  string updated_at = 10;
  string financial_snapshot_url = 11;
  string cover_image_url = 12;
  string grid_cover_image_url = 13;
  string cin = 14;
}

message BondIssuingInstitutionUpdateRequest {
  string id = 1;
  string name = 2;
  string description = 3;
  string sector = 4;
  string website_url = 5;
  string logo_url = 6;
    string color = 7;
  string financial_snapshot_url = 8;
  string cover_image_url = 9;
  string grid_cover_image_url = 10;
}

message BondIssuingInstitutionUpdateResponse {

}

message BondOfferingDetail {
  string id = 1;
  string deal_type = 2;
  string bid_offer = 3;
  string bond_detail_id = 4;
  string seller_id = 5;
  string created_at = 6;
  string updated_at = 7;
  bool is_active = 8;
  int32 min_lot_size = 9;
  int32 bond_settlement_type = 10;
  string party_id = 11;
  double yield = 12;
  string seller_order_id = 13;
  string expiry_time = 14;
  int32 repeat_user_default_quantity = 15;
  int32 new_user_default_quantity = 16;
  bool is_t_zero_settlement_supported = 17;

}

message BondOfferingDetailResponse {
  repeated BondOfferingDetail bond_offering_details = 1;
}

message BondInventoryResponse {
  string id = 1;
  string bond_offering_detail_id = 2;
  optional string count = 3;
  string created_at = 4;
  string updated_at = 5;
  optional string valid_from = 6;
  optional string valid_till = 7;
  optional bool is_active = 8;
  optional string order_limit = 9;
  optional InventoryAlarmStatus alarm_status = 10;
  optional string max_count = 11;
  optional string total_count = 12;
}

message BondDetailsDashboard{
  string id = 1;
  optional string type = 2;
  optional string issuer = 3;
  optional string name = 4;
  optional string coupon_rate = 5;
  optional string put_call = 6;
  optional RepaymentFrequency coupon_frequency = 7;
  optional RepaymentFrequency principal_repayment_frequency = 8;
  optional string principal_repayment_frequency_desc = 9;
  optional string maturity_date = 10;
  optional string display_title = 11;
  optional string isin_code = 12;
  optional string rating = 13;
  optional string rating_agency = 14;
  optional string rating_supporting_url = 15;
  string bond_issuing_institution_id = 16;
  optional BondType bond_type = 17;
  optional string type_of_yield = 18;
  optional bool is_active = 19;
  optional string nature_of_bond = 20;
  optional string issue_size = 21;
  optional string issue_date = 22;
  optional string issue_price = 23;
  optional string issue_face_value = 24;
  string created_at = 25;
  optional string updated_at = 26;
  optional string bg_color = 27;
  optional string information_memorandum = 28;
  optional string debunture_trustee = 29;
  optional string rating_date = 30;
  optional string issue_mode = 31;
  optional string nature = 32;
  optional string seniority = 33;
  optional string coupon_type = 34;
  optional string yield = 35;
  optional string per_user_purchase_limit = 36;
  optional string min_lot_size = 37;
  optional InvestabilityStatus investability_status = 38;
  optional bool is_t_zero_settlement_supported = 39;
  optional bool include_in_collection = 40;
  optional bool block_trade = 41;
  optional string sell_spread = 42;
  optional bool include_in_search = 43;
  optional string buyer_party_id = 44;
  optional string daily_sell_limit = 45;
}

message GetAllBondsRequest {
  int32 page = 1;
  int32 size = 2;
  string search_string = 3;
}

message GetAllBondsResponse {
  repeated BondDetailsDashboard bond_details = 1;
  bool has_next_page = 2;
}

message BondDetailsUpdateRequest{
  string id = 1;
  optional string type = 2;
  optional string issuer = 3;
  optional string name = 4;
  optional string coupon_rate = 5;
  optional string put_call = 6;
  optional RepaymentFrequency coupon_frequency = 7;
  optional RepaymentFrequency principal_repayment_frequency = 8;
  optional string principal_repayment_frequency_desc = 9;
  optional string maturity_date = 10;
  optional string display_title = 11;
  optional string isin_code = 12;
  optional string rating = 13;
  optional string rating_agency = 14;
  optional string rating_supporting_url = 15;
  optional BondType bond_type = 16;
  optional string type_of_yield = 17;
  optional bool is_active = 18;
  optional string nature_of_bond = 19;
  optional string issue_size = 20;
  optional string issue_date = 21;
  optional string issue_price = 22;
  optional string issue_face_value = 23;
  optional string bg_color = 24;
  optional string information_memorandum = 25;
  optional string debunture_trustee = 26;
  optional string rating_date = 27;
  optional string issue_mode = 28;
  optional string nature = 29;
  optional string seniority = 30;
  optional string coupon_type = 31;
  optional string yield = 32;
  optional string per_user_purchase_limit = 33;
  optional string min_lot_size = 34;
  optional InvestabilityStatus investability_status = 35;
  optional bool is_t_zero_settlement_supported = 36;
  optional bool include_in_collection = 37;
  optional bool block_trade = 38;
  optional string sell_spread = 39;
  optional bool include_in_search = 40;
  optional string buyer_party_id = 41;
  optional string daily_sell_limit = 42;
}

message BondDetailsUpdateResponse {
}

message AllISINSRequest {
}

message BondIssuingInstitution {
  string id = 1;
  string name = 2;
}

message AllISINSResponse {
  repeated BondIssuingInstitution bond_issuing_institutions = 1;
}

message PartyDetailsRequest {
}

message PartyDetail {
  string id = 1;
  string name = 2;
}

message PartyDetailsResponse{
  repeated PartyDetail party_details = 1;
}

enum MediaType {
  UNKNOWN_MEDIA_TYPE = 0;
  IMAGE = 1;
  VIDEO = 2;
}

enum ScreenType {
  SCREEN_TYPE_UNKNOWN = 0;
  MOBILE = 1;
  DESKTOP = 2;
  TABLET = 3;
}

enum BondMediaItemParentType {
  BOND_MEDIA_ITEM_PARENT_TYPE_UNKNOWN = 0;
  BOND_DETAIL = 1;
  BOND_ISSUING_INSTITUTION = 2;
}

message MediaItemRequest {
  string id = 1;
  BondMediaItemParentType parent_type = 2;
}

message MediaItemDashboard {
  string id = 1;
  string parent_id = 2;
  BondMediaItemParentType parent_type = 3;
  string section = 4;
  MediaType media_type = 5;
  string url = 6;
  ScreenType screen_type = 7;
  string redirect_deeplink = 8;
  string created_at = 9;
  string updated_at = 10;
  bool is_active = 11;
}

message MediaItemResponse{
  repeated MediaItemDashboard media_items = 1;
}

message MediaItemUpdateRequest {
  string id = 1;
  BondMediaItemParentType parent_type = 3;
  string section = 4;
  MediaType media_type = 5;
  string media_url = 6;
  ScreenType screen_type = 7;
  string redirect_deeplink = 8;
  bool is_active = 9;
}

message MediaItemUpdateResponse {
}

message BondDetailRequest {
  string isisn_code = 1;
}

message AllBond {
  string id = 1;
  string display_title = 2;
  bool is_active = 3;
  string name = 4;
  InvestabilityStatus investability_status = 5;
  string isin_code = 6;
  string maturity_date = 7;
  double coupon_rate = 8;
  RepaymentFrequency coupon_frequency = 9;
  int32 per_user_purchase_limit = 10;
  BondType bond_type = 11;
  bool include_in_collection = 12;
  bool is_sold_out = 13;
  string record_date = 14;
  bool is_record_date_verified = 15;
  string bond_cashflow_schedule_id = 16;
}

message AllBondDetailsRequest{

}

message AllBondDetailsResponse{
  repeated AllBond bond_details = 1;
}

message UpdateOfferingActiveStatusRequest {
  string id = 1;
  bool is_active = 2;
}

message UpdateOfferingActiveStatusResponse {
}

message UpdateBondOfferingYieldRequest {
  string bond_offering_id = 1;
  double yield = 2;
}

message UpdateBondOfferingYieldResponse {
}

message BondCashflowSchedule {
  string id = 1;
  string date = 2;
  double interest_payment_amount = 3;
  double principal_repayment_amount = 4;
  string record_date = 5;
  RepaymentType type = 6;
  double face_value = 7;
  bool is_active = 8;
  double maturity_amount = 9;
  bool is_record_date_verified = 10;
}

message GetBondCashflowScheduleRequest {
  string bond_detail_id = 1;
}

message GetBondCashflowScheduleResponse {
  repeated BondCashflowSchedule bond_cashflow_schedule = 1;
}

message CreateTagRequest {
  string name = 1;
  string color = 2;
  string bg_color = 3;
  string shimmer_color = 4;
}

message CreateTagResponse {
}

message GetAllTagsRequest {
}

message Tag {
  string id = 1;
  string name = 2;
  string color = 3;
  string bg_color = 4;
  string shimmer_color = 5;
}

message GetAllTagsResponse {
  repeated Tag tag = 1;
}

message TagCollectionItem {
  string id = 1;
  string tag_id = 2;
  string collection_id = 3;
  string collection_item_isin_code = 4;
  string tag_name = 5;
}

message CreateTagCollectionItemRequest {
  string tag_id = 1;
  string collection_id = 2;
  string collection_item_isin_code = 3;
}

message CreateTagCollectionItemResponse {
}

message GetAllTagCollectionItemRequest {
}

message GetAllTagCollectionItemResponse {
  repeated TagCollectionItem tag_collection_item = 1;
}

message DeleteTagCollectionItemRequest {
  string id = 1;
}

message DeleteTagCollectionItemResponse {
}

message UpdateTagResponse {
}

message getTagRequest {
  string tag_id = 1;
}

message BondOfferingUpdateRequest {
  string bond_offering_id = 1;
  double yield = 2;
  string min_lot_size = 3;
  string repeat_user_default_quantity = 4;
  string deal_type = 5;
  string bid_offer = 6;
  string bond_settlement_type = 7;
  string expiry_time = 8;
  string new_user_default_quantity = 9;
  bool is_t_zero_settlement_supported = 10;
}

message BondOfferingUpdateResponse {

}

message GetBondPricesRequest {
  string bond_offering_id = 1;
}

message BondPrice {
  string id = 1;
  double accrued_interest = 2;
  double clean_price = 3;
  double dirty_price = 4;
  double face_value = 5;
  bool is_active = 6;
  string settlement_date = 7;
}

message GetBondPricesResponse {
  repeated BondPrice bond_prices = 1;
}

message UpdateBondInventoryRequest {
  string bond_offering_id = 1;
  string max_count = 2;
  string order_limit = 3;
  InventoryAlarmStatus alarm_status = 4;
  string valid_from = 5;
  string valid_till = 6;
  bool is_active = 7;
  string count_difference = 8;
  string total_count_difference = 9;
}

message UpdateBondInventoryResponse {
}

message CreateMediaItemRequest {
  optional string parent_id = 1;
  optional BondMediaItemParentType parent_type = 2;
  optional string section = 3;
  optional MediaItem.MediaType media_type = 4;
  optional string media_url = 5;
  optional MediaItem.ScreenType screen_type = 6;
  optional string redirect_deeplink = 7;
}

message CreateMediaItemResponse {
}

message VerifyRecordDateRequest {
  string bond_detail_id = 1;
  string cashflow_schedule_id = 2;
}

message VerifyRecordDateResponse {
}

message GetMediaItemRequest {
  string media_item_id = 1;
}

message GetMediaItemResponse {
  MediaItemDashboard media_item = 1;
}

message UpdateRecordDateRequest {
  optional string cashflow_schedule_id = 1;
  optional string record_date = 2;
}

message UpdateRecordDateResponse {
}

service CatalogService {
  rpc getAllBondsIsActive(GetAllBondsRequest) returns (GetAllBondsResponse);
  rpc getAllBondsIsNotActive(GetAllBondsRequest) returns (GetAllBondsResponse);
  rpc getBondIssuingInstitution(IdRequest) returns (BondIssuingInstitutionResponse);
  rpc getBondOfferingDetail(IdRequest) returns (BondOfferingDetailResponse);
  rpc getBondInventory(IdRequest) returns (BondInventoryResponse);
  rpc updateBondDetails(BondDetailsUpdateRequest) returns (BondDetailsUpdateResponse);
  rpc getPartyDetails(PartyDetailsRequest) returns (PartyDetailsResponse);
  rpc getAllISINS(AllISINSRequest) returns (AllISINSResponse);
  rpc getMediaItem(MediaItemRequest) returns (MediaItemResponse);
  rpc updateMediaItem(MediaItemUpdateRequest) returns (MediaItemUpdateResponse);
  rpc updateIssuingInstitution(BondIssuingInstitutionUpdateRequest) returns (BondIssuingInstitutionUpdateResponse);
  rpc getBondDetailByISIN(BondDetailRequest) returns (BondDetailsDashboard);
  rpc getAllBondDetails(AllBondDetailsRequest) returns (AllBondDetailsResponse);
  rpc updateOfferingActiveStatus(UpdateOfferingActiveStatusRequest) returns (UpdateOfferingActiveStatusResponse);
  rpc updateBondOfferingYield(UpdateBondOfferingYieldRequest) returns (UpdateBondOfferingYieldResponse);
  rpc getBondCashflowSchedule(GetBondCashflowScheduleRequest) returns (GetBondCashflowScheduleResponse);
  rpc createTag(CreateTagRequest) returns (CreateTagResponse);
  rpc getAllTags(GetAllTagsRequest) returns (GetAllTagsResponse);
  rpc createTagCollectionItem(CreateTagCollectionItemRequest) returns (CreateTagCollectionItemResponse);
  rpc getAllTagCollectionItem(GetAllTagCollectionItemRequest) returns (GetAllTagCollectionItemResponse);
  rpc deleteTagCollectionItem(DeleteTagCollectionItemRequest) returns (DeleteTagCollectionItemResponse);
  rpc updateTag(Tag) returns (UpdateTagResponse);
  rpc getTag(getTagRequest) returns (Tag);
  rpc updateBondOffering(BondOfferingUpdateRequest) returns (BondOfferingUpdateResponse);
  rpc verifyRecordDate(VerifyRecordDateRequest) returns (VerifyRecordDateResponse);
  rpc getMediaItemById(GetMediaItemRequest) returns (GetMediaItemResponse);
  rpc updateRecordDate(UpdateRecordDateRequest) returns (UpdateRecordDateResponse);
}
