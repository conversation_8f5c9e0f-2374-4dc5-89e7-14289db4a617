syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.business";
option java_multiple_files = true;
import "Collection.proto";


message RecommendedBankItem {
  BankResponse bank_response = 1;
  double lowest_fixed_deposit_rate = 2;
  double highest_fixed_deposit_rate = 3;
  FixedDepositResponse highest_fixed_deposit_response = 4;
  bool is_recommended = 5;
  Tag tag = 6;
}

message RecommendedBankResponse {
  repeated RecommendedBankItem recommended_bank_list = 1;
}

message CompareBankCell {
  oneof value{
    string string_value = 2;
    bool bool_value = 3;
  }
  optional string string_color = 4;
  optional string caption = 5;
  optional string caption_color = 6;
}

message CompareBankItem {
  repeated CompareBankCell compare_bank_list = 1;
}

message CompareBankResponse {
  repeated RecommendedBankItem recommended_bank_list = 1;
  repeated string titles = 2;
  repeated CompareBankItem compare_bank_item_list = 3;
  repeated RecommendedBankItem add_bank_list = 4;
}


message CompareBankResponseV2 {
  repeated CompareBankItemV2 compare_bank_item_list_v2 = 1;
}

message CompareBankItemV2 {
  string bank_name = 1;
  string bank_logo = 2;
  string roi = 3;
  string bank_id = 4;
}

