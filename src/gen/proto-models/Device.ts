// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Device.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum DeviceType {
  CLIENT_TYPE_UNKNOWN = 0,
  ANDROID = 1,
  IOS = 2,
  WEB = 3,
  MOBILE_WEB = 4,
  UNRECOGNIZED = -1,
}

export function deviceTypeFromJSON(object: any): DeviceType {
  switch (object) {
    case 0:
    case "CLIENT_TYPE_UNKNOWN":
      return DeviceType.CLIENT_TYPE_UNKNOWN;
    case 1:
    case "ANDROID":
      return DeviceType.ANDROID;
    case 2:
    case "IOS":
      return DeviceType.IOS;
    case 3:
    case "WEB":
      return DeviceType.WEB;
    case 4:
    case "MOBILE_WEB":
      return DeviceType.MOBILE_WEB;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DeviceType.UNRECOGNIZED;
  }
}

export function deviceTypeToJSON(object: DeviceType): string {
  switch (object) {
    case DeviceType.CLIENT_TYPE_UNKNOWN:
      return "CLIENT_TYPE_UNKNOWN";
    case DeviceType.ANDROID:
      return "ANDROID";
    case DeviceType.IOS:
      return "IOS";
    case DeviceType.WEB:
      return "WEB";
    case DeviceType.MOBILE_WEB:
      return "MOBILE_WEB";
    case DeviceType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface UserDevice {
  id: string;
  deviceType: DeviceType;
  osVersion: string;
  model: string;
  appVersion: string;
  lastActiveTime: string;
  notificationToken: string;
  aaId: string;
  idfv?: string | undefined;
  appsflyerId?: string | undefined;
  appVersionName?: string | undefined;
  aie?: boolean | undefined;
  att?: number | undefined;
  appStore?: string | undefined;
  appInstanceId?: string | undefined;
}

export interface UpdateDeviceRequest {
  userDevice: UserDevice | undefined;
}

export interface UpdateDeviceResponse {
}

function createBaseUserDevice(): UserDevice {
  return {
    id: "",
    deviceType: 0,
    osVersion: "",
    model: "",
    appVersion: "",
    lastActiveTime: "",
    notificationToken: "",
    aaId: "",
    idfv: undefined,
    appsflyerId: undefined,
    appVersionName: undefined,
    aie: undefined,
    att: undefined,
    appStore: undefined,
    appInstanceId: undefined,
  };
}

export const UserDevice: MessageFns<UserDevice> = {
  encode(message: UserDevice, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.deviceType !== 0) {
      writer.uint32(16).int32(message.deviceType);
    }
    if (message.osVersion !== "") {
      writer.uint32(26).string(message.osVersion);
    }
    if (message.model !== "") {
      writer.uint32(34).string(message.model);
    }
    if (message.appVersion !== "") {
      writer.uint32(42).string(message.appVersion);
    }
    if (message.lastActiveTime !== "") {
      writer.uint32(50).string(message.lastActiveTime);
    }
    if (message.notificationToken !== "") {
      writer.uint32(58).string(message.notificationToken);
    }
    if (message.aaId !== "") {
      writer.uint32(66).string(message.aaId);
    }
    if (message.idfv !== undefined) {
      writer.uint32(74).string(message.idfv);
    }
    if (message.appsflyerId !== undefined) {
      writer.uint32(82).string(message.appsflyerId);
    }
    if (message.appVersionName !== undefined) {
      writer.uint32(90).string(message.appVersionName);
    }
    if (message.aie !== undefined) {
      writer.uint32(96).bool(message.aie);
    }
    if (message.att !== undefined) {
      writer.uint32(104).int64(message.att);
    }
    if (message.appStore !== undefined) {
      writer.uint32(114).string(message.appStore);
    }
    if (message.appInstanceId !== undefined) {
      writer.uint32(130).string(message.appInstanceId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserDevice {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserDevice();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.deviceType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.osVersion = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.model = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.appVersion = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.lastActiveTime = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.notificationToken = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.aaId = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.idfv = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.appsflyerId = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.appVersionName = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.aie = reader.bool();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.att = longToNumber(reader.int64());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.appStore = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.appInstanceId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserDevice {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      deviceType: isSet(object.deviceType) ? deviceTypeFromJSON(object.deviceType) : 0,
      osVersion: isSet(object.osVersion) ? globalThis.String(object.osVersion) : "",
      model: isSet(object.model) ? globalThis.String(object.model) : "",
      appVersion: isSet(object.appVersion) ? globalThis.String(object.appVersion) : "",
      lastActiveTime: isSet(object.lastActiveTime) ? globalThis.String(object.lastActiveTime) : "",
      notificationToken: isSet(object.notificationToken) ? globalThis.String(object.notificationToken) : "",
      aaId: isSet(object.aaId) ? globalThis.String(object.aaId) : "",
      idfv: isSet(object.idfv) ? globalThis.String(object.idfv) : undefined,
      appsflyerId: isSet(object.appsflyerId) ? globalThis.String(object.appsflyerId) : undefined,
      appVersionName: isSet(object.appVersionName) ? globalThis.String(object.appVersionName) : undefined,
      aie: isSet(object.aie) ? globalThis.Boolean(object.aie) : undefined,
      att: isSet(object.att) ? globalThis.Number(object.att) : undefined,
      appStore: isSet(object.appStore) ? globalThis.String(object.appStore) : undefined,
      appInstanceId: isSet(object.appInstanceId) ? globalThis.String(object.appInstanceId) : undefined,
    };
  },

  toJSON(message: UserDevice): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.deviceType !== 0) {
      obj.deviceType = deviceTypeToJSON(message.deviceType);
    }
    if (message.osVersion !== "") {
      obj.osVersion = message.osVersion;
    }
    if (message.model !== "") {
      obj.model = message.model;
    }
    if (message.appVersion !== "") {
      obj.appVersion = message.appVersion;
    }
    if (message.lastActiveTime !== "") {
      obj.lastActiveTime = message.lastActiveTime;
    }
    if (message.notificationToken !== "") {
      obj.notificationToken = message.notificationToken;
    }
    if (message.aaId !== "") {
      obj.aaId = message.aaId;
    }
    if (message.idfv !== undefined) {
      obj.idfv = message.idfv;
    }
    if (message.appsflyerId !== undefined) {
      obj.appsflyerId = message.appsflyerId;
    }
    if (message.appVersionName !== undefined) {
      obj.appVersionName = message.appVersionName;
    }
    if (message.aie !== undefined) {
      obj.aie = message.aie;
    }
    if (message.att !== undefined) {
      obj.att = Math.round(message.att);
    }
    if (message.appStore !== undefined) {
      obj.appStore = message.appStore;
    }
    if (message.appInstanceId !== undefined) {
      obj.appInstanceId = message.appInstanceId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserDevice>, I>>(base?: I): UserDevice {
    return UserDevice.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserDevice>, I>>(object: I): UserDevice {
    const message = createBaseUserDevice();
    message.id = object.id ?? "";
    message.deviceType = object.deviceType ?? 0;
    message.osVersion = object.osVersion ?? "";
    message.model = object.model ?? "";
    message.appVersion = object.appVersion ?? "";
    message.lastActiveTime = object.lastActiveTime ?? "";
    message.notificationToken = object.notificationToken ?? "";
    message.aaId = object.aaId ?? "";
    message.idfv = object.idfv ?? undefined;
    message.appsflyerId = object.appsflyerId ?? undefined;
    message.appVersionName = object.appVersionName ?? undefined;
    message.aie = object.aie ?? undefined;
    message.att = object.att ?? undefined;
    message.appStore = object.appStore ?? undefined;
    message.appInstanceId = object.appInstanceId ?? undefined;
    return message;
  },
};

function createBaseUpdateDeviceRequest(): UpdateDeviceRequest {
  return { userDevice: undefined };
}

export const UpdateDeviceRequest: MessageFns<UpdateDeviceRequest> = {
  encode(message: UpdateDeviceRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userDevice !== undefined) {
      UserDevice.encode(message.userDevice, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateDeviceRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateDeviceRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userDevice = UserDevice.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateDeviceRequest {
    return { userDevice: isSet(object.userDevice) ? UserDevice.fromJSON(object.userDevice) : undefined };
  },

  toJSON(message: UpdateDeviceRequest): unknown {
    const obj: any = {};
    if (message.userDevice !== undefined) {
      obj.userDevice = UserDevice.toJSON(message.userDevice);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateDeviceRequest>, I>>(base?: I): UpdateDeviceRequest {
    return UpdateDeviceRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateDeviceRequest>, I>>(object: I): UpdateDeviceRequest {
    const message = createBaseUpdateDeviceRequest();
    message.userDevice = (object.userDevice !== undefined && object.userDevice !== null)
      ? UserDevice.fromPartial(object.userDevice)
      : undefined;
    return message;
  },
};

function createBaseUpdateDeviceResponse(): UpdateDeviceResponse {
  return {};
}

export const UpdateDeviceResponse: MessageFns<UpdateDeviceResponse> = {
  encode(_: UpdateDeviceResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateDeviceResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateDeviceResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateDeviceResponse {
    return {};
  },

  toJSON(_: UpdateDeviceResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateDeviceResponse>, I>>(base?: I): UpdateDeviceResponse {
    return UpdateDeviceResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateDeviceResponse>, I>>(_: I): UpdateDeviceResponse {
    const message = createBaseUpdateDeviceResponse();
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
