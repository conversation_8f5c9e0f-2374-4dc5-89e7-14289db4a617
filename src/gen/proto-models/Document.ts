// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Document.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export interface ShareTextResponse {
  shareText: string;
}

export interface ReferralLinkResponse {
  referralLink: string;
}

function createBaseShareTextResponse(): ShareTextResponse {
  return { shareText: "" };
}

export const ShareTextResponse: MessageFns<ShareTextResponse> = {
  encode(message: ShareTextResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.shareText !== "") {
      writer.uint32(10).string(message.shareText);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ShareTextResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseShareTextResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.shareText = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ShareTextResponse {
    return { shareText: isSet(object.shareText) ? globalThis.String(object.shareText) : "" };
  },

  toJSON(message: ShareTextResponse): unknown {
    const obj: any = {};
    if (message.shareText !== "") {
      obj.shareText = message.shareText;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ShareTextResponse>, I>>(base?: I): ShareTextResponse {
    return ShareTextResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ShareTextResponse>, I>>(object: I): ShareTextResponse {
    const message = createBaseShareTextResponse();
    message.shareText = object.shareText ?? "";
    return message;
  },
};

function createBaseReferralLinkResponse(): ReferralLinkResponse {
  return { referralLink: "" };
}

export const ReferralLinkResponse: MessageFns<ReferralLinkResponse> = {
  encode(message: ReferralLinkResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.referralLink !== "") {
      writer.uint32(10).string(message.referralLink);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferralLinkResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferralLinkResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.referralLink = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReferralLinkResponse {
    return { referralLink: isSet(object.referralLink) ? globalThis.String(object.referralLink) : "" };
  },

  toJSON(message: ReferralLinkResponse): unknown {
    const obj: any = {};
    if (message.referralLink !== "") {
      obj.referralLink = message.referralLink;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReferralLinkResponse>, I>>(base?: I): ReferralLinkResponse {
    return ReferralLinkResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReferralLinkResponse>, I>>(object: I): ReferralLinkResponse {
    const message = createBaseReferralLinkResponse();
    message.referralLink = object.referralLink ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
