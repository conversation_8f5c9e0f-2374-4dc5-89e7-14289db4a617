// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Auth.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { UserDevice } from "./Device";

export const protobufPackage = "com.stablemoney.api.identity";

export enum AuthType {
  LOGIN_TYPE_UNKNOWN = 0,
  EMAIL_OTP = 1,
  GOOGLE = 2,
  APPLE = 3,
  MOBILE_OTP = 4,
  UNRECOGNIZED = -1,
}

export function authTypeFromJSON(object: any): AuthType {
  switch (object) {
    case 0:
    case "LOGIN_TYPE_UNKNOWN":
      return AuthType.LOGIN_TYPE_UNKNOWN;
    case 1:
    case "EMAIL_OTP":
      return AuthType.EMAIL_OTP;
    case 2:
    case "GOOGLE":
      return AuthType.GOOGLE;
    case 3:
    case "APPLE":
      return AuthType.APPLE;
    case 4:
    case "MOBILE_OTP":
      return AuthType.MOBILE_OTP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AuthType.UNRECOGNIZED;
  }
}

export function authTypeToJSON(object: AuthType): string {
  switch (object) {
    case AuthType.LOGIN_TYPE_UNKNOWN:
      return "LOGIN_TYPE_UNKNOWN";
    case AuthType.EMAIL_OTP:
      return "EMAIL_OTP";
    case AuthType.GOOGLE:
      return "GOOGLE";
    case AuthType.APPLE:
      return "APPLE";
    case AuthType.MOBILE_OTP:
      return "MOBILE_OTP";
    case AuthType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AuthProcess {
  AUTH_PROCESS_UNKNOWN = 0,
  LOGIN = 1,
  VERIFY = 2,
  UNRECOGNIZED = -1,
}

export function authProcessFromJSON(object: any): AuthProcess {
  switch (object) {
    case 0:
    case "AUTH_PROCESS_UNKNOWN":
      return AuthProcess.AUTH_PROCESS_UNKNOWN;
    case 1:
    case "LOGIN":
      return AuthProcess.LOGIN;
    case 2:
    case "VERIFY":
      return AuthProcess.VERIFY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AuthProcess.UNRECOGNIZED;
  }
}

export function authProcessToJSON(object: AuthProcess): string {
  switch (object) {
    case AuthProcess.AUTH_PROCESS_UNKNOWN:
      return "AUTH_PROCESS_UNKNOWN";
    case AuthProcess.LOGIN:
      return "LOGIN";
    case AuthProcess.VERIFY:
      return "VERIFY";
    case AuthProcess.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface InitiateAuthRequest {
  authType: AuthType;
  userDevice: UserDevice | undefined;
  email?: string | undefined;
  appleLoginRequest?: AppleLoginRequest | undefined;
  googleLoginRequest?: GoogleLoginRequest | undefined;
  mobileLoginRequest?: MobileLoginRequest | undefined;
}

export interface AppleLoginRequest {
  authorisationCode: string;
  name: string;
}

export interface GoogleLoginRequest {
  idToken: string;
}

export interface InitiateAuthResponse {
  userId: string;
  otpChallenge?: OTPChallenge | undefined;
  authenticationResult?: AuthenticationResult | undefined;
}

export interface AuthenticationResult {
  token: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: string;
}

export interface RespondToAuthChallengeResponse {
  userId: string;
  authenticationResult: AuthenticationResult | undefined;
}

export interface OTPChallenge {
  challengeId: string;
  expiry: number;
}

export interface RespondToAuthChallengeRequest {
  userId: string;
  challengeId: string;
  answer: string;
}

export interface RefreshTokenRequest {
  token: string;
}

export interface RefreshTokenResponse {
  userId: string;
  authenticationResult: AuthenticationResult | undefined;
}

export interface MobileLoginRequest {
  mobile: string;
  countryCode: string;
  encryptedMobile: string;
  encryptionKey: string;
}

export interface InitiateVerifyRequest {
  authType: AuthType;
  email?: string | undefined;
  appleLoginRequest?: AppleLoginRequest | undefined;
  googleLoginRequest?: GoogleLoginRequest | undefined;
  mobileLoginRequest?: MobileLoginRequest | undefined;
}

export interface InitiateVerifyResponse {
  isEmailVerified?: boolean | undefined;
  otpChallenge?: OTPChallenge | undefined;
}

export interface RespondToVerifyChallengeRequest {
  challengeId: string;
  answer: string;
}

export interface RespondToVerifyChallengeResponse {
}

export interface EmptyProtoResponse {
}

export interface ZohoTokenGenerationResponse {
  jwtToken: string;
}

function createBaseInitiateAuthRequest(): InitiateAuthRequest {
  return {
    authType: 0,
    userDevice: undefined,
    email: undefined,
    appleLoginRequest: undefined,
    googleLoginRequest: undefined,
    mobileLoginRequest: undefined,
  };
}

export const InitiateAuthRequest: MessageFns<InitiateAuthRequest> = {
  encode(message: InitiateAuthRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.authType !== 0) {
      writer.uint32(8).int32(message.authType);
    }
    if (message.userDevice !== undefined) {
      UserDevice.encode(message.userDevice, writer.uint32(18).fork()).join();
    }
    if (message.email !== undefined) {
      writer.uint32(26).string(message.email);
    }
    if (message.appleLoginRequest !== undefined) {
      AppleLoginRequest.encode(message.appleLoginRequest, writer.uint32(34).fork()).join();
    }
    if (message.googleLoginRequest !== undefined) {
      GoogleLoginRequest.encode(message.googleLoginRequest, writer.uint32(42).fork()).join();
    }
    if (message.mobileLoginRequest !== undefined) {
      MobileLoginRequest.encode(message.mobileLoginRequest, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateAuthRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateAuthRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.authType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userDevice = UserDevice.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.appleLoginRequest = AppleLoginRequest.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.googleLoginRequest = GoogleLoginRequest.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.mobileLoginRequest = MobileLoginRequest.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateAuthRequest {
    return {
      authType: isSet(object.authType) ? authTypeFromJSON(object.authType) : 0,
      userDevice: isSet(object.userDevice) ? UserDevice.fromJSON(object.userDevice) : undefined,
      email: isSet(object.email) ? globalThis.String(object.email) : undefined,
      appleLoginRequest: isSet(object.appleLoginRequest)
        ? AppleLoginRequest.fromJSON(object.appleLoginRequest)
        : undefined,
      googleLoginRequest: isSet(object.googleLoginRequest)
        ? GoogleLoginRequest.fromJSON(object.googleLoginRequest)
        : undefined,
      mobileLoginRequest: isSet(object.mobileLoginRequest)
        ? MobileLoginRequest.fromJSON(object.mobileLoginRequest)
        : undefined,
    };
  },

  toJSON(message: InitiateAuthRequest): unknown {
    const obj: any = {};
    if (message.authType !== 0) {
      obj.authType = authTypeToJSON(message.authType);
    }
    if (message.userDevice !== undefined) {
      obj.userDevice = UserDevice.toJSON(message.userDevice);
    }
    if (message.email !== undefined) {
      obj.email = message.email;
    }
    if (message.appleLoginRequest !== undefined) {
      obj.appleLoginRequest = AppleLoginRequest.toJSON(message.appleLoginRequest);
    }
    if (message.googleLoginRequest !== undefined) {
      obj.googleLoginRequest = GoogleLoginRequest.toJSON(message.googleLoginRequest);
    }
    if (message.mobileLoginRequest !== undefined) {
      obj.mobileLoginRequest = MobileLoginRequest.toJSON(message.mobileLoginRequest);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateAuthRequest>, I>>(base?: I): InitiateAuthRequest {
    return InitiateAuthRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateAuthRequest>, I>>(object: I): InitiateAuthRequest {
    const message = createBaseInitiateAuthRequest();
    message.authType = object.authType ?? 0;
    message.userDevice = (object.userDevice !== undefined && object.userDevice !== null)
      ? UserDevice.fromPartial(object.userDevice)
      : undefined;
    message.email = object.email ?? undefined;
    message.appleLoginRequest = (object.appleLoginRequest !== undefined && object.appleLoginRequest !== null)
      ? AppleLoginRequest.fromPartial(object.appleLoginRequest)
      : undefined;
    message.googleLoginRequest = (object.googleLoginRequest !== undefined && object.googleLoginRequest !== null)
      ? GoogleLoginRequest.fromPartial(object.googleLoginRequest)
      : undefined;
    message.mobileLoginRequest = (object.mobileLoginRequest !== undefined && object.mobileLoginRequest !== null)
      ? MobileLoginRequest.fromPartial(object.mobileLoginRequest)
      : undefined;
    return message;
  },
};

function createBaseAppleLoginRequest(): AppleLoginRequest {
  return { authorisationCode: "", name: "" };
}

export const AppleLoginRequest: MessageFns<AppleLoginRequest> = {
  encode(message: AppleLoginRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.authorisationCode !== "") {
      writer.uint32(10).string(message.authorisationCode);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AppleLoginRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppleLoginRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.authorisationCode = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppleLoginRequest {
    return {
      authorisationCode: isSet(object.authorisationCode) ? globalThis.String(object.authorisationCode) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: AppleLoginRequest): unknown {
    const obj: any = {};
    if (message.authorisationCode !== "") {
      obj.authorisationCode = message.authorisationCode;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppleLoginRequest>, I>>(base?: I): AppleLoginRequest {
    return AppleLoginRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppleLoginRequest>, I>>(object: I): AppleLoginRequest {
    const message = createBaseAppleLoginRequest();
    message.authorisationCode = object.authorisationCode ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseGoogleLoginRequest(): GoogleLoginRequest {
  return { idToken: "" };
}

export const GoogleLoginRequest: MessageFns<GoogleLoginRequest> = {
  encode(message: GoogleLoginRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.idToken !== "") {
      writer.uint32(10).string(message.idToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoogleLoginRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoogleLoginRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.idToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoogleLoginRequest {
    return { idToken: isSet(object.idToken) ? globalThis.String(object.idToken) : "" };
  },

  toJSON(message: GoogleLoginRequest): unknown {
    const obj: any = {};
    if (message.idToken !== "") {
      obj.idToken = message.idToken;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoogleLoginRequest>, I>>(base?: I): GoogleLoginRequest {
    return GoogleLoginRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoogleLoginRequest>, I>>(object: I): GoogleLoginRequest {
    const message = createBaseGoogleLoginRequest();
    message.idToken = object.idToken ?? "";
    return message;
  },
};

function createBaseInitiateAuthResponse(): InitiateAuthResponse {
  return { userId: "", otpChallenge: undefined, authenticationResult: undefined };
}

export const InitiateAuthResponse: MessageFns<InitiateAuthResponse> = {
  encode(message: InitiateAuthResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.otpChallenge !== undefined) {
      OTPChallenge.encode(message.otpChallenge, writer.uint32(18).fork()).join();
    }
    if (message.authenticationResult !== undefined) {
      AuthenticationResult.encode(message.authenticationResult, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateAuthResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateAuthResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.otpChallenge = OTPChallenge.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.authenticationResult = AuthenticationResult.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateAuthResponse {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      otpChallenge: isSet(object.otpChallenge) ? OTPChallenge.fromJSON(object.otpChallenge) : undefined,
      authenticationResult: isSet(object.authenticationResult)
        ? AuthenticationResult.fromJSON(object.authenticationResult)
        : undefined,
    };
  },

  toJSON(message: InitiateAuthResponse): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.otpChallenge !== undefined) {
      obj.otpChallenge = OTPChallenge.toJSON(message.otpChallenge);
    }
    if (message.authenticationResult !== undefined) {
      obj.authenticationResult = AuthenticationResult.toJSON(message.authenticationResult);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateAuthResponse>, I>>(base?: I): InitiateAuthResponse {
    return InitiateAuthResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateAuthResponse>, I>>(object: I): InitiateAuthResponse {
    const message = createBaseInitiateAuthResponse();
    message.userId = object.userId ?? "";
    message.otpChallenge = (object.otpChallenge !== undefined && object.otpChallenge !== null)
      ? OTPChallenge.fromPartial(object.otpChallenge)
      : undefined;
    message.authenticationResult = (object.authenticationResult !== undefined && object.authenticationResult !== null)
      ? AuthenticationResult.fromPartial(object.authenticationResult)
      : undefined;
    return message;
  },
};

function createBaseAuthenticationResult(): AuthenticationResult {
  return { token: "", refreshToken: "", tokenType: "", expiresIn: "" };
}

export const AuthenticationResult: MessageFns<AuthenticationResult> = {
  encode(message: AuthenticationResult, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.token !== "") {
      writer.uint32(10).string(message.token);
    }
    if (message.refreshToken !== "") {
      writer.uint32(18).string(message.refreshToken);
    }
    if (message.tokenType !== "") {
      writer.uint32(26).string(message.tokenType);
    }
    if (message.expiresIn !== "") {
      writer.uint32(34).string(message.expiresIn);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AuthenticationResult {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAuthenticationResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.token = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.refreshToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tokenType = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.expiresIn = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AuthenticationResult {
    return {
      token: isSet(object.token) ? globalThis.String(object.token) : "",
      refreshToken: isSet(object.refreshToken) ? globalThis.String(object.refreshToken) : "",
      tokenType: isSet(object.tokenType) ? globalThis.String(object.tokenType) : "",
      expiresIn: isSet(object.expiresIn) ? globalThis.String(object.expiresIn) : "",
    };
  },

  toJSON(message: AuthenticationResult): unknown {
    const obj: any = {};
    if (message.token !== "") {
      obj.token = message.token;
    }
    if (message.refreshToken !== "") {
      obj.refreshToken = message.refreshToken;
    }
    if (message.tokenType !== "") {
      obj.tokenType = message.tokenType;
    }
    if (message.expiresIn !== "") {
      obj.expiresIn = message.expiresIn;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AuthenticationResult>, I>>(base?: I): AuthenticationResult {
    return AuthenticationResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuthenticationResult>, I>>(object: I): AuthenticationResult {
    const message = createBaseAuthenticationResult();
    message.token = object.token ?? "";
    message.refreshToken = object.refreshToken ?? "";
    message.tokenType = object.tokenType ?? "";
    message.expiresIn = object.expiresIn ?? "";
    return message;
  },
};

function createBaseRespondToAuthChallengeResponse(): RespondToAuthChallengeResponse {
  return { userId: "", authenticationResult: undefined };
}

export const RespondToAuthChallengeResponse: MessageFns<RespondToAuthChallengeResponse> = {
  encode(message: RespondToAuthChallengeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.authenticationResult !== undefined) {
      AuthenticationResult.encode(message.authenticationResult, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RespondToAuthChallengeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRespondToAuthChallengeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.authenticationResult = AuthenticationResult.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RespondToAuthChallengeResponse {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      authenticationResult: isSet(object.authenticationResult)
        ? AuthenticationResult.fromJSON(object.authenticationResult)
        : undefined,
    };
  },

  toJSON(message: RespondToAuthChallengeResponse): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.authenticationResult !== undefined) {
      obj.authenticationResult = AuthenticationResult.toJSON(message.authenticationResult);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RespondToAuthChallengeResponse>, I>>(base?: I): RespondToAuthChallengeResponse {
    return RespondToAuthChallengeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RespondToAuthChallengeResponse>, I>>(
    object: I,
  ): RespondToAuthChallengeResponse {
    const message = createBaseRespondToAuthChallengeResponse();
    message.userId = object.userId ?? "";
    message.authenticationResult = (object.authenticationResult !== undefined && object.authenticationResult !== null)
      ? AuthenticationResult.fromPartial(object.authenticationResult)
      : undefined;
    return message;
  },
};

function createBaseOTPChallenge(): OTPChallenge {
  return { challengeId: "", expiry: 0 };
}

export const OTPChallenge: MessageFns<OTPChallenge> = {
  encode(message: OTPChallenge, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.challengeId !== "") {
      writer.uint32(10).string(message.challengeId);
    }
    if (message.expiry !== 0) {
      writer.uint32(16).int64(message.expiry);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OTPChallenge {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOTPChallenge();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.challengeId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.expiry = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OTPChallenge {
    return {
      challengeId: isSet(object.challengeId) ? globalThis.String(object.challengeId) : "",
      expiry: isSet(object.expiry) ? globalThis.Number(object.expiry) : 0,
    };
  },

  toJSON(message: OTPChallenge): unknown {
    const obj: any = {};
    if (message.challengeId !== "") {
      obj.challengeId = message.challengeId;
    }
    if (message.expiry !== 0) {
      obj.expiry = Math.round(message.expiry);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OTPChallenge>, I>>(base?: I): OTPChallenge {
    return OTPChallenge.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OTPChallenge>, I>>(object: I): OTPChallenge {
    const message = createBaseOTPChallenge();
    message.challengeId = object.challengeId ?? "";
    message.expiry = object.expiry ?? 0;
    return message;
  },
};

function createBaseRespondToAuthChallengeRequest(): RespondToAuthChallengeRequest {
  return { userId: "", challengeId: "", answer: "" };
}

export const RespondToAuthChallengeRequest: MessageFns<RespondToAuthChallengeRequest> = {
  encode(message: RespondToAuthChallengeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.challengeId !== "") {
      writer.uint32(18).string(message.challengeId);
    }
    if (message.answer !== "") {
      writer.uint32(26).string(message.answer);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RespondToAuthChallengeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRespondToAuthChallengeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.challengeId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.answer = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RespondToAuthChallengeRequest {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      challengeId: isSet(object.challengeId) ? globalThis.String(object.challengeId) : "",
      answer: isSet(object.answer) ? globalThis.String(object.answer) : "",
    };
  },

  toJSON(message: RespondToAuthChallengeRequest): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.challengeId !== "") {
      obj.challengeId = message.challengeId;
    }
    if (message.answer !== "") {
      obj.answer = message.answer;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RespondToAuthChallengeRequest>, I>>(base?: I): RespondToAuthChallengeRequest {
    return RespondToAuthChallengeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RespondToAuthChallengeRequest>, I>>(
    object: I,
  ): RespondToAuthChallengeRequest {
    const message = createBaseRespondToAuthChallengeRequest();
    message.userId = object.userId ?? "";
    message.challengeId = object.challengeId ?? "";
    message.answer = object.answer ?? "";
    return message;
  },
};

function createBaseRefreshTokenRequest(): RefreshTokenRequest {
  return { token: "" };
}

export const RefreshTokenRequest: MessageFns<RefreshTokenRequest> = {
  encode(message: RefreshTokenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.token !== "") {
      writer.uint32(10).string(message.token);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RefreshTokenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRefreshTokenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.token = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RefreshTokenRequest {
    return { token: isSet(object.token) ? globalThis.String(object.token) : "" };
  },

  toJSON(message: RefreshTokenRequest): unknown {
    const obj: any = {};
    if (message.token !== "") {
      obj.token = message.token;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RefreshTokenRequest>, I>>(base?: I): RefreshTokenRequest {
    return RefreshTokenRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshTokenRequest>, I>>(object: I): RefreshTokenRequest {
    const message = createBaseRefreshTokenRequest();
    message.token = object.token ?? "";
    return message;
  },
};

function createBaseRefreshTokenResponse(): RefreshTokenResponse {
  return { userId: "", authenticationResult: undefined };
}

export const RefreshTokenResponse: MessageFns<RefreshTokenResponse> = {
  encode(message: RefreshTokenResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.authenticationResult !== undefined) {
      AuthenticationResult.encode(message.authenticationResult, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RefreshTokenResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRefreshTokenResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.authenticationResult = AuthenticationResult.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RefreshTokenResponse {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      authenticationResult: isSet(object.authenticationResult)
        ? AuthenticationResult.fromJSON(object.authenticationResult)
        : undefined,
    };
  },

  toJSON(message: RefreshTokenResponse): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.authenticationResult !== undefined) {
      obj.authenticationResult = AuthenticationResult.toJSON(message.authenticationResult);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RefreshTokenResponse>, I>>(base?: I): RefreshTokenResponse {
    return RefreshTokenResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshTokenResponse>, I>>(object: I): RefreshTokenResponse {
    const message = createBaseRefreshTokenResponse();
    message.userId = object.userId ?? "";
    message.authenticationResult = (object.authenticationResult !== undefined && object.authenticationResult !== null)
      ? AuthenticationResult.fromPartial(object.authenticationResult)
      : undefined;
    return message;
  },
};

function createBaseMobileLoginRequest(): MobileLoginRequest {
  return { mobile: "", countryCode: "", encryptedMobile: "", encryptionKey: "" };
}

export const MobileLoginRequest: MessageFns<MobileLoginRequest> = {
  encode(message: MobileLoginRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mobile !== "") {
      writer.uint32(10).string(message.mobile);
    }
    if (message.countryCode !== "") {
      writer.uint32(18).string(message.countryCode);
    }
    if (message.encryptedMobile !== "") {
      writer.uint32(26).string(message.encryptedMobile);
    }
    if (message.encryptionKey !== "") {
      writer.uint32(34).string(message.encryptionKey);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MobileLoginRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMobileLoginRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.mobile = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.countryCode = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.encryptedMobile = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.encryptionKey = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MobileLoginRequest {
    return {
      mobile: isSet(object.mobile) ? globalThis.String(object.mobile) : "",
      countryCode: isSet(object.countryCode) ? globalThis.String(object.countryCode) : "",
      encryptedMobile: isSet(object.encryptedMobile) ? globalThis.String(object.encryptedMobile) : "",
      encryptionKey: isSet(object.encryptionKey) ? globalThis.String(object.encryptionKey) : "",
    };
  },

  toJSON(message: MobileLoginRequest): unknown {
    const obj: any = {};
    if (message.mobile !== "") {
      obj.mobile = message.mobile;
    }
    if (message.countryCode !== "") {
      obj.countryCode = message.countryCode;
    }
    if (message.encryptedMobile !== "") {
      obj.encryptedMobile = message.encryptedMobile;
    }
    if (message.encryptionKey !== "") {
      obj.encryptionKey = message.encryptionKey;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MobileLoginRequest>, I>>(base?: I): MobileLoginRequest {
    return MobileLoginRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MobileLoginRequest>, I>>(object: I): MobileLoginRequest {
    const message = createBaseMobileLoginRequest();
    message.mobile = object.mobile ?? "";
    message.countryCode = object.countryCode ?? "";
    message.encryptedMobile = object.encryptedMobile ?? "";
    message.encryptionKey = object.encryptionKey ?? "";
    return message;
  },
};

function createBaseInitiateVerifyRequest(): InitiateVerifyRequest {
  return {
    authType: 0,
    email: undefined,
    appleLoginRequest: undefined,
    googleLoginRequest: undefined,
    mobileLoginRequest: undefined,
  };
}

export const InitiateVerifyRequest: MessageFns<InitiateVerifyRequest> = {
  encode(message: InitiateVerifyRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.authType !== 0) {
      writer.uint32(8).int32(message.authType);
    }
    if (message.email !== undefined) {
      writer.uint32(18).string(message.email);
    }
    if (message.appleLoginRequest !== undefined) {
      AppleLoginRequest.encode(message.appleLoginRequest, writer.uint32(26).fork()).join();
    }
    if (message.googleLoginRequest !== undefined) {
      GoogleLoginRequest.encode(message.googleLoginRequest, writer.uint32(34).fork()).join();
    }
    if (message.mobileLoginRequest !== undefined) {
      MobileLoginRequest.encode(message.mobileLoginRequest, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateVerifyRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateVerifyRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.authType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.appleLoginRequest = AppleLoginRequest.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.googleLoginRequest = GoogleLoginRequest.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.mobileLoginRequest = MobileLoginRequest.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateVerifyRequest {
    return {
      authType: isSet(object.authType) ? authTypeFromJSON(object.authType) : 0,
      email: isSet(object.email) ? globalThis.String(object.email) : undefined,
      appleLoginRequest: isSet(object.appleLoginRequest)
        ? AppleLoginRequest.fromJSON(object.appleLoginRequest)
        : undefined,
      googleLoginRequest: isSet(object.googleLoginRequest)
        ? GoogleLoginRequest.fromJSON(object.googleLoginRequest)
        : undefined,
      mobileLoginRequest: isSet(object.mobileLoginRequest)
        ? MobileLoginRequest.fromJSON(object.mobileLoginRequest)
        : undefined,
    };
  },

  toJSON(message: InitiateVerifyRequest): unknown {
    const obj: any = {};
    if (message.authType !== 0) {
      obj.authType = authTypeToJSON(message.authType);
    }
    if (message.email !== undefined) {
      obj.email = message.email;
    }
    if (message.appleLoginRequest !== undefined) {
      obj.appleLoginRequest = AppleLoginRequest.toJSON(message.appleLoginRequest);
    }
    if (message.googleLoginRequest !== undefined) {
      obj.googleLoginRequest = GoogleLoginRequest.toJSON(message.googleLoginRequest);
    }
    if (message.mobileLoginRequest !== undefined) {
      obj.mobileLoginRequest = MobileLoginRequest.toJSON(message.mobileLoginRequest);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateVerifyRequest>, I>>(base?: I): InitiateVerifyRequest {
    return InitiateVerifyRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateVerifyRequest>, I>>(object: I): InitiateVerifyRequest {
    const message = createBaseInitiateVerifyRequest();
    message.authType = object.authType ?? 0;
    message.email = object.email ?? undefined;
    message.appleLoginRequest = (object.appleLoginRequest !== undefined && object.appleLoginRequest !== null)
      ? AppleLoginRequest.fromPartial(object.appleLoginRequest)
      : undefined;
    message.googleLoginRequest = (object.googleLoginRequest !== undefined && object.googleLoginRequest !== null)
      ? GoogleLoginRequest.fromPartial(object.googleLoginRequest)
      : undefined;
    message.mobileLoginRequest = (object.mobileLoginRequest !== undefined && object.mobileLoginRequest !== null)
      ? MobileLoginRequest.fromPartial(object.mobileLoginRequest)
      : undefined;
    return message;
  },
};

function createBaseInitiateVerifyResponse(): InitiateVerifyResponse {
  return { isEmailVerified: undefined, otpChallenge: undefined };
}

export const InitiateVerifyResponse: MessageFns<InitiateVerifyResponse> = {
  encode(message: InitiateVerifyResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isEmailVerified !== undefined) {
      writer.uint32(8).bool(message.isEmailVerified);
    }
    if (message.otpChallenge !== undefined) {
      OTPChallenge.encode(message.otpChallenge, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateVerifyResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateVerifyResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isEmailVerified = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.otpChallenge = OTPChallenge.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateVerifyResponse {
    return {
      isEmailVerified: isSet(object.isEmailVerified) ? globalThis.Boolean(object.isEmailVerified) : undefined,
      otpChallenge: isSet(object.otpChallenge) ? OTPChallenge.fromJSON(object.otpChallenge) : undefined,
    };
  },

  toJSON(message: InitiateVerifyResponse): unknown {
    const obj: any = {};
    if (message.isEmailVerified !== undefined) {
      obj.isEmailVerified = message.isEmailVerified;
    }
    if (message.otpChallenge !== undefined) {
      obj.otpChallenge = OTPChallenge.toJSON(message.otpChallenge);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateVerifyResponse>, I>>(base?: I): InitiateVerifyResponse {
    return InitiateVerifyResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateVerifyResponse>, I>>(object: I): InitiateVerifyResponse {
    const message = createBaseInitiateVerifyResponse();
    message.isEmailVerified = object.isEmailVerified ?? undefined;
    message.otpChallenge = (object.otpChallenge !== undefined && object.otpChallenge !== null)
      ? OTPChallenge.fromPartial(object.otpChallenge)
      : undefined;
    return message;
  },
};

function createBaseRespondToVerifyChallengeRequest(): RespondToVerifyChallengeRequest {
  return { challengeId: "", answer: "" };
}

export const RespondToVerifyChallengeRequest: MessageFns<RespondToVerifyChallengeRequest> = {
  encode(message: RespondToVerifyChallengeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.challengeId !== "") {
      writer.uint32(10).string(message.challengeId);
    }
    if (message.answer !== "") {
      writer.uint32(18).string(message.answer);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RespondToVerifyChallengeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRespondToVerifyChallengeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.challengeId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.answer = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RespondToVerifyChallengeRequest {
    return {
      challengeId: isSet(object.challengeId) ? globalThis.String(object.challengeId) : "",
      answer: isSet(object.answer) ? globalThis.String(object.answer) : "",
    };
  },

  toJSON(message: RespondToVerifyChallengeRequest): unknown {
    const obj: any = {};
    if (message.challengeId !== "") {
      obj.challengeId = message.challengeId;
    }
    if (message.answer !== "") {
      obj.answer = message.answer;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RespondToVerifyChallengeRequest>, I>>(base?: I): RespondToVerifyChallengeRequest {
    return RespondToVerifyChallengeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RespondToVerifyChallengeRequest>, I>>(
    object: I,
  ): RespondToVerifyChallengeRequest {
    const message = createBaseRespondToVerifyChallengeRequest();
    message.challengeId = object.challengeId ?? "";
    message.answer = object.answer ?? "";
    return message;
  },
};

function createBaseRespondToVerifyChallengeResponse(): RespondToVerifyChallengeResponse {
  return {};
}

export const RespondToVerifyChallengeResponse: MessageFns<RespondToVerifyChallengeResponse> = {
  encode(_: RespondToVerifyChallengeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RespondToVerifyChallengeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRespondToVerifyChallengeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): RespondToVerifyChallengeResponse {
    return {};
  },

  toJSON(_: RespondToVerifyChallengeResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<RespondToVerifyChallengeResponse>, I>>(
    base?: I,
  ): RespondToVerifyChallengeResponse {
    return RespondToVerifyChallengeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RespondToVerifyChallengeResponse>, I>>(
    _: I,
  ): RespondToVerifyChallengeResponse {
    const message = createBaseRespondToVerifyChallengeResponse();
    return message;
  },
};

function createBaseEmptyProtoResponse(): EmptyProtoResponse {
  return {};
}

export const EmptyProtoResponse: MessageFns<EmptyProtoResponse> = {
  encode(_: EmptyProtoResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmptyProtoResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmptyProtoResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): EmptyProtoResponse {
    return {};
  },

  toJSON(_: EmptyProtoResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<EmptyProtoResponse>, I>>(base?: I): EmptyProtoResponse {
    return EmptyProtoResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmptyProtoResponse>, I>>(_: I): EmptyProtoResponse {
    const message = createBaseEmptyProtoResponse();
    return message;
  },
};

function createBaseZohoTokenGenerationResponse(): ZohoTokenGenerationResponse {
  return { jwtToken: "" };
}

export const ZohoTokenGenerationResponse: MessageFns<ZohoTokenGenerationResponse> = {
  encode(message: ZohoTokenGenerationResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.jwtToken !== "") {
      writer.uint32(10).string(message.jwtToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ZohoTokenGenerationResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseZohoTokenGenerationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.jwtToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ZohoTokenGenerationResponse {
    return { jwtToken: isSet(object.jwtToken) ? globalThis.String(object.jwtToken) : "" };
  },

  toJSON(message: ZohoTokenGenerationResponse): unknown {
    const obj: any = {};
    if (message.jwtToken !== "") {
      obj.jwtToken = message.jwtToken;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ZohoTokenGenerationResponse>, I>>(base?: I): ZohoTokenGenerationResponse {
    return ZohoTokenGenerationResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ZohoTokenGenerationResponse>, I>>(object: I): ZohoTokenGenerationResponse {
    const message = createBaseZohoTokenGenerationResponse();
    message.jwtToken = object.jwtToken ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
