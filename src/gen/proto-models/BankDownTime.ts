// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: BankDownTime.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Empty } from "./google/protobuf/empty";

export const protobufPackage = "com.stablemoney.api.business.bankdowntime";

export enum DownTimeApplicability {
  DOWN_TIME_APPLICABILITY_UNKNOWN = 0,
  NTB_ONLY = 1,
  ETB_ONLY = 2,
  BOTH = 3,
  UNRECOGNIZED = -1,
}

export function downTimeApplicabilityFromJSON(object: any): DownTimeApplicability {
  switch (object) {
    case 0:
    case "DOWN_TIME_APPLICABILITY_UNKNOWN":
      return DownTimeApplicability.DOWN_TIME_APPLICABILITY_UNKNOWN;
    case 1:
    case "NTB_ONLY":
      return DownTimeApplicability.NTB_ONLY;
    case 2:
    case "ETB_ONLY":
      return DownTimeApplicability.ETB_ONLY;
    case 3:
    case "BOTH":
      return DownTimeApplicability.BOTH;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DownTimeApplicability.UNRECOGNIZED;
  }
}

export function downTimeApplicabilityToJSON(object: DownTimeApplicability): string {
  switch (object) {
    case DownTimeApplicability.DOWN_TIME_APPLICABILITY_UNKNOWN:
      return "DOWN_TIME_APPLICABILITY_UNKNOWN";
    case DownTimeApplicability.NTB_ONLY:
      return "NTB_ONLY";
    case DownTimeApplicability.ETB_ONLY:
      return "ETB_ONLY";
    case DownTimeApplicability.BOTH:
      return "BOTH";
    case DownTimeApplicability.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface WidgetConfiguration {
  widgetPositions: WidgetConfiguration_WidgetPosition[];
}

export enum WidgetConfiguration_ImpactedWidget {
  IMPACTED_WIDGET_UNKNOWN = 0,
  CAROUSEL = 1,
  COLLECTIONS = 2,
  UNRECOGNIZED = -1,
}

export function widgetConfiguration_ImpactedWidgetFromJSON(object: any): WidgetConfiguration_ImpactedWidget {
  switch (object) {
    case 0:
    case "IMPACTED_WIDGET_UNKNOWN":
      return WidgetConfiguration_ImpactedWidget.IMPACTED_WIDGET_UNKNOWN;
    case 1:
    case "CAROUSEL":
      return WidgetConfiguration_ImpactedWidget.CAROUSEL;
    case 2:
    case "COLLECTIONS":
      return WidgetConfiguration_ImpactedWidget.COLLECTIONS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetConfiguration_ImpactedWidget.UNRECOGNIZED;
  }
}

export function widgetConfiguration_ImpactedWidgetToJSON(object: WidgetConfiguration_ImpactedWidget): string {
  switch (object) {
    case WidgetConfiguration_ImpactedWidget.IMPACTED_WIDGET_UNKNOWN:
      return "IMPACTED_WIDGET_UNKNOWN";
    case WidgetConfiguration_ImpactedWidget.CAROUSEL:
      return "CAROUSEL";
    case WidgetConfiguration_ImpactedWidget.COLLECTIONS:
      return "COLLECTIONS";
    case WidgetConfiguration_ImpactedWidget.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum WidgetConfiguration_PositionType {
  POSITION_TYPE_UNKNOWN = 0,
  LAST = 2,
  DISAPPEAR = 3,
  SPECIFIC = 4,
  UNRECOGNIZED = -1,
}

export function widgetConfiguration_PositionTypeFromJSON(object: any): WidgetConfiguration_PositionType {
  switch (object) {
    case 0:
    case "POSITION_TYPE_UNKNOWN":
      return WidgetConfiguration_PositionType.POSITION_TYPE_UNKNOWN;
    case 2:
    case "LAST":
      return WidgetConfiguration_PositionType.LAST;
    case 3:
    case "DISAPPEAR":
      return WidgetConfiguration_PositionType.DISAPPEAR;
    case 4:
    case "SPECIFIC":
      return WidgetConfiguration_PositionType.SPECIFIC;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetConfiguration_PositionType.UNRECOGNIZED;
  }
}

export function widgetConfiguration_PositionTypeToJSON(object: WidgetConfiguration_PositionType): string {
  switch (object) {
    case WidgetConfiguration_PositionType.POSITION_TYPE_UNKNOWN:
      return "POSITION_TYPE_UNKNOWN";
    case WidgetConfiguration_PositionType.LAST:
      return "LAST";
    case WidgetConfiguration_PositionType.DISAPPEAR:
      return "DISAPPEAR";
    case WidgetConfiguration_PositionType.SPECIFIC:
      return "SPECIFIC";
    case WidgetConfiguration_PositionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface WidgetConfiguration_WidgetPosition {
  impactedWidget: WidgetConfiguration_ImpactedWidget;
  positionType: WidgetConfiguration_PositionType;
  position: number;
}

export interface BankDownTime {
  id: string;
  bankId: string;
  applicability: DownTimeApplicability;
  downTimeStart: number;
  downTimeEnd: number;
  isRecurring: boolean;
  widgetConfiguration: WidgetConfiguration | undefined;
  downTimeMessage: string;
  downTimeMessageForInvestedUser: string;
  downTimeMessageForNonInvestedUser: string;
  bankName: string;
}

export interface BankDownTimesField {
  id: string;
  bankId: string;
  applicability: DownTimeApplicability;
  downTimeStart: number;
  downTimeEnd: number;
  isRecurring: boolean;
  widgetConfiguration: WidgetConfiguration | undefined;
  bankName: string;
}

export interface DeleteBankDownTimeRequest {
  id: string;
}

export interface BankDownTimeRequest {
  id: string;
}

export interface BankDownTimeResponse {
  bankDownTime: BankDownTime | undefined;
}

export interface BanksDownTimeResponse {
  BankDownTimesField: BankDownTimesField[];
}

export interface BankDetail {
  bankId: string;
  bankName: string;
  fsi: string;
}

export interface AllBanksDetailsResponse {
  bankDetail: BankDetail[];
}

function createBaseWidgetConfiguration(): WidgetConfiguration {
  return { widgetPositions: [] };
}

export const WidgetConfiguration: MessageFns<WidgetConfiguration> = {
  encode(message: WidgetConfiguration, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.widgetPositions) {
      WidgetConfiguration_WidgetPosition.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetConfiguration {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetConfiguration();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.widgetPositions.push(WidgetConfiguration_WidgetPosition.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetConfiguration {
    return {
      widgetPositions: globalThis.Array.isArray(object?.widgetPositions)
        ? object.widgetPositions.map((e: any) => WidgetConfiguration_WidgetPosition.fromJSON(e))
        : [],
    };
  },

  toJSON(message: WidgetConfiguration): unknown {
    const obj: any = {};
    if (message.widgetPositions?.length) {
      obj.widgetPositions = message.widgetPositions.map((e) => WidgetConfiguration_WidgetPosition.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetConfiguration>, I>>(base?: I): WidgetConfiguration {
    return WidgetConfiguration.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetConfiguration>, I>>(object: I): WidgetConfiguration {
    const message = createBaseWidgetConfiguration();
    message.widgetPositions = object.widgetPositions?.map((e) => WidgetConfiguration_WidgetPosition.fromPartial(e)) ||
      [];
    return message;
  },
};

function createBaseWidgetConfiguration_WidgetPosition(): WidgetConfiguration_WidgetPosition {
  return { impactedWidget: 0, positionType: 0, position: 0 };
}

export const WidgetConfiguration_WidgetPosition: MessageFns<WidgetConfiguration_WidgetPosition> = {
  encode(message: WidgetConfiguration_WidgetPosition, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.impactedWidget !== 0) {
      writer.uint32(8).int32(message.impactedWidget);
    }
    if (message.positionType !== 0) {
      writer.uint32(16).int32(message.positionType);
    }
    if (message.position !== 0) {
      writer.uint32(24).int32(message.position);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WidgetConfiguration_WidgetPosition {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWidgetConfiguration_WidgetPosition();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.impactedWidget = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.positionType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.position = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WidgetConfiguration_WidgetPosition {
    return {
      impactedWidget: isSet(object.impactedWidget)
        ? widgetConfiguration_ImpactedWidgetFromJSON(object.impactedWidget)
        : 0,
      positionType: isSet(object.positionType) ? widgetConfiguration_PositionTypeFromJSON(object.positionType) : 0,
      position: isSet(object.position) ? globalThis.Number(object.position) : 0,
    };
  },

  toJSON(message: WidgetConfiguration_WidgetPosition): unknown {
    const obj: any = {};
    if (message.impactedWidget !== 0) {
      obj.impactedWidget = widgetConfiguration_ImpactedWidgetToJSON(message.impactedWidget);
    }
    if (message.positionType !== 0) {
      obj.positionType = widgetConfiguration_PositionTypeToJSON(message.positionType);
    }
    if (message.position !== 0) {
      obj.position = Math.round(message.position);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WidgetConfiguration_WidgetPosition>, I>>(
    base?: I,
  ): WidgetConfiguration_WidgetPosition {
    return WidgetConfiguration_WidgetPosition.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WidgetConfiguration_WidgetPosition>, I>>(
    object: I,
  ): WidgetConfiguration_WidgetPosition {
    const message = createBaseWidgetConfiguration_WidgetPosition();
    message.impactedWidget = object.impactedWidget ?? 0;
    message.positionType = object.positionType ?? 0;
    message.position = object.position ?? 0;
    return message;
  },
};

function createBaseBankDownTime(): BankDownTime {
  return {
    id: "",
    bankId: "",
    applicability: 0,
    downTimeStart: 0,
    downTimeEnd: 0,
    isRecurring: false,
    widgetConfiguration: undefined,
    downTimeMessage: "",
    downTimeMessageForInvestedUser: "",
    downTimeMessageForNonInvestedUser: "",
    bankName: "",
  };
}

export const BankDownTime: MessageFns<BankDownTime> = {
  encode(message: BankDownTime, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.bankId !== "") {
      writer.uint32(18).string(message.bankId);
    }
    if (message.applicability !== 0) {
      writer.uint32(24).int32(message.applicability);
    }
    if (message.downTimeStart !== 0) {
      writer.uint32(32).int64(message.downTimeStart);
    }
    if (message.downTimeEnd !== 0) {
      writer.uint32(48).int64(message.downTimeEnd);
    }
    if (message.isRecurring !== false) {
      writer.uint32(64).bool(message.isRecurring);
    }
    if (message.widgetConfiguration !== undefined) {
      WidgetConfiguration.encode(message.widgetConfiguration, writer.uint32(82).fork()).join();
    }
    if (message.downTimeMessage !== "") {
      writer.uint32(98).string(message.downTimeMessage);
    }
    if (message.downTimeMessageForInvestedUser !== "") {
      writer.uint32(114).string(message.downTimeMessageForInvestedUser);
    }
    if (message.downTimeMessageForNonInvestedUser !== "") {
      writer.uint32(130).string(message.downTimeMessageForNonInvestedUser);
    }
    if (message.bankName !== "") {
      writer.uint32(138).string(message.bankName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankDownTime {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankDownTime();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.applicability = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.downTimeStart = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.downTimeEnd = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isRecurring = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.widgetConfiguration = WidgetConfiguration.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.downTimeMessage = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.downTimeMessageForInvestedUser = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.downTimeMessageForNonInvestedUser = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankDownTime {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      applicability: isSet(object.applicability) ? downTimeApplicabilityFromJSON(object.applicability) : 0,
      downTimeStart: isSet(object.downTimeStart) ? globalThis.Number(object.downTimeStart) : 0,
      downTimeEnd: isSet(object.downTimeEnd) ? globalThis.Number(object.downTimeEnd) : 0,
      isRecurring: isSet(object.isRecurring) ? globalThis.Boolean(object.isRecurring) : false,
      widgetConfiguration: isSet(object.widgetConfiguration)
        ? WidgetConfiguration.fromJSON(object.widgetConfiguration)
        : undefined,
      downTimeMessage: isSet(object.downTimeMessage) ? globalThis.String(object.downTimeMessage) : "",
      downTimeMessageForInvestedUser: isSet(object.downTimeMessageForInvestedUser)
        ? globalThis.String(object.downTimeMessageForInvestedUser)
        : "",
      downTimeMessageForNonInvestedUser: isSet(object.downTimeMessageForNonInvestedUser)
        ? globalThis.String(object.downTimeMessageForNonInvestedUser)
        : "",
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
    };
  },

  toJSON(message: BankDownTime): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.applicability !== 0) {
      obj.applicability = downTimeApplicabilityToJSON(message.applicability);
    }
    if (message.downTimeStart !== 0) {
      obj.downTimeStart = Math.round(message.downTimeStart);
    }
    if (message.downTimeEnd !== 0) {
      obj.downTimeEnd = Math.round(message.downTimeEnd);
    }
    if (message.isRecurring !== false) {
      obj.isRecurring = message.isRecurring;
    }
    if (message.widgetConfiguration !== undefined) {
      obj.widgetConfiguration = WidgetConfiguration.toJSON(message.widgetConfiguration);
    }
    if (message.downTimeMessage !== "") {
      obj.downTimeMessage = message.downTimeMessage;
    }
    if (message.downTimeMessageForInvestedUser !== "") {
      obj.downTimeMessageForInvestedUser = message.downTimeMessageForInvestedUser;
    }
    if (message.downTimeMessageForNonInvestedUser !== "") {
      obj.downTimeMessageForNonInvestedUser = message.downTimeMessageForNonInvestedUser;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankDownTime>, I>>(base?: I): BankDownTime {
    return BankDownTime.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankDownTime>, I>>(object: I): BankDownTime {
    const message = createBaseBankDownTime();
    message.id = object.id ?? "";
    message.bankId = object.bankId ?? "";
    message.applicability = object.applicability ?? 0;
    message.downTimeStart = object.downTimeStart ?? 0;
    message.downTimeEnd = object.downTimeEnd ?? 0;
    message.isRecurring = object.isRecurring ?? false;
    message.widgetConfiguration = (object.widgetConfiguration !== undefined && object.widgetConfiguration !== null)
      ? WidgetConfiguration.fromPartial(object.widgetConfiguration)
      : undefined;
    message.downTimeMessage = object.downTimeMessage ?? "";
    message.downTimeMessageForInvestedUser = object.downTimeMessageForInvestedUser ?? "";
    message.downTimeMessageForNonInvestedUser = object.downTimeMessageForNonInvestedUser ?? "";
    message.bankName = object.bankName ?? "";
    return message;
  },
};

function createBaseBankDownTimesField(): BankDownTimesField {
  return {
    id: "",
    bankId: "",
    applicability: 0,
    downTimeStart: 0,
    downTimeEnd: 0,
    isRecurring: false,
    widgetConfiguration: undefined,
    bankName: "",
  };
}

export const BankDownTimesField: MessageFns<BankDownTimesField> = {
  encode(message: BankDownTimesField, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.bankId !== "") {
      writer.uint32(18).string(message.bankId);
    }
    if (message.applicability !== 0) {
      writer.uint32(24).int32(message.applicability);
    }
    if (message.downTimeStart !== 0) {
      writer.uint32(32).int64(message.downTimeStart);
    }
    if (message.downTimeEnd !== 0) {
      writer.uint32(48).int64(message.downTimeEnd);
    }
    if (message.isRecurring !== false) {
      writer.uint32(56).bool(message.isRecurring);
    }
    if (message.widgetConfiguration !== undefined) {
      WidgetConfiguration.encode(message.widgetConfiguration, writer.uint32(66).fork()).join();
    }
    if (message.bankName !== "") {
      writer.uint32(74).string(message.bankName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankDownTimesField {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankDownTimesField();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.applicability = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.downTimeStart = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.downTimeEnd = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.isRecurring = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.widgetConfiguration = WidgetConfiguration.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankDownTimesField {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      applicability: isSet(object.applicability) ? downTimeApplicabilityFromJSON(object.applicability) : 0,
      downTimeStart: isSet(object.downTimeStart) ? globalThis.Number(object.downTimeStart) : 0,
      downTimeEnd: isSet(object.downTimeEnd) ? globalThis.Number(object.downTimeEnd) : 0,
      isRecurring: isSet(object.isRecurring) ? globalThis.Boolean(object.isRecurring) : false,
      widgetConfiguration: isSet(object.widgetConfiguration)
        ? WidgetConfiguration.fromJSON(object.widgetConfiguration)
        : undefined,
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
    };
  },

  toJSON(message: BankDownTimesField): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.applicability !== 0) {
      obj.applicability = downTimeApplicabilityToJSON(message.applicability);
    }
    if (message.downTimeStart !== 0) {
      obj.downTimeStart = Math.round(message.downTimeStart);
    }
    if (message.downTimeEnd !== 0) {
      obj.downTimeEnd = Math.round(message.downTimeEnd);
    }
    if (message.isRecurring !== false) {
      obj.isRecurring = message.isRecurring;
    }
    if (message.widgetConfiguration !== undefined) {
      obj.widgetConfiguration = WidgetConfiguration.toJSON(message.widgetConfiguration);
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankDownTimesField>, I>>(base?: I): BankDownTimesField {
    return BankDownTimesField.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankDownTimesField>, I>>(object: I): BankDownTimesField {
    const message = createBaseBankDownTimesField();
    message.id = object.id ?? "";
    message.bankId = object.bankId ?? "";
    message.applicability = object.applicability ?? 0;
    message.downTimeStart = object.downTimeStart ?? 0;
    message.downTimeEnd = object.downTimeEnd ?? 0;
    message.isRecurring = object.isRecurring ?? false;
    message.widgetConfiguration = (object.widgetConfiguration !== undefined && object.widgetConfiguration !== null)
      ? WidgetConfiguration.fromPartial(object.widgetConfiguration)
      : undefined;
    message.bankName = object.bankName ?? "";
    return message;
  },
};

function createBaseDeleteBankDownTimeRequest(): DeleteBankDownTimeRequest {
  return { id: "" };
}

export const DeleteBankDownTimeRequest: MessageFns<DeleteBankDownTimeRequest> = {
  encode(message: DeleteBankDownTimeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteBankDownTimeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteBankDownTimeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteBankDownTimeRequest {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: DeleteBankDownTimeRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteBankDownTimeRequest>, I>>(base?: I): DeleteBankDownTimeRequest {
    return DeleteBankDownTimeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBankDownTimeRequest>, I>>(object: I): DeleteBankDownTimeRequest {
    const message = createBaseDeleteBankDownTimeRequest();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseBankDownTimeRequest(): BankDownTimeRequest {
  return { id: "" };
}

export const BankDownTimeRequest: MessageFns<BankDownTimeRequest> = {
  encode(message: BankDownTimeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankDownTimeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankDownTimeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankDownTimeRequest {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: BankDownTimeRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankDownTimeRequest>, I>>(base?: I): BankDownTimeRequest {
    return BankDownTimeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankDownTimeRequest>, I>>(object: I): BankDownTimeRequest {
    const message = createBaseBankDownTimeRequest();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseBankDownTimeResponse(): BankDownTimeResponse {
  return { bankDownTime: undefined };
}

export const BankDownTimeResponse: MessageFns<BankDownTimeResponse> = {
  encode(message: BankDownTimeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankDownTime !== undefined) {
      BankDownTime.encode(message.bankDownTime, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankDownTimeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankDownTimeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankDownTime = BankDownTime.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankDownTimeResponse {
    return { bankDownTime: isSet(object.bankDownTime) ? BankDownTime.fromJSON(object.bankDownTime) : undefined };
  },

  toJSON(message: BankDownTimeResponse): unknown {
    const obj: any = {};
    if (message.bankDownTime !== undefined) {
      obj.bankDownTime = BankDownTime.toJSON(message.bankDownTime);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankDownTimeResponse>, I>>(base?: I): BankDownTimeResponse {
    return BankDownTimeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankDownTimeResponse>, I>>(object: I): BankDownTimeResponse {
    const message = createBaseBankDownTimeResponse();
    message.bankDownTime = (object.bankDownTime !== undefined && object.bankDownTime !== null)
      ? BankDownTime.fromPartial(object.bankDownTime)
      : undefined;
    return message;
  },
};

function createBaseBanksDownTimeResponse(): BanksDownTimeResponse {
  return { BankDownTimesField: [] };
}

export const BanksDownTimeResponse: MessageFns<BanksDownTimeResponse> = {
  encode(message: BanksDownTimeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.BankDownTimesField) {
      BankDownTimesField.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BanksDownTimeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBanksDownTimeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.BankDownTimesField.push(BankDownTimesField.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BanksDownTimeResponse {
    return {
      BankDownTimesField: globalThis.Array.isArray(object?.BankDownTimesField)
        ? object.BankDownTimesField.map((e: any) => BankDownTimesField.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BanksDownTimeResponse): unknown {
    const obj: any = {};
    if (message.BankDownTimesField?.length) {
      obj.BankDownTimesField = message.BankDownTimesField.map((e) => BankDownTimesField.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BanksDownTimeResponse>, I>>(base?: I): BanksDownTimeResponse {
    return BanksDownTimeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BanksDownTimeResponse>, I>>(object: I): BanksDownTimeResponse {
    const message = createBaseBanksDownTimeResponse();
    message.BankDownTimesField = object.BankDownTimesField?.map((e) => BankDownTimesField.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBankDetail(): BankDetail {
  return { bankId: "", bankName: "", fsi: "" };
}

export const BankDetail: MessageFns<BankDetail> = {
  encode(message: BankDetail, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    if (message.bankName !== "") {
      writer.uint32(18).string(message.bankName);
    }
    if (message.fsi !== "") {
      writer.uint32(26).string(message.fsi);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankDetail {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankDetail();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fsi = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankDetail {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      fsi: isSet(object.fsi) ? globalThis.String(object.fsi) : "",
    };
  },

  toJSON(message: BankDetail): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.fsi !== "") {
      obj.fsi = message.fsi;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankDetail>, I>>(base?: I): BankDetail {
    return BankDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankDetail>, I>>(object: I): BankDetail {
    const message = createBaseBankDetail();
    message.bankId = object.bankId ?? "";
    message.bankName = object.bankName ?? "";
    message.fsi = object.fsi ?? "";
    return message;
  },
};

function createBaseAllBanksDetailsResponse(): AllBanksDetailsResponse {
  return { bankDetail: [] };
}

export const AllBanksDetailsResponse: MessageFns<AllBanksDetailsResponse> = {
  encode(message: AllBanksDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bankDetail) {
      BankDetail.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllBanksDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllBanksDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankDetail.push(BankDetail.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllBanksDetailsResponse {
    return {
      bankDetail: globalThis.Array.isArray(object?.bankDetail)
        ? object.bankDetail.map((e: any) => BankDetail.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AllBanksDetailsResponse): unknown {
    const obj: any = {};
    if (message.bankDetail?.length) {
      obj.bankDetail = message.bankDetail.map((e) => BankDetail.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllBanksDetailsResponse>, I>>(base?: I): AllBanksDetailsResponse {
    return AllBanksDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllBanksDetailsResponse>, I>>(object: I): AllBanksDetailsResponse {
    const message = createBaseAllBanksDetailsResponse();
    message.bankDetail = object.bankDetail?.map((e) => BankDetail.fromPartial(e)) || [];
    return message;
  },
};

export interface BankDownTimeAdminService {
  addOrUpdateBankDownTime(request: BankDownTime): Promise<BankDownTime>;
  deleteBankDownTime(request: DeleteBankDownTimeRequest): Promise<Empty>;
  getBankDownTime(request: BankDownTimeRequest): Promise<BankDownTimeResponse>;
  getBanksDownTime(request: Empty): Promise<BanksDownTimeResponse>;
  getAllBanksDetails(request: Empty): Promise<AllBanksDetailsResponse>;
}

export const BankDownTimeAdminServiceServiceName = "com.stablemoney.api.business.bankdowntime.BankDownTimeAdminService";
export class BankDownTimeAdminServiceClientImpl implements BankDownTimeAdminService {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || BankDownTimeAdminServiceServiceName;
    this.rpc = rpc;
    this.addOrUpdateBankDownTime = this.addOrUpdateBankDownTime.bind(this);
    this.deleteBankDownTime = this.deleteBankDownTime.bind(this);
    this.getBankDownTime = this.getBankDownTime.bind(this);
    this.getBanksDownTime = this.getBanksDownTime.bind(this);
    this.getAllBanksDetails = this.getAllBanksDetails.bind(this);
  }
  addOrUpdateBankDownTime(request: BankDownTime): Promise<BankDownTime> {
    const data = BankDownTime.encode(request).finish();
    const promise = this.rpc.request(this.service, "addOrUpdateBankDownTime", data);
    return promise.then((data) => BankDownTime.decode(new BinaryReader(data)));
  }

  deleteBankDownTime(request: DeleteBankDownTimeRequest): Promise<Empty> {
    const data = DeleteBankDownTimeRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "deleteBankDownTime", data);
    return promise.then((data) => Empty.decode(new BinaryReader(data)));
  }

  getBankDownTime(request: BankDownTimeRequest): Promise<BankDownTimeResponse> {
    const data = BankDownTimeRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getBankDownTime", data);
    return promise.then((data) => BankDownTimeResponse.decode(new BinaryReader(data)));
  }

  getBanksDownTime(request: Empty): Promise<BanksDownTimeResponse> {
    const data = Empty.encode(request).finish();
    const promise = this.rpc.request(this.service, "getBanksDownTime", data);
    return promise.then((data) => BanksDownTimeResponse.decode(new BinaryReader(data)));
  }

  getAllBanksDetails(request: Empty): Promise<AllBanksDetailsResponse> {
    const data = Empty.encode(request).finish();
    const promise = this.rpc.request(this.service, "getAllBanksDetails", data);
    return promise.then((data) => AllBanksDetailsResponse.decode(new BinaryReader(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
