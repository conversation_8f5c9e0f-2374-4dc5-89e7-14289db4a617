// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: InvestmentWithdrawalSurvey.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { DataKey } from "./Common";

export const protobufPackage = "com.stablemoney.api.investment.withdrawal";

export interface WithdrawalTimeline {
  items: WithdrawalTimeline_TimelineItem[];
}

export interface WithdrawalTimeline_TimelineItem {
  dataKey: DataKey | undefined;
  complete: boolean;
}

export interface WithdrawalSurvey {
  userFixedDeposit: WithdrawalSurvey_UserFixedDeposit | undefined;
  surveySubmitted: boolean;
  surveyResponse: string;
  withdrawalTimestamp: number;
  estimatedRefundTime: number;
  refundProcessed: boolean;
  withdrawalTimeline: WithdrawalTimeline | undefined;
}

export interface WithdrawalSurvey_UserFixedDeposit {
  id: string;
  fdIdentifier: string;
  journeyId: string;
  bankName: string;
  bankLogoUrl: string;
  tenure: string;
  investmentAmount: number;
}

function createBaseWithdrawalTimeline(): WithdrawalTimeline {
  return { items: [] };
}

export const WithdrawalTimeline: MessageFns<WithdrawalTimeline> = {
  encode(message: WithdrawalTimeline, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.items) {
      WithdrawalTimeline_TimelineItem.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalTimeline {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalTimeline();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.items.push(WithdrawalTimeline_TimelineItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalTimeline {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => WithdrawalTimeline_TimelineItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: WithdrawalTimeline): unknown {
    const obj: any = {};
    if (message.items?.length) {
      obj.items = message.items.map((e) => WithdrawalTimeline_TimelineItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalTimeline>, I>>(base?: I): WithdrawalTimeline {
    return WithdrawalTimeline.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalTimeline>, I>>(object: I): WithdrawalTimeline {
    const message = createBaseWithdrawalTimeline();
    message.items = object.items?.map((e) => WithdrawalTimeline_TimelineItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseWithdrawalTimeline_TimelineItem(): WithdrawalTimeline_TimelineItem {
  return { dataKey: undefined, complete: false };
}

export const WithdrawalTimeline_TimelineItem: MessageFns<WithdrawalTimeline_TimelineItem> = {
  encode(message: WithdrawalTimeline_TimelineItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.dataKey !== undefined) {
      DataKey.encode(message.dataKey, writer.uint32(10).fork()).join();
    }
    if (message.complete !== false) {
      writer.uint32(16).bool(message.complete);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalTimeline_TimelineItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalTimeline_TimelineItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.dataKey = DataKey.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.complete = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalTimeline_TimelineItem {
    return {
      dataKey: isSet(object.dataKey) ? DataKey.fromJSON(object.dataKey) : undefined,
      complete: isSet(object.complete) ? globalThis.Boolean(object.complete) : false,
    };
  },

  toJSON(message: WithdrawalTimeline_TimelineItem): unknown {
    const obj: any = {};
    if (message.dataKey !== undefined) {
      obj.dataKey = DataKey.toJSON(message.dataKey);
    }
    if (message.complete !== false) {
      obj.complete = message.complete;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalTimeline_TimelineItem>, I>>(base?: I): WithdrawalTimeline_TimelineItem {
    return WithdrawalTimeline_TimelineItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalTimeline_TimelineItem>, I>>(
    object: I,
  ): WithdrawalTimeline_TimelineItem {
    const message = createBaseWithdrawalTimeline_TimelineItem();
    message.dataKey = (object.dataKey !== undefined && object.dataKey !== null)
      ? DataKey.fromPartial(object.dataKey)
      : undefined;
    message.complete = object.complete ?? false;
    return message;
  },
};

function createBaseWithdrawalSurvey(): WithdrawalSurvey {
  return {
    userFixedDeposit: undefined,
    surveySubmitted: false,
    surveyResponse: "",
    withdrawalTimestamp: 0,
    estimatedRefundTime: 0,
    refundProcessed: false,
    withdrawalTimeline: undefined,
  };
}

export const WithdrawalSurvey: MessageFns<WithdrawalSurvey> = {
  encode(message: WithdrawalSurvey, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userFixedDeposit !== undefined) {
      WithdrawalSurvey_UserFixedDeposit.encode(message.userFixedDeposit, writer.uint32(10).fork()).join();
    }
    if (message.surveySubmitted !== false) {
      writer.uint32(16).bool(message.surveySubmitted);
    }
    if (message.surveyResponse !== "") {
      writer.uint32(26).string(message.surveyResponse);
    }
    if (message.withdrawalTimestamp !== 0) {
      writer.uint32(32).int64(message.withdrawalTimestamp);
    }
    if (message.estimatedRefundTime !== 0) {
      writer.uint32(40).int64(message.estimatedRefundTime);
    }
    if (message.refundProcessed !== false) {
      writer.uint32(64).bool(message.refundProcessed);
    }
    if (message.withdrawalTimeline !== undefined) {
      WithdrawalTimeline.encode(message.withdrawalTimeline, writer.uint32(82).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalSurvey {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalSurvey();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userFixedDeposit = WithdrawalSurvey_UserFixedDeposit.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.surveySubmitted = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.surveyResponse = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.withdrawalTimestamp = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.estimatedRefundTime = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.refundProcessed = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.withdrawalTimeline = WithdrawalTimeline.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalSurvey {
    return {
      userFixedDeposit: isSet(object.userFixedDeposit)
        ? WithdrawalSurvey_UserFixedDeposit.fromJSON(object.userFixedDeposit)
        : undefined,
      surveySubmitted: isSet(object.surveySubmitted) ? globalThis.Boolean(object.surveySubmitted) : false,
      surveyResponse: isSet(object.surveyResponse) ? globalThis.String(object.surveyResponse) : "",
      withdrawalTimestamp: isSet(object.withdrawalTimestamp) ? globalThis.Number(object.withdrawalTimestamp) : 0,
      estimatedRefundTime: isSet(object.estimatedRefundTime) ? globalThis.Number(object.estimatedRefundTime) : 0,
      refundProcessed: isSet(object.refundProcessed) ? globalThis.Boolean(object.refundProcessed) : false,
      withdrawalTimeline: isSet(object.withdrawalTimeline)
        ? WithdrawalTimeline.fromJSON(object.withdrawalTimeline)
        : undefined,
    };
  },

  toJSON(message: WithdrawalSurvey): unknown {
    const obj: any = {};
    if (message.userFixedDeposit !== undefined) {
      obj.userFixedDeposit = WithdrawalSurvey_UserFixedDeposit.toJSON(message.userFixedDeposit);
    }
    if (message.surveySubmitted !== false) {
      obj.surveySubmitted = message.surveySubmitted;
    }
    if (message.surveyResponse !== "") {
      obj.surveyResponse = message.surveyResponse;
    }
    if (message.withdrawalTimestamp !== 0) {
      obj.withdrawalTimestamp = Math.round(message.withdrawalTimestamp);
    }
    if (message.estimatedRefundTime !== 0) {
      obj.estimatedRefundTime = Math.round(message.estimatedRefundTime);
    }
    if (message.refundProcessed !== false) {
      obj.refundProcessed = message.refundProcessed;
    }
    if (message.withdrawalTimeline !== undefined) {
      obj.withdrawalTimeline = WithdrawalTimeline.toJSON(message.withdrawalTimeline);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalSurvey>, I>>(base?: I): WithdrawalSurvey {
    return WithdrawalSurvey.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalSurvey>, I>>(object: I): WithdrawalSurvey {
    const message = createBaseWithdrawalSurvey();
    message.userFixedDeposit = (object.userFixedDeposit !== undefined && object.userFixedDeposit !== null)
      ? WithdrawalSurvey_UserFixedDeposit.fromPartial(object.userFixedDeposit)
      : undefined;
    message.surveySubmitted = object.surveySubmitted ?? false;
    message.surveyResponse = object.surveyResponse ?? "";
    message.withdrawalTimestamp = object.withdrawalTimestamp ?? 0;
    message.estimatedRefundTime = object.estimatedRefundTime ?? 0;
    message.refundProcessed = object.refundProcessed ?? false;
    message.withdrawalTimeline = (object.withdrawalTimeline !== undefined && object.withdrawalTimeline !== null)
      ? WithdrawalTimeline.fromPartial(object.withdrawalTimeline)
      : undefined;
    return message;
  },
};

function createBaseWithdrawalSurvey_UserFixedDeposit(): WithdrawalSurvey_UserFixedDeposit {
  return { id: "", fdIdentifier: "", journeyId: "", bankName: "", bankLogoUrl: "", tenure: "", investmentAmount: 0 };
}

export const WithdrawalSurvey_UserFixedDeposit: MessageFns<WithdrawalSurvey_UserFixedDeposit> = {
  encode(message: WithdrawalSurvey_UserFixedDeposit, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.fdIdentifier !== "") {
      writer.uint32(18).string(message.fdIdentifier);
    }
    if (message.journeyId !== "") {
      writer.uint32(34).string(message.journeyId);
    }
    if (message.bankName !== "") {
      writer.uint32(50).string(message.bankName);
    }
    if (message.bankLogoUrl !== "") {
      writer.uint32(66).string(message.bankLogoUrl);
    }
    if (message.tenure !== "") {
      writer.uint32(82).string(message.tenure);
    }
    if (message.investmentAmount !== 0) {
      writer.uint32(97).double(message.investmentAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalSurvey_UserFixedDeposit {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalSurvey_UserFixedDeposit();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fdIdentifier = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.journeyId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.bankLogoUrl = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalSurvey_UserFixedDeposit {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      fdIdentifier: isSet(object.fdIdentifier) ? globalThis.String(object.fdIdentifier) : "",
      journeyId: isSet(object.journeyId) ? globalThis.String(object.journeyId) : "",
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogoUrl: isSet(object.bankLogoUrl) ? globalThis.String(object.bankLogoUrl) : "",
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
    };
  },

  toJSON(message: WithdrawalSurvey_UserFixedDeposit): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.fdIdentifier !== "") {
      obj.fdIdentifier = message.fdIdentifier;
    }
    if (message.journeyId !== "") {
      obj.journeyId = message.journeyId;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogoUrl !== "") {
      obj.bankLogoUrl = message.bankLogoUrl;
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalSurvey_UserFixedDeposit>, I>>(
    base?: I,
  ): WithdrawalSurvey_UserFixedDeposit {
    return WithdrawalSurvey_UserFixedDeposit.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalSurvey_UserFixedDeposit>, I>>(
    object: I,
  ): WithdrawalSurvey_UserFixedDeposit {
    const message = createBaseWithdrawalSurvey_UserFixedDeposit();
    message.id = object.id ?? "";
    message.fdIdentifier = object.fdIdentifier ?? "";
    message.journeyId = object.journeyId ?? "";
    message.bankName = object.bankName ?? "";
    message.bankLogoUrl = object.bankLogoUrl ?? "";
    message.tenure = object.tenure ?? "";
    message.investmentAmount = object.investmentAmount ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
