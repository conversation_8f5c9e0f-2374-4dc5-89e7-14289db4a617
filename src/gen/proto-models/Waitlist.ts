// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Waitlist.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export interface AddReferrerRequest {
  refererId: string;
  metadata: string;
}

export interface AddReferrerByUrlRequest {
  url: string;
}

export interface AddReferrerResponse {
  status?: string | undefined;
}

export interface GetReferralUrlRequest {
}

export interface GetReferralUrlResponse {
  referralUrl: string;
}

export interface GetWaitlistPositionResponse {
  position: number;
}

export interface GetWaitlistPositionRequest {
}

export interface GenerateReferralLinkRequest {
}

export interface GenerateReferralLinkResponse {
  referralLink: string;
}

function createBaseAddReferrerRequest(): AddReferrerRequest {
  return { refererId: "", metadata: "" };
}

export const AddReferrerRequest: MessageFns<AddReferrerRequest> = {
  encode(message: AddReferrerRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.refererId !== "") {
      writer.uint32(10).string(message.refererId);
    }
    if (message.metadata !== "") {
      writer.uint32(18).string(message.metadata);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddReferrerRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddReferrerRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.refererId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.metadata = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddReferrerRequest {
    return {
      refererId: isSet(object.refererId) ? globalThis.String(object.refererId) : "",
      metadata: isSet(object.metadata) ? globalThis.String(object.metadata) : "",
    };
  },

  toJSON(message: AddReferrerRequest): unknown {
    const obj: any = {};
    if (message.refererId !== "") {
      obj.refererId = message.refererId;
    }
    if (message.metadata !== "") {
      obj.metadata = message.metadata;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddReferrerRequest>, I>>(base?: I): AddReferrerRequest {
    return AddReferrerRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddReferrerRequest>, I>>(object: I): AddReferrerRequest {
    const message = createBaseAddReferrerRequest();
    message.refererId = object.refererId ?? "";
    message.metadata = object.metadata ?? "";
    return message;
  },
};

function createBaseAddReferrerByUrlRequest(): AddReferrerByUrlRequest {
  return { url: "" };
}

export const AddReferrerByUrlRequest: MessageFns<AddReferrerByUrlRequest> = {
  encode(message: AddReferrerByUrlRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.url !== "") {
      writer.uint32(10).string(message.url);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddReferrerByUrlRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddReferrerByUrlRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.url = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddReferrerByUrlRequest {
    return { url: isSet(object.url) ? globalThis.String(object.url) : "" };
  },

  toJSON(message: AddReferrerByUrlRequest): unknown {
    const obj: any = {};
    if (message.url !== "") {
      obj.url = message.url;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddReferrerByUrlRequest>, I>>(base?: I): AddReferrerByUrlRequest {
    return AddReferrerByUrlRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddReferrerByUrlRequest>, I>>(object: I): AddReferrerByUrlRequest {
    const message = createBaseAddReferrerByUrlRequest();
    message.url = object.url ?? "";
    return message;
  },
};

function createBaseAddReferrerResponse(): AddReferrerResponse {
  return { status: undefined };
}

export const AddReferrerResponse: MessageFns<AddReferrerResponse> = {
  encode(message: AddReferrerResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== undefined) {
      writer.uint32(10).string(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddReferrerResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddReferrerResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.status = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddReferrerResponse {
    return { status: isSet(object.status) ? globalThis.String(object.status) : undefined };
  },

  toJSON(message: AddReferrerResponse): unknown {
    const obj: any = {};
    if (message.status !== undefined) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddReferrerResponse>, I>>(base?: I): AddReferrerResponse {
    return AddReferrerResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddReferrerResponse>, I>>(object: I): AddReferrerResponse {
    const message = createBaseAddReferrerResponse();
    message.status = object.status ?? undefined;
    return message;
  },
};

function createBaseGetReferralUrlRequest(): GetReferralUrlRequest {
  return {};
}

export const GetReferralUrlRequest: MessageFns<GetReferralUrlRequest> = {
  encode(_: GetReferralUrlRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetReferralUrlRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetReferralUrlRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): GetReferralUrlRequest {
    return {};
  },

  toJSON(_: GetReferralUrlRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetReferralUrlRequest>, I>>(base?: I): GetReferralUrlRequest {
    return GetReferralUrlRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetReferralUrlRequest>, I>>(_: I): GetReferralUrlRequest {
    const message = createBaseGetReferralUrlRequest();
    return message;
  },
};

function createBaseGetReferralUrlResponse(): GetReferralUrlResponse {
  return { referralUrl: "" };
}

export const GetReferralUrlResponse: MessageFns<GetReferralUrlResponse> = {
  encode(message: GetReferralUrlResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.referralUrl !== "") {
      writer.uint32(10).string(message.referralUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetReferralUrlResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetReferralUrlResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.referralUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetReferralUrlResponse {
    return { referralUrl: isSet(object.referralUrl) ? globalThis.String(object.referralUrl) : "" };
  },

  toJSON(message: GetReferralUrlResponse): unknown {
    const obj: any = {};
    if (message.referralUrl !== "") {
      obj.referralUrl = message.referralUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetReferralUrlResponse>, I>>(base?: I): GetReferralUrlResponse {
    return GetReferralUrlResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetReferralUrlResponse>, I>>(object: I): GetReferralUrlResponse {
    const message = createBaseGetReferralUrlResponse();
    message.referralUrl = object.referralUrl ?? "";
    return message;
  },
};

function createBaseGetWaitlistPositionResponse(): GetWaitlistPositionResponse {
  return { position: 0 };
}

export const GetWaitlistPositionResponse: MessageFns<GetWaitlistPositionResponse> = {
  encode(message: GetWaitlistPositionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.position !== 0) {
      writer.uint32(8).int64(message.position);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetWaitlistPositionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetWaitlistPositionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.position = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetWaitlistPositionResponse {
    return { position: isSet(object.position) ? globalThis.Number(object.position) : 0 };
  },

  toJSON(message: GetWaitlistPositionResponse): unknown {
    const obj: any = {};
    if (message.position !== 0) {
      obj.position = Math.round(message.position);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetWaitlistPositionResponse>, I>>(base?: I): GetWaitlistPositionResponse {
    return GetWaitlistPositionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetWaitlistPositionResponse>, I>>(object: I): GetWaitlistPositionResponse {
    const message = createBaseGetWaitlistPositionResponse();
    message.position = object.position ?? 0;
    return message;
  },
};

function createBaseGetWaitlistPositionRequest(): GetWaitlistPositionRequest {
  return {};
}

export const GetWaitlistPositionRequest: MessageFns<GetWaitlistPositionRequest> = {
  encode(_: GetWaitlistPositionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetWaitlistPositionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetWaitlistPositionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): GetWaitlistPositionRequest {
    return {};
  },

  toJSON(_: GetWaitlistPositionRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetWaitlistPositionRequest>, I>>(base?: I): GetWaitlistPositionRequest {
    return GetWaitlistPositionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetWaitlistPositionRequest>, I>>(_: I): GetWaitlistPositionRequest {
    const message = createBaseGetWaitlistPositionRequest();
    return message;
  },
};

function createBaseGenerateReferralLinkRequest(): GenerateReferralLinkRequest {
  return {};
}

export const GenerateReferralLinkRequest: MessageFns<GenerateReferralLinkRequest> = {
  encode(_: GenerateReferralLinkRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GenerateReferralLinkRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerateReferralLinkRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): GenerateReferralLinkRequest {
    return {};
  },

  toJSON(_: GenerateReferralLinkRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GenerateReferralLinkRequest>, I>>(base?: I): GenerateReferralLinkRequest {
    return GenerateReferralLinkRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateReferralLinkRequest>, I>>(_: I): GenerateReferralLinkRequest {
    const message = createBaseGenerateReferralLinkRequest();
    return message;
  },
};

function createBaseGenerateReferralLinkResponse(): GenerateReferralLinkResponse {
  return { referralLink: "" };
}

export const GenerateReferralLinkResponse: MessageFns<GenerateReferralLinkResponse> = {
  encode(message: GenerateReferralLinkResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.referralLink !== "") {
      writer.uint32(10).string(message.referralLink);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GenerateReferralLinkResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerateReferralLinkResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.referralLink = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GenerateReferralLinkResponse {
    return { referralLink: isSet(object.referralLink) ? globalThis.String(object.referralLink) : "" };
  },

  toJSON(message: GenerateReferralLinkResponse): unknown {
    const obj: any = {};
    if (message.referralLink !== "") {
      obj.referralLink = message.referralLink;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GenerateReferralLinkResponse>, I>>(base?: I): GenerateReferralLinkResponse {
    return GenerateReferralLinkResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateReferralLinkResponse>, I>>(object: I): GenerateReferralLinkResponse {
    const message = createBaseGenerateReferralLinkResponse();
    message.referralLink = object.referralLink ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
