// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: UserRating.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum RatingSource {
  UNKNOWN_RATING_SOURCE = 0,
  IN_APP_RATING_SOURCE = 1,
  TD_BOOKED_EMAIL_RATING_SOURCE = 2,
  UNRECOGNIZED = -1,
}

export function ratingSourceFromJSON(object: any): RatingSource {
  switch (object) {
    case 0:
    case "UNKNOWN_RATING_SOURCE":
      return RatingSource.UNKNOWN_RATING_SOURCE;
    case 1:
    case "IN_APP_RATING_SOURCE":
      return RatingSource.IN_APP_RATING_SOURCE;
    case 2:
    case "TD_BOOKED_EMAIL_RATING_SOURCE":
      return RatingSource.TD_BOOKED_EMAIL_RATING_SOURCE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RatingSource.UNRECOGNIZED;
  }
}

export function ratingSourceToJSON(object: RatingSource): string {
  switch (object) {
    case RatingSource.UNKNOWN_RATING_SOURCE:
      return "UNKNOWN_RATING_SOURCE";
    case RatingSource.IN_APP_RATING_SOURCE:
      return "IN_APP_RATING_SOURCE";
    case RatingSource.TD_BOOKED_EMAIL_RATING_SOURCE:
      return "TD_BOOKED_EMAIL_RATING_SOURCE";
    case RatingSource.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface PostRatingRequest {
  rating?: number | undefined;
  review?: string | undefined;
  isReviewSkipped?: boolean | undefined;
  userId: string;
  isGooglePlayRatingShown: boolean;
}

export interface PostRatingResponse {
}

export interface UserRatingRequest {
  userId: string;
}

export interface UserRatingResponse {
  rating: number;
  review: string;
  isReviewSkipped: boolean;
  inAppReviewCohort: string;
  showInAppReview: boolean;
}

export interface EmailFeedbackRequest {
  customerId: string;
  bookingId: string;
  rating: number;
  goodExperienceOption: string[];
  message: string;
}

export interface EmailFeedbackResponse {
  status: boolean;
}

function createBasePostRatingRequest(): PostRatingRequest {
  return {
    rating: undefined,
    review: undefined,
    isReviewSkipped: undefined,
    userId: "",
    isGooglePlayRatingShown: false,
  };
}

export const PostRatingRequest: MessageFns<PostRatingRequest> = {
  encode(message: PostRatingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rating !== undefined) {
      writer.uint32(9).double(message.rating);
    }
    if (message.review !== undefined) {
      writer.uint32(18).string(message.review);
    }
    if (message.isReviewSkipped !== undefined) {
      writer.uint32(24).bool(message.isReviewSkipped);
    }
    if (message.userId !== "") {
      writer.uint32(34).string(message.userId);
    }
    if (message.isGooglePlayRatingShown !== false) {
      writer.uint32(48).bool(message.isGooglePlayRatingShown);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PostRatingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePostRatingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.rating = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.review = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isReviewSkipped = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isGooglePlayRatingShown = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PostRatingRequest {
    return {
      rating: isSet(object.rating) ? globalThis.Number(object.rating) : undefined,
      review: isSet(object.review) ? globalThis.String(object.review) : undefined,
      isReviewSkipped: isSet(object.isReviewSkipped) ? globalThis.Boolean(object.isReviewSkipped) : undefined,
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      isGooglePlayRatingShown: isSet(object.isGooglePlayRatingShown)
        ? globalThis.Boolean(object.isGooglePlayRatingShown)
        : false,
    };
  },

  toJSON(message: PostRatingRequest): unknown {
    const obj: any = {};
    if (message.rating !== undefined) {
      obj.rating = message.rating;
    }
    if (message.review !== undefined) {
      obj.review = message.review;
    }
    if (message.isReviewSkipped !== undefined) {
      obj.isReviewSkipped = message.isReviewSkipped;
    }
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.isGooglePlayRatingShown !== false) {
      obj.isGooglePlayRatingShown = message.isGooglePlayRatingShown;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PostRatingRequest>, I>>(base?: I): PostRatingRequest {
    return PostRatingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PostRatingRequest>, I>>(object: I): PostRatingRequest {
    const message = createBasePostRatingRequest();
    message.rating = object.rating ?? undefined;
    message.review = object.review ?? undefined;
    message.isReviewSkipped = object.isReviewSkipped ?? undefined;
    message.userId = object.userId ?? "";
    message.isGooglePlayRatingShown = object.isGooglePlayRatingShown ?? false;
    return message;
  },
};

function createBasePostRatingResponse(): PostRatingResponse {
  return {};
}

export const PostRatingResponse: MessageFns<PostRatingResponse> = {
  encode(_: PostRatingResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PostRatingResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePostRatingResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): PostRatingResponse {
    return {};
  },

  toJSON(_: PostRatingResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<PostRatingResponse>, I>>(base?: I): PostRatingResponse {
    return PostRatingResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PostRatingResponse>, I>>(_: I): PostRatingResponse {
    const message = createBasePostRatingResponse();
    return message;
  },
};

function createBaseUserRatingRequest(): UserRatingRequest {
  return { userId: "" };
}

export const UserRatingRequest: MessageFns<UserRatingRequest> = {
  encode(message: UserRatingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserRatingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserRatingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserRatingRequest {
    return { userId: isSet(object.userId) ? globalThis.String(object.userId) : "" };
  },

  toJSON(message: UserRatingRequest): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserRatingRequest>, I>>(base?: I): UserRatingRequest {
    return UserRatingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserRatingRequest>, I>>(object: I): UserRatingRequest {
    const message = createBaseUserRatingRequest();
    message.userId = object.userId ?? "";
    return message;
  },
};

function createBaseUserRatingResponse(): UserRatingResponse {
  return { rating: 0, review: "", isReviewSkipped: false, inAppReviewCohort: "", showInAppReview: false };
}

export const UserRatingResponse: MessageFns<UserRatingResponse> = {
  encode(message: UserRatingResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rating !== 0) {
      writer.uint32(9).double(message.rating);
    }
    if (message.review !== "") {
      writer.uint32(18).string(message.review);
    }
    if (message.isReviewSkipped !== false) {
      writer.uint32(24).bool(message.isReviewSkipped);
    }
    if (message.inAppReviewCohort !== "") {
      writer.uint32(34).string(message.inAppReviewCohort);
    }
    if (message.showInAppReview !== false) {
      writer.uint32(48).bool(message.showInAppReview);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserRatingResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserRatingResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.rating = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.review = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isReviewSkipped = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.inAppReviewCohort = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.showInAppReview = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserRatingResponse {
    return {
      rating: isSet(object.rating) ? globalThis.Number(object.rating) : 0,
      review: isSet(object.review) ? globalThis.String(object.review) : "",
      isReviewSkipped: isSet(object.isReviewSkipped) ? globalThis.Boolean(object.isReviewSkipped) : false,
      inAppReviewCohort: isSet(object.inAppReviewCohort) ? globalThis.String(object.inAppReviewCohort) : "",
      showInAppReview: isSet(object.showInAppReview) ? globalThis.Boolean(object.showInAppReview) : false,
    };
  },

  toJSON(message: UserRatingResponse): unknown {
    const obj: any = {};
    if (message.rating !== 0) {
      obj.rating = message.rating;
    }
    if (message.review !== "") {
      obj.review = message.review;
    }
    if (message.isReviewSkipped !== false) {
      obj.isReviewSkipped = message.isReviewSkipped;
    }
    if (message.inAppReviewCohort !== "") {
      obj.inAppReviewCohort = message.inAppReviewCohort;
    }
    if (message.showInAppReview !== false) {
      obj.showInAppReview = message.showInAppReview;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserRatingResponse>, I>>(base?: I): UserRatingResponse {
    return UserRatingResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserRatingResponse>, I>>(object: I): UserRatingResponse {
    const message = createBaseUserRatingResponse();
    message.rating = object.rating ?? 0;
    message.review = object.review ?? "";
    message.isReviewSkipped = object.isReviewSkipped ?? false;
    message.inAppReviewCohort = object.inAppReviewCohort ?? "";
    message.showInAppReview = object.showInAppReview ?? false;
    return message;
  },
};

function createBaseEmailFeedbackRequest(): EmailFeedbackRequest {
  return { customerId: "", bookingId: "", rating: 0, goodExperienceOption: [], message: "" };
}

export const EmailFeedbackRequest: MessageFns<EmailFeedbackRequest> = {
  encode(message: EmailFeedbackRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.customerId !== "") {
      writer.uint32(10).string(message.customerId);
    }
    if (message.bookingId !== "") {
      writer.uint32(18).string(message.bookingId);
    }
    if (message.rating !== 0) {
      writer.uint32(24).int32(message.rating);
    }
    for (const v of message.goodExperienceOption) {
      writer.uint32(34).string(v!);
    }
    if (message.message !== "") {
      writer.uint32(42).string(message.message);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmailFeedbackRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmailFeedbackRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.customerId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bookingId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.rating = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.goodExperienceOption.push(reader.string());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.message = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmailFeedbackRequest {
    return {
      customerId: isSet(object.customerId) ? globalThis.String(object.customerId) : "",
      bookingId: isSet(object.bookingId) ? globalThis.String(object.bookingId) : "",
      rating: isSet(object.rating) ? globalThis.Number(object.rating) : 0,
      goodExperienceOption: globalThis.Array.isArray(object?.goodExperienceOption)
        ? object.goodExperienceOption.map((e: any) => globalThis.String(e))
        : [],
      message: isSet(object.message) ? globalThis.String(object.message) : "",
    };
  },

  toJSON(message: EmailFeedbackRequest): unknown {
    const obj: any = {};
    if (message.customerId !== "") {
      obj.customerId = message.customerId;
    }
    if (message.bookingId !== "") {
      obj.bookingId = message.bookingId;
    }
    if (message.rating !== 0) {
      obj.rating = Math.round(message.rating);
    }
    if (message.goodExperienceOption?.length) {
      obj.goodExperienceOption = message.goodExperienceOption;
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmailFeedbackRequest>, I>>(base?: I): EmailFeedbackRequest {
    return EmailFeedbackRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmailFeedbackRequest>, I>>(object: I): EmailFeedbackRequest {
    const message = createBaseEmailFeedbackRequest();
    message.customerId = object.customerId ?? "";
    message.bookingId = object.bookingId ?? "";
    message.rating = object.rating ?? 0;
    message.goodExperienceOption = object.goodExperienceOption?.map((e) => e) || [];
    message.message = object.message ?? "";
    return message;
  },
};

function createBaseEmailFeedbackResponse(): EmailFeedbackResponse {
  return { status: false };
}

export const EmailFeedbackResponse: MessageFns<EmailFeedbackResponse> = {
  encode(message: EmailFeedbackResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmailFeedbackResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmailFeedbackResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmailFeedbackResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: EmailFeedbackResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmailFeedbackResponse>, I>>(base?: I): EmailFeedbackResponse {
    return EmailFeedbackResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmailFeedbackResponse>, I>>(object: I): EmailFeedbackResponse {
    const message = createBaseEmailFeedbackResponse();
    message.status = object.status ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
