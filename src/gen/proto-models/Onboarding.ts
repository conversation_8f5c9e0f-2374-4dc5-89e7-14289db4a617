// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Onboarding.proto

/* eslint-disable */
import { Binary<PERSON><PERSON>er, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  KycType,
  kycTypeFromJSON,
  kycTypeToJSON,
  OnBoardingStatus,
  onBoardingStatusFromJSON,
  onBoardingStatusToJSON,
} from "./Kyc";

export const protobufPackage = "com.stablemoney.api.identity";

export enum OnboardingModule {
  ONBOARDING_MODULE_UNKNOWN = 0,
  APP_ONBOARDING = 1,
  FIXED_DEPOSIT = 2,
  MUTUAL_FUND = 3,
  BOND_ONBOARDING = 4,
  UNRECOGNIZED = -1,
}

export function onboardingModuleFromJSON(object: any): OnboardingModule {
  switch (object) {
    case 0:
    case "ONBOARDING_MODULE_UNKNOWN":
      return OnboardingModule.ONBOARDING_MODULE_UNKNOWN;
    case 1:
    case "APP_ONBOARDING":
      return OnboardingModule.APP_ONBOARDING;
    case 2:
    case "FIXED_DEPOSIT":
      return OnboardingModule.FIXED_DEPOSIT;
    case 3:
    case "MUTUAL_FUND":
      return OnboardingModule.MUTUAL_FUND;
    case 4:
    case "BOND_ONBOARDING":
      return OnboardingModule.BOND_ONBOARDING;
    case -1:
    case "UNRECOGNIZED":
    default:
      return OnboardingModule.UNRECOGNIZED;
  }
}

export function onboardingModuleToJSON(object: OnboardingModule): string {
  switch (object) {
    case OnboardingModule.ONBOARDING_MODULE_UNKNOWN:
      return "ONBOARDING_MODULE_UNKNOWN";
    case OnboardingModule.APP_ONBOARDING:
      return "APP_ONBOARDING";
    case OnboardingModule.FIXED_DEPOSIT:
      return "FIXED_DEPOSIT";
    case OnboardingModule.MUTUAL_FUND:
      return "MUTUAL_FUND";
    case OnboardingModule.BOND_ONBOARDING:
      return "BOND_ONBOARDING";
    case OnboardingModule.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface UserOnboardingStepsResponse {
  data: UserStateData[];
}

export interface UserStateData {
  onboardingStep: KycType;
  status: OnBoardingStatus;
}

export interface OnboardingModuleStepsResponse {
  data: OnboardingModuleSteps[];
}

export interface OnboardingModuleSteps {
  onboardingStep: KycType;
  kycSequence: number;
  module: string;
  isSkippable: boolean;
}

export interface OnboardingState {
  next?: KycType | undefined;
}

export interface SkipOnboardingResponse {
}

function createBaseUserOnboardingStepsResponse(): UserOnboardingStepsResponse {
  return { data: [] };
}

export const UserOnboardingStepsResponse: MessageFns<UserOnboardingStepsResponse> = {
  encode(message: UserOnboardingStepsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      UserStateData.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserOnboardingStepsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserOnboardingStepsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.data.push(UserStateData.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserOnboardingStepsResponse {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => UserStateData.fromJSON(e)) : [],
    };
  },

  toJSON(message: UserOnboardingStepsResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => UserStateData.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserOnboardingStepsResponse>, I>>(base?: I): UserOnboardingStepsResponse {
    return UserOnboardingStepsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserOnboardingStepsResponse>, I>>(object: I): UserOnboardingStepsResponse {
    const message = createBaseUserOnboardingStepsResponse();
    message.data = object.data?.map((e) => UserStateData.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserStateData(): UserStateData {
  return { onboardingStep: 0, status: 0 };
}

export const UserStateData: MessageFns<UserStateData> = {
  encode(message: UserStateData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.onboardingStep !== 0) {
      writer.uint32(8).int32(message.onboardingStep);
    }
    if (message.status !== 0) {
      writer.uint32(16).int32(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserStateData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserStateData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.onboardingStep = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.status = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserStateData {
    return {
      onboardingStep: isSet(object.onboardingStep) ? kycTypeFromJSON(object.onboardingStep) : 0,
      status: isSet(object.status) ? onBoardingStatusFromJSON(object.status) : 0,
    };
  },

  toJSON(message: UserStateData): unknown {
    const obj: any = {};
    if (message.onboardingStep !== 0) {
      obj.onboardingStep = kycTypeToJSON(message.onboardingStep);
    }
    if (message.status !== 0) {
      obj.status = onBoardingStatusToJSON(message.status);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserStateData>, I>>(base?: I): UserStateData {
    return UserStateData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserStateData>, I>>(object: I): UserStateData {
    const message = createBaseUserStateData();
    message.onboardingStep = object.onboardingStep ?? 0;
    message.status = object.status ?? 0;
    return message;
  },
};

function createBaseOnboardingModuleStepsResponse(): OnboardingModuleStepsResponse {
  return { data: [] };
}

export const OnboardingModuleStepsResponse: MessageFns<OnboardingModuleStepsResponse> = {
  encode(message: OnboardingModuleStepsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      OnboardingModuleSteps.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OnboardingModuleStepsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOnboardingModuleStepsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(OnboardingModuleSteps.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OnboardingModuleStepsResponse {
    return {
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => OnboardingModuleSteps.fromJSON(e))
        : [],
    };
  },

  toJSON(message: OnboardingModuleStepsResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => OnboardingModuleSteps.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OnboardingModuleStepsResponse>, I>>(base?: I): OnboardingModuleStepsResponse {
    return OnboardingModuleStepsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnboardingModuleStepsResponse>, I>>(
    object: I,
  ): OnboardingModuleStepsResponse {
    const message = createBaseOnboardingModuleStepsResponse();
    message.data = object.data?.map((e) => OnboardingModuleSteps.fromPartial(e)) || [];
    return message;
  },
};

function createBaseOnboardingModuleSteps(): OnboardingModuleSteps {
  return { onboardingStep: 0, kycSequence: 0, module: "", isSkippable: false };
}

export const OnboardingModuleSteps: MessageFns<OnboardingModuleSteps> = {
  encode(message: OnboardingModuleSteps, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.onboardingStep !== 0) {
      writer.uint32(8).int32(message.onboardingStep);
    }
    if (message.kycSequence !== 0) {
      writer.uint32(16).int32(message.kycSequence);
    }
    if (message.module !== "") {
      writer.uint32(26).string(message.module);
    }
    if (message.isSkippable !== false) {
      writer.uint32(32).bool(message.isSkippable);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OnboardingModuleSteps {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOnboardingModuleSteps();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.onboardingStep = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.kycSequence = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.module = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isSkippable = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OnboardingModuleSteps {
    return {
      onboardingStep: isSet(object.onboardingStep) ? kycTypeFromJSON(object.onboardingStep) : 0,
      kycSequence: isSet(object.kycSequence) ? globalThis.Number(object.kycSequence) : 0,
      module: isSet(object.module) ? globalThis.String(object.module) : "",
      isSkippable: isSet(object.isSkippable) ? globalThis.Boolean(object.isSkippable) : false,
    };
  },

  toJSON(message: OnboardingModuleSteps): unknown {
    const obj: any = {};
    if (message.onboardingStep !== 0) {
      obj.onboardingStep = kycTypeToJSON(message.onboardingStep);
    }
    if (message.kycSequence !== 0) {
      obj.kycSequence = Math.round(message.kycSequence);
    }
    if (message.module !== "") {
      obj.module = message.module;
    }
    if (message.isSkippable !== false) {
      obj.isSkippable = message.isSkippable;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OnboardingModuleSteps>, I>>(base?: I): OnboardingModuleSteps {
    return OnboardingModuleSteps.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnboardingModuleSteps>, I>>(object: I): OnboardingModuleSteps {
    const message = createBaseOnboardingModuleSteps();
    message.onboardingStep = object.onboardingStep ?? 0;
    message.kycSequence = object.kycSequence ?? 0;
    message.module = object.module ?? "";
    message.isSkippable = object.isSkippable ?? false;
    return message;
  },
};

function createBaseOnboardingState(): OnboardingState {
  return { next: undefined };
}

export const OnboardingState: MessageFns<OnboardingState> = {
  encode(message: OnboardingState, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.next !== undefined) {
      writer.uint32(8).int32(message.next);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OnboardingState {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOnboardingState();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.next = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OnboardingState {
    return { next: isSet(object.next) ? kycTypeFromJSON(object.next) : undefined };
  },

  toJSON(message: OnboardingState): unknown {
    const obj: any = {};
    if (message.next !== undefined) {
      obj.next = kycTypeToJSON(message.next);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OnboardingState>, I>>(base?: I): OnboardingState {
    return OnboardingState.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnboardingState>, I>>(object: I): OnboardingState {
    const message = createBaseOnboardingState();
    message.next = object.next ?? undefined;
    return message;
  },
};

function createBaseSkipOnboardingResponse(): SkipOnboardingResponse {
  return {};
}

export const SkipOnboardingResponse: MessageFns<SkipOnboardingResponse> = {
  encode(_: SkipOnboardingResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SkipOnboardingResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSkipOnboardingResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): SkipOnboardingResponse {
    return {};
  },

  toJSON(_: SkipOnboardingResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SkipOnboardingResponse>, I>>(base?: I): SkipOnboardingResponse {
    return SkipOnboardingResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkipOnboardingResponse>, I>>(_: I): SkipOnboardingResponse {
    const message = createBaseSkipOnboardingResponse();
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
