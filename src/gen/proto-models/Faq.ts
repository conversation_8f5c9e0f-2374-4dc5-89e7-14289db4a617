// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Faq.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.business.faq";

export enum FaqType {
  UNKNOWN_FAQ_TYPE = 0,
  DEFAULT_FAQ_TYPE = 1,
  REWARD_FAQ_TYPE = 2,
  REFERRAL_FAQ_TYPE = 3,
  CATEGORY_FAQ_TYPE = 4,
  UNRECOGNIZED = -1,
}

export function faqTypeFromJSON(object: any): FaqType {
  switch (object) {
    case 0:
    case "UNKNOWN_FAQ_TYPE":
      return FaqType.UNKNOWN_FAQ_TYPE;
    case 1:
    case "DEFAULT_FAQ_TYPE":
      return FaqType.DEFAULT_FAQ_TYPE;
    case 2:
    case "REWARD_FAQ_TYPE":
      return FaqType.REWARD_FAQ_TYPE;
    case 3:
    case "REFERRAL_FAQ_TYPE":
      return FaqType.REFERRAL_FAQ_TYPE;
    case 4:
    case "CATEGORY_FAQ_TYPE":
      return FaqType.CATEGORY_FAQ_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FaqType.UNRECOGNIZED;
  }
}

export function faqTypeToJSON(object: FaqType): string {
  switch (object) {
    case FaqType.UNKNOWN_FAQ_TYPE:
      return "UNKNOWN_FAQ_TYPE";
    case FaqType.DEFAULT_FAQ_TYPE:
      return "DEFAULT_FAQ_TYPE";
    case FaqType.REWARD_FAQ_TYPE:
      return "REWARD_FAQ_TYPE";
    case FaqType.REFERRAL_FAQ_TYPE:
      return "REFERRAL_FAQ_TYPE";
    case FaqType.CATEGORY_FAQ_TYPE:
      return "CATEGORY_FAQ_TYPE";
    case FaqType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum BusinessUnit {
  UNKNOWN_BUSINESS_UNIT = 0,
  ALPHA = 1,
  FINSERV = 2,
  BROKING = 3,
  UNRECOGNIZED = -1,
}

export function businessUnitFromJSON(object: any): BusinessUnit {
  switch (object) {
    case 0:
    case "UNKNOWN_BUSINESS_UNIT":
      return BusinessUnit.UNKNOWN_BUSINESS_UNIT;
    case 1:
    case "ALPHA":
      return BusinessUnit.ALPHA;
    case 2:
    case "FINSERV":
      return BusinessUnit.FINSERV;
    case 3:
    case "BROKING":
      return BusinessUnit.BROKING;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BusinessUnit.UNRECOGNIZED;
  }
}

export function businessUnitToJSON(object: BusinessUnit): string {
  switch (object) {
    case BusinessUnit.UNKNOWN_BUSINESS_UNIT:
      return "UNKNOWN_BUSINESS_UNIT";
    case BusinessUnit.ALPHA:
      return "ALPHA";
    case BusinessUnit.FINSERV:
      return "FINSERV";
    case BusinessUnit.BROKING:
      return "BROKING";
    case BusinessUnit.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface BankFaqResponse {
  bankFaq: Faq[];
}

export interface Faq {
  question: string;
  answer: string;
  htmlAnswer: string;
  bankFaqCategoryList: string[];
  questionTag: string;
  faqId?: string | undefined;
  minVersionNumber: number;
  maxVersionNumber: number;
  userName?: string | undefined;
  userLocation?: string | undefined;
  userProfileUrl?: string | undefined;
  upvotes?: number | undefined;
  downvotes?: number | undefined;
}

export interface SupportFaqResponse {
  bankFaq: Faq[];
}

export interface SupportFaqV2 {
  faq: Faq | undefined;
  supportFaqIconUrl: string;
}

export interface SupportFaqResponseV2 {
  categoryId: string;
  categoryName: string;
  categoryIconUrl: string;
  faqs: SupportFaqV2[];
  categoryDescription: string;
}

export interface SupportFaqCategoryResponseV2 {
  categorySupportFaq: SupportFaqResponseV2[];
  topCategoryId: string;
}

export interface AskQuestionRequest {
  question: string;
}

export interface GetFaqsRequest {
  businessUnit: BusinessUnit;
  namespace: string;
  identifier: string;
}

export interface GetFaqsResponse {
  faqs: Faq[];
}

function createBaseBankFaqResponse(): BankFaqResponse {
  return { bankFaq: [] };
}

export const BankFaqResponse: MessageFns<BankFaqResponse> = {
  encode(message: BankFaqResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bankFaq) {
      Faq.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankFaqResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankFaqResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankFaq.push(Faq.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankFaqResponse {
    return {
      bankFaq: globalThis.Array.isArray(object?.bankFaq) ? object.bankFaq.map((e: any) => Faq.fromJSON(e)) : [],
    };
  },

  toJSON(message: BankFaqResponse): unknown {
    const obj: any = {};
    if (message.bankFaq?.length) {
      obj.bankFaq = message.bankFaq.map((e) => Faq.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankFaqResponse>, I>>(base?: I): BankFaqResponse {
    return BankFaqResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankFaqResponse>, I>>(object: I): BankFaqResponse {
    const message = createBaseBankFaqResponse();
    message.bankFaq = object.bankFaq?.map((e) => Faq.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFaq(): Faq {
  return {
    question: "",
    answer: "",
    htmlAnswer: "",
    bankFaqCategoryList: [],
    questionTag: "",
    faqId: undefined,
    minVersionNumber: 0,
    maxVersionNumber: 0,
    userName: undefined,
    userLocation: undefined,
    userProfileUrl: undefined,
    upvotes: undefined,
    downvotes: undefined,
  };
}

export const Faq: MessageFns<Faq> = {
  encode(message: Faq, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.question !== "") {
      writer.uint32(10).string(message.question);
    }
    if (message.answer !== "") {
      writer.uint32(18).string(message.answer);
    }
    if (message.htmlAnswer !== "") {
      writer.uint32(26).string(message.htmlAnswer);
    }
    for (const v of message.bankFaqCategoryList) {
      writer.uint32(34).string(v!);
    }
    if (message.questionTag !== "") {
      writer.uint32(42).string(message.questionTag);
    }
    if (message.faqId !== undefined) {
      writer.uint32(50).string(message.faqId);
    }
    if (message.minVersionNumber !== 0) {
      writer.uint32(56).int32(message.minVersionNumber);
    }
    if (message.maxVersionNumber !== 0) {
      writer.uint32(64).int32(message.maxVersionNumber);
    }
    if (message.userName !== undefined) {
      writer.uint32(74).string(message.userName);
    }
    if (message.userLocation !== undefined) {
      writer.uint32(82).string(message.userLocation);
    }
    if (message.userProfileUrl !== undefined) {
      writer.uint32(90).string(message.userProfileUrl);
    }
    if (message.upvotes !== undefined) {
      writer.uint32(96).int32(message.upvotes);
    }
    if (message.downvotes !== undefined) {
      writer.uint32(104).int32(message.downvotes);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Faq {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFaq();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.question = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.answer = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.htmlAnswer = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bankFaqCategoryList.push(reader.string());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.questionTag = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.faqId = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.minVersionNumber = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.maxVersionNumber = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.userName = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.userLocation = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.userProfileUrl = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.upvotes = reader.int32();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.downvotes = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Faq {
    return {
      question: isSet(object.question) ? globalThis.String(object.question) : "",
      answer: isSet(object.answer) ? globalThis.String(object.answer) : "",
      htmlAnswer: isSet(object.htmlAnswer) ? globalThis.String(object.htmlAnswer) : "",
      bankFaqCategoryList: globalThis.Array.isArray(object?.bankFaqCategoryList)
        ? object.bankFaqCategoryList.map((e: any) => globalThis.String(e))
        : [],
      questionTag: isSet(object.questionTag) ? globalThis.String(object.questionTag) : "",
      faqId: isSet(object.faqId) ? globalThis.String(object.faqId) : undefined,
      minVersionNumber: isSet(object.minVersionNumber) ? globalThis.Number(object.minVersionNumber) : 0,
      maxVersionNumber: isSet(object.maxVersionNumber) ? globalThis.Number(object.maxVersionNumber) : 0,
      userName: isSet(object.userName) ? globalThis.String(object.userName) : undefined,
      userLocation: isSet(object.userLocation) ? globalThis.String(object.userLocation) : undefined,
      userProfileUrl: isSet(object.userProfileUrl) ? globalThis.String(object.userProfileUrl) : undefined,
      upvotes: isSet(object.upvotes) ? globalThis.Number(object.upvotes) : undefined,
      downvotes: isSet(object.downvotes) ? globalThis.Number(object.downvotes) : undefined,
    };
  },

  toJSON(message: Faq): unknown {
    const obj: any = {};
    if (message.question !== "") {
      obj.question = message.question;
    }
    if (message.answer !== "") {
      obj.answer = message.answer;
    }
    if (message.htmlAnswer !== "") {
      obj.htmlAnswer = message.htmlAnswer;
    }
    if (message.bankFaqCategoryList?.length) {
      obj.bankFaqCategoryList = message.bankFaqCategoryList;
    }
    if (message.questionTag !== "") {
      obj.questionTag = message.questionTag;
    }
    if (message.faqId !== undefined) {
      obj.faqId = message.faqId;
    }
    if (message.minVersionNumber !== 0) {
      obj.minVersionNumber = Math.round(message.minVersionNumber);
    }
    if (message.maxVersionNumber !== 0) {
      obj.maxVersionNumber = Math.round(message.maxVersionNumber);
    }
    if (message.userName !== undefined) {
      obj.userName = message.userName;
    }
    if (message.userLocation !== undefined) {
      obj.userLocation = message.userLocation;
    }
    if (message.userProfileUrl !== undefined) {
      obj.userProfileUrl = message.userProfileUrl;
    }
    if (message.upvotes !== undefined) {
      obj.upvotes = Math.round(message.upvotes);
    }
    if (message.downvotes !== undefined) {
      obj.downvotes = Math.round(message.downvotes);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Faq>, I>>(base?: I): Faq {
    return Faq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Faq>, I>>(object: I): Faq {
    const message = createBaseFaq();
    message.question = object.question ?? "";
    message.answer = object.answer ?? "";
    message.htmlAnswer = object.htmlAnswer ?? "";
    message.bankFaqCategoryList = object.bankFaqCategoryList?.map((e) => e) || [];
    message.questionTag = object.questionTag ?? "";
    message.faqId = object.faqId ?? undefined;
    message.minVersionNumber = object.minVersionNumber ?? 0;
    message.maxVersionNumber = object.maxVersionNumber ?? 0;
    message.userName = object.userName ?? undefined;
    message.userLocation = object.userLocation ?? undefined;
    message.userProfileUrl = object.userProfileUrl ?? undefined;
    message.upvotes = object.upvotes ?? undefined;
    message.downvotes = object.downvotes ?? undefined;
    return message;
  },
};

function createBaseSupportFaqResponse(): SupportFaqResponse {
  return { bankFaq: [] };
}

export const SupportFaqResponse: MessageFns<SupportFaqResponse> = {
  encode(message: SupportFaqResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bankFaq) {
      Faq.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SupportFaqResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSupportFaqResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankFaq.push(Faq.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SupportFaqResponse {
    return {
      bankFaq: globalThis.Array.isArray(object?.bankFaq) ? object.bankFaq.map((e: any) => Faq.fromJSON(e)) : [],
    };
  },

  toJSON(message: SupportFaqResponse): unknown {
    const obj: any = {};
    if (message.bankFaq?.length) {
      obj.bankFaq = message.bankFaq.map((e) => Faq.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SupportFaqResponse>, I>>(base?: I): SupportFaqResponse {
    return SupportFaqResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SupportFaqResponse>, I>>(object: I): SupportFaqResponse {
    const message = createBaseSupportFaqResponse();
    message.bankFaq = object.bankFaq?.map((e) => Faq.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSupportFaqV2(): SupportFaqV2 {
  return { faq: undefined, supportFaqIconUrl: "" };
}

export const SupportFaqV2: MessageFns<SupportFaqV2> = {
  encode(message: SupportFaqV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.faq !== undefined) {
      Faq.encode(message.faq, writer.uint32(10).fork()).join();
    }
    if (message.supportFaqIconUrl !== "") {
      writer.uint32(26).string(message.supportFaqIconUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SupportFaqV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSupportFaqV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.faq = Faq.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.supportFaqIconUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SupportFaqV2 {
    return {
      faq: isSet(object.faq) ? Faq.fromJSON(object.faq) : undefined,
      supportFaqIconUrl: isSet(object.supportFaqIconUrl) ? globalThis.String(object.supportFaqIconUrl) : "",
    };
  },

  toJSON(message: SupportFaqV2): unknown {
    const obj: any = {};
    if (message.faq !== undefined) {
      obj.faq = Faq.toJSON(message.faq);
    }
    if (message.supportFaqIconUrl !== "") {
      obj.supportFaqIconUrl = message.supportFaqIconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SupportFaqV2>, I>>(base?: I): SupportFaqV2 {
    return SupportFaqV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SupportFaqV2>, I>>(object: I): SupportFaqV2 {
    const message = createBaseSupportFaqV2();
    message.faq = (object.faq !== undefined && object.faq !== null) ? Faq.fromPartial(object.faq) : undefined;
    message.supportFaqIconUrl = object.supportFaqIconUrl ?? "";
    return message;
  },
};

function createBaseSupportFaqResponseV2(): SupportFaqResponseV2 {
  return { categoryId: "", categoryName: "", categoryIconUrl: "", faqs: [], categoryDescription: "" };
}

export const SupportFaqResponseV2: MessageFns<SupportFaqResponseV2> = {
  encode(message: SupportFaqResponseV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryId !== "") {
      writer.uint32(10).string(message.categoryId);
    }
    if (message.categoryName !== "") {
      writer.uint32(18).string(message.categoryName);
    }
    if (message.categoryIconUrl !== "") {
      writer.uint32(26).string(message.categoryIconUrl);
    }
    for (const v of message.faqs) {
      SupportFaqV2.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.categoryDescription !== "") {
      writer.uint32(42).string(message.categoryDescription);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SupportFaqResponseV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSupportFaqResponseV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.categoryName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.categoryIconUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.faqs.push(SupportFaqV2.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.categoryDescription = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SupportFaqResponseV2 {
    return {
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      categoryName: isSet(object.categoryName) ? globalThis.String(object.categoryName) : "",
      categoryIconUrl: isSet(object.categoryIconUrl) ? globalThis.String(object.categoryIconUrl) : "",
      faqs: globalThis.Array.isArray(object?.faqs) ? object.faqs.map((e: any) => SupportFaqV2.fromJSON(e)) : [],
      categoryDescription: isSet(object.categoryDescription) ? globalThis.String(object.categoryDescription) : "",
    };
  },

  toJSON(message: SupportFaqResponseV2): unknown {
    const obj: any = {};
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.categoryName !== "") {
      obj.categoryName = message.categoryName;
    }
    if (message.categoryIconUrl !== "") {
      obj.categoryIconUrl = message.categoryIconUrl;
    }
    if (message.faqs?.length) {
      obj.faqs = message.faqs.map((e) => SupportFaqV2.toJSON(e));
    }
    if (message.categoryDescription !== "") {
      obj.categoryDescription = message.categoryDescription;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SupportFaqResponseV2>, I>>(base?: I): SupportFaqResponseV2 {
    return SupportFaqResponseV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SupportFaqResponseV2>, I>>(object: I): SupportFaqResponseV2 {
    const message = createBaseSupportFaqResponseV2();
    message.categoryId = object.categoryId ?? "";
    message.categoryName = object.categoryName ?? "";
    message.categoryIconUrl = object.categoryIconUrl ?? "";
    message.faqs = object.faqs?.map((e) => SupportFaqV2.fromPartial(e)) || [];
    message.categoryDescription = object.categoryDescription ?? "";
    return message;
  },
};

function createBaseSupportFaqCategoryResponseV2(): SupportFaqCategoryResponseV2 {
  return { categorySupportFaq: [], topCategoryId: "" };
}

export const SupportFaqCategoryResponseV2: MessageFns<SupportFaqCategoryResponseV2> = {
  encode(message: SupportFaqCategoryResponseV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.categorySupportFaq) {
      SupportFaqResponseV2.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.topCategoryId !== "") {
      writer.uint32(18).string(message.topCategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SupportFaqCategoryResponseV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSupportFaqCategoryResponseV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categorySupportFaq.push(SupportFaqResponseV2.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.topCategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SupportFaqCategoryResponseV2 {
    return {
      categorySupportFaq: globalThis.Array.isArray(object?.categorySupportFaq)
        ? object.categorySupportFaq.map((e: any) => SupportFaqResponseV2.fromJSON(e))
        : [],
      topCategoryId: isSet(object.topCategoryId) ? globalThis.String(object.topCategoryId) : "",
    };
  },

  toJSON(message: SupportFaqCategoryResponseV2): unknown {
    const obj: any = {};
    if (message.categorySupportFaq?.length) {
      obj.categorySupportFaq = message.categorySupportFaq.map((e) => SupportFaqResponseV2.toJSON(e));
    }
    if (message.topCategoryId !== "") {
      obj.topCategoryId = message.topCategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SupportFaqCategoryResponseV2>, I>>(base?: I): SupportFaqCategoryResponseV2 {
    return SupportFaqCategoryResponseV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SupportFaqCategoryResponseV2>, I>>(object: I): SupportFaqCategoryResponseV2 {
    const message = createBaseSupportFaqCategoryResponseV2();
    message.categorySupportFaq = object.categorySupportFaq?.map((e) => SupportFaqResponseV2.fromPartial(e)) || [];
    message.topCategoryId = object.topCategoryId ?? "";
    return message;
  },
};

function createBaseAskQuestionRequest(): AskQuestionRequest {
  return { question: "" };
}

export const AskQuestionRequest: MessageFns<AskQuestionRequest> = {
  encode(message: AskQuestionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.question !== "") {
      writer.uint32(10).string(message.question);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AskQuestionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAskQuestionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.question = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AskQuestionRequest {
    return { question: isSet(object.question) ? globalThis.String(object.question) : "" };
  },

  toJSON(message: AskQuestionRequest): unknown {
    const obj: any = {};
    if (message.question !== "") {
      obj.question = message.question;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AskQuestionRequest>, I>>(base?: I): AskQuestionRequest {
    return AskQuestionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AskQuestionRequest>, I>>(object: I): AskQuestionRequest {
    const message = createBaseAskQuestionRequest();
    message.question = object.question ?? "";
    return message;
  },
};

function createBaseGetFaqsRequest(): GetFaqsRequest {
  return { businessUnit: 0, namespace: "", identifier: "" };
}

export const GetFaqsRequest: MessageFns<GetFaqsRequest> = {
  encode(message: GetFaqsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.businessUnit !== 0) {
      writer.uint32(8).int32(message.businessUnit);
    }
    if (message.namespace !== "") {
      writer.uint32(18).string(message.namespace);
    }
    if (message.identifier !== "") {
      writer.uint32(26).string(message.identifier);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetFaqsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetFaqsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.businessUnit = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.namespace = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.identifier = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetFaqsRequest {
    return {
      businessUnit: isSet(object.businessUnit) ? businessUnitFromJSON(object.businessUnit) : 0,
      namespace: isSet(object.namespace) ? globalThis.String(object.namespace) : "",
      identifier: isSet(object.identifier) ? globalThis.String(object.identifier) : "",
    };
  },

  toJSON(message: GetFaqsRequest): unknown {
    const obj: any = {};
    if (message.businessUnit !== 0) {
      obj.businessUnit = businessUnitToJSON(message.businessUnit);
    }
    if (message.namespace !== "") {
      obj.namespace = message.namespace;
    }
    if (message.identifier !== "") {
      obj.identifier = message.identifier;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetFaqsRequest>, I>>(base?: I): GetFaqsRequest {
    return GetFaqsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFaqsRequest>, I>>(object: I): GetFaqsRequest {
    const message = createBaseGetFaqsRequest();
    message.businessUnit = object.businessUnit ?? 0;
    message.namespace = object.namespace ?? "";
    message.identifier = object.identifier ?? "";
    return message;
  },
};

function createBaseGetFaqsResponse(): GetFaqsResponse {
  return { faqs: [] };
}

export const GetFaqsResponse: MessageFns<GetFaqsResponse> = {
  encode(message: GetFaqsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.faqs) {
      Faq.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetFaqsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetFaqsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.faqs.push(Faq.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetFaqsResponse {
    return { faqs: globalThis.Array.isArray(object?.faqs) ? object.faqs.map((e: any) => Faq.fromJSON(e)) : [] };
  },

  toJSON(message: GetFaqsResponse): unknown {
    const obj: any = {};
    if (message.faqs?.length) {
      obj.faqs = message.faqs.map((e) => Faq.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetFaqsResponse>, I>>(base?: I): GetFaqsResponse {
    return GetFaqsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFaqsResponse>, I>>(object: I): GetFaqsResponse {
    const message = createBaseGetFaqsResponse();
    message.faqs = object.faqs?.map((e) => Faq.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
