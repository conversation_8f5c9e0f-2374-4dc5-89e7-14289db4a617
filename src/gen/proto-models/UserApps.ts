// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: UserApps.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { DeviceType, deviceTypeFromJSON, deviceTypeToJSON } from "./Device";

export const protobufPackage = "com.stablemoney.api.identity";

export interface InstalledAppItem {
  appName: string;
  packageName: string;
  versionName: string;
  isSystemApp: boolean;
  deviceType: DeviceType;
}

export interface UserInstalledAppsRequest {
  installedApps: InstalledAppItem[];
}

export interface UserInstalledAppsResponse {
}

export interface AppItem {
  appName: string;
  packageName: string;
}

export interface AppsListResponse {
  appItemsList: AppItem[];
}

function createBaseInstalledAppItem(): InstalledAppItem {
  return { appName: "", packageName: "", versionName: "", isSystemApp: false, deviceType: 0 };
}

export const InstalledAppItem: MessageFns<InstalledAppItem> = {
  encode(message: InstalledAppItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.appName !== "") {
      writer.uint32(10).string(message.appName);
    }
    if (message.packageName !== "") {
      writer.uint32(18).string(message.packageName);
    }
    if (message.versionName !== "") {
      writer.uint32(26).string(message.versionName);
    }
    if (message.isSystemApp !== false) {
      writer.uint32(32).bool(message.isSystemApp);
    }
    if (message.deviceType !== 0) {
      writer.uint32(40).int32(message.deviceType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InstalledAppItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInstalledAppItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.appName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.packageName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.versionName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isSystemApp = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.deviceType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InstalledAppItem {
    return {
      appName: isSet(object.appName) ? globalThis.String(object.appName) : "",
      packageName: isSet(object.packageName) ? globalThis.String(object.packageName) : "",
      versionName: isSet(object.versionName) ? globalThis.String(object.versionName) : "",
      isSystemApp: isSet(object.isSystemApp) ? globalThis.Boolean(object.isSystemApp) : false,
      deviceType: isSet(object.deviceType) ? deviceTypeFromJSON(object.deviceType) : 0,
    };
  },

  toJSON(message: InstalledAppItem): unknown {
    const obj: any = {};
    if (message.appName !== "") {
      obj.appName = message.appName;
    }
    if (message.packageName !== "") {
      obj.packageName = message.packageName;
    }
    if (message.versionName !== "") {
      obj.versionName = message.versionName;
    }
    if (message.isSystemApp !== false) {
      obj.isSystemApp = message.isSystemApp;
    }
    if (message.deviceType !== 0) {
      obj.deviceType = deviceTypeToJSON(message.deviceType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InstalledAppItem>, I>>(base?: I): InstalledAppItem {
    return InstalledAppItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InstalledAppItem>, I>>(object: I): InstalledAppItem {
    const message = createBaseInstalledAppItem();
    message.appName = object.appName ?? "";
    message.packageName = object.packageName ?? "";
    message.versionName = object.versionName ?? "";
    message.isSystemApp = object.isSystemApp ?? false;
    message.deviceType = object.deviceType ?? 0;
    return message;
  },
};

function createBaseUserInstalledAppsRequest(): UserInstalledAppsRequest {
  return { installedApps: [] };
}

export const UserInstalledAppsRequest: MessageFns<UserInstalledAppsRequest> = {
  encode(message: UserInstalledAppsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.installedApps) {
      InstalledAppItem.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserInstalledAppsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserInstalledAppsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.installedApps.push(InstalledAppItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserInstalledAppsRequest {
    return {
      installedApps: globalThis.Array.isArray(object?.installedApps)
        ? object.installedApps.map((e: any) => InstalledAppItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: UserInstalledAppsRequest): unknown {
    const obj: any = {};
    if (message.installedApps?.length) {
      obj.installedApps = message.installedApps.map((e) => InstalledAppItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserInstalledAppsRequest>, I>>(base?: I): UserInstalledAppsRequest {
    return UserInstalledAppsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInstalledAppsRequest>, I>>(object: I): UserInstalledAppsRequest {
    const message = createBaseUserInstalledAppsRequest();
    message.installedApps = object.installedApps?.map((e) => InstalledAppItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserInstalledAppsResponse(): UserInstalledAppsResponse {
  return {};
}

export const UserInstalledAppsResponse: MessageFns<UserInstalledAppsResponse> = {
  encode(_: UserInstalledAppsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserInstalledAppsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserInstalledAppsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UserInstalledAppsResponse {
    return {};
  },

  toJSON(_: UserInstalledAppsResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UserInstalledAppsResponse>, I>>(base?: I): UserInstalledAppsResponse {
    return UserInstalledAppsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInstalledAppsResponse>, I>>(_: I): UserInstalledAppsResponse {
    const message = createBaseUserInstalledAppsResponse();
    return message;
  },
};

function createBaseAppItem(): AppItem {
  return { appName: "", packageName: "" };
}

export const AppItem: MessageFns<AppItem> = {
  encode(message: AppItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.appName !== "") {
      writer.uint32(10).string(message.appName);
    }
    if (message.packageName !== "") {
      writer.uint32(18).string(message.packageName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AppItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.appName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.packageName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppItem {
    return {
      appName: isSet(object.appName) ? globalThis.String(object.appName) : "",
      packageName: isSet(object.packageName) ? globalThis.String(object.packageName) : "",
    };
  },

  toJSON(message: AppItem): unknown {
    const obj: any = {};
    if (message.appName !== "") {
      obj.appName = message.appName;
    }
    if (message.packageName !== "") {
      obj.packageName = message.packageName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppItem>, I>>(base?: I): AppItem {
    return AppItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppItem>, I>>(object: I): AppItem {
    const message = createBaseAppItem();
    message.appName = object.appName ?? "";
    message.packageName = object.packageName ?? "";
    return message;
  },
};

function createBaseAppsListResponse(): AppsListResponse {
  return { appItemsList: [] };
}

export const AppsListResponse: MessageFns<AppsListResponse> = {
  encode(message: AppsListResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.appItemsList) {
      AppItem.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AppsListResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppsListResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.appItemsList.push(AppItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppsListResponse {
    return {
      appItemsList: globalThis.Array.isArray(object?.appItemsList)
        ? object.appItemsList.map((e: any) => AppItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AppsListResponse): unknown {
    const obj: any = {};
    if (message.appItemsList?.length) {
      obj.appItemsList = message.appItemsList.map((e) => AppItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppsListResponse>, I>>(base?: I): AppsListResponse {
    return AppsListResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppsListResponse>, I>>(object: I): AppsListResponse {
    const message = createBaseAppsListResponse();
    message.appItemsList = object.appItemsList?.map((e) => AppItem.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
