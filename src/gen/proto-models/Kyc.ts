// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Kyc.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum KycType {
  KYC_TYPE_UNKNOWN = 0,
  POI = 1,
  KYC = 2,
  POA = 3,
  SELFIE = 4,
  ESIGN = 5,
  BANK_ACCOUNT = 6,
  DEMAT_ACCOUNT = 7,
  NOMINEE = 8,
  WET_SIGNATURE = 9,
  USER_PROFILE = 10,
  PAN_KYC = 11,
  NAME = 12,
  QUESTIONNAIRE = 13,
  EMAIL = 14,
  WHITELIST_CHECK = 15,
  UNRECOGNIZED = -1,
}

export function kycTypeFromJSON(object: any): KycType {
  switch (object) {
    case 0:
    case "KYC_TYPE_UNKNOWN":
      return KycType.KYC_TYPE_UNKNOWN;
    case 1:
    case "POI":
      return KycType.POI;
    case 2:
    case "KYC":
      return KycType.KYC;
    case 3:
    case "POA":
      return KycType.POA;
    case 4:
    case "SELFIE":
      return KycType.SELFIE;
    case 5:
    case "ESIGN":
      return KycType.ESIGN;
    case 6:
    case "BANK_ACCOUNT":
      return KycType.BANK_ACCOUNT;
    case 7:
    case "DEMAT_ACCOUNT":
      return KycType.DEMAT_ACCOUNT;
    case 8:
    case "NOMINEE":
      return KycType.NOMINEE;
    case 9:
    case "WET_SIGNATURE":
      return KycType.WET_SIGNATURE;
    case 10:
    case "USER_PROFILE":
      return KycType.USER_PROFILE;
    case 11:
    case "PAN_KYC":
      return KycType.PAN_KYC;
    case 12:
    case "NAME":
      return KycType.NAME;
    case 13:
    case "QUESTIONNAIRE":
      return KycType.QUESTIONNAIRE;
    case 14:
    case "EMAIL":
      return KycType.EMAIL;
    case 15:
    case "WHITELIST_CHECK":
      return KycType.WHITELIST_CHECK;
    case -1:
    case "UNRECOGNIZED":
    default:
      return KycType.UNRECOGNIZED;
  }
}

export function kycTypeToJSON(object: KycType): string {
  switch (object) {
    case KycType.KYC_TYPE_UNKNOWN:
      return "KYC_TYPE_UNKNOWN";
    case KycType.POI:
      return "POI";
    case KycType.KYC:
      return "KYC";
    case KycType.POA:
      return "POA";
    case KycType.SELFIE:
      return "SELFIE";
    case KycType.ESIGN:
      return "ESIGN";
    case KycType.BANK_ACCOUNT:
      return "BANK_ACCOUNT";
    case KycType.DEMAT_ACCOUNT:
      return "DEMAT_ACCOUNT";
    case KycType.NOMINEE:
      return "NOMINEE";
    case KycType.WET_SIGNATURE:
      return "WET_SIGNATURE";
    case KycType.USER_PROFILE:
      return "USER_PROFILE";
    case KycType.PAN_KYC:
      return "PAN_KYC";
    case KycType.NAME:
      return "NAME";
    case KycType.QUESTIONNAIRE:
      return "QUESTIONNAIRE";
    case KycType.EMAIL:
      return "EMAIL";
    case KycType.WHITELIST_CHECK:
      return "WHITELIST_CHECK";
    case KycType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ProofType {
  PROOF_TYPE_UNKNOWN = 0,
  PAN = 1,
  AADHAR = 2,
  PASSPORT = 3,
  DRIVING_LICENSE = 4,
  VOTER_ID = 5,
  GOVT_ID = 6,
  REGULATORY_ID = 7,
  PSU_ID = 8,
  BANK_ID = 9,
  PUBLIC_FINANCIAL_INSTITUTION_ID = 10,
  COLLEGE_ID = 11,
  PROFESSIONAL_BODY_ID = 12,
  CREDIT_CARD = 13,
  OTHER_ID = 16,
  BANK_PASSBOOK = 17,
  BANK_ACCOUNT_STATEMENT = 18,
  RATION_CARD = 19,
  LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE = 20,
  LAND_LINE_TELEPHONE_BILL = 21,
  ELECTRICITY_BILL = 22,
  GAS_BILL = 23,
  FLAT_MAINTENANCE_BILL = 24,
  INSURANCE_COPY = 25,
  SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE = 26,
  POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS = 27,
  POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS = 28,
  POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT = 29,
  POA_ISSUED_BY_PARLIAMENT = 30,
  POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY = 31,
  POA_ISSUED_BY_NOTARY_PUBLIC = 32,
  POA_ISSUED_BY_GAZETTED_OFFICER = 33,
  ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT = 34,
  ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES = 35,
  ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS = 36,
  ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS = 37,
  ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS = 38,
  ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES = 39,
  ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL = 40,
  UNRECOGNIZED = -1,
}

export function proofTypeFromJSON(object: any): ProofType {
  switch (object) {
    case 0:
    case "PROOF_TYPE_UNKNOWN":
      return ProofType.PROOF_TYPE_UNKNOWN;
    case 1:
    case "PAN":
      return ProofType.PAN;
    case 2:
    case "AADHAR":
      return ProofType.AADHAR;
    case 3:
    case "PASSPORT":
      return ProofType.PASSPORT;
    case 4:
    case "DRIVING_LICENSE":
      return ProofType.DRIVING_LICENSE;
    case 5:
    case "VOTER_ID":
      return ProofType.VOTER_ID;
    case 6:
    case "GOVT_ID":
      return ProofType.GOVT_ID;
    case 7:
    case "REGULATORY_ID":
      return ProofType.REGULATORY_ID;
    case 8:
    case "PSU_ID":
      return ProofType.PSU_ID;
    case 9:
    case "BANK_ID":
      return ProofType.BANK_ID;
    case 10:
    case "PUBLIC_FINANCIAL_INSTITUTION_ID":
      return ProofType.PUBLIC_FINANCIAL_INSTITUTION_ID;
    case 11:
    case "COLLEGE_ID":
      return ProofType.COLLEGE_ID;
    case 12:
    case "PROFESSIONAL_BODY_ID":
      return ProofType.PROFESSIONAL_BODY_ID;
    case 13:
    case "CREDIT_CARD":
      return ProofType.CREDIT_CARD;
    case 16:
    case "OTHER_ID":
      return ProofType.OTHER_ID;
    case 17:
    case "BANK_PASSBOOK":
      return ProofType.BANK_PASSBOOK;
    case 18:
    case "BANK_ACCOUNT_STATEMENT":
      return ProofType.BANK_ACCOUNT_STATEMENT;
    case 19:
    case "RATION_CARD":
      return ProofType.RATION_CARD;
    case 20:
    case "LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE":
      return ProofType.LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE;
    case 21:
    case "LAND_LINE_TELEPHONE_BILL":
      return ProofType.LAND_LINE_TELEPHONE_BILL;
    case 22:
    case "ELECTRICITY_BILL":
      return ProofType.ELECTRICITY_BILL;
    case 23:
    case "GAS_BILL":
      return ProofType.GAS_BILL;
    case 24:
    case "FLAT_MAINTENANCE_BILL":
      return ProofType.FLAT_MAINTENANCE_BILL;
    case 25:
    case "INSURANCE_COPY":
      return ProofType.INSURANCE_COPY;
    case 26:
    case "SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE":
      return ProofType.SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE;
    case 27:
    case "POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS":
      return ProofType.POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS;
    case 28:
    case "POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS":
      return ProofType
        .POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS;
    case 29:
    case "POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT":
      return ProofType.POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT;
    case 30:
    case "POA_ISSUED_BY_PARLIAMENT":
      return ProofType.POA_ISSUED_BY_PARLIAMENT;
    case 31:
    case "POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY":
      return ProofType.POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY;
    case 32:
    case "POA_ISSUED_BY_NOTARY_PUBLIC":
      return ProofType.POA_ISSUED_BY_NOTARY_PUBLIC;
    case 33:
    case "POA_ISSUED_BY_GAZETTED_OFFICER":
      return ProofType.POA_ISSUED_BY_GAZETTED_OFFICER;
    case 34:
    case "ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT":
      return ProofType.ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT;
    case 35:
    case "ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES":
      return ProofType.ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES;
    case 36:
    case "ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS":
      return ProofType.ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS;
    case 37:
    case "ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS":
      return ProofType.ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS;
    case 38:
    case "ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS":
      return ProofType.ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS;
    case 39:
    case "ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES":
      return ProofType.ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES;
    case 40:
    case "ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL":
      return ProofType.ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ProofType.UNRECOGNIZED;
  }
}

export function proofTypeToJSON(object: ProofType): string {
  switch (object) {
    case ProofType.PROOF_TYPE_UNKNOWN:
      return "PROOF_TYPE_UNKNOWN";
    case ProofType.PAN:
      return "PAN";
    case ProofType.AADHAR:
      return "AADHAR";
    case ProofType.PASSPORT:
      return "PASSPORT";
    case ProofType.DRIVING_LICENSE:
      return "DRIVING_LICENSE";
    case ProofType.VOTER_ID:
      return "VOTER_ID";
    case ProofType.GOVT_ID:
      return "GOVT_ID";
    case ProofType.REGULATORY_ID:
      return "REGULATORY_ID";
    case ProofType.PSU_ID:
      return "PSU_ID";
    case ProofType.BANK_ID:
      return "BANK_ID";
    case ProofType.PUBLIC_FINANCIAL_INSTITUTION_ID:
      return "PUBLIC_FINANCIAL_INSTITUTION_ID";
    case ProofType.COLLEGE_ID:
      return "COLLEGE_ID";
    case ProofType.PROFESSIONAL_BODY_ID:
      return "PROFESSIONAL_BODY_ID";
    case ProofType.CREDIT_CARD:
      return "CREDIT_CARD";
    case ProofType.OTHER_ID:
      return "OTHER_ID";
    case ProofType.BANK_PASSBOOK:
      return "BANK_PASSBOOK";
    case ProofType.BANK_ACCOUNT_STATEMENT:
      return "BANK_ACCOUNT_STATEMENT";
    case ProofType.RATION_CARD:
      return "RATION_CARD";
    case ProofType.LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE:
      return "LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE";
    case ProofType.LAND_LINE_TELEPHONE_BILL:
      return "LAND_LINE_TELEPHONE_BILL";
    case ProofType.ELECTRICITY_BILL:
      return "ELECTRICITY_BILL";
    case ProofType.GAS_BILL:
      return "GAS_BILL";
    case ProofType.FLAT_MAINTENANCE_BILL:
      return "FLAT_MAINTENANCE_BILL";
    case ProofType.INSURANCE_COPY:
      return "INSURANCE_COPY";
    case ProofType.SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE:
      return "SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE";
    case ProofType.POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS:
      return "POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS";
    case ProofType
      .POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS:
      return "POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS";
    case ProofType.POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT:
      return "POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT";
    case ProofType.POA_ISSUED_BY_PARLIAMENT:
      return "POA_ISSUED_BY_PARLIAMENT";
    case ProofType.POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY:
      return "POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY";
    case ProofType.POA_ISSUED_BY_NOTARY_PUBLIC:
      return "POA_ISSUED_BY_NOTARY_PUBLIC";
    case ProofType.POA_ISSUED_BY_GAZETTED_OFFICER:
      return "POA_ISSUED_BY_GAZETTED_OFFICER";
    case ProofType.ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT:
      return "ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT";
    case ProofType.ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES:
      return "ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES";
    case ProofType.ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS:
      return "ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS";
    case ProofType.ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS:
      return "ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS";
    case ProofType.ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS:
      return "ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS";
    case ProofType.ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES:
      return "ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES";
    case ProofType.ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL:
      return "ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL";
    case ProofType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum KycProvider {
  KYC_PROVIDER_UNKNOWN = 0,
  NONE = 1,
  CDSL = 2,
  DIGIO = 3,
  HYPERVERGE = 4,
  TARRAKKI = 5,
  UNRECOGNIZED = -1,
}

export function kycProviderFromJSON(object: any): KycProvider {
  switch (object) {
    case 0:
    case "KYC_PROVIDER_UNKNOWN":
      return KycProvider.KYC_PROVIDER_UNKNOWN;
    case 1:
    case "NONE":
      return KycProvider.NONE;
    case 2:
    case "CDSL":
      return KycProvider.CDSL;
    case 3:
    case "DIGIO":
      return KycProvider.DIGIO;
    case 4:
    case "HYPERVERGE":
      return KycProvider.HYPERVERGE;
    case 5:
    case "TARRAKKI":
      return KycProvider.TARRAKKI;
    case -1:
    case "UNRECOGNIZED":
    default:
      return KycProvider.UNRECOGNIZED;
  }
}

export function kycProviderToJSON(object: KycProvider): string {
  switch (object) {
    case KycProvider.KYC_PROVIDER_UNKNOWN:
      return "KYC_PROVIDER_UNKNOWN";
    case KycProvider.NONE:
      return "NONE";
    case KycProvider.CDSL:
      return "CDSL";
    case KycProvider.DIGIO:
      return "DIGIO";
    case KycProvider.HYPERVERGE:
      return "HYPERVERGE";
    case KycProvider.TARRAKKI:
      return "TARRAKKI";
    case KycProvider.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum OnBoardingStatus {
  ONBOARDING_STATUS_UNKNOWN = 0,
  PENDING = 1,
  COMPLETE = 2,
  REJECTED = 3,
  SKIPPED = 4,
  UNRECOGNIZED = -1,
}

export function onBoardingStatusFromJSON(object: any): OnBoardingStatus {
  switch (object) {
    case 0:
    case "ONBOARDING_STATUS_UNKNOWN":
      return OnBoardingStatus.ONBOARDING_STATUS_UNKNOWN;
    case 1:
    case "PENDING":
      return OnBoardingStatus.PENDING;
    case 2:
    case "COMPLETE":
      return OnBoardingStatus.COMPLETE;
    case 3:
    case "REJECTED":
      return OnBoardingStatus.REJECTED;
    case 4:
    case "SKIPPED":
      return OnBoardingStatus.SKIPPED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return OnBoardingStatus.UNRECOGNIZED;
  }
}

export function onBoardingStatusToJSON(object: OnBoardingStatus): string {
  switch (object) {
    case OnBoardingStatus.ONBOARDING_STATUS_UNKNOWN:
      return "ONBOARDING_STATUS_UNKNOWN";
    case OnBoardingStatus.PENDING:
      return "PENDING";
    case OnBoardingStatus.COMPLETE:
      return "COMPLETE";
    case OnBoardingStatus.REJECTED:
      return "REJECTED";
    case OnBoardingStatus.SKIPPED:
      return "SKIPPED";
    case OnBoardingStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum EsignStep {
  ESIGN_STEP_UNKNOWN = 0,
  ESIGN_STEP_GENERATE_TOKEN = 1,
  ESIGN_STEP_STATUS = 2,
  UNRECOGNIZED = -1,
}

export function esignStepFromJSON(object: any): EsignStep {
  switch (object) {
    case 0:
    case "ESIGN_STEP_UNKNOWN":
      return EsignStep.ESIGN_STEP_UNKNOWN;
    case 1:
    case "ESIGN_STEP_GENERATE_TOKEN":
      return EsignStep.ESIGN_STEP_GENERATE_TOKEN;
    case 2:
    case "ESIGN_STEP_STATUS":
      return EsignStep.ESIGN_STEP_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return EsignStep.UNRECOGNIZED;
  }
}

export function esignStepToJSON(object: EsignStep): string {
  switch (object) {
    case EsignStep.ESIGN_STEP_UNKNOWN:
      return "ESIGN_STEP_UNKNOWN";
    case EsignStep.ESIGN_STEP_GENERATE_TOKEN:
      return "ESIGN_STEP_GENERATE_TOKEN";
    case EsignStep.ESIGN_STEP_STATUS:
      return "ESIGN_STEP_STATUS";
    case EsignStep.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum PanKycStep {
  UNKNOWN_PAN_STEP = 0,
  NAME_FETCH = 1,
  PAN_STATUS = 2,
  UNRECOGNIZED = -1,
}

export function panKycStepFromJSON(object: any): PanKycStep {
  switch (object) {
    case 0:
    case "UNKNOWN_PAN_STEP":
      return PanKycStep.UNKNOWN_PAN_STEP;
    case 1:
    case "NAME_FETCH":
      return PanKycStep.NAME_FETCH;
    case 2:
    case "PAN_STATUS":
      return PanKycStep.PAN_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PanKycStep.UNRECOGNIZED;
  }
}

export function panKycStepToJSON(object: PanKycStep): string {
  switch (object) {
    case PanKycStep.UNKNOWN_PAN_STEP:
      return "UNKNOWN_PAN_STEP";
    case PanKycStep.NAME_FETCH:
      return "NAME_FETCH";
    case PanKycStep.PAN_STATUS:
      return "PAN_STATUS";
    case PanKycStep.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum KycStep {
  UNKNOWN_STEP = 0,
  GENERATE_TOKEN = 1,
  GET_STATUS = 2,
  UNRECOGNIZED = -1,
}

export function kycStepFromJSON(object: any): KycStep {
  switch (object) {
    case 0:
    case "UNKNOWN_STEP":
      return KycStep.UNKNOWN_STEP;
    case 1:
    case "GENERATE_TOKEN":
      return KycStep.GENERATE_TOKEN;
    case 2:
    case "GET_STATUS":
      return KycStep.GET_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return KycStep.UNRECOGNIZED;
  }
}

export function kycStepToJSON(object: KycStep): string {
  switch (object) {
    case KycStep.UNKNOWN_STEP:
      return "UNKNOWN_STEP";
    case KycStep.GENERATE_TOKEN:
      return "GENERATE_TOKEN";
    case KycStep.GET_STATUS:
      return "GET_STATUS";
    case KycStep.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum SelfieKycStep {
  UNKNOWN_SELFIE_STEP = 0,
  START_SELFIE_STEP = 1,
  SET_STATUS = 2,
  UNRECOGNIZED = -1,
}

export function selfieKycStepFromJSON(object: any): SelfieKycStep {
  switch (object) {
    case 0:
    case "UNKNOWN_SELFIE_STEP":
      return SelfieKycStep.UNKNOWN_SELFIE_STEP;
    case 1:
    case "START_SELFIE_STEP":
      return SelfieKycStep.START_SELFIE_STEP;
    case 2:
    case "SET_STATUS":
      return SelfieKycStep.SET_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SelfieKycStep.UNRECOGNIZED;
  }
}

export function selfieKycStepToJSON(object: SelfieKycStep): string {
  switch (object) {
    case SelfieKycStep.UNKNOWN_SELFIE_STEP:
      return "UNKNOWN_SELFIE_STEP";
    case SelfieKycStep.START_SELFIE_STEP:
      return "START_SELFIE_STEP";
    case SelfieKycStep.SET_STATUS:
      return "SET_STATUS";
    case SelfieKycStep.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum RaaDuration {
  UNKNOWN_RAA_DURATION = 0,
  RAA_60_DAYS = 1,
  RAA_90_DAYS = 2,
  UNRECOGNIZED = -1,
}

export function raaDurationFromJSON(object: any): RaaDuration {
  switch (object) {
    case 0:
    case "UNKNOWN_RAA_DURATION":
      return RaaDuration.UNKNOWN_RAA_DURATION;
    case 1:
    case "RAA_60_DAYS":
      return RaaDuration.RAA_60_DAYS;
    case 2:
    case "RAA_90_DAYS":
      return RaaDuration.RAA_90_DAYS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RaaDuration.UNRECOGNIZED;
  }
}

export function raaDurationToJSON(object: RaaDuration): string {
  switch (object) {
    case RaaDuration.UNKNOWN_RAA_DURATION:
      return "UNKNOWN_RAA_DURATION";
    case RaaDuration.RAA_60_DAYS:
      return "RAA_60_DAYS";
    case RaaDuration.RAA_90_DAYS:
      return "RAA_90_DAYS";
    case RaaDuration.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface GenerateTokenRequest {
}

export interface GenerateTokenResponse {
  id: string;
  accessToken: string;
  customerIdentifier: string;
  isNew: boolean;
}

export interface InitiateKycRequest {
  kycType: KycType;
  panKycRequest?: PanKycRequest | undefined;
  wetSignatureRequest?: WetSignatureRequest | undefined;
  selfieRequest?: SelfieRequest | undefined;
  kycRequest?: KycRequest | undefined;
  esignRequest?: EsignRequest | undefined;
}

export interface EsignRequest {
  step: EsignStep;
  generateTokenRequest?: GenerateTokenRequest | undefined;
  statusRequest?: StatusRequest | undefined;
}

export interface KycRequest {
  step: KycStep;
  generateTokenRequest?: GenerateTokenRequest | undefined;
  statusRequest?: StatusRequest | undefined;
}

export interface InitiateKycResponse {
  panKycResponse?: PanKycResponse | undefined;
  wetSignatureResponse?: WetSignatureResponse | undefined;
  selfieResponse?: SelfieResponse | undefined;
  kycResponse?: KycResponse | undefined;
  esignResponse?: EsignKycResponse | undefined;
}

export interface EsignKycResponse {
  generateTokenResponse?: GenerateTokenResponse | undefined;
  statusResponse?: StatusResponse | undefined;
}

export interface KycResponse {
  generateTokenResponse?: GenerateTokenResponse | undefined;
  statusResponse?: StatusResponse | undefined;
}

export interface SelfieRequest {
  step: SelfieKycStep;
  startSelfiStepRequest?: StartSelfiStepRequest | undefined;
  setSelfiStatusRequest?: SetSelfiStatusRequest | undefined;
}

export interface StartSelfiStepRequest {
}

export interface SetSelfiStatusRequest {
  transactionId: string;
  status: string;
}

export interface SelfieResponse {
  startSelfiStepResponse?: StartSelfiStepResponse | undefined;
  setSelfiStatusResponse?: SetSelfiStatusResponse | undefined;
}

export interface StartSelfiStepResponse {
  selfie: string;
  workflowId: string;
  accessToken: string;
  transactionId: string;
}

export interface SetSelfiStatusResponse {
}

export interface PanKycResponse {
  step: PanKycStep;
  kycFetchNameByPanResponse?: KycFetchNameByPanResponse | undefined;
  kycValidateNameAndGetPanStatusResponse?: KycValidateNameAndGetPanStatusResponse | undefined;
}

export interface PanKycRequest {
  step: PanKycStep;
  kycFetchNameByPanRequest?: KycFetchNameByPanRequest | undefined;
  kycValidateNameAndGetKycStatusRequest?: KycValidateNameAndGetKycStatusRequest | undefined;
}

export interface KycFetchNameByPanResponse {
  pan: string;
  fullName: string;
}

export interface KycFetchNameByPanRequest {
  pan: string;
}

export interface KycValidateNameAndGetPanStatusRequest {
  isNameMatch: boolean;
  dob: string;
}

export interface KycValidateNameAndGetPanStatusResponse {
  panKycStatus: boolean;
}

export interface StatusRequest {
  id: string;
}

export interface StatusResponse {
  kycStatus: string;
  description: string;
  digilockerStatus: string;
}

export interface WetSignatureRequest {
  raaDuration: RaaDuration;
  isPep: boolean;
  isIndianCitizen: boolean;
  documentId: string;
  creditReportConsent: boolean;
}

export interface WetSignatureResponse {
}

export interface KycValidateNameAndGetKycStatusRequest {
  isNameMatch: boolean;
}

function createBaseGenerateTokenRequest(): GenerateTokenRequest {
  return {};
}

export const GenerateTokenRequest: MessageFns<GenerateTokenRequest> = {
  encode(_: GenerateTokenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GenerateTokenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerateTokenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): GenerateTokenRequest {
    return {};
  },

  toJSON(_: GenerateTokenRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GenerateTokenRequest>, I>>(base?: I): GenerateTokenRequest {
    return GenerateTokenRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateTokenRequest>, I>>(_: I): GenerateTokenRequest {
    const message = createBaseGenerateTokenRequest();
    return message;
  },
};

function createBaseGenerateTokenResponse(): GenerateTokenResponse {
  return { id: "", accessToken: "", customerIdentifier: "", isNew: false };
}

export const GenerateTokenResponse: MessageFns<GenerateTokenResponse> = {
  encode(message: GenerateTokenResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.accessToken !== "") {
      writer.uint32(18).string(message.accessToken);
    }
    if (message.customerIdentifier !== "") {
      writer.uint32(26).string(message.customerIdentifier);
    }
    if (message.isNew !== false) {
      writer.uint32(32).bool(message.isNew);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GenerateTokenResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerateTokenResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accessToken = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.customerIdentifier = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isNew = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GenerateTokenResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      accessToken: isSet(object.accessToken) ? globalThis.String(object.accessToken) : "",
      customerIdentifier: isSet(object.customerIdentifier) ? globalThis.String(object.customerIdentifier) : "",
      isNew: isSet(object.isNew) ? globalThis.Boolean(object.isNew) : false,
    };
  },

  toJSON(message: GenerateTokenResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.accessToken !== "") {
      obj.accessToken = message.accessToken;
    }
    if (message.customerIdentifier !== "") {
      obj.customerIdentifier = message.customerIdentifier;
    }
    if (message.isNew !== false) {
      obj.isNew = message.isNew;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GenerateTokenResponse>, I>>(base?: I): GenerateTokenResponse {
    return GenerateTokenResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateTokenResponse>, I>>(object: I): GenerateTokenResponse {
    const message = createBaseGenerateTokenResponse();
    message.id = object.id ?? "";
    message.accessToken = object.accessToken ?? "";
    message.customerIdentifier = object.customerIdentifier ?? "";
    message.isNew = object.isNew ?? false;
    return message;
  },
};

function createBaseInitiateKycRequest(): InitiateKycRequest {
  return {
    kycType: 0,
    panKycRequest: undefined,
    wetSignatureRequest: undefined,
    selfieRequest: undefined,
    kycRequest: undefined,
    esignRequest: undefined,
  };
}

export const InitiateKycRequest: MessageFns<InitiateKycRequest> = {
  encode(message: InitiateKycRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.kycType !== 0) {
      writer.uint32(8).int32(message.kycType);
    }
    if (message.panKycRequest !== undefined) {
      PanKycRequest.encode(message.panKycRequest, writer.uint32(18).fork()).join();
    }
    if (message.wetSignatureRequest !== undefined) {
      WetSignatureRequest.encode(message.wetSignatureRequest, writer.uint32(26).fork()).join();
    }
    if (message.selfieRequest !== undefined) {
      SelfieRequest.encode(message.selfieRequest, writer.uint32(34).fork()).join();
    }
    if (message.kycRequest !== undefined) {
      KycRequest.encode(message.kycRequest, writer.uint32(42).fork()).join();
    }
    if (message.esignRequest !== undefined) {
      EsignRequest.encode(message.esignRequest, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateKycRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateKycRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.kycType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.panKycRequest = PanKycRequest.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.wetSignatureRequest = WetSignatureRequest.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.selfieRequest = SelfieRequest.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.kycRequest = KycRequest.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.esignRequest = EsignRequest.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateKycRequest {
    return {
      kycType: isSet(object.kycType) ? kycTypeFromJSON(object.kycType) : 0,
      panKycRequest: isSet(object.panKycRequest) ? PanKycRequest.fromJSON(object.panKycRequest) : undefined,
      wetSignatureRequest: isSet(object.wetSignatureRequest)
        ? WetSignatureRequest.fromJSON(object.wetSignatureRequest)
        : undefined,
      selfieRequest: isSet(object.selfieRequest) ? SelfieRequest.fromJSON(object.selfieRequest) : undefined,
      kycRequest: isSet(object.kycRequest) ? KycRequest.fromJSON(object.kycRequest) : undefined,
      esignRequest: isSet(object.esignRequest) ? EsignRequest.fromJSON(object.esignRequest) : undefined,
    };
  },

  toJSON(message: InitiateKycRequest): unknown {
    const obj: any = {};
    if (message.kycType !== 0) {
      obj.kycType = kycTypeToJSON(message.kycType);
    }
    if (message.panKycRequest !== undefined) {
      obj.panKycRequest = PanKycRequest.toJSON(message.panKycRequest);
    }
    if (message.wetSignatureRequest !== undefined) {
      obj.wetSignatureRequest = WetSignatureRequest.toJSON(message.wetSignatureRequest);
    }
    if (message.selfieRequest !== undefined) {
      obj.selfieRequest = SelfieRequest.toJSON(message.selfieRequest);
    }
    if (message.kycRequest !== undefined) {
      obj.kycRequest = KycRequest.toJSON(message.kycRequest);
    }
    if (message.esignRequest !== undefined) {
      obj.esignRequest = EsignRequest.toJSON(message.esignRequest);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateKycRequest>, I>>(base?: I): InitiateKycRequest {
    return InitiateKycRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateKycRequest>, I>>(object: I): InitiateKycRequest {
    const message = createBaseInitiateKycRequest();
    message.kycType = object.kycType ?? 0;
    message.panKycRequest = (object.panKycRequest !== undefined && object.panKycRequest !== null)
      ? PanKycRequest.fromPartial(object.panKycRequest)
      : undefined;
    message.wetSignatureRequest = (object.wetSignatureRequest !== undefined && object.wetSignatureRequest !== null)
      ? WetSignatureRequest.fromPartial(object.wetSignatureRequest)
      : undefined;
    message.selfieRequest = (object.selfieRequest !== undefined && object.selfieRequest !== null)
      ? SelfieRequest.fromPartial(object.selfieRequest)
      : undefined;
    message.kycRequest = (object.kycRequest !== undefined && object.kycRequest !== null)
      ? KycRequest.fromPartial(object.kycRequest)
      : undefined;
    message.esignRequest = (object.esignRequest !== undefined && object.esignRequest !== null)
      ? EsignRequest.fromPartial(object.esignRequest)
      : undefined;
    return message;
  },
};

function createBaseEsignRequest(): EsignRequest {
  return { step: 0, generateTokenRequest: undefined, statusRequest: undefined };
}

export const EsignRequest: MessageFns<EsignRequest> = {
  encode(message: EsignRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.step !== 0) {
      writer.uint32(8).int32(message.step);
    }
    if (message.generateTokenRequest !== undefined) {
      GenerateTokenRequest.encode(message.generateTokenRequest, writer.uint32(18).fork()).join();
    }
    if (message.statusRequest !== undefined) {
      StatusRequest.encode(message.statusRequest, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EsignRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEsignRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.step = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.generateTokenRequest = GenerateTokenRequest.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.statusRequest = StatusRequest.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EsignRequest {
    return {
      step: isSet(object.step) ? esignStepFromJSON(object.step) : 0,
      generateTokenRequest: isSet(object.generateTokenRequest)
        ? GenerateTokenRequest.fromJSON(object.generateTokenRequest)
        : undefined,
      statusRequest: isSet(object.statusRequest) ? StatusRequest.fromJSON(object.statusRequest) : undefined,
    };
  },

  toJSON(message: EsignRequest): unknown {
    const obj: any = {};
    if (message.step !== 0) {
      obj.step = esignStepToJSON(message.step);
    }
    if (message.generateTokenRequest !== undefined) {
      obj.generateTokenRequest = GenerateTokenRequest.toJSON(message.generateTokenRequest);
    }
    if (message.statusRequest !== undefined) {
      obj.statusRequest = StatusRequest.toJSON(message.statusRequest);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EsignRequest>, I>>(base?: I): EsignRequest {
    return EsignRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EsignRequest>, I>>(object: I): EsignRequest {
    const message = createBaseEsignRequest();
    message.step = object.step ?? 0;
    message.generateTokenRequest = (object.generateTokenRequest !== undefined && object.generateTokenRequest !== null)
      ? GenerateTokenRequest.fromPartial(object.generateTokenRequest)
      : undefined;
    message.statusRequest = (object.statusRequest !== undefined && object.statusRequest !== null)
      ? StatusRequest.fromPartial(object.statusRequest)
      : undefined;
    return message;
  },
};

function createBaseKycRequest(): KycRequest {
  return { step: 0, generateTokenRequest: undefined, statusRequest: undefined };
}

export const KycRequest: MessageFns<KycRequest> = {
  encode(message: KycRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.step !== 0) {
      writer.uint32(8).int32(message.step);
    }
    if (message.generateTokenRequest !== undefined) {
      GenerateTokenRequest.encode(message.generateTokenRequest, writer.uint32(18).fork()).join();
    }
    if (message.statusRequest !== undefined) {
      StatusRequest.encode(message.statusRequest, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): KycRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKycRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.step = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.generateTokenRequest = GenerateTokenRequest.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.statusRequest = StatusRequest.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KycRequest {
    return {
      step: isSet(object.step) ? kycStepFromJSON(object.step) : 0,
      generateTokenRequest: isSet(object.generateTokenRequest)
        ? GenerateTokenRequest.fromJSON(object.generateTokenRequest)
        : undefined,
      statusRequest: isSet(object.statusRequest) ? StatusRequest.fromJSON(object.statusRequest) : undefined,
    };
  },

  toJSON(message: KycRequest): unknown {
    const obj: any = {};
    if (message.step !== 0) {
      obj.step = kycStepToJSON(message.step);
    }
    if (message.generateTokenRequest !== undefined) {
      obj.generateTokenRequest = GenerateTokenRequest.toJSON(message.generateTokenRequest);
    }
    if (message.statusRequest !== undefined) {
      obj.statusRequest = StatusRequest.toJSON(message.statusRequest);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KycRequest>, I>>(base?: I): KycRequest {
    return KycRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KycRequest>, I>>(object: I): KycRequest {
    const message = createBaseKycRequest();
    message.step = object.step ?? 0;
    message.generateTokenRequest = (object.generateTokenRequest !== undefined && object.generateTokenRequest !== null)
      ? GenerateTokenRequest.fromPartial(object.generateTokenRequest)
      : undefined;
    message.statusRequest = (object.statusRequest !== undefined && object.statusRequest !== null)
      ? StatusRequest.fromPartial(object.statusRequest)
      : undefined;
    return message;
  },
};

function createBaseInitiateKycResponse(): InitiateKycResponse {
  return {
    panKycResponse: undefined,
    wetSignatureResponse: undefined,
    selfieResponse: undefined,
    kycResponse: undefined,
    esignResponse: undefined,
  };
}

export const InitiateKycResponse: MessageFns<InitiateKycResponse> = {
  encode(message: InitiateKycResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.panKycResponse !== undefined) {
      PanKycResponse.encode(message.panKycResponse, writer.uint32(10).fork()).join();
    }
    if (message.wetSignatureResponse !== undefined) {
      WetSignatureResponse.encode(message.wetSignatureResponse, writer.uint32(18).fork()).join();
    }
    if (message.selfieResponse !== undefined) {
      SelfieResponse.encode(message.selfieResponse, writer.uint32(26).fork()).join();
    }
    if (message.kycResponse !== undefined) {
      KycResponse.encode(message.kycResponse, writer.uint32(34).fork()).join();
    }
    if (message.esignResponse !== undefined) {
      EsignKycResponse.encode(message.esignResponse, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateKycResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateKycResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.panKycResponse = PanKycResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.wetSignatureResponse = WetSignatureResponse.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.selfieResponse = SelfieResponse.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.kycResponse = KycResponse.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.esignResponse = EsignKycResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateKycResponse {
    return {
      panKycResponse: isSet(object.panKycResponse) ? PanKycResponse.fromJSON(object.panKycResponse) : undefined,
      wetSignatureResponse: isSet(object.wetSignatureResponse)
        ? WetSignatureResponse.fromJSON(object.wetSignatureResponse)
        : undefined,
      selfieResponse: isSet(object.selfieResponse) ? SelfieResponse.fromJSON(object.selfieResponse) : undefined,
      kycResponse: isSet(object.kycResponse) ? KycResponse.fromJSON(object.kycResponse) : undefined,
      esignResponse: isSet(object.esignResponse) ? EsignKycResponse.fromJSON(object.esignResponse) : undefined,
    };
  },

  toJSON(message: InitiateKycResponse): unknown {
    const obj: any = {};
    if (message.panKycResponse !== undefined) {
      obj.panKycResponse = PanKycResponse.toJSON(message.panKycResponse);
    }
    if (message.wetSignatureResponse !== undefined) {
      obj.wetSignatureResponse = WetSignatureResponse.toJSON(message.wetSignatureResponse);
    }
    if (message.selfieResponse !== undefined) {
      obj.selfieResponse = SelfieResponse.toJSON(message.selfieResponse);
    }
    if (message.kycResponse !== undefined) {
      obj.kycResponse = KycResponse.toJSON(message.kycResponse);
    }
    if (message.esignResponse !== undefined) {
      obj.esignResponse = EsignKycResponse.toJSON(message.esignResponse);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateKycResponse>, I>>(base?: I): InitiateKycResponse {
    return InitiateKycResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateKycResponse>, I>>(object: I): InitiateKycResponse {
    const message = createBaseInitiateKycResponse();
    message.panKycResponse = (object.panKycResponse !== undefined && object.panKycResponse !== null)
      ? PanKycResponse.fromPartial(object.panKycResponse)
      : undefined;
    message.wetSignatureResponse = (object.wetSignatureResponse !== undefined && object.wetSignatureResponse !== null)
      ? WetSignatureResponse.fromPartial(object.wetSignatureResponse)
      : undefined;
    message.selfieResponse = (object.selfieResponse !== undefined && object.selfieResponse !== null)
      ? SelfieResponse.fromPartial(object.selfieResponse)
      : undefined;
    message.kycResponse = (object.kycResponse !== undefined && object.kycResponse !== null)
      ? KycResponse.fromPartial(object.kycResponse)
      : undefined;
    message.esignResponse = (object.esignResponse !== undefined && object.esignResponse !== null)
      ? EsignKycResponse.fromPartial(object.esignResponse)
      : undefined;
    return message;
  },
};

function createBaseEsignKycResponse(): EsignKycResponse {
  return { generateTokenResponse: undefined, statusResponse: undefined };
}

export const EsignKycResponse: MessageFns<EsignKycResponse> = {
  encode(message: EsignKycResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.generateTokenResponse !== undefined) {
      GenerateTokenResponse.encode(message.generateTokenResponse, writer.uint32(10).fork()).join();
    }
    if (message.statusResponse !== undefined) {
      StatusResponse.encode(message.statusResponse, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EsignKycResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEsignKycResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.generateTokenResponse = GenerateTokenResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.statusResponse = StatusResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EsignKycResponse {
    return {
      generateTokenResponse: isSet(object.generateTokenResponse)
        ? GenerateTokenResponse.fromJSON(object.generateTokenResponse)
        : undefined,
      statusResponse: isSet(object.statusResponse) ? StatusResponse.fromJSON(object.statusResponse) : undefined,
    };
  },

  toJSON(message: EsignKycResponse): unknown {
    const obj: any = {};
    if (message.generateTokenResponse !== undefined) {
      obj.generateTokenResponse = GenerateTokenResponse.toJSON(message.generateTokenResponse);
    }
    if (message.statusResponse !== undefined) {
      obj.statusResponse = StatusResponse.toJSON(message.statusResponse);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EsignKycResponse>, I>>(base?: I): EsignKycResponse {
    return EsignKycResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EsignKycResponse>, I>>(object: I): EsignKycResponse {
    const message = createBaseEsignKycResponse();
    message.generateTokenResponse =
      (object.generateTokenResponse !== undefined && object.generateTokenResponse !== null)
        ? GenerateTokenResponse.fromPartial(object.generateTokenResponse)
        : undefined;
    message.statusResponse = (object.statusResponse !== undefined && object.statusResponse !== null)
      ? StatusResponse.fromPartial(object.statusResponse)
      : undefined;
    return message;
  },
};

function createBaseKycResponse(): KycResponse {
  return { generateTokenResponse: undefined, statusResponse: undefined };
}

export const KycResponse: MessageFns<KycResponse> = {
  encode(message: KycResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.generateTokenResponse !== undefined) {
      GenerateTokenResponse.encode(message.generateTokenResponse, writer.uint32(10).fork()).join();
    }
    if (message.statusResponse !== undefined) {
      StatusResponse.encode(message.statusResponse, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): KycResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKycResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.generateTokenResponse = GenerateTokenResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.statusResponse = StatusResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KycResponse {
    return {
      generateTokenResponse: isSet(object.generateTokenResponse)
        ? GenerateTokenResponse.fromJSON(object.generateTokenResponse)
        : undefined,
      statusResponse: isSet(object.statusResponse) ? StatusResponse.fromJSON(object.statusResponse) : undefined,
    };
  },

  toJSON(message: KycResponse): unknown {
    const obj: any = {};
    if (message.generateTokenResponse !== undefined) {
      obj.generateTokenResponse = GenerateTokenResponse.toJSON(message.generateTokenResponse);
    }
    if (message.statusResponse !== undefined) {
      obj.statusResponse = StatusResponse.toJSON(message.statusResponse);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KycResponse>, I>>(base?: I): KycResponse {
    return KycResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KycResponse>, I>>(object: I): KycResponse {
    const message = createBaseKycResponse();
    message.generateTokenResponse =
      (object.generateTokenResponse !== undefined && object.generateTokenResponse !== null)
        ? GenerateTokenResponse.fromPartial(object.generateTokenResponse)
        : undefined;
    message.statusResponse = (object.statusResponse !== undefined && object.statusResponse !== null)
      ? StatusResponse.fromPartial(object.statusResponse)
      : undefined;
    return message;
  },
};

function createBaseSelfieRequest(): SelfieRequest {
  return { step: 0, startSelfiStepRequest: undefined, setSelfiStatusRequest: undefined };
}

export const SelfieRequest: MessageFns<SelfieRequest> = {
  encode(message: SelfieRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.step !== 0) {
      writer.uint32(8).int32(message.step);
    }
    if (message.startSelfiStepRequest !== undefined) {
      StartSelfiStepRequest.encode(message.startSelfiStepRequest, writer.uint32(18).fork()).join();
    }
    if (message.setSelfiStatusRequest !== undefined) {
      SetSelfiStatusRequest.encode(message.setSelfiStatusRequest, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SelfieRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSelfieRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.step = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.startSelfiStepRequest = StartSelfiStepRequest.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.setSelfiStatusRequest = SetSelfiStatusRequest.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SelfieRequest {
    return {
      step: isSet(object.step) ? selfieKycStepFromJSON(object.step) : 0,
      startSelfiStepRequest: isSet(object.startSelfiStepRequest)
        ? StartSelfiStepRequest.fromJSON(object.startSelfiStepRequest)
        : undefined,
      setSelfiStatusRequest: isSet(object.setSelfiStatusRequest)
        ? SetSelfiStatusRequest.fromJSON(object.setSelfiStatusRequest)
        : undefined,
    };
  },

  toJSON(message: SelfieRequest): unknown {
    const obj: any = {};
    if (message.step !== 0) {
      obj.step = selfieKycStepToJSON(message.step);
    }
    if (message.startSelfiStepRequest !== undefined) {
      obj.startSelfiStepRequest = StartSelfiStepRequest.toJSON(message.startSelfiStepRequest);
    }
    if (message.setSelfiStatusRequest !== undefined) {
      obj.setSelfiStatusRequest = SetSelfiStatusRequest.toJSON(message.setSelfiStatusRequest);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SelfieRequest>, I>>(base?: I): SelfieRequest {
    return SelfieRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SelfieRequest>, I>>(object: I): SelfieRequest {
    const message = createBaseSelfieRequest();
    message.step = object.step ?? 0;
    message.startSelfiStepRequest =
      (object.startSelfiStepRequest !== undefined && object.startSelfiStepRequest !== null)
        ? StartSelfiStepRequest.fromPartial(object.startSelfiStepRequest)
        : undefined;
    message.setSelfiStatusRequest =
      (object.setSelfiStatusRequest !== undefined && object.setSelfiStatusRequest !== null)
        ? SetSelfiStatusRequest.fromPartial(object.setSelfiStatusRequest)
        : undefined;
    return message;
  },
};

function createBaseStartSelfiStepRequest(): StartSelfiStepRequest {
  return {};
}

export const StartSelfiStepRequest: MessageFns<StartSelfiStepRequest> = {
  encode(_: StartSelfiStepRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StartSelfiStepRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStartSelfiStepRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): StartSelfiStepRequest {
    return {};
  },

  toJSON(_: StartSelfiStepRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<StartSelfiStepRequest>, I>>(base?: I): StartSelfiStepRequest {
    return StartSelfiStepRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StartSelfiStepRequest>, I>>(_: I): StartSelfiStepRequest {
    const message = createBaseStartSelfiStepRequest();
    return message;
  },
};

function createBaseSetSelfiStatusRequest(): SetSelfiStatusRequest {
  return { transactionId: "", status: "" };
}

export const SetSelfiStatusRequest: MessageFns<SetSelfiStatusRequest> = {
  encode(message: SetSelfiStatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transactionId !== "") {
      writer.uint32(10).string(message.transactionId);
    }
    if (message.status !== "") {
      writer.uint32(18).string(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetSelfiStatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetSelfiStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.status = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SetSelfiStatusRequest {
    return {
      transactionId: isSet(object.transactionId) ? globalThis.String(object.transactionId) : "",
      status: isSet(object.status) ? globalThis.String(object.status) : "",
    };
  },

  toJSON(message: SetSelfiStatusRequest): unknown {
    const obj: any = {};
    if (message.transactionId !== "") {
      obj.transactionId = message.transactionId;
    }
    if (message.status !== "") {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SetSelfiStatusRequest>, I>>(base?: I): SetSelfiStatusRequest {
    return SetSelfiStatusRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetSelfiStatusRequest>, I>>(object: I): SetSelfiStatusRequest {
    const message = createBaseSetSelfiStatusRequest();
    message.transactionId = object.transactionId ?? "";
    message.status = object.status ?? "";
    return message;
  },
};

function createBaseSelfieResponse(): SelfieResponse {
  return { startSelfiStepResponse: undefined, setSelfiStatusResponse: undefined };
}

export const SelfieResponse: MessageFns<SelfieResponse> = {
  encode(message: SelfieResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.startSelfiStepResponse !== undefined) {
      StartSelfiStepResponse.encode(message.startSelfiStepResponse, writer.uint32(10).fork()).join();
    }
    if (message.setSelfiStatusResponse !== undefined) {
      SetSelfiStatusResponse.encode(message.setSelfiStatusResponse, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SelfieResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSelfieResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.startSelfiStepResponse = StartSelfiStepResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.setSelfiStatusResponse = SetSelfiStatusResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SelfieResponse {
    return {
      startSelfiStepResponse: isSet(object.startSelfiStepResponse)
        ? StartSelfiStepResponse.fromJSON(object.startSelfiStepResponse)
        : undefined,
      setSelfiStatusResponse: isSet(object.setSelfiStatusResponse)
        ? SetSelfiStatusResponse.fromJSON(object.setSelfiStatusResponse)
        : undefined,
    };
  },

  toJSON(message: SelfieResponse): unknown {
    const obj: any = {};
    if (message.startSelfiStepResponse !== undefined) {
      obj.startSelfiStepResponse = StartSelfiStepResponse.toJSON(message.startSelfiStepResponse);
    }
    if (message.setSelfiStatusResponse !== undefined) {
      obj.setSelfiStatusResponse = SetSelfiStatusResponse.toJSON(message.setSelfiStatusResponse);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SelfieResponse>, I>>(base?: I): SelfieResponse {
    return SelfieResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SelfieResponse>, I>>(object: I): SelfieResponse {
    const message = createBaseSelfieResponse();
    message.startSelfiStepResponse =
      (object.startSelfiStepResponse !== undefined && object.startSelfiStepResponse !== null)
        ? StartSelfiStepResponse.fromPartial(object.startSelfiStepResponse)
        : undefined;
    message.setSelfiStatusResponse =
      (object.setSelfiStatusResponse !== undefined && object.setSelfiStatusResponse !== null)
        ? SetSelfiStatusResponse.fromPartial(object.setSelfiStatusResponse)
        : undefined;
    return message;
  },
};

function createBaseStartSelfiStepResponse(): StartSelfiStepResponse {
  return { selfie: "", workflowId: "", accessToken: "", transactionId: "" };
}

export const StartSelfiStepResponse: MessageFns<StartSelfiStepResponse> = {
  encode(message: StartSelfiStepResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.selfie !== "") {
      writer.uint32(10).string(message.selfie);
    }
    if (message.workflowId !== "") {
      writer.uint32(18).string(message.workflowId);
    }
    if (message.accessToken !== "") {
      writer.uint32(26).string(message.accessToken);
    }
    if (message.transactionId !== "") {
      writer.uint32(34).string(message.transactionId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StartSelfiStepResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStartSelfiStepResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.selfie = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.workflowId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accessToken = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.transactionId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StartSelfiStepResponse {
    return {
      selfie: isSet(object.selfie) ? globalThis.String(object.selfie) : "",
      workflowId: isSet(object.workflowId) ? globalThis.String(object.workflowId) : "",
      accessToken: isSet(object.accessToken) ? globalThis.String(object.accessToken) : "",
      transactionId: isSet(object.transactionId) ? globalThis.String(object.transactionId) : "",
    };
  },

  toJSON(message: StartSelfiStepResponse): unknown {
    const obj: any = {};
    if (message.selfie !== "") {
      obj.selfie = message.selfie;
    }
    if (message.workflowId !== "") {
      obj.workflowId = message.workflowId;
    }
    if (message.accessToken !== "") {
      obj.accessToken = message.accessToken;
    }
    if (message.transactionId !== "") {
      obj.transactionId = message.transactionId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StartSelfiStepResponse>, I>>(base?: I): StartSelfiStepResponse {
    return StartSelfiStepResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StartSelfiStepResponse>, I>>(object: I): StartSelfiStepResponse {
    const message = createBaseStartSelfiStepResponse();
    message.selfie = object.selfie ?? "";
    message.workflowId = object.workflowId ?? "";
    message.accessToken = object.accessToken ?? "";
    message.transactionId = object.transactionId ?? "";
    return message;
  },
};

function createBaseSetSelfiStatusResponse(): SetSelfiStatusResponse {
  return {};
}

export const SetSelfiStatusResponse: MessageFns<SetSelfiStatusResponse> = {
  encode(_: SetSelfiStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetSelfiStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetSelfiStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): SetSelfiStatusResponse {
    return {};
  },

  toJSON(_: SetSelfiStatusResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SetSelfiStatusResponse>, I>>(base?: I): SetSelfiStatusResponse {
    return SetSelfiStatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetSelfiStatusResponse>, I>>(_: I): SetSelfiStatusResponse {
    const message = createBaseSetSelfiStatusResponse();
    return message;
  },
};

function createBasePanKycResponse(): PanKycResponse {
  return { step: 0, kycFetchNameByPanResponse: undefined, kycValidateNameAndGetPanStatusResponse: undefined };
}

export const PanKycResponse: MessageFns<PanKycResponse> = {
  encode(message: PanKycResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.step !== 0) {
      writer.uint32(8).int32(message.step);
    }
    if (message.kycFetchNameByPanResponse !== undefined) {
      KycFetchNameByPanResponse.encode(message.kycFetchNameByPanResponse, writer.uint32(18).fork()).join();
    }
    if (message.kycValidateNameAndGetPanStatusResponse !== undefined) {
      KycValidateNameAndGetPanStatusResponse.encode(
        message.kycValidateNameAndGetPanStatusResponse,
        writer.uint32(26).fork(),
      ).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PanKycResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePanKycResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.step = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.kycFetchNameByPanResponse = KycFetchNameByPanResponse.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.kycValidateNameAndGetPanStatusResponse = KycValidateNameAndGetPanStatusResponse.decode(
            reader,
            reader.uint32(),
          );
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PanKycResponse {
    return {
      step: isSet(object.step) ? panKycStepFromJSON(object.step) : 0,
      kycFetchNameByPanResponse: isSet(object.kycFetchNameByPanResponse)
        ? KycFetchNameByPanResponse.fromJSON(object.kycFetchNameByPanResponse)
        : undefined,
      kycValidateNameAndGetPanStatusResponse: isSet(object.kycValidateNameAndGetPanStatusResponse)
        ? KycValidateNameAndGetPanStatusResponse.fromJSON(object.kycValidateNameAndGetPanStatusResponse)
        : undefined,
    };
  },

  toJSON(message: PanKycResponse): unknown {
    const obj: any = {};
    if (message.step !== 0) {
      obj.step = panKycStepToJSON(message.step);
    }
    if (message.kycFetchNameByPanResponse !== undefined) {
      obj.kycFetchNameByPanResponse = KycFetchNameByPanResponse.toJSON(message.kycFetchNameByPanResponse);
    }
    if (message.kycValidateNameAndGetPanStatusResponse !== undefined) {
      obj.kycValidateNameAndGetPanStatusResponse = KycValidateNameAndGetPanStatusResponse.toJSON(
        message.kycValidateNameAndGetPanStatusResponse,
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PanKycResponse>, I>>(base?: I): PanKycResponse {
    return PanKycResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PanKycResponse>, I>>(object: I): PanKycResponse {
    const message = createBasePanKycResponse();
    message.step = object.step ?? 0;
    message.kycFetchNameByPanResponse =
      (object.kycFetchNameByPanResponse !== undefined && object.kycFetchNameByPanResponse !== null)
        ? KycFetchNameByPanResponse.fromPartial(object.kycFetchNameByPanResponse)
        : undefined;
    message.kycValidateNameAndGetPanStatusResponse =
      (object.kycValidateNameAndGetPanStatusResponse !== undefined &&
          object.kycValidateNameAndGetPanStatusResponse !== null)
        ? KycValidateNameAndGetPanStatusResponse.fromPartial(object.kycValidateNameAndGetPanStatusResponse)
        : undefined;
    return message;
  },
};

function createBasePanKycRequest(): PanKycRequest {
  return { step: 0, kycFetchNameByPanRequest: undefined, kycValidateNameAndGetKycStatusRequest: undefined };
}

export const PanKycRequest: MessageFns<PanKycRequest> = {
  encode(message: PanKycRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.step !== 0) {
      writer.uint32(8).int32(message.step);
    }
    if (message.kycFetchNameByPanRequest !== undefined) {
      KycFetchNameByPanRequest.encode(message.kycFetchNameByPanRequest, writer.uint32(18).fork()).join();
    }
    if (message.kycValidateNameAndGetKycStatusRequest !== undefined) {
      KycValidateNameAndGetKycStatusRequest.encode(
        message.kycValidateNameAndGetKycStatusRequest,
        writer.uint32(26).fork(),
      ).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PanKycRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePanKycRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.step = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.kycFetchNameByPanRequest = KycFetchNameByPanRequest.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.kycValidateNameAndGetKycStatusRequest = KycValidateNameAndGetKycStatusRequest.decode(
            reader,
            reader.uint32(),
          );
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PanKycRequest {
    return {
      step: isSet(object.step) ? panKycStepFromJSON(object.step) : 0,
      kycFetchNameByPanRequest: isSet(object.kycFetchNameByPanRequest)
        ? KycFetchNameByPanRequest.fromJSON(object.kycFetchNameByPanRequest)
        : undefined,
      kycValidateNameAndGetKycStatusRequest: isSet(object.kycValidateNameAndGetKycStatusRequest)
        ? KycValidateNameAndGetKycStatusRequest.fromJSON(object.kycValidateNameAndGetKycStatusRequest)
        : undefined,
    };
  },

  toJSON(message: PanKycRequest): unknown {
    const obj: any = {};
    if (message.step !== 0) {
      obj.step = panKycStepToJSON(message.step);
    }
    if (message.kycFetchNameByPanRequest !== undefined) {
      obj.kycFetchNameByPanRequest = KycFetchNameByPanRequest.toJSON(message.kycFetchNameByPanRequest);
    }
    if (message.kycValidateNameAndGetKycStatusRequest !== undefined) {
      obj.kycValidateNameAndGetKycStatusRequest = KycValidateNameAndGetKycStatusRequest.toJSON(
        message.kycValidateNameAndGetKycStatusRequest,
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PanKycRequest>, I>>(base?: I): PanKycRequest {
    return PanKycRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PanKycRequest>, I>>(object: I): PanKycRequest {
    const message = createBasePanKycRequest();
    message.step = object.step ?? 0;
    message.kycFetchNameByPanRequest =
      (object.kycFetchNameByPanRequest !== undefined && object.kycFetchNameByPanRequest !== null)
        ? KycFetchNameByPanRequest.fromPartial(object.kycFetchNameByPanRequest)
        : undefined;
    message.kycValidateNameAndGetKycStatusRequest =
      (object.kycValidateNameAndGetKycStatusRequest !== undefined &&
          object.kycValidateNameAndGetKycStatusRequest !== null)
        ? KycValidateNameAndGetKycStatusRequest.fromPartial(object.kycValidateNameAndGetKycStatusRequest)
        : undefined;
    return message;
  },
};

function createBaseKycFetchNameByPanResponse(): KycFetchNameByPanResponse {
  return { pan: "", fullName: "" };
}

export const KycFetchNameByPanResponse: MessageFns<KycFetchNameByPanResponse> = {
  encode(message: KycFetchNameByPanResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pan !== "") {
      writer.uint32(10).string(message.pan);
    }
    if (message.fullName !== "") {
      writer.uint32(18).string(message.fullName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): KycFetchNameByPanResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKycFetchNameByPanResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pan = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fullName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KycFetchNameByPanResponse {
    return {
      pan: isSet(object.pan) ? globalThis.String(object.pan) : "",
      fullName: isSet(object.fullName) ? globalThis.String(object.fullName) : "",
    };
  },

  toJSON(message: KycFetchNameByPanResponse): unknown {
    const obj: any = {};
    if (message.pan !== "") {
      obj.pan = message.pan;
    }
    if (message.fullName !== "") {
      obj.fullName = message.fullName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KycFetchNameByPanResponse>, I>>(base?: I): KycFetchNameByPanResponse {
    return KycFetchNameByPanResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KycFetchNameByPanResponse>, I>>(object: I): KycFetchNameByPanResponse {
    const message = createBaseKycFetchNameByPanResponse();
    message.pan = object.pan ?? "";
    message.fullName = object.fullName ?? "";
    return message;
  },
};

function createBaseKycFetchNameByPanRequest(): KycFetchNameByPanRequest {
  return { pan: "" };
}

export const KycFetchNameByPanRequest: MessageFns<KycFetchNameByPanRequest> = {
  encode(message: KycFetchNameByPanRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pan !== "") {
      writer.uint32(10).string(message.pan);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): KycFetchNameByPanRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKycFetchNameByPanRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pan = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KycFetchNameByPanRequest {
    return { pan: isSet(object.pan) ? globalThis.String(object.pan) : "" };
  },

  toJSON(message: KycFetchNameByPanRequest): unknown {
    const obj: any = {};
    if (message.pan !== "") {
      obj.pan = message.pan;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KycFetchNameByPanRequest>, I>>(base?: I): KycFetchNameByPanRequest {
    return KycFetchNameByPanRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KycFetchNameByPanRequest>, I>>(object: I): KycFetchNameByPanRequest {
    const message = createBaseKycFetchNameByPanRequest();
    message.pan = object.pan ?? "";
    return message;
  },
};

function createBaseKycValidateNameAndGetPanStatusRequest(): KycValidateNameAndGetPanStatusRequest {
  return { isNameMatch: false, dob: "" };
}

export const KycValidateNameAndGetPanStatusRequest: MessageFns<KycValidateNameAndGetPanStatusRequest> = {
  encode(message: KycValidateNameAndGetPanStatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isNameMatch !== false) {
      writer.uint32(8).bool(message.isNameMatch);
    }
    if (message.dob !== "") {
      writer.uint32(18).string(message.dob);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): KycValidateNameAndGetPanStatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKycValidateNameAndGetPanStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isNameMatch = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.dob = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KycValidateNameAndGetPanStatusRequest {
    return {
      isNameMatch: isSet(object.isNameMatch) ? globalThis.Boolean(object.isNameMatch) : false,
      dob: isSet(object.dob) ? globalThis.String(object.dob) : "",
    };
  },

  toJSON(message: KycValidateNameAndGetPanStatusRequest): unknown {
    const obj: any = {};
    if (message.isNameMatch !== false) {
      obj.isNameMatch = message.isNameMatch;
    }
    if (message.dob !== "") {
      obj.dob = message.dob;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KycValidateNameAndGetPanStatusRequest>, I>>(
    base?: I,
  ): KycValidateNameAndGetPanStatusRequest {
    return KycValidateNameAndGetPanStatusRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KycValidateNameAndGetPanStatusRequest>, I>>(
    object: I,
  ): KycValidateNameAndGetPanStatusRequest {
    const message = createBaseKycValidateNameAndGetPanStatusRequest();
    message.isNameMatch = object.isNameMatch ?? false;
    message.dob = object.dob ?? "";
    return message;
  },
};

function createBaseKycValidateNameAndGetPanStatusResponse(): KycValidateNameAndGetPanStatusResponse {
  return { panKycStatus: false };
}

export const KycValidateNameAndGetPanStatusResponse: MessageFns<KycValidateNameAndGetPanStatusResponse> = {
  encode(message: KycValidateNameAndGetPanStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.panKycStatus !== false) {
      writer.uint32(8).bool(message.panKycStatus);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): KycValidateNameAndGetPanStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKycValidateNameAndGetPanStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.panKycStatus = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KycValidateNameAndGetPanStatusResponse {
    return { panKycStatus: isSet(object.panKycStatus) ? globalThis.Boolean(object.panKycStatus) : false };
  },

  toJSON(message: KycValidateNameAndGetPanStatusResponse): unknown {
    const obj: any = {};
    if (message.panKycStatus !== false) {
      obj.panKycStatus = message.panKycStatus;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KycValidateNameAndGetPanStatusResponse>, I>>(
    base?: I,
  ): KycValidateNameAndGetPanStatusResponse {
    return KycValidateNameAndGetPanStatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KycValidateNameAndGetPanStatusResponse>, I>>(
    object: I,
  ): KycValidateNameAndGetPanStatusResponse {
    const message = createBaseKycValidateNameAndGetPanStatusResponse();
    message.panKycStatus = object.panKycStatus ?? false;
    return message;
  },
};

function createBaseStatusRequest(): StatusRequest {
  return { id: "" };
}

export const StatusRequest: MessageFns<StatusRequest> = {
  encode(message: StatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StatusRequest {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: StatusRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StatusRequest>, I>>(base?: I): StatusRequest {
    return StatusRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StatusRequest>, I>>(object: I): StatusRequest {
    const message = createBaseStatusRequest();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseStatusResponse(): StatusResponse {
  return { kycStatus: "", description: "", digilockerStatus: "" };
}

export const StatusResponse: MessageFns<StatusResponse> = {
  encode(message: StatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.kycStatus !== "") {
      writer.uint32(10).string(message.kycStatus);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.digilockerStatus !== "") {
      writer.uint32(26).string(message.digilockerStatus);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.kycStatus = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.digilockerStatus = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StatusResponse {
    return {
      kycStatus: isSet(object.kycStatus) ? globalThis.String(object.kycStatus) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      digilockerStatus: isSet(object.digilockerStatus) ? globalThis.String(object.digilockerStatus) : "",
    };
  },

  toJSON(message: StatusResponse): unknown {
    const obj: any = {};
    if (message.kycStatus !== "") {
      obj.kycStatus = message.kycStatus;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.digilockerStatus !== "") {
      obj.digilockerStatus = message.digilockerStatus;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StatusResponse>, I>>(base?: I): StatusResponse {
    return StatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StatusResponse>, I>>(object: I): StatusResponse {
    const message = createBaseStatusResponse();
    message.kycStatus = object.kycStatus ?? "";
    message.description = object.description ?? "";
    message.digilockerStatus = object.digilockerStatus ?? "";
    return message;
  },
};

function createBaseWetSignatureRequest(): WetSignatureRequest {
  return { raaDuration: 0, isPep: false, isIndianCitizen: false, documentId: "", creditReportConsent: false };
}

export const WetSignatureRequest: MessageFns<WetSignatureRequest> = {
  encode(message: WetSignatureRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.raaDuration !== 0) {
      writer.uint32(8).int32(message.raaDuration);
    }
    if (message.isPep !== false) {
      writer.uint32(16).bool(message.isPep);
    }
    if (message.isIndianCitizen !== false) {
      writer.uint32(24).bool(message.isIndianCitizen);
    }
    if (message.documentId !== "") {
      writer.uint32(34).string(message.documentId);
    }
    if (message.creditReportConsent !== false) {
      writer.uint32(40).bool(message.creditReportConsent);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WetSignatureRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWetSignatureRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.raaDuration = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isPep = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isIndianCitizen = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.documentId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.creditReportConsent = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WetSignatureRequest {
    return {
      raaDuration: isSet(object.raaDuration) ? raaDurationFromJSON(object.raaDuration) : 0,
      isPep: isSet(object.isPep) ? globalThis.Boolean(object.isPep) : false,
      isIndianCitizen: isSet(object.isIndianCitizen) ? globalThis.Boolean(object.isIndianCitizen) : false,
      documentId: isSet(object.documentId) ? globalThis.String(object.documentId) : "",
      creditReportConsent: isSet(object.creditReportConsent) ? globalThis.Boolean(object.creditReportConsent) : false,
    };
  },

  toJSON(message: WetSignatureRequest): unknown {
    const obj: any = {};
    if (message.raaDuration !== 0) {
      obj.raaDuration = raaDurationToJSON(message.raaDuration);
    }
    if (message.isPep !== false) {
      obj.isPep = message.isPep;
    }
    if (message.isIndianCitizen !== false) {
      obj.isIndianCitizen = message.isIndianCitizen;
    }
    if (message.documentId !== "") {
      obj.documentId = message.documentId;
    }
    if (message.creditReportConsent !== false) {
      obj.creditReportConsent = message.creditReportConsent;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WetSignatureRequest>, I>>(base?: I): WetSignatureRequest {
    return WetSignatureRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WetSignatureRequest>, I>>(object: I): WetSignatureRequest {
    const message = createBaseWetSignatureRequest();
    message.raaDuration = object.raaDuration ?? 0;
    message.isPep = object.isPep ?? false;
    message.isIndianCitizen = object.isIndianCitizen ?? false;
    message.documentId = object.documentId ?? "";
    message.creditReportConsent = object.creditReportConsent ?? false;
    return message;
  },
};

function createBaseWetSignatureResponse(): WetSignatureResponse {
  return {};
}

export const WetSignatureResponse: MessageFns<WetSignatureResponse> = {
  encode(_: WetSignatureResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WetSignatureResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWetSignatureResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): WetSignatureResponse {
    return {};
  },

  toJSON(_: WetSignatureResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<WetSignatureResponse>, I>>(base?: I): WetSignatureResponse {
    return WetSignatureResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WetSignatureResponse>, I>>(_: I): WetSignatureResponse {
    const message = createBaseWetSignatureResponse();
    return message;
  },
};

function createBaseKycValidateNameAndGetKycStatusRequest(): KycValidateNameAndGetKycStatusRequest {
  return { isNameMatch: false };
}

export const KycValidateNameAndGetKycStatusRequest: MessageFns<KycValidateNameAndGetKycStatusRequest> = {
  encode(message: KycValidateNameAndGetKycStatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isNameMatch !== false) {
      writer.uint32(8).bool(message.isNameMatch);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): KycValidateNameAndGetKycStatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKycValidateNameAndGetKycStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isNameMatch = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KycValidateNameAndGetKycStatusRequest {
    return { isNameMatch: isSet(object.isNameMatch) ? globalThis.Boolean(object.isNameMatch) : false };
  },

  toJSON(message: KycValidateNameAndGetKycStatusRequest): unknown {
    const obj: any = {};
    if (message.isNameMatch !== false) {
      obj.isNameMatch = message.isNameMatch;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KycValidateNameAndGetKycStatusRequest>, I>>(
    base?: I,
  ): KycValidateNameAndGetKycStatusRequest {
    return KycValidateNameAndGetKycStatusRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KycValidateNameAndGetKycStatusRequest>, I>>(
    object: I,
  ): KycValidateNameAndGetKycStatusRequest {
    const message = createBaseKycValidateNameAndGetKycStatusRequest();
    message.isNameMatch = object.isNameMatch ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
