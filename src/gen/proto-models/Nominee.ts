// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Nominee.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum RelationshipType {
  UNKNOWN_RELATIONSHIP = 0,
  FATHER = 1,
  MOTHER = 2,
  DAUGHTER = 3,
  SON = 4,
  SPOUSE = 5,
  SIBLING = 6,
  OTHER = 7,
  UNRECOGNIZED = -1,
}

export function relationshipTypeFromJSON(object: any): RelationshipType {
  switch (object) {
    case 0:
    case "UNKNOWN_RELATIONSHIP":
      return RelationshipType.UNKNOWN_RELATIONSHIP;
    case 1:
    case "FATHER":
      return RelationshipType.FATHER;
    case 2:
    case "MOTHER":
      return RelationshipType.MOTHER;
    case 3:
    case "DAUGHTER":
      return RelationshipType.DAUGHTER;
    case 4:
    case "SON":
      return RelationshipType.SON;
    case 5:
    case "SPOUSE":
      return RelationshipType.SPOUSE;
    case 6:
    case "SIBLING":
      return RelationshipType.SIBLING;
    case 7:
    case "OTHER":
      return RelationshipType.OTHER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RelationshipType.UNRECOGNIZED;
  }
}

export function relationshipTypeToJSON(object: RelationshipType): string {
  switch (object) {
    case RelationshipType.UNKNOWN_RELATIONSHIP:
      return "UNKNOWN_RELATIONSHIP";
    case RelationshipType.FATHER:
      return "FATHER";
    case RelationshipType.MOTHER:
      return "MOTHER";
    case RelationshipType.DAUGHTER:
      return "DAUGHTER";
    case RelationshipType.SON:
      return "SON";
    case RelationshipType.SPOUSE:
      return "SPOUSE";
    case RelationshipType.SIBLING:
      return "SIBLING";
    case RelationshipType.OTHER:
      return "OTHER";
    case RelationshipType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface AddressProto {
  addressLine1: string;
  addressLine2: string;
  addressLine3: string;
  city: string;
  postCode: string;
  state: string;
  country: string;
}

export interface NomineeDetails {
  name: string;
  relationshipType: RelationshipType;
  dob: string;
  relationship: string;
  guardianDetails: GuardianDetails | undefined;
  address: AddressProto | undefined;
  allocationPercentage: number;
  id: string;
}

export interface GuardianDetails {
  name: string;
  relationshipType: RelationshipType;
  relationship: string;
  address: AddressProto | undefined;
}

export interface AddNomineeRequest {
  optOut: boolean;
  nomineeDetails: NomineeDetails[];
}

export interface AddNomineeResponse {
}

export interface GetNomineeRequest {
}

export interface GetNomineeResponse {
  nomineeDetails: NomineeDetails[];
}

export interface UpdateNomineeRequest {
  nomineeDetails: NomineeDetails | undefined;
}

function createBaseAddressProto(): AddressProto {
  return { addressLine1: "", addressLine2: "", addressLine3: "", city: "", postCode: "", state: "", country: "" };
}

export const AddressProto: MessageFns<AddressProto> = {
  encode(message: AddressProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.addressLine1 !== "") {
      writer.uint32(10).string(message.addressLine1);
    }
    if (message.addressLine2 !== "") {
      writer.uint32(18).string(message.addressLine2);
    }
    if (message.addressLine3 !== "") {
      writer.uint32(26).string(message.addressLine3);
    }
    if (message.city !== "") {
      writer.uint32(34).string(message.city);
    }
    if (message.postCode !== "") {
      writer.uint32(42).string(message.postCode);
    }
    if (message.state !== "") {
      writer.uint32(50).string(message.state);
    }
    if (message.country !== "") {
      writer.uint32(58).string(message.country);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddressProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddressProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.addressLine1 = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.addressLine2 = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.addressLine3 = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.city = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.postCode = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.state = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.country = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddressProto {
    return {
      addressLine1: isSet(object.addressLine1) ? globalThis.String(object.addressLine1) : "",
      addressLine2: isSet(object.addressLine2) ? globalThis.String(object.addressLine2) : "",
      addressLine3: isSet(object.addressLine3) ? globalThis.String(object.addressLine3) : "",
      city: isSet(object.city) ? globalThis.String(object.city) : "",
      postCode: isSet(object.postCode) ? globalThis.String(object.postCode) : "",
      state: isSet(object.state) ? globalThis.String(object.state) : "",
      country: isSet(object.country) ? globalThis.String(object.country) : "",
    };
  },

  toJSON(message: AddressProto): unknown {
    const obj: any = {};
    if (message.addressLine1 !== "") {
      obj.addressLine1 = message.addressLine1;
    }
    if (message.addressLine2 !== "") {
      obj.addressLine2 = message.addressLine2;
    }
    if (message.addressLine3 !== "") {
      obj.addressLine3 = message.addressLine3;
    }
    if (message.city !== "") {
      obj.city = message.city;
    }
    if (message.postCode !== "") {
      obj.postCode = message.postCode;
    }
    if (message.state !== "") {
      obj.state = message.state;
    }
    if (message.country !== "") {
      obj.country = message.country;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddressProto>, I>>(base?: I): AddressProto {
    return AddressProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddressProto>, I>>(object: I): AddressProto {
    const message = createBaseAddressProto();
    message.addressLine1 = object.addressLine1 ?? "";
    message.addressLine2 = object.addressLine2 ?? "";
    message.addressLine3 = object.addressLine3 ?? "";
    message.city = object.city ?? "";
    message.postCode = object.postCode ?? "";
    message.state = object.state ?? "";
    message.country = object.country ?? "";
    return message;
  },
};

function createBaseNomineeDetails(): NomineeDetails {
  return {
    name: "",
    relationshipType: 0,
    dob: "",
    relationship: "",
    guardianDetails: undefined,
    address: undefined,
    allocationPercentage: 0,
    id: "",
  };
}

export const NomineeDetails: MessageFns<NomineeDetails> = {
  encode(message: NomineeDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.relationshipType !== 0) {
      writer.uint32(16).int32(message.relationshipType);
    }
    if (message.dob !== "") {
      writer.uint32(26).string(message.dob);
    }
    if (message.relationship !== "") {
      writer.uint32(34).string(message.relationship);
    }
    if (message.guardianDetails !== undefined) {
      GuardianDetails.encode(message.guardianDetails, writer.uint32(42).fork()).join();
    }
    if (message.address !== undefined) {
      AddressProto.encode(message.address, writer.uint32(50).fork()).join();
    }
    if (message.allocationPercentage !== 0) {
      writer.uint32(57).double(message.allocationPercentage);
    }
    if (message.id !== "") {
      writer.uint32(66).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NomineeDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNomineeDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.relationshipType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.dob = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.relationship = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.guardianDetails = GuardianDetails.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.address = AddressProto.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.allocationPercentage = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NomineeDetails {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      relationshipType: isSet(object.relationshipType) ? relationshipTypeFromJSON(object.relationshipType) : 0,
      dob: isSet(object.dob) ? globalThis.String(object.dob) : "",
      relationship: isSet(object.relationship) ? globalThis.String(object.relationship) : "",
      guardianDetails: isSet(object.guardianDetails) ? GuardianDetails.fromJSON(object.guardianDetails) : undefined,
      address: isSet(object.address) ? AddressProto.fromJSON(object.address) : undefined,
      allocationPercentage: isSet(object.allocationPercentage) ? globalThis.Number(object.allocationPercentage) : 0,
      id: isSet(object.id) ? globalThis.String(object.id) : "",
    };
  },

  toJSON(message: NomineeDetails): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.relationshipType !== 0) {
      obj.relationshipType = relationshipTypeToJSON(message.relationshipType);
    }
    if (message.dob !== "") {
      obj.dob = message.dob;
    }
    if (message.relationship !== "") {
      obj.relationship = message.relationship;
    }
    if (message.guardianDetails !== undefined) {
      obj.guardianDetails = GuardianDetails.toJSON(message.guardianDetails);
    }
    if (message.address !== undefined) {
      obj.address = AddressProto.toJSON(message.address);
    }
    if (message.allocationPercentage !== 0) {
      obj.allocationPercentage = message.allocationPercentage;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NomineeDetails>, I>>(base?: I): NomineeDetails {
    return NomineeDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NomineeDetails>, I>>(object: I): NomineeDetails {
    const message = createBaseNomineeDetails();
    message.name = object.name ?? "";
    message.relationshipType = object.relationshipType ?? 0;
    message.dob = object.dob ?? "";
    message.relationship = object.relationship ?? "";
    message.guardianDetails = (object.guardianDetails !== undefined && object.guardianDetails !== null)
      ? GuardianDetails.fromPartial(object.guardianDetails)
      : undefined;
    message.address = (object.address !== undefined && object.address !== null)
      ? AddressProto.fromPartial(object.address)
      : undefined;
    message.allocationPercentage = object.allocationPercentage ?? 0;
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseGuardianDetails(): GuardianDetails {
  return { name: "", relationshipType: 0, relationship: "", address: undefined };
}

export const GuardianDetails: MessageFns<GuardianDetails> = {
  encode(message: GuardianDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.relationshipType !== 0) {
      writer.uint32(16).int32(message.relationshipType);
    }
    if (message.relationship !== "") {
      writer.uint32(26).string(message.relationship);
    }
    if (message.address !== undefined) {
      AddressProto.encode(message.address, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GuardianDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGuardianDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.relationshipType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.relationship = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.address = AddressProto.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GuardianDetails {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      relationshipType: isSet(object.relationshipType) ? relationshipTypeFromJSON(object.relationshipType) : 0,
      relationship: isSet(object.relationship) ? globalThis.String(object.relationship) : "",
      address: isSet(object.address) ? AddressProto.fromJSON(object.address) : undefined,
    };
  },

  toJSON(message: GuardianDetails): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.relationshipType !== 0) {
      obj.relationshipType = relationshipTypeToJSON(message.relationshipType);
    }
    if (message.relationship !== "") {
      obj.relationship = message.relationship;
    }
    if (message.address !== undefined) {
      obj.address = AddressProto.toJSON(message.address);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GuardianDetails>, I>>(base?: I): GuardianDetails {
    return GuardianDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GuardianDetails>, I>>(object: I): GuardianDetails {
    const message = createBaseGuardianDetails();
    message.name = object.name ?? "";
    message.relationshipType = object.relationshipType ?? 0;
    message.relationship = object.relationship ?? "";
    message.address = (object.address !== undefined && object.address !== null)
      ? AddressProto.fromPartial(object.address)
      : undefined;
    return message;
  },
};

function createBaseAddNomineeRequest(): AddNomineeRequest {
  return { optOut: false, nomineeDetails: [] };
}

export const AddNomineeRequest: MessageFns<AddNomineeRequest> = {
  encode(message: AddNomineeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.optOut !== false) {
      writer.uint32(8).bool(message.optOut);
    }
    for (const v of message.nomineeDetails) {
      NomineeDetails.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddNomineeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddNomineeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.optOut = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nomineeDetails.push(NomineeDetails.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddNomineeRequest {
    return {
      optOut: isSet(object.optOut) ? globalThis.Boolean(object.optOut) : false,
      nomineeDetails: globalThis.Array.isArray(object?.nomineeDetails)
        ? object.nomineeDetails.map((e: any) => NomineeDetails.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AddNomineeRequest): unknown {
    const obj: any = {};
    if (message.optOut !== false) {
      obj.optOut = message.optOut;
    }
    if (message.nomineeDetails?.length) {
      obj.nomineeDetails = message.nomineeDetails.map((e) => NomineeDetails.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddNomineeRequest>, I>>(base?: I): AddNomineeRequest {
    return AddNomineeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddNomineeRequest>, I>>(object: I): AddNomineeRequest {
    const message = createBaseAddNomineeRequest();
    message.optOut = object.optOut ?? false;
    message.nomineeDetails = object.nomineeDetails?.map((e) => NomineeDetails.fromPartial(e)) || [];
    return message;
  },
};

function createBaseAddNomineeResponse(): AddNomineeResponse {
  return {};
}

export const AddNomineeResponse: MessageFns<AddNomineeResponse> = {
  encode(_: AddNomineeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddNomineeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddNomineeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): AddNomineeResponse {
    return {};
  },

  toJSON(_: AddNomineeResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<AddNomineeResponse>, I>>(base?: I): AddNomineeResponse {
    return AddNomineeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddNomineeResponse>, I>>(_: I): AddNomineeResponse {
    const message = createBaseAddNomineeResponse();
    return message;
  },
};

function createBaseGetNomineeRequest(): GetNomineeRequest {
  return {};
}

export const GetNomineeRequest: MessageFns<GetNomineeRequest> = {
  encode(_: GetNomineeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNomineeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNomineeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): GetNomineeRequest {
    return {};
  },

  toJSON(_: GetNomineeRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNomineeRequest>, I>>(base?: I): GetNomineeRequest {
    return GetNomineeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNomineeRequest>, I>>(_: I): GetNomineeRequest {
    const message = createBaseGetNomineeRequest();
    return message;
  },
};

function createBaseGetNomineeResponse(): GetNomineeResponse {
  return { nomineeDetails: [] };
}

export const GetNomineeResponse: MessageFns<GetNomineeResponse> = {
  encode(message: GetNomineeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.nomineeDetails) {
      NomineeDetails.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNomineeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNomineeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.nomineeDetails.push(NomineeDetails.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNomineeResponse {
    return {
      nomineeDetails: globalThis.Array.isArray(object?.nomineeDetails)
        ? object.nomineeDetails.map((e: any) => NomineeDetails.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetNomineeResponse): unknown {
    const obj: any = {};
    if (message.nomineeDetails?.length) {
      obj.nomineeDetails = message.nomineeDetails.map((e) => NomineeDetails.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNomineeResponse>, I>>(base?: I): GetNomineeResponse {
    return GetNomineeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNomineeResponse>, I>>(object: I): GetNomineeResponse {
    const message = createBaseGetNomineeResponse();
    message.nomineeDetails = object.nomineeDetails?.map((e) => NomineeDetails.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUpdateNomineeRequest(): UpdateNomineeRequest {
  return { nomineeDetails: undefined };
}

export const UpdateNomineeRequest: MessageFns<UpdateNomineeRequest> = {
  encode(message: UpdateNomineeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nomineeDetails !== undefined) {
      NomineeDetails.encode(message.nomineeDetails, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateNomineeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateNomineeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nomineeDetails = NomineeDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateNomineeRequest {
    return {
      nomineeDetails: isSet(object.nomineeDetails) ? NomineeDetails.fromJSON(object.nomineeDetails) : undefined,
    };
  },

  toJSON(message: UpdateNomineeRequest): unknown {
    const obj: any = {};
    if (message.nomineeDetails !== undefined) {
      obj.nomineeDetails = NomineeDetails.toJSON(message.nomineeDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateNomineeRequest>, I>>(base?: I): UpdateNomineeRequest {
    return UpdateNomineeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateNomineeRequest>, I>>(object: I): UpdateNomineeRequest {
    const message = createBaseUpdateNomineeRequest();
    message.nomineeDetails = (object.nomineeDetails !== undefined && object.nomineeDetails !== null)
      ? NomineeDetails.fromPartial(object.nomineeDetails)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
