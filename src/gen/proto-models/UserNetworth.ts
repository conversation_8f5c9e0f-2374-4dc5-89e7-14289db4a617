// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: UserNetworth.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.internal.api";

export interface GetUserNetWorthHistoryRequest {
  userId: string;
}

export interface GetUserNetWorthHistoryResponse {
  userProfile: GetUserNetWorthHistoryResponse_UserProfile | undefined;
  totalValue: number;
  netGains: number;
  netWorthLogs: GetUserNetWorthHistoryResponse_NetWorthLog[];
  percentile: number;
  activeDepositsCount: number;
}

export interface GetUserNetWorthHistoryResponse_UserProfile {
  name: string;
  memberSince: string;
  isNewUser: boolean;
}

export interface GetUserNetWorthHistoryResponse_NetWorthLog {
  date: string;
  netWorth: number;
}

function createBaseGetUserNetWorthHistoryRequest(): GetUserNetWorthHistoryRequest {
  return { userId: "" };
}

export const GetUserNetWorthHistoryRequest: MessageFns<GetUserNetWorthHistoryRequest> = {
  encode(message: GetUserNetWorthHistoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserNetWorthHistoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserNetWorthHistoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetUserNetWorthHistoryRequest {
    return { userId: isSet(object.userId) ? globalThis.String(object.userId) : "" };
  },

  toJSON(message: GetUserNetWorthHistoryRequest): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetUserNetWorthHistoryRequest>, I>>(base?: I): GetUserNetWorthHistoryRequest {
    return GetUserNetWorthHistoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserNetWorthHistoryRequest>, I>>(
    object: I,
  ): GetUserNetWorthHistoryRequest {
    const message = createBaseGetUserNetWorthHistoryRequest();
    message.userId = object.userId ?? "";
    return message;
  },
};

function createBaseGetUserNetWorthHistoryResponse(): GetUserNetWorthHistoryResponse {
  return {
    userProfile: undefined,
    totalValue: 0,
    netGains: 0,
    netWorthLogs: [],
    percentile: 0,
    activeDepositsCount: 0,
  };
}

export const GetUserNetWorthHistoryResponse: MessageFns<GetUserNetWorthHistoryResponse> = {
  encode(message: GetUserNetWorthHistoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userProfile !== undefined) {
      GetUserNetWorthHistoryResponse_UserProfile.encode(message.userProfile, writer.uint32(10).fork()).join();
    }
    if (message.totalValue !== 0) {
      writer.uint32(17).double(message.totalValue);
    }
    if (message.netGains !== 0) {
      writer.uint32(25).double(message.netGains);
    }
    for (const v of message.netWorthLogs) {
      GetUserNetWorthHistoryResponse_NetWorthLog.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.percentile !== 0) {
      writer.uint32(41).double(message.percentile);
    }
    if (message.activeDepositsCount !== 0) {
      writer.uint32(48).int32(message.activeDepositsCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserNetWorthHistoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserNetWorthHistoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userProfile = GetUserNetWorthHistoryResponse_UserProfile.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.totalValue = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.netGains = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.netWorthLogs.push(GetUserNetWorthHistoryResponse_NetWorthLog.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.percentile = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.activeDepositsCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetUserNetWorthHistoryResponse {
    return {
      userProfile: isSet(object.userProfile)
        ? GetUserNetWorthHistoryResponse_UserProfile.fromJSON(object.userProfile)
        : undefined,
      totalValue: isSet(object.totalValue) ? globalThis.Number(object.totalValue) : 0,
      netGains: isSet(object.netGains) ? globalThis.Number(object.netGains) : 0,
      netWorthLogs: globalThis.Array.isArray(object?.netWorthLogs)
        ? object.netWorthLogs.map((e: any) => GetUserNetWorthHistoryResponse_NetWorthLog.fromJSON(e))
        : [],
      percentile: isSet(object.percentile) ? globalThis.Number(object.percentile) : 0,
      activeDepositsCount: isSet(object.activeDepositsCount) ? globalThis.Number(object.activeDepositsCount) : 0,
    };
  },

  toJSON(message: GetUserNetWorthHistoryResponse): unknown {
    const obj: any = {};
    if (message.userProfile !== undefined) {
      obj.userProfile = GetUserNetWorthHistoryResponse_UserProfile.toJSON(message.userProfile);
    }
    if (message.totalValue !== 0) {
      obj.totalValue = message.totalValue;
    }
    if (message.netGains !== 0) {
      obj.netGains = message.netGains;
    }
    if (message.netWorthLogs?.length) {
      obj.netWorthLogs = message.netWorthLogs.map((e) => GetUserNetWorthHistoryResponse_NetWorthLog.toJSON(e));
    }
    if (message.percentile !== 0) {
      obj.percentile = message.percentile;
    }
    if (message.activeDepositsCount !== 0) {
      obj.activeDepositsCount = Math.round(message.activeDepositsCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetUserNetWorthHistoryResponse>, I>>(base?: I): GetUserNetWorthHistoryResponse {
    return GetUserNetWorthHistoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserNetWorthHistoryResponse>, I>>(
    object: I,
  ): GetUserNetWorthHistoryResponse {
    const message = createBaseGetUserNetWorthHistoryResponse();
    message.userProfile = (object.userProfile !== undefined && object.userProfile !== null)
      ? GetUserNetWorthHistoryResponse_UserProfile.fromPartial(object.userProfile)
      : undefined;
    message.totalValue = object.totalValue ?? 0;
    message.netGains = object.netGains ?? 0;
    message.netWorthLogs = object.netWorthLogs?.map((e) => GetUserNetWorthHistoryResponse_NetWorthLog.fromPartial(e)) ||
      [];
    message.percentile = object.percentile ?? 0;
    message.activeDepositsCount = object.activeDepositsCount ?? 0;
    return message;
  },
};

function createBaseGetUserNetWorthHistoryResponse_UserProfile(): GetUserNetWorthHistoryResponse_UserProfile {
  return { name: "", memberSince: "", isNewUser: false };
}

export const GetUserNetWorthHistoryResponse_UserProfile: MessageFns<GetUserNetWorthHistoryResponse_UserProfile> = {
  encode(message: GetUserNetWorthHistoryResponse_UserProfile, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.memberSince !== "") {
      writer.uint32(18).string(message.memberSince);
    }
    if (message.isNewUser !== false) {
      writer.uint32(24).bool(message.isNewUser);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserNetWorthHistoryResponse_UserProfile {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserNetWorthHistoryResponse_UserProfile();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.memberSince = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isNewUser = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetUserNetWorthHistoryResponse_UserProfile {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      memberSince: isSet(object.memberSince) ? globalThis.String(object.memberSince) : "",
      isNewUser: isSet(object.isNewUser) ? globalThis.Boolean(object.isNewUser) : false,
    };
  },

  toJSON(message: GetUserNetWorthHistoryResponse_UserProfile): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.memberSince !== "") {
      obj.memberSince = message.memberSince;
    }
    if (message.isNewUser !== false) {
      obj.isNewUser = message.isNewUser;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetUserNetWorthHistoryResponse_UserProfile>, I>>(
    base?: I,
  ): GetUserNetWorthHistoryResponse_UserProfile {
    return GetUserNetWorthHistoryResponse_UserProfile.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserNetWorthHistoryResponse_UserProfile>, I>>(
    object: I,
  ): GetUserNetWorthHistoryResponse_UserProfile {
    const message = createBaseGetUserNetWorthHistoryResponse_UserProfile();
    message.name = object.name ?? "";
    message.memberSince = object.memberSince ?? "";
    message.isNewUser = object.isNewUser ?? false;
    return message;
  },
};

function createBaseGetUserNetWorthHistoryResponse_NetWorthLog(): GetUserNetWorthHistoryResponse_NetWorthLog {
  return { date: "", netWorth: 0 };
}

export const GetUserNetWorthHistoryResponse_NetWorthLog: MessageFns<GetUserNetWorthHistoryResponse_NetWorthLog> = {
  encode(message: GetUserNetWorthHistoryResponse_NetWorthLog, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.date !== "") {
      writer.uint32(10).string(message.date);
    }
    if (message.netWorth !== 0) {
      writer.uint32(17).double(message.netWorth);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetUserNetWorthHistoryResponse_NetWorthLog {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserNetWorthHistoryResponse_NetWorthLog();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.date = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.netWorth = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetUserNetWorthHistoryResponse_NetWorthLog {
    return {
      date: isSet(object.date) ? globalThis.String(object.date) : "",
      netWorth: isSet(object.netWorth) ? globalThis.Number(object.netWorth) : 0,
    };
  },

  toJSON(message: GetUserNetWorthHistoryResponse_NetWorthLog): unknown {
    const obj: any = {};
    if (message.date !== "") {
      obj.date = message.date;
    }
    if (message.netWorth !== 0) {
      obj.netWorth = message.netWorth;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetUserNetWorthHistoryResponse_NetWorthLog>, I>>(
    base?: I,
  ): GetUserNetWorthHistoryResponse_NetWorthLog {
    return GetUserNetWorthHistoryResponse_NetWorthLog.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserNetWorthHistoryResponse_NetWorthLog>, I>>(
    object: I,
  ): GetUserNetWorthHistoryResponse_NetWorthLog {
    const message = createBaseGetUserNetWorthHistoryResponse_NetWorthLog();
    message.date = object.date ?? "";
    message.netWorth = object.netWorth ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
