// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Catalog.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { IdRequest } from "./BrokingCollection";

export const protobufPackage = "com.stablemoney.api.broking";

export enum BondType {
  BOND_TYPE_UNKNOWN = 0,
  T_BILL = 1,
  GOVT_SECURITIES = 2,
  STATE_DEVELOPMENT_LOAN = 3,
  MUNICIPAL_BONDS = 4,
  CORPORATE_BONDS = 5,
  UNRECOGNIZED = -1,
}

export function bondTypeFromJSON(object: any): BondType {
  switch (object) {
    case 0:
    case "BOND_TYPE_UNKNOWN":
      return BondType.BOND_TYPE_UNKNOWN;
    case 1:
    case "T_BILL":
      return BondType.T_BILL;
    case 2:
    case "GOVT_SECURITIES":
      return BondType.GOVT_SECURITIES;
    case 3:
    case "STATE_DEVELOPMENT_LOAN":
      return BondType.STATE_DEVELOPMENT_LOAN;
    case 4:
    case "MUNICIPAL_BONDS":
      return BondType.MUNICIPAL_BONDS;
    case 5:
    case "CORPORATE_BONDS":
      return BondType.CORPORATE_BONDS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BondType.UNRECOGNIZED;
  }
}

export function bondTypeToJSON(object: BondType): string {
  switch (object) {
    case BondType.BOND_TYPE_UNKNOWN:
      return "BOND_TYPE_UNKNOWN";
    case BondType.T_BILL:
      return "T_BILL";
    case BondType.GOVT_SECURITIES:
      return "GOVT_SECURITIES";
    case BondType.STATE_DEVELOPMENT_LOAN:
      return "STATE_DEVELOPMENT_LOAN";
    case BondType.MUNICIPAL_BONDS:
      return "MUNICIPAL_BONDS";
    case BondType.CORPORATE_BONDS:
      return "CORPORATE_BONDS";
    case BondType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum InventoryAlarmStatus {
  ALARM_STATUS_UNKNOWN = 0,
  NORMAL = 1,
  LEVEL_1 = 2,
  UNRECOGNIZED = -1,
}

export function inventoryAlarmStatusFromJSON(object: any): InventoryAlarmStatus {
  switch (object) {
    case 0:
    case "ALARM_STATUS_UNKNOWN":
      return InventoryAlarmStatus.ALARM_STATUS_UNKNOWN;
    case 1:
    case "NORMAL":
      return InventoryAlarmStatus.NORMAL;
    case 2:
    case "LEVEL_1":
      return InventoryAlarmStatus.LEVEL_1;
    case -1:
    case "UNRECOGNIZED":
    default:
      return InventoryAlarmStatus.UNRECOGNIZED;
  }
}

export function inventoryAlarmStatusToJSON(object: InventoryAlarmStatus): string {
  switch (object) {
    case InventoryAlarmStatus.ALARM_STATUS_UNKNOWN:
      return "ALARM_STATUS_UNKNOWN";
    case InventoryAlarmStatus.NORMAL:
      return "NORMAL";
    case InventoryAlarmStatus.LEVEL_1:
      return "LEVEL_1";
    case InventoryAlarmStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum RepaymentFrequency {
  REPAYMENT_FREQUENCY_UNKNOWN = 0,
  MONTHLY = 1,
  QUARTERLY = 2,
  HALF_YEARLY = 3,
  YEARLY = 4,
  CUMULATIVE = 5,
  UNRECOGNIZED = -1,
}

export function repaymentFrequencyFromJSON(object: any): RepaymentFrequency {
  switch (object) {
    case 0:
    case "REPAYMENT_FREQUENCY_UNKNOWN":
      return RepaymentFrequency.REPAYMENT_FREQUENCY_UNKNOWN;
    case 1:
    case "MONTHLY":
      return RepaymentFrequency.MONTHLY;
    case 2:
    case "QUARTERLY":
      return RepaymentFrequency.QUARTERLY;
    case 3:
    case "HALF_YEARLY":
      return RepaymentFrequency.HALF_YEARLY;
    case 4:
    case "YEARLY":
      return RepaymentFrequency.YEARLY;
    case 5:
    case "CUMULATIVE":
      return RepaymentFrequency.CUMULATIVE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RepaymentFrequency.UNRECOGNIZED;
  }
}

export function repaymentFrequencyToJSON(object: RepaymentFrequency): string {
  switch (object) {
    case RepaymentFrequency.REPAYMENT_FREQUENCY_UNKNOWN:
      return "REPAYMENT_FREQUENCY_UNKNOWN";
    case RepaymentFrequency.MONTHLY:
      return "MONTHLY";
    case RepaymentFrequency.QUARTERLY:
      return "QUARTERLY";
    case RepaymentFrequency.HALF_YEARLY:
      return "HALF_YEARLY";
    case RepaymentFrequency.YEARLY:
      return "YEARLY";
    case RepaymentFrequency.CUMULATIVE:
      return "CUMULATIVE";
    case RepaymentFrequency.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum InvestabilityStatus {
  INVESTABILITY_STATUS_UNKNOWN = 0,
  LIVE = 1,
  SOLD_OUT = 2,
  COMING_SOON = 3,
  INACTIVE = 4,
  UNRECOGNIZED = -1,
}

export function investabilityStatusFromJSON(object: any): InvestabilityStatus {
  switch (object) {
    case 0:
    case "INVESTABILITY_STATUS_UNKNOWN":
      return InvestabilityStatus.INVESTABILITY_STATUS_UNKNOWN;
    case 1:
    case "LIVE":
      return InvestabilityStatus.LIVE;
    case 2:
    case "SOLD_OUT":
      return InvestabilityStatus.SOLD_OUT;
    case 3:
    case "COMING_SOON":
      return InvestabilityStatus.COMING_SOON;
    case 4:
    case "INACTIVE":
      return InvestabilityStatus.INACTIVE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return InvestabilityStatus.UNRECOGNIZED;
  }
}

export function investabilityStatusToJSON(object: InvestabilityStatus): string {
  switch (object) {
    case InvestabilityStatus.INVESTABILITY_STATUS_UNKNOWN:
      return "INVESTABILITY_STATUS_UNKNOWN";
    case InvestabilityStatus.LIVE:
      return "LIVE";
    case InvestabilityStatus.SOLD_OUT:
      return "SOLD_OUT";
    case InvestabilityStatus.COMING_SOON:
      return "COMING_SOON";
    case InvestabilityStatus.INACTIVE:
      return "INACTIVE";
    case InvestabilityStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum RepaymentType {
  REPAYMENT_TYPE_UNKNOWN = 0,
  INTEREST_PAYMENT = 1,
  PRINCIPAL_REPAYMENT = 2,
  PRINCIPAL_REPAYMENT_AND_INTEREST_PAYMENT = 3,
  UNRECOGNIZED = -1,
}

export function repaymentTypeFromJSON(object: any): RepaymentType {
  switch (object) {
    case 0:
    case "REPAYMENT_TYPE_UNKNOWN":
      return RepaymentType.REPAYMENT_TYPE_UNKNOWN;
    case 1:
    case "INTEREST_PAYMENT":
      return RepaymentType.INTEREST_PAYMENT;
    case 2:
    case "PRINCIPAL_REPAYMENT":
      return RepaymentType.PRINCIPAL_REPAYMENT;
    case 3:
    case "PRINCIPAL_REPAYMENT_AND_INTEREST_PAYMENT":
      return RepaymentType.PRINCIPAL_REPAYMENT_AND_INTEREST_PAYMENT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RepaymentType.UNRECOGNIZED;
  }
}

export function repaymentTypeToJSON(object: RepaymentType): string {
  switch (object) {
    case RepaymentType.REPAYMENT_TYPE_UNKNOWN:
      return "REPAYMENT_TYPE_UNKNOWN";
    case RepaymentType.INTEREST_PAYMENT:
      return "INTEREST_PAYMENT";
    case RepaymentType.PRINCIPAL_REPAYMENT:
      return "PRINCIPAL_REPAYMENT";
    case RepaymentType.PRINCIPAL_REPAYMENT_AND_INTEREST_PAYMENT:
      return "PRINCIPAL_REPAYMENT_AND_INTEREST_PAYMENT";
    case RepaymentType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum MediaType {
  UNKNOWN_MEDIA_TYPE = 0,
  IMAGE = 1,
  VIDEO = 2,
  UNRECOGNIZED = -1,
}

export function mediaTypeFromJSON(object: any): MediaType {
  switch (object) {
    case 0:
    case "UNKNOWN_MEDIA_TYPE":
      return MediaType.UNKNOWN_MEDIA_TYPE;
    case 1:
    case "IMAGE":
      return MediaType.IMAGE;
    case 2:
    case "VIDEO":
      return MediaType.VIDEO;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MediaType.UNRECOGNIZED;
  }
}

export function mediaTypeToJSON(object: MediaType): string {
  switch (object) {
    case MediaType.UNKNOWN_MEDIA_TYPE:
      return "UNKNOWN_MEDIA_TYPE";
    case MediaType.IMAGE:
      return "IMAGE";
    case MediaType.VIDEO:
      return "VIDEO";
    case MediaType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ScreenType {
  SCREEN_TYPE_UNKNOWN = 0,
  MOBILE = 1,
  DESKTOP = 2,
  TABLET = 3,
  UNRECOGNIZED = -1,
}

export function screenTypeFromJSON(object: any): ScreenType {
  switch (object) {
    case 0:
    case "SCREEN_TYPE_UNKNOWN":
      return ScreenType.SCREEN_TYPE_UNKNOWN;
    case 1:
    case "MOBILE":
      return ScreenType.MOBILE;
    case 2:
    case "DESKTOP":
      return ScreenType.DESKTOP;
    case 3:
    case "TABLET":
      return ScreenType.TABLET;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ScreenType.UNRECOGNIZED;
  }
}

export function screenTypeToJSON(object: ScreenType): string {
  switch (object) {
    case ScreenType.SCREEN_TYPE_UNKNOWN:
      return "SCREEN_TYPE_UNKNOWN";
    case ScreenType.MOBILE:
      return "MOBILE";
    case ScreenType.DESKTOP:
      return "DESKTOP";
    case ScreenType.TABLET:
      return "TABLET";
    case ScreenType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum BondMediaItemParentType {
  BOND_MEDIA_ITEM_PARENT_TYPE_UNKNOWN = 0,
  BOND_DETAIL = 1,
  BOND_ISSUING_INSTITUTION = 2,
  UNRECOGNIZED = -1,
}

export function bondMediaItemParentTypeFromJSON(object: any): BondMediaItemParentType {
  switch (object) {
    case 0:
    case "BOND_MEDIA_ITEM_PARENT_TYPE_UNKNOWN":
      return BondMediaItemParentType.BOND_MEDIA_ITEM_PARENT_TYPE_UNKNOWN;
    case 1:
    case "BOND_DETAIL":
      return BondMediaItemParentType.BOND_DETAIL;
    case 2:
    case "BOND_ISSUING_INSTITUTION":
      return BondMediaItemParentType.BOND_ISSUING_INSTITUTION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BondMediaItemParentType.UNRECOGNIZED;
  }
}

export function bondMediaItemParentTypeToJSON(object: BondMediaItemParentType): string {
  switch (object) {
    case BondMediaItemParentType.BOND_MEDIA_ITEM_PARENT_TYPE_UNKNOWN:
      return "BOND_MEDIA_ITEM_PARENT_TYPE_UNKNOWN";
    case BondMediaItemParentType.BOND_DETAIL:
      return "BOND_DETAIL";
    case BondMediaItemParentType.BOND_ISSUING_INSTITUTION:
      return "BOND_ISSUING_INSTITUTION";
    case BondMediaItemParentType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface MediaItem {
}

export enum MediaItem_MediaType {
  UNKNOWN_MEDIA_TYPE = 0,
  IMAGE = 1,
  VIDEO = 2,
  UNRECOGNIZED = -1,
}

export function mediaItem_MediaTypeFromJSON(object: any): MediaItem_MediaType {
  switch (object) {
    case 0:
    case "UNKNOWN_MEDIA_TYPE":
      return MediaItem_MediaType.UNKNOWN_MEDIA_TYPE;
    case 1:
    case "IMAGE":
      return MediaItem_MediaType.IMAGE;
    case 2:
    case "VIDEO":
      return MediaItem_MediaType.VIDEO;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MediaItem_MediaType.UNRECOGNIZED;
  }
}

export function mediaItem_MediaTypeToJSON(object: MediaItem_MediaType): string {
  switch (object) {
    case MediaItem_MediaType.UNKNOWN_MEDIA_TYPE:
      return "UNKNOWN_MEDIA_TYPE";
    case MediaItem_MediaType.IMAGE:
      return "IMAGE";
    case MediaItem_MediaType.VIDEO:
      return "VIDEO";
    case MediaItem_MediaType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum MediaItem_ScreenType {
  SCREEN_TYPE_UNKNOWN = 0,
  MOBILE = 1,
  DESKTOP = 2,
  TABLET = 3,
  UNRECOGNIZED = -1,
}

export function mediaItem_ScreenTypeFromJSON(object: any): MediaItem_ScreenType {
  switch (object) {
    case 0:
    case "SCREEN_TYPE_UNKNOWN":
      return MediaItem_ScreenType.SCREEN_TYPE_UNKNOWN;
    case 1:
    case "MOBILE":
      return MediaItem_ScreenType.MOBILE;
    case 2:
    case "DESKTOP":
      return MediaItem_ScreenType.DESKTOP;
    case 3:
    case "TABLET":
      return MediaItem_ScreenType.TABLET;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MediaItem_ScreenType.UNRECOGNIZED;
  }
}

export function mediaItem_ScreenTypeToJSON(object: MediaItem_ScreenType): string {
  switch (object) {
    case MediaItem_ScreenType.SCREEN_TYPE_UNKNOWN:
      return "SCREEN_TYPE_UNKNOWN";
    case MediaItem_ScreenType.MOBILE:
      return "MOBILE";
    case MediaItem_ScreenType.DESKTOP:
      return "DESKTOP";
    case MediaItem_ScreenType.TABLET:
      return "TABLET";
    case MediaItem_ScreenType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface BondIssuingInstitutionResponse {
  id: string;
  name: string;
  slug: string;
  description: string;
  sector: string;
  logoUrl: string;
  color: string;
  websiteUrl: string;
  createdAt: string;
  updatedAt: string;
  financialSnapshotUrl: string;
  coverImageUrl: string;
  gridCoverImageUrl: string;
  cin: string;
}

export interface BondIssuingInstitutionUpdateRequest {
  id: string;
  name: string;
  description: string;
  sector: string;
  websiteUrl: string;
  logoUrl: string;
  color: string;
  financialSnapshotUrl: string;
  coverImageUrl: string;
  gridCoverImageUrl: string;
}

export interface BondIssuingInstitutionUpdateResponse {
}

export interface BondOfferingDetail {
  id: string;
  dealType: string;
  bidOffer: string;
  bondDetailId: string;
  sellerId: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  minLotSize: number;
  bondSettlementType: number;
  partyId: string;
  yield: number;
  sellerOrderId: string;
  expiryTime: string;
  repeatUserDefaultQuantity: number;
  newUserDefaultQuantity: number;
  isTZeroSettlementSupported: boolean;
}

export interface BondOfferingDetailResponse {
  bondOfferingDetails: BondOfferingDetail[];
}

export interface BondInventoryResponse {
  id: string;
  bondOfferingDetailId: string;
  count?: string | undefined;
  createdAt: string;
  updatedAt: string;
  validFrom?: string | undefined;
  validTill?: string | undefined;
  isActive?: boolean | undefined;
  orderLimit?: string | undefined;
  alarmStatus?: InventoryAlarmStatus | undefined;
  maxCount?: string | undefined;
  totalCount?: string | undefined;
}

export interface BondDetailsDashboard {
  id: string;
  type?: string | undefined;
  issuer?: string | undefined;
  name?: string | undefined;
  couponRate?: string | undefined;
  putCall?: string | undefined;
  couponFrequency?: RepaymentFrequency | undefined;
  principalRepaymentFrequency?: RepaymentFrequency | undefined;
  principalRepaymentFrequencyDesc?: string | undefined;
  maturityDate?: string | undefined;
  displayTitle?: string | undefined;
  isinCode?: string | undefined;
  rating?: string | undefined;
  ratingAgency?: string | undefined;
  ratingSupportingUrl?: string | undefined;
  bondIssuingInstitutionId: string;
  bondType?: BondType | undefined;
  typeOfYield?: string | undefined;
  isActive?: boolean | undefined;
  natureOfBond?: string | undefined;
  issueSize?: string | undefined;
  issueDate?: string | undefined;
  issuePrice?: string | undefined;
  issueFaceValue?: string | undefined;
  createdAt: string;
  updatedAt?: string | undefined;
  bgColor?: string | undefined;
  informationMemorandum?: string | undefined;
  debuntureTrustee?: string | undefined;
  ratingDate?: string | undefined;
  issueMode?: string | undefined;
  nature?: string | undefined;
  seniority?: string | undefined;
  couponType?: string | undefined;
  yield?: string | undefined;
  perUserPurchaseLimit?: string | undefined;
  minLotSize?: string | undefined;
  investabilityStatus?: InvestabilityStatus | undefined;
  isTZeroSettlementSupported?: boolean | undefined;
  includeInCollection?: boolean | undefined;
  blockTrade?: boolean | undefined;
  sellSpread?: string | undefined;
  includeInSearch?: boolean | undefined;
  buyerPartyId?: string | undefined;
  dailySellLimit?: string | undefined;
}

export interface GetAllBondsRequest {
  page: number;
  size: number;
  searchString: string;
}

export interface GetAllBondsResponse {
  bondDetails: BondDetailsDashboard[];
  hasNextPage: boolean;
}

export interface BondDetailsUpdateRequest {
  id: string;
  type?: string | undefined;
  issuer?: string | undefined;
  name?: string | undefined;
  couponRate?: string | undefined;
  putCall?: string | undefined;
  couponFrequency?: RepaymentFrequency | undefined;
  principalRepaymentFrequency?: RepaymentFrequency | undefined;
  principalRepaymentFrequencyDesc?: string | undefined;
  maturityDate?: string | undefined;
  displayTitle?: string | undefined;
  isinCode?: string | undefined;
  rating?: string | undefined;
  ratingAgency?: string | undefined;
  ratingSupportingUrl?: string | undefined;
  bondType?: BondType | undefined;
  typeOfYield?: string | undefined;
  isActive?: boolean | undefined;
  natureOfBond?: string | undefined;
  issueSize?: string | undefined;
  issueDate?: string | undefined;
  issuePrice?: string | undefined;
  issueFaceValue?: string | undefined;
  bgColor?: string | undefined;
  informationMemorandum?: string | undefined;
  debuntureTrustee?: string | undefined;
  ratingDate?: string | undefined;
  issueMode?: string | undefined;
  nature?: string | undefined;
  seniority?: string | undefined;
  couponType?: string | undefined;
  yield?: string | undefined;
  perUserPurchaseLimit?: string | undefined;
  minLotSize?: string | undefined;
  investabilityStatus?: InvestabilityStatus | undefined;
  isTZeroSettlementSupported?: boolean | undefined;
  includeInCollection?: boolean | undefined;
  blockTrade?: boolean | undefined;
  sellSpread?: string | undefined;
  includeInSearch?: boolean | undefined;
  buyerPartyId?: string | undefined;
  dailySellLimit?: string | undefined;
}

export interface BondDetailsUpdateResponse {
}

export interface AllISINSRequest {
}

export interface BondIssuingInstitution {
  id: string;
  name: string;
}

export interface AllISINSResponse {
  bondIssuingInstitutions: BondIssuingInstitution[];
}

export interface PartyDetailsRequest {
}

export interface PartyDetail {
  id: string;
  name: string;
}

export interface PartyDetailsResponse {
  partyDetails: PartyDetail[];
}

export interface MediaItemRequest {
  id: string;
  parentType: BondMediaItemParentType;
}

export interface MediaItemDashboard {
  id: string;
  parentId: string;
  parentType: BondMediaItemParentType;
  section: string;
  mediaType: MediaType;
  url: string;
  screenType: ScreenType;
  redirectDeeplink: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

export interface MediaItemResponse {
  mediaItems: MediaItemDashboard[];
}

export interface MediaItemUpdateRequest {
  id: string;
  parentType: BondMediaItemParentType;
  section: string;
  mediaType: MediaType;
  mediaUrl: string;
  screenType: ScreenType;
  redirectDeeplink: string;
  isActive: boolean;
}

export interface MediaItemUpdateResponse {
}

export interface BondDetailRequest {
  isisnCode: string;
}

export interface AllBond {
  id: string;
  displayTitle: string;
  isActive: boolean;
  name: string;
  investabilityStatus: InvestabilityStatus;
  isinCode: string;
  maturityDate: string;
  couponRate: number;
  couponFrequency: RepaymentFrequency;
  perUserPurchaseLimit: number;
  bondType: BondType;
  includeInCollection: boolean;
  isSoldOut: boolean;
  recordDate: string;
  isRecordDateVerified: boolean;
  bondCashflowScheduleId: string;
}

export interface AllBondDetailsRequest {
}

export interface AllBondDetailsResponse {
  bondDetails: AllBond[];
}

export interface UpdateOfferingActiveStatusRequest {
  id: string;
  isActive: boolean;
}

export interface UpdateOfferingActiveStatusResponse {
}

export interface UpdateBondOfferingYieldRequest {
  bondOfferingId: string;
  yield: number;
}

export interface UpdateBondOfferingYieldResponse {
}

export interface BondCashflowSchedule {
  id: string;
  date: string;
  interestPaymentAmount: number;
  principalRepaymentAmount: number;
  recordDate: string;
  type: RepaymentType;
  faceValue: number;
  isActive: boolean;
  maturityAmount: number;
  isRecordDateVerified: boolean;
}

export interface GetBondCashflowScheduleRequest {
  bondDetailId: string;
}

export interface GetBondCashflowScheduleResponse {
  bondCashflowSchedule: BondCashflowSchedule[];
}

export interface CreateTagRequest {
  name: string;
  color: string;
  bgColor: string;
  shimmerColor: string;
}

export interface CreateTagResponse {
}

export interface GetAllTagsRequest {
}

export interface Tag {
  id: string;
  name: string;
  color: string;
  bgColor: string;
  shimmerColor: string;
}

export interface GetAllTagsResponse {
  tag: Tag[];
}

export interface TagCollectionItem {
  id: string;
  tagId: string;
  collectionId: string;
  collectionItemIsinCode: string;
  tagName: string;
}

export interface CreateTagCollectionItemRequest {
  tagId: string;
  collectionId: string;
  collectionItemIsinCode: string;
}

export interface CreateTagCollectionItemResponse {
}

export interface GetAllTagCollectionItemRequest {
}

export interface GetAllTagCollectionItemResponse {
  tagCollectionItem: TagCollectionItem[];
}

export interface DeleteTagCollectionItemRequest {
  id: string;
}

export interface DeleteTagCollectionItemResponse {
}

export interface UpdateTagResponse {
}

export interface getTagRequest {
  tagId: string;
}

export interface BondOfferingUpdateRequest {
  bondOfferingId: string;
  yield: number;
  minLotSize: string;
  repeatUserDefaultQuantity: string;
  dealType: string;
  bidOffer: string;
  bondSettlementType: string;
  expiryTime: string;
  newUserDefaultQuantity: string;
  isTZeroSettlementSupported: boolean;
}

export interface BondOfferingUpdateResponse {
}

export interface GetBondPricesRequest {
  bondOfferingId: string;
}

export interface BondPrice {
  id: string;
  accruedInterest: number;
  cleanPrice: number;
  dirtyPrice: number;
  faceValue: number;
  isActive: boolean;
  settlementDate: string;
}

export interface GetBondPricesResponse {
  bondPrices: BondPrice[];
}

export interface UpdateBondInventoryRequest {
  bondOfferingId: string;
  maxCount: string;
  orderLimit: string;
  alarmStatus: InventoryAlarmStatus;
  validFrom: string;
  validTill: string;
  isActive: boolean;
  countDifference: string;
  totalCountDifference: string;
}

export interface UpdateBondInventoryResponse {
}

export interface CreateMediaItemRequest {
  parentId?: string | undefined;
  parentType?: BondMediaItemParentType | undefined;
  section?: string | undefined;
  mediaType?: MediaItem_MediaType | undefined;
  mediaUrl?: string | undefined;
  screenType?: MediaItem_ScreenType | undefined;
  redirectDeeplink?: string | undefined;
}

export interface CreateMediaItemResponse {
}

export interface VerifyRecordDateRequest {
  bondDetailId: string;
  cashflowScheduleId: string;
}

export interface VerifyRecordDateResponse {
}

export interface GetMediaItemRequest {
  mediaItemId: string;
}

export interface GetMediaItemResponse {
  mediaItem: MediaItemDashboard | undefined;
}

export interface UpdateRecordDateRequest {
  cashflowScheduleId?: string | undefined;
  recordDate?: string | undefined;
}

export interface UpdateRecordDateResponse {
}

function createBaseMediaItem(): MediaItem {
  return {};
}

export const MediaItem: MessageFns<MediaItem> = {
  encode(_: MediaItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MediaItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMediaItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): MediaItem {
    return {};
  },

  toJSON(_: MediaItem): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<MediaItem>, I>>(base?: I): MediaItem {
    return MediaItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MediaItem>, I>>(_: I): MediaItem {
    const message = createBaseMediaItem();
    return message;
  },
};

function createBaseBondIssuingInstitutionResponse(): BondIssuingInstitutionResponse {
  return {
    id: "",
    name: "",
    slug: "",
    description: "",
    sector: "",
    logoUrl: "",
    color: "",
    websiteUrl: "",
    createdAt: "",
    updatedAt: "",
    financialSnapshotUrl: "",
    coverImageUrl: "",
    gridCoverImageUrl: "",
    cin: "",
  };
}

export const BondIssuingInstitutionResponse: MessageFns<BondIssuingInstitutionResponse> = {
  encode(message: BondIssuingInstitutionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.slug !== "") {
      writer.uint32(26).string(message.slug);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.sector !== "") {
      writer.uint32(42).string(message.sector);
    }
    if (message.logoUrl !== "") {
      writer.uint32(50).string(message.logoUrl);
    }
    if (message.color !== "") {
      writer.uint32(58).string(message.color);
    }
    if (message.websiteUrl !== "") {
      writer.uint32(66).string(message.websiteUrl);
    }
    if (message.createdAt !== "") {
      writer.uint32(74).string(message.createdAt);
    }
    if (message.updatedAt !== "") {
      writer.uint32(82).string(message.updatedAt);
    }
    if (message.financialSnapshotUrl !== "") {
      writer.uint32(90).string(message.financialSnapshotUrl);
    }
    if (message.coverImageUrl !== "") {
      writer.uint32(98).string(message.coverImageUrl);
    }
    if (message.gridCoverImageUrl !== "") {
      writer.uint32(106).string(message.gridCoverImageUrl);
    }
    if (message.cin !== "") {
      writer.uint32(114).string(message.cin);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondIssuingInstitutionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondIssuingInstitutionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.slug = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.sector = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.websiteUrl = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.createdAt = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.updatedAt = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.financialSnapshotUrl = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.coverImageUrl = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.gridCoverImageUrl = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.cin = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondIssuingInstitutionResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      slug: isSet(object.slug) ? globalThis.String(object.slug) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      sector: isSet(object.sector) ? globalThis.String(object.sector) : "",
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      websiteUrl: isSet(object.websiteUrl) ? globalThis.String(object.websiteUrl) : "",
      createdAt: isSet(object.createdAt) ? globalThis.String(object.createdAt) : "",
      updatedAt: isSet(object.updatedAt) ? globalThis.String(object.updatedAt) : "",
      financialSnapshotUrl: isSet(object.financialSnapshotUrl) ? globalThis.String(object.financialSnapshotUrl) : "",
      coverImageUrl: isSet(object.coverImageUrl) ? globalThis.String(object.coverImageUrl) : "",
      gridCoverImageUrl: isSet(object.gridCoverImageUrl) ? globalThis.String(object.gridCoverImageUrl) : "",
      cin: isSet(object.cin) ? globalThis.String(object.cin) : "",
    };
  },

  toJSON(message: BondIssuingInstitutionResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.slug !== "") {
      obj.slug = message.slug;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.sector !== "") {
      obj.sector = message.sector;
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.websiteUrl !== "") {
      obj.websiteUrl = message.websiteUrl;
    }
    if (message.createdAt !== "") {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== "") {
      obj.updatedAt = message.updatedAt;
    }
    if (message.financialSnapshotUrl !== "") {
      obj.financialSnapshotUrl = message.financialSnapshotUrl;
    }
    if (message.coverImageUrl !== "") {
      obj.coverImageUrl = message.coverImageUrl;
    }
    if (message.gridCoverImageUrl !== "") {
      obj.gridCoverImageUrl = message.gridCoverImageUrl;
    }
    if (message.cin !== "") {
      obj.cin = message.cin;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondIssuingInstitutionResponse>, I>>(base?: I): BondIssuingInstitutionResponse {
    return BondIssuingInstitutionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondIssuingInstitutionResponse>, I>>(
    object: I,
  ): BondIssuingInstitutionResponse {
    const message = createBaseBondIssuingInstitutionResponse();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.slug = object.slug ?? "";
    message.description = object.description ?? "";
    message.sector = object.sector ?? "";
    message.logoUrl = object.logoUrl ?? "";
    message.color = object.color ?? "";
    message.websiteUrl = object.websiteUrl ?? "";
    message.createdAt = object.createdAt ?? "";
    message.updatedAt = object.updatedAt ?? "";
    message.financialSnapshotUrl = object.financialSnapshotUrl ?? "";
    message.coverImageUrl = object.coverImageUrl ?? "";
    message.gridCoverImageUrl = object.gridCoverImageUrl ?? "";
    message.cin = object.cin ?? "";
    return message;
  },
};

function createBaseBondIssuingInstitutionUpdateRequest(): BondIssuingInstitutionUpdateRequest {
  return {
    id: "",
    name: "",
    description: "",
    sector: "",
    websiteUrl: "",
    logoUrl: "",
    color: "",
    financialSnapshotUrl: "",
    coverImageUrl: "",
    gridCoverImageUrl: "",
  };
}

export const BondIssuingInstitutionUpdateRequest: MessageFns<BondIssuingInstitutionUpdateRequest> = {
  encode(message: BondIssuingInstitutionUpdateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.sector !== "") {
      writer.uint32(34).string(message.sector);
    }
    if (message.websiteUrl !== "") {
      writer.uint32(42).string(message.websiteUrl);
    }
    if (message.logoUrl !== "") {
      writer.uint32(50).string(message.logoUrl);
    }
    if (message.color !== "") {
      writer.uint32(58).string(message.color);
    }
    if (message.financialSnapshotUrl !== "") {
      writer.uint32(66).string(message.financialSnapshotUrl);
    }
    if (message.coverImageUrl !== "") {
      writer.uint32(74).string(message.coverImageUrl);
    }
    if (message.gridCoverImageUrl !== "") {
      writer.uint32(82).string(message.gridCoverImageUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondIssuingInstitutionUpdateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondIssuingInstitutionUpdateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.sector = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.websiteUrl = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.financialSnapshotUrl = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.coverImageUrl = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.gridCoverImageUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondIssuingInstitutionUpdateRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      sector: isSet(object.sector) ? globalThis.String(object.sector) : "",
      websiteUrl: isSet(object.websiteUrl) ? globalThis.String(object.websiteUrl) : "",
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      financialSnapshotUrl: isSet(object.financialSnapshotUrl) ? globalThis.String(object.financialSnapshotUrl) : "",
      coverImageUrl: isSet(object.coverImageUrl) ? globalThis.String(object.coverImageUrl) : "",
      gridCoverImageUrl: isSet(object.gridCoverImageUrl) ? globalThis.String(object.gridCoverImageUrl) : "",
    };
  },

  toJSON(message: BondIssuingInstitutionUpdateRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.sector !== "") {
      obj.sector = message.sector;
    }
    if (message.websiteUrl !== "") {
      obj.websiteUrl = message.websiteUrl;
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.financialSnapshotUrl !== "") {
      obj.financialSnapshotUrl = message.financialSnapshotUrl;
    }
    if (message.coverImageUrl !== "") {
      obj.coverImageUrl = message.coverImageUrl;
    }
    if (message.gridCoverImageUrl !== "") {
      obj.gridCoverImageUrl = message.gridCoverImageUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondIssuingInstitutionUpdateRequest>, I>>(
    base?: I,
  ): BondIssuingInstitutionUpdateRequest {
    return BondIssuingInstitutionUpdateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondIssuingInstitutionUpdateRequest>, I>>(
    object: I,
  ): BondIssuingInstitutionUpdateRequest {
    const message = createBaseBondIssuingInstitutionUpdateRequest();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.description = object.description ?? "";
    message.sector = object.sector ?? "";
    message.websiteUrl = object.websiteUrl ?? "";
    message.logoUrl = object.logoUrl ?? "";
    message.color = object.color ?? "";
    message.financialSnapshotUrl = object.financialSnapshotUrl ?? "";
    message.coverImageUrl = object.coverImageUrl ?? "";
    message.gridCoverImageUrl = object.gridCoverImageUrl ?? "";
    return message;
  },
};

function createBaseBondIssuingInstitutionUpdateResponse(): BondIssuingInstitutionUpdateResponse {
  return {};
}

export const BondIssuingInstitutionUpdateResponse: MessageFns<BondIssuingInstitutionUpdateResponse> = {
  encode(_: BondIssuingInstitutionUpdateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondIssuingInstitutionUpdateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondIssuingInstitutionUpdateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): BondIssuingInstitutionUpdateResponse {
    return {};
  },

  toJSON(_: BondIssuingInstitutionUpdateResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<BondIssuingInstitutionUpdateResponse>, I>>(
    base?: I,
  ): BondIssuingInstitutionUpdateResponse {
    return BondIssuingInstitutionUpdateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondIssuingInstitutionUpdateResponse>, I>>(
    _: I,
  ): BondIssuingInstitutionUpdateResponse {
    const message = createBaseBondIssuingInstitutionUpdateResponse();
    return message;
  },
};

function createBaseBondOfferingDetail(): BondOfferingDetail {
  return {
    id: "",
    dealType: "",
    bidOffer: "",
    bondDetailId: "",
    sellerId: "",
    createdAt: "",
    updatedAt: "",
    isActive: false,
    minLotSize: 0,
    bondSettlementType: 0,
    partyId: "",
    yield: 0,
    sellerOrderId: "",
    expiryTime: "",
    repeatUserDefaultQuantity: 0,
    newUserDefaultQuantity: 0,
    isTZeroSettlementSupported: false,
  };
}

export const BondOfferingDetail: MessageFns<BondOfferingDetail> = {
  encode(message: BondOfferingDetail, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.dealType !== "") {
      writer.uint32(18).string(message.dealType);
    }
    if (message.bidOffer !== "") {
      writer.uint32(26).string(message.bidOffer);
    }
    if (message.bondDetailId !== "") {
      writer.uint32(34).string(message.bondDetailId);
    }
    if (message.sellerId !== "") {
      writer.uint32(42).string(message.sellerId);
    }
    if (message.createdAt !== "") {
      writer.uint32(50).string(message.createdAt);
    }
    if (message.updatedAt !== "") {
      writer.uint32(58).string(message.updatedAt);
    }
    if (message.isActive !== false) {
      writer.uint32(64).bool(message.isActive);
    }
    if (message.minLotSize !== 0) {
      writer.uint32(72).int32(message.minLotSize);
    }
    if (message.bondSettlementType !== 0) {
      writer.uint32(80).int32(message.bondSettlementType);
    }
    if (message.partyId !== "") {
      writer.uint32(90).string(message.partyId);
    }
    if (message.yield !== 0) {
      writer.uint32(97).double(message.yield);
    }
    if (message.sellerOrderId !== "") {
      writer.uint32(106).string(message.sellerOrderId);
    }
    if (message.expiryTime !== "") {
      writer.uint32(114).string(message.expiryTime);
    }
    if (message.repeatUserDefaultQuantity !== 0) {
      writer.uint32(120).int32(message.repeatUserDefaultQuantity);
    }
    if (message.newUserDefaultQuantity !== 0) {
      writer.uint32(128).int32(message.newUserDefaultQuantity);
    }
    if (message.isTZeroSettlementSupported !== false) {
      writer.uint32(136).bool(message.isTZeroSettlementSupported);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondOfferingDetail {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondOfferingDetail();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.dealType = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bidOffer = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bondDetailId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.sellerId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.createdAt = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.updatedAt = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.minLotSize = reader.int32();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.bondSettlementType = reader.int32();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.partyId = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.yield = reader.double();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.sellerOrderId = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.expiryTime = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.repeatUserDefaultQuantity = reader.int32();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.newUserDefaultQuantity = reader.int32();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.isTZeroSettlementSupported = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondOfferingDetail {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      dealType: isSet(object.dealType) ? globalThis.String(object.dealType) : "",
      bidOffer: isSet(object.bidOffer) ? globalThis.String(object.bidOffer) : "",
      bondDetailId: isSet(object.bondDetailId) ? globalThis.String(object.bondDetailId) : "",
      sellerId: isSet(object.sellerId) ? globalThis.String(object.sellerId) : "",
      createdAt: isSet(object.createdAt) ? globalThis.String(object.createdAt) : "",
      updatedAt: isSet(object.updatedAt) ? globalThis.String(object.updatedAt) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      minLotSize: isSet(object.minLotSize) ? globalThis.Number(object.minLotSize) : 0,
      bondSettlementType: isSet(object.bondSettlementType) ? globalThis.Number(object.bondSettlementType) : 0,
      partyId: isSet(object.partyId) ? globalThis.String(object.partyId) : "",
      yield: isSet(object.yield) ? globalThis.Number(object.yield) : 0,
      sellerOrderId: isSet(object.sellerOrderId) ? globalThis.String(object.sellerOrderId) : "",
      expiryTime: isSet(object.expiryTime) ? globalThis.String(object.expiryTime) : "",
      repeatUserDefaultQuantity: isSet(object.repeatUserDefaultQuantity)
        ? globalThis.Number(object.repeatUserDefaultQuantity)
        : 0,
      newUserDefaultQuantity: isSet(object.newUserDefaultQuantity)
        ? globalThis.Number(object.newUserDefaultQuantity)
        : 0,
      isTZeroSettlementSupported: isSet(object.isTZeroSettlementSupported)
        ? globalThis.Boolean(object.isTZeroSettlementSupported)
        : false,
    };
  },

  toJSON(message: BondOfferingDetail): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.dealType !== "") {
      obj.dealType = message.dealType;
    }
    if (message.bidOffer !== "") {
      obj.bidOffer = message.bidOffer;
    }
    if (message.bondDetailId !== "") {
      obj.bondDetailId = message.bondDetailId;
    }
    if (message.sellerId !== "") {
      obj.sellerId = message.sellerId;
    }
    if (message.createdAt !== "") {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== "") {
      obj.updatedAt = message.updatedAt;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.minLotSize !== 0) {
      obj.minLotSize = Math.round(message.minLotSize);
    }
    if (message.bondSettlementType !== 0) {
      obj.bondSettlementType = Math.round(message.bondSettlementType);
    }
    if (message.partyId !== "") {
      obj.partyId = message.partyId;
    }
    if (message.yield !== 0) {
      obj.yield = message.yield;
    }
    if (message.sellerOrderId !== "") {
      obj.sellerOrderId = message.sellerOrderId;
    }
    if (message.expiryTime !== "") {
      obj.expiryTime = message.expiryTime;
    }
    if (message.repeatUserDefaultQuantity !== 0) {
      obj.repeatUserDefaultQuantity = Math.round(message.repeatUserDefaultQuantity);
    }
    if (message.newUserDefaultQuantity !== 0) {
      obj.newUserDefaultQuantity = Math.round(message.newUserDefaultQuantity);
    }
    if (message.isTZeroSettlementSupported !== false) {
      obj.isTZeroSettlementSupported = message.isTZeroSettlementSupported;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondOfferingDetail>, I>>(base?: I): BondOfferingDetail {
    return BondOfferingDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondOfferingDetail>, I>>(object: I): BondOfferingDetail {
    const message = createBaseBondOfferingDetail();
    message.id = object.id ?? "";
    message.dealType = object.dealType ?? "";
    message.bidOffer = object.bidOffer ?? "";
    message.bondDetailId = object.bondDetailId ?? "";
    message.sellerId = object.sellerId ?? "";
    message.createdAt = object.createdAt ?? "";
    message.updatedAt = object.updatedAt ?? "";
    message.isActive = object.isActive ?? false;
    message.minLotSize = object.minLotSize ?? 0;
    message.bondSettlementType = object.bondSettlementType ?? 0;
    message.partyId = object.partyId ?? "";
    message.yield = object.yield ?? 0;
    message.sellerOrderId = object.sellerOrderId ?? "";
    message.expiryTime = object.expiryTime ?? "";
    message.repeatUserDefaultQuantity = object.repeatUserDefaultQuantity ?? 0;
    message.newUserDefaultQuantity = object.newUserDefaultQuantity ?? 0;
    message.isTZeroSettlementSupported = object.isTZeroSettlementSupported ?? false;
    return message;
  },
};

function createBaseBondOfferingDetailResponse(): BondOfferingDetailResponse {
  return { bondOfferingDetails: [] };
}

export const BondOfferingDetailResponse: MessageFns<BondOfferingDetailResponse> = {
  encode(message: BondOfferingDetailResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bondOfferingDetails) {
      BondOfferingDetail.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondOfferingDetailResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondOfferingDetailResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondOfferingDetails.push(BondOfferingDetail.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondOfferingDetailResponse {
    return {
      bondOfferingDetails: globalThis.Array.isArray(object?.bondOfferingDetails)
        ? object.bondOfferingDetails.map((e: any) => BondOfferingDetail.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BondOfferingDetailResponse): unknown {
    const obj: any = {};
    if (message.bondOfferingDetails?.length) {
      obj.bondOfferingDetails = message.bondOfferingDetails.map((e) => BondOfferingDetail.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondOfferingDetailResponse>, I>>(base?: I): BondOfferingDetailResponse {
    return BondOfferingDetailResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondOfferingDetailResponse>, I>>(object: I): BondOfferingDetailResponse {
    const message = createBaseBondOfferingDetailResponse();
    message.bondOfferingDetails = object.bondOfferingDetails?.map((e) => BondOfferingDetail.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBondInventoryResponse(): BondInventoryResponse {
  return {
    id: "",
    bondOfferingDetailId: "",
    count: undefined,
    createdAt: "",
    updatedAt: "",
    validFrom: undefined,
    validTill: undefined,
    isActive: undefined,
    orderLimit: undefined,
    alarmStatus: undefined,
    maxCount: undefined,
    totalCount: undefined,
  };
}

export const BondInventoryResponse: MessageFns<BondInventoryResponse> = {
  encode(message: BondInventoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.bondOfferingDetailId !== "") {
      writer.uint32(18).string(message.bondOfferingDetailId);
    }
    if (message.count !== undefined) {
      writer.uint32(26).string(message.count);
    }
    if (message.createdAt !== "") {
      writer.uint32(34).string(message.createdAt);
    }
    if (message.updatedAt !== "") {
      writer.uint32(42).string(message.updatedAt);
    }
    if (message.validFrom !== undefined) {
      writer.uint32(50).string(message.validFrom);
    }
    if (message.validTill !== undefined) {
      writer.uint32(58).string(message.validTill);
    }
    if (message.isActive !== undefined) {
      writer.uint32(64).bool(message.isActive);
    }
    if (message.orderLimit !== undefined) {
      writer.uint32(74).string(message.orderLimit);
    }
    if (message.alarmStatus !== undefined) {
      writer.uint32(80).int32(message.alarmStatus);
    }
    if (message.maxCount !== undefined) {
      writer.uint32(90).string(message.maxCount);
    }
    if (message.totalCount !== undefined) {
      writer.uint32(98).string(message.totalCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondInventoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondInventoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bondOfferingDetailId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.count = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.createdAt = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.updatedAt = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.validFrom = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.validTill = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.orderLimit = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.alarmStatus = reader.int32() as any;
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.maxCount = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.totalCount = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondInventoryResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      bondOfferingDetailId: isSet(object.bondOfferingDetailId) ? globalThis.String(object.bondOfferingDetailId) : "",
      count: isSet(object.count) ? globalThis.String(object.count) : undefined,
      createdAt: isSet(object.createdAt) ? globalThis.String(object.createdAt) : "",
      updatedAt: isSet(object.updatedAt) ? globalThis.String(object.updatedAt) : "",
      validFrom: isSet(object.validFrom) ? globalThis.String(object.validFrom) : undefined,
      validTill: isSet(object.validTill) ? globalThis.String(object.validTill) : undefined,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : undefined,
      orderLimit: isSet(object.orderLimit) ? globalThis.String(object.orderLimit) : undefined,
      alarmStatus: isSet(object.alarmStatus) ? inventoryAlarmStatusFromJSON(object.alarmStatus) : undefined,
      maxCount: isSet(object.maxCount) ? globalThis.String(object.maxCount) : undefined,
      totalCount: isSet(object.totalCount) ? globalThis.String(object.totalCount) : undefined,
    };
  },

  toJSON(message: BondInventoryResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.bondOfferingDetailId !== "") {
      obj.bondOfferingDetailId = message.bondOfferingDetailId;
    }
    if (message.count !== undefined) {
      obj.count = message.count;
    }
    if (message.createdAt !== "") {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== "") {
      obj.updatedAt = message.updatedAt;
    }
    if (message.validFrom !== undefined) {
      obj.validFrom = message.validFrom;
    }
    if (message.validTill !== undefined) {
      obj.validTill = message.validTill;
    }
    if (message.isActive !== undefined) {
      obj.isActive = message.isActive;
    }
    if (message.orderLimit !== undefined) {
      obj.orderLimit = message.orderLimit;
    }
    if (message.alarmStatus !== undefined) {
      obj.alarmStatus = inventoryAlarmStatusToJSON(message.alarmStatus);
    }
    if (message.maxCount !== undefined) {
      obj.maxCount = message.maxCount;
    }
    if (message.totalCount !== undefined) {
      obj.totalCount = message.totalCount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondInventoryResponse>, I>>(base?: I): BondInventoryResponse {
    return BondInventoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondInventoryResponse>, I>>(object: I): BondInventoryResponse {
    const message = createBaseBondInventoryResponse();
    message.id = object.id ?? "";
    message.bondOfferingDetailId = object.bondOfferingDetailId ?? "";
    message.count = object.count ?? undefined;
    message.createdAt = object.createdAt ?? "";
    message.updatedAt = object.updatedAt ?? "";
    message.validFrom = object.validFrom ?? undefined;
    message.validTill = object.validTill ?? undefined;
    message.isActive = object.isActive ?? undefined;
    message.orderLimit = object.orderLimit ?? undefined;
    message.alarmStatus = object.alarmStatus ?? undefined;
    message.maxCount = object.maxCount ?? undefined;
    message.totalCount = object.totalCount ?? undefined;
    return message;
  },
};

function createBaseBondDetailsDashboard(): BondDetailsDashboard {
  return {
    id: "",
    type: undefined,
    issuer: undefined,
    name: undefined,
    couponRate: undefined,
    putCall: undefined,
    couponFrequency: undefined,
    principalRepaymentFrequency: undefined,
    principalRepaymentFrequencyDesc: undefined,
    maturityDate: undefined,
    displayTitle: undefined,
    isinCode: undefined,
    rating: undefined,
    ratingAgency: undefined,
    ratingSupportingUrl: undefined,
    bondIssuingInstitutionId: "",
    bondType: undefined,
    typeOfYield: undefined,
    isActive: undefined,
    natureOfBond: undefined,
    issueSize: undefined,
    issueDate: undefined,
    issuePrice: undefined,
    issueFaceValue: undefined,
    createdAt: "",
    updatedAt: undefined,
    bgColor: undefined,
    informationMemorandum: undefined,
    debuntureTrustee: undefined,
    ratingDate: undefined,
    issueMode: undefined,
    nature: undefined,
    seniority: undefined,
    couponType: undefined,
    yield: undefined,
    perUserPurchaseLimit: undefined,
    minLotSize: undefined,
    investabilityStatus: undefined,
    isTZeroSettlementSupported: undefined,
    includeInCollection: undefined,
    blockTrade: undefined,
    sellSpread: undefined,
    includeInSearch: undefined,
    buyerPartyId: undefined,
    dailySellLimit: undefined,
  };
}

export const BondDetailsDashboard: MessageFns<BondDetailsDashboard> = {
  encode(message: BondDetailsDashboard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.type !== undefined) {
      writer.uint32(18).string(message.type);
    }
    if (message.issuer !== undefined) {
      writer.uint32(26).string(message.issuer);
    }
    if (message.name !== undefined) {
      writer.uint32(34).string(message.name);
    }
    if (message.couponRate !== undefined) {
      writer.uint32(42).string(message.couponRate);
    }
    if (message.putCall !== undefined) {
      writer.uint32(50).string(message.putCall);
    }
    if (message.couponFrequency !== undefined) {
      writer.uint32(56).int32(message.couponFrequency);
    }
    if (message.principalRepaymentFrequency !== undefined) {
      writer.uint32(64).int32(message.principalRepaymentFrequency);
    }
    if (message.principalRepaymentFrequencyDesc !== undefined) {
      writer.uint32(74).string(message.principalRepaymentFrequencyDesc);
    }
    if (message.maturityDate !== undefined) {
      writer.uint32(82).string(message.maturityDate);
    }
    if (message.displayTitle !== undefined) {
      writer.uint32(90).string(message.displayTitle);
    }
    if (message.isinCode !== undefined) {
      writer.uint32(98).string(message.isinCode);
    }
    if (message.rating !== undefined) {
      writer.uint32(106).string(message.rating);
    }
    if (message.ratingAgency !== undefined) {
      writer.uint32(114).string(message.ratingAgency);
    }
    if (message.ratingSupportingUrl !== undefined) {
      writer.uint32(122).string(message.ratingSupportingUrl);
    }
    if (message.bondIssuingInstitutionId !== "") {
      writer.uint32(130).string(message.bondIssuingInstitutionId);
    }
    if (message.bondType !== undefined) {
      writer.uint32(136).int32(message.bondType);
    }
    if (message.typeOfYield !== undefined) {
      writer.uint32(146).string(message.typeOfYield);
    }
    if (message.isActive !== undefined) {
      writer.uint32(152).bool(message.isActive);
    }
    if (message.natureOfBond !== undefined) {
      writer.uint32(162).string(message.natureOfBond);
    }
    if (message.issueSize !== undefined) {
      writer.uint32(170).string(message.issueSize);
    }
    if (message.issueDate !== undefined) {
      writer.uint32(178).string(message.issueDate);
    }
    if (message.issuePrice !== undefined) {
      writer.uint32(186).string(message.issuePrice);
    }
    if (message.issueFaceValue !== undefined) {
      writer.uint32(194).string(message.issueFaceValue);
    }
    if (message.createdAt !== "") {
      writer.uint32(202).string(message.createdAt);
    }
    if (message.updatedAt !== undefined) {
      writer.uint32(210).string(message.updatedAt);
    }
    if (message.bgColor !== undefined) {
      writer.uint32(218).string(message.bgColor);
    }
    if (message.informationMemorandum !== undefined) {
      writer.uint32(226).string(message.informationMemorandum);
    }
    if (message.debuntureTrustee !== undefined) {
      writer.uint32(234).string(message.debuntureTrustee);
    }
    if (message.ratingDate !== undefined) {
      writer.uint32(242).string(message.ratingDate);
    }
    if (message.issueMode !== undefined) {
      writer.uint32(250).string(message.issueMode);
    }
    if (message.nature !== undefined) {
      writer.uint32(258).string(message.nature);
    }
    if (message.seniority !== undefined) {
      writer.uint32(266).string(message.seniority);
    }
    if (message.couponType !== undefined) {
      writer.uint32(274).string(message.couponType);
    }
    if (message.yield !== undefined) {
      writer.uint32(282).string(message.yield);
    }
    if (message.perUserPurchaseLimit !== undefined) {
      writer.uint32(290).string(message.perUserPurchaseLimit);
    }
    if (message.minLotSize !== undefined) {
      writer.uint32(298).string(message.minLotSize);
    }
    if (message.investabilityStatus !== undefined) {
      writer.uint32(304).int32(message.investabilityStatus);
    }
    if (message.isTZeroSettlementSupported !== undefined) {
      writer.uint32(312).bool(message.isTZeroSettlementSupported);
    }
    if (message.includeInCollection !== undefined) {
      writer.uint32(320).bool(message.includeInCollection);
    }
    if (message.blockTrade !== undefined) {
      writer.uint32(328).bool(message.blockTrade);
    }
    if (message.sellSpread !== undefined) {
      writer.uint32(338).string(message.sellSpread);
    }
    if (message.includeInSearch !== undefined) {
      writer.uint32(344).bool(message.includeInSearch);
    }
    if (message.buyerPartyId !== undefined) {
      writer.uint32(354).string(message.buyerPartyId);
    }
    if (message.dailySellLimit !== undefined) {
      writer.uint32(362).string(message.dailySellLimit);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondDetailsDashboard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondDetailsDashboard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.issuer = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.couponRate = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.putCall = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.couponFrequency = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.principalRepaymentFrequency = reader.int32() as any;
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.principalRepaymentFrequencyDesc = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.maturityDate = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.displayTitle = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.isinCode = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.rating = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.ratingAgency = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.ratingSupportingUrl = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.bondIssuingInstitutionId = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.bondType = reader.int32() as any;
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.typeOfYield = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 152) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.natureOfBond = reader.string();
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.issueSize = reader.string();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.issueDate = reader.string();
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.issuePrice = reader.string();
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.issueFaceValue = reader.string();
          continue;
        }
        case 25: {
          if (tag !== 202) {
            break;
          }

          message.createdAt = reader.string();
          continue;
        }
        case 26: {
          if (tag !== 210) {
            break;
          }

          message.updatedAt = reader.string();
          continue;
        }
        case 27: {
          if (tag !== 218) {
            break;
          }

          message.bgColor = reader.string();
          continue;
        }
        case 28: {
          if (tag !== 226) {
            break;
          }

          message.informationMemorandum = reader.string();
          continue;
        }
        case 29: {
          if (tag !== 234) {
            break;
          }

          message.debuntureTrustee = reader.string();
          continue;
        }
        case 30: {
          if (tag !== 242) {
            break;
          }

          message.ratingDate = reader.string();
          continue;
        }
        case 31: {
          if (tag !== 250) {
            break;
          }

          message.issueMode = reader.string();
          continue;
        }
        case 32: {
          if (tag !== 258) {
            break;
          }

          message.nature = reader.string();
          continue;
        }
        case 33: {
          if (tag !== 266) {
            break;
          }

          message.seniority = reader.string();
          continue;
        }
        case 34: {
          if (tag !== 274) {
            break;
          }

          message.couponType = reader.string();
          continue;
        }
        case 35: {
          if (tag !== 282) {
            break;
          }

          message.yield = reader.string();
          continue;
        }
        case 36: {
          if (tag !== 290) {
            break;
          }

          message.perUserPurchaseLimit = reader.string();
          continue;
        }
        case 37: {
          if (tag !== 298) {
            break;
          }

          message.minLotSize = reader.string();
          continue;
        }
        case 38: {
          if (tag !== 304) {
            break;
          }

          message.investabilityStatus = reader.int32() as any;
          continue;
        }
        case 39: {
          if (tag !== 312) {
            break;
          }

          message.isTZeroSettlementSupported = reader.bool();
          continue;
        }
        case 40: {
          if (tag !== 320) {
            break;
          }

          message.includeInCollection = reader.bool();
          continue;
        }
        case 41: {
          if (tag !== 328) {
            break;
          }

          message.blockTrade = reader.bool();
          continue;
        }
        case 42: {
          if (tag !== 338) {
            break;
          }

          message.sellSpread = reader.string();
          continue;
        }
        case 43: {
          if (tag !== 344) {
            break;
          }

          message.includeInSearch = reader.bool();
          continue;
        }
        case 44: {
          if (tag !== 354) {
            break;
          }

          message.buyerPartyId = reader.string();
          continue;
        }
        case 45: {
          if (tag !== 362) {
            break;
          }

          message.dailySellLimit = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondDetailsDashboard {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      type: isSet(object.type) ? globalThis.String(object.type) : undefined,
      issuer: isSet(object.issuer) ? globalThis.String(object.issuer) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
      couponRate: isSet(object.couponRate) ? globalThis.String(object.couponRate) : undefined,
      putCall: isSet(object.putCall) ? globalThis.String(object.putCall) : undefined,
      couponFrequency: isSet(object.couponFrequency) ? repaymentFrequencyFromJSON(object.couponFrequency) : undefined,
      principalRepaymentFrequency: isSet(object.principalRepaymentFrequency)
        ? repaymentFrequencyFromJSON(object.principalRepaymentFrequency)
        : undefined,
      principalRepaymentFrequencyDesc: isSet(object.principalRepaymentFrequencyDesc)
        ? globalThis.String(object.principalRepaymentFrequencyDesc)
        : undefined,
      maturityDate: isSet(object.maturityDate) ? globalThis.String(object.maturityDate) : undefined,
      displayTitle: isSet(object.displayTitle) ? globalThis.String(object.displayTitle) : undefined,
      isinCode: isSet(object.isinCode) ? globalThis.String(object.isinCode) : undefined,
      rating: isSet(object.rating) ? globalThis.String(object.rating) : undefined,
      ratingAgency: isSet(object.ratingAgency) ? globalThis.String(object.ratingAgency) : undefined,
      ratingSupportingUrl: isSet(object.ratingSupportingUrl)
        ? globalThis.String(object.ratingSupportingUrl)
        : undefined,
      bondIssuingInstitutionId: isSet(object.bondIssuingInstitutionId)
        ? globalThis.String(object.bondIssuingInstitutionId)
        : "",
      bondType: isSet(object.bondType) ? bondTypeFromJSON(object.bondType) : undefined,
      typeOfYield: isSet(object.typeOfYield) ? globalThis.String(object.typeOfYield) : undefined,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : undefined,
      natureOfBond: isSet(object.natureOfBond) ? globalThis.String(object.natureOfBond) : undefined,
      issueSize: isSet(object.issueSize) ? globalThis.String(object.issueSize) : undefined,
      issueDate: isSet(object.issueDate) ? globalThis.String(object.issueDate) : undefined,
      issuePrice: isSet(object.issuePrice) ? globalThis.String(object.issuePrice) : undefined,
      issueFaceValue: isSet(object.issueFaceValue) ? globalThis.String(object.issueFaceValue) : undefined,
      createdAt: isSet(object.createdAt) ? globalThis.String(object.createdAt) : "",
      updatedAt: isSet(object.updatedAt) ? globalThis.String(object.updatedAt) : undefined,
      bgColor: isSet(object.bgColor) ? globalThis.String(object.bgColor) : undefined,
      informationMemorandum: isSet(object.informationMemorandum)
        ? globalThis.String(object.informationMemorandum)
        : undefined,
      debuntureTrustee: isSet(object.debuntureTrustee) ? globalThis.String(object.debuntureTrustee) : undefined,
      ratingDate: isSet(object.ratingDate) ? globalThis.String(object.ratingDate) : undefined,
      issueMode: isSet(object.issueMode) ? globalThis.String(object.issueMode) : undefined,
      nature: isSet(object.nature) ? globalThis.String(object.nature) : undefined,
      seniority: isSet(object.seniority) ? globalThis.String(object.seniority) : undefined,
      couponType: isSet(object.couponType) ? globalThis.String(object.couponType) : undefined,
      yield: isSet(object.yield) ? globalThis.String(object.yield) : undefined,
      perUserPurchaseLimit: isSet(object.perUserPurchaseLimit)
        ? globalThis.String(object.perUserPurchaseLimit)
        : undefined,
      minLotSize: isSet(object.minLotSize) ? globalThis.String(object.minLotSize) : undefined,
      investabilityStatus: isSet(object.investabilityStatus)
        ? investabilityStatusFromJSON(object.investabilityStatus)
        : undefined,
      isTZeroSettlementSupported: isSet(object.isTZeroSettlementSupported)
        ? globalThis.Boolean(object.isTZeroSettlementSupported)
        : undefined,
      includeInCollection: isSet(object.includeInCollection)
        ? globalThis.Boolean(object.includeInCollection)
        : undefined,
      blockTrade: isSet(object.blockTrade) ? globalThis.Boolean(object.blockTrade) : undefined,
      sellSpread: isSet(object.sellSpread) ? globalThis.String(object.sellSpread) : undefined,
      includeInSearch: isSet(object.includeInSearch) ? globalThis.Boolean(object.includeInSearch) : undefined,
      buyerPartyId: isSet(object.buyerPartyId) ? globalThis.String(object.buyerPartyId) : undefined,
      dailySellLimit: isSet(object.dailySellLimit) ? globalThis.String(object.dailySellLimit) : undefined,
    };
  },

  toJSON(message: BondDetailsDashboard): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.type !== undefined) {
      obj.type = message.type;
    }
    if (message.issuer !== undefined) {
      obj.issuer = message.issuer;
    }
    if (message.name !== undefined) {
      obj.name = message.name;
    }
    if (message.couponRate !== undefined) {
      obj.couponRate = message.couponRate;
    }
    if (message.putCall !== undefined) {
      obj.putCall = message.putCall;
    }
    if (message.couponFrequency !== undefined) {
      obj.couponFrequency = repaymentFrequencyToJSON(message.couponFrequency);
    }
    if (message.principalRepaymentFrequency !== undefined) {
      obj.principalRepaymentFrequency = repaymentFrequencyToJSON(message.principalRepaymentFrequency);
    }
    if (message.principalRepaymentFrequencyDesc !== undefined) {
      obj.principalRepaymentFrequencyDesc = message.principalRepaymentFrequencyDesc;
    }
    if (message.maturityDate !== undefined) {
      obj.maturityDate = message.maturityDate;
    }
    if (message.displayTitle !== undefined) {
      obj.displayTitle = message.displayTitle;
    }
    if (message.isinCode !== undefined) {
      obj.isinCode = message.isinCode;
    }
    if (message.rating !== undefined) {
      obj.rating = message.rating;
    }
    if (message.ratingAgency !== undefined) {
      obj.ratingAgency = message.ratingAgency;
    }
    if (message.ratingSupportingUrl !== undefined) {
      obj.ratingSupportingUrl = message.ratingSupportingUrl;
    }
    if (message.bondIssuingInstitutionId !== "") {
      obj.bondIssuingInstitutionId = message.bondIssuingInstitutionId;
    }
    if (message.bondType !== undefined) {
      obj.bondType = bondTypeToJSON(message.bondType);
    }
    if (message.typeOfYield !== undefined) {
      obj.typeOfYield = message.typeOfYield;
    }
    if (message.isActive !== undefined) {
      obj.isActive = message.isActive;
    }
    if (message.natureOfBond !== undefined) {
      obj.natureOfBond = message.natureOfBond;
    }
    if (message.issueSize !== undefined) {
      obj.issueSize = message.issueSize;
    }
    if (message.issueDate !== undefined) {
      obj.issueDate = message.issueDate;
    }
    if (message.issuePrice !== undefined) {
      obj.issuePrice = message.issuePrice;
    }
    if (message.issueFaceValue !== undefined) {
      obj.issueFaceValue = message.issueFaceValue;
    }
    if (message.createdAt !== "") {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== undefined) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.bgColor !== undefined) {
      obj.bgColor = message.bgColor;
    }
    if (message.informationMemorandum !== undefined) {
      obj.informationMemorandum = message.informationMemorandum;
    }
    if (message.debuntureTrustee !== undefined) {
      obj.debuntureTrustee = message.debuntureTrustee;
    }
    if (message.ratingDate !== undefined) {
      obj.ratingDate = message.ratingDate;
    }
    if (message.issueMode !== undefined) {
      obj.issueMode = message.issueMode;
    }
    if (message.nature !== undefined) {
      obj.nature = message.nature;
    }
    if (message.seniority !== undefined) {
      obj.seniority = message.seniority;
    }
    if (message.couponType !== undefined) {
      obj.couponType = message.couponType;
    }
    if (message.yield !== undefined) {
      obj.yield = message.yield;
    }
    if (message.perUserPurchaseLimit !== undefined) {
      obj.perUserPurchaseLimit = message.perUserPurchaseLimit;
    }
    if (message.minLotSize !== undefined) {
      obj.minLotSize = message.minLotSize;
    }
    if (message.investabilityStatus !== undefined) {
      obj.investabilityStatus = investabilityStatusToJSON(message.investabilityStatus);
    }
    if (message.isTZeroSettlementSupported !== undefined) {
      obj.isTZeroSettlementSupported = message.isTZeroSettlementSupported;
    }
    if (message.includeInCollection !== undefined) {
      obj.includeInCollection = message.includeInCollection;
    }
    if (message.blockTrade !== undefined) {
      obj.blockTrade = message.blockTrade;
    }
    if (message.sellSpread !== undefined) {
      obj.sellSpread = message.sellSpread;
    }
    if (message.includeInSearch !== undefined) {
      obj.includeInSearch = message.includeInSearch;
    }
    if (message.buyerPartyId !== undefined) {
      obj.buyerPartyId = message.buyerPartyId;
    }
    if (message.dailySellLimit !== undefined) {
      obj.dailySellLimit = message.dailySellLimit;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondDetailsDashboard>, I>>(base?: I): BondDetailsDashboard {
    return BondDetailsDashboard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondDetailsDashboard>, I>>(object: I): BondDetailsDashboard {
    const message = createBaseBondDetailsDashboard();
    message.id = object.id ?? "";
    message.type = object.type ?? undefined;
    message.issuer = object.issuer ?? undefined;
    message.name = object.name ?? undefined;
    message.couponRate = object.couponRate ?? undefined;
    message.putCall = object.putCall ?? undefined;
    message.couponFrequency = object.couponFrequency ?? undefined;
    message.principalRepaymentFrequency = object.principalRepaymentFrequency ?? undefined;
    message.principalRepaymentFrequencyDesc = object.principalRepaymentFrequencyDesc ?? undefined;
    message.maturityDate = object.maturityDate ?? undefined;
    message.displayTitle = object.displayTitle ?? undefined;
    message.isinCode = object.isinCode ?? undefined;
    message.rating = object.rating ?? undefined;
    message.ratingAgency = object.ratingAgency ?? undefined;
    message.ratingSupportingUrl = object.ratingSupportingUrl ?? undefined;
    message.bondIssuingInstitutionId = object.bondIssuingInstitutionId ?? "";
    message.bondType = object.bondType ?? undefined;
    message.typeOfYield = object.typeOfYield ?? undefined;
    message.isActive = object.isActive ?? undefined;
    message.natureOfBond = object.natureOfBond ?? undefined;
    message.issueSize = object.issueSize ?? undefined;
    message.issueDate = object.issueDate ?? undefined;
    message.issuePrice = object.issuePrice ?? undefined;
    message.issueFaceValue = object.issueFaceValue ?? undefined;
    message.createdAt = object.createdAt ?? "";
    message.updatedAt = object.updatedAt ?? undefined;
    message.bgColor = object.bgColor ?? undefined;
    message.informationMemorandum = object.informationMemorandum ?? undefined;
    message.debuntureTrustee = object.debuntureTrustee ?? undefined;
    message.ratingDate = object.ratingDate ?? undefined;
    message.issueMode = object.issueMode ?? undefined;
    message.nature = object.nature ?? undefined;
    message.seniority = object.seniority ?? undefined;
    message.couponType = object.couponType ?? undefined;
    message.yield = object.yield ?? undefined;
    message.perUserPurchaseLimit = object.perUserPurchaseLimit ?? undefined;
    message.minLotSize = object.minLotSize ?? undefined;
    message.investabilityStatus = object.investabilityStatus ?? undefined;
    message.isTZeroSettlementSupported = object.isTZeroSettlementSupported ?? undefined;
    message.includeInCollection = object.includeInCollection ?? undefined;
    message.blockTrade = object.blockTrade ?? undefined;
    message.sellSpread = object.sellSpread ?? undefined;
    message.includeInSearch = object.includeInSearch ?? undefined;
    message.buyerPartyId = object.buyerPartyId ?? undefined;
    message.dailySellLimit = object.dailySellLimit ?? undefined;
    return message;
  },
};

function createBaseGetAllBondsRequest(): GetAllBondsRequest {
  return { page: 0, size: 0, searchString: "" };
}

export const GetAllBondsRequest: MessageFns<GetAllBondsRequest> = {
  encode(message: GetAllBondsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.page !== 0) {
      writer.uint32(8).int32(message.page);
    }
    if (message.size !== 0) {
      writer.uint32(16).int32(message.size);
    }
    if (message.searchString !== "") {
      writer.uint32(26).string(message.searchString);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllBondsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllBondsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.page = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.size = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.searchString = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAllBondsRequest {
    return {
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      size: isSet(object.size) ? globalThis.Number(object.size) : 0,
      searchString: isSet(object.searchString) ? globalThis.String(object.searchString) : "",
    };
  },

  toJSON(message: GetAllBondsRequest): unknown {
    const obj: any = {};
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.size !== 0) {
      obj.size = Math.round(message.size);
    }
    if (message.searchString !== "") {
      obj.searchString = message.searchString;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAllBondsRequest>, I>>(base?: I): GetAllBondsRequest {
    return GetAllBondsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAllBondsRequest>, I>>(object: I): GetAllBondsRequest {
    const message = createBaseGetAllBondsRequest();
    message.page = object.page ?? 0;
    message.size = object.size ?? 0;
    message.searchString = object.searchString ?? "";
    return message;
  },
};

function createBaseGetAllBondsResponse(): GetAllBondsResponse {
  return { bondDetails: [], hasNextPage: false };
}

export const GetAllBondsResponse: MessageFns<GetAllBondsResponse> = {
  encode(message: GetAllBondsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bondDetails) {
      BondDetailsDashboard.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.hasNextPage !== false) {
      writer.uint32(16).bool(message.hasNextPage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllBondsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllBondsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondDetails.push(BondDetailsDashboard.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasNextPage = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAllBondsResponse {
    return {
      bondDetails: globalThis.Array.isArray(object?.bondDetails)
        ? object.bondDetails.map((e: any) => BondDetailsDashboard.fromJSON(e))
        : [],
      hasNextPage: isSet(object.hasNextPage) ? globalThis.Boolean(object.hasNextPage) : false,
    };
  },

  toJSON(message: GetAllBondsResponse): unknown {
    const obj: any = {};
    if (message.bondDetails?.length) {
      obj.bondDetails = message.bondDetails.map((e) => BondDetailsDashboard.toJSON(e));
    }
    if (message.hasNextPage !== false) {
      obj.hasNextPage = message.hasNextPage;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAllBondsResponse>, I>>(base?: I): GetAllBondsResponse {
    return GetAllBondsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAllBondsResponse>, I>>(object: I): GetAllBondsResponse {
    const message = createBaseGetAllBondsResponse();
    message.bondDetails = object.bondDetails?.map((e) => BondDetailsDashboard.fromPartial(e)) || [];
    message.hasNextPage = object.hasNextPage ?? false;
    return message;
  },
};

function createBaseBondDetailsUpdateRequest(): BondDetailsUpdateRequest {
  return {
    id: "",
    type: undefined,
    issuer: undefined,
    name: undefined,
    couponRate: undefined,
    putCall: undefined,
    couponFrequency: undefined,
    principalRepaymentFrequency: undefined,
    principalRepaymentFrequencyDesc: undefined,
    maturityDate: undefined,
    displayTitle: undefined,
    isinCode: undefined,
    rating: undefined,
    ratingAgency: undefined,
    ratingSupportingUrl: undefined,
    bondType: undefined,
    typeOfYield: undefined,
    isActive: undefined,
    natureOfBond: undefined,
    issueSize: undefined,
    issueDate: undefined,
    issuePrice: undefined,
    issueFaceValue: undefined,
    bgColor: undefined,
    informationMemorandum: undefined,
    debuntureTrustee: undefined,
    ratingDate: undefined,
    issueMode: undefined,
    nature: undefined,
    seniority: undefined,
    couponType: undefined,
    yield: undefined,
    perUserPurchaseLimit: undefined,
    minLotSize: undefined,
    investabilityStatus: undefined,
    isTZeroSettlementSupported: undefined,
    includeInCollection: undefined,
    blockTrade: undefined,
    sellSpread: undefined,
    includeInSearch: undefined,
    buyerPartyId: undefined,
    dailySellLimit: undefined,
  };
}

export const BondDetailsUpdateRequest: MessageFns<BondDetailsUpdateRequest> = {
  encode(message: BondDetailsUpdateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.type !== undefined) {
      writer.uint32(18).string(message.type);
    }
    if (message.issuer !== undefined) {
      writer.uint32(26).string(message.issuer);
    }
    if (message.name !== undefined) {
      writer.uint32(34).string(message.name);
    }
    if (message.couponRate !== undefined) {
      writer.uint32(42).string(message.couponRate);
    }
    if (message.putCall !== undefined) {
      writer.uint32(50).string(message.putCall);
    }
    if (message.couponFrequency !== undefined) {
      writer.uint32(56).int32(message.couponFrequency);
    }
    if (message.principalRepaymentFrequency !== undefined) {
      writer.uint32(64).int32(message.principalRepaymentFrequency);
    }
    if (message.principalRepaymentFrequencyDesc !== undefined) {
      writer.uint32(74).string(message.principalRepaymentFrequencyDesc);
    }
    if (message.maturityDate !== undefined) {
      writer.uint32(82).string(message.maturityDate);
    }
    if (message.displayTitle !== undefined) {
      writer.uint32(90).string(message.displayTitle);
    }
    if (message.isinCode !== undefined) {
      writer.uint32(98).string(message.isinCode);
    }
    if (message.rating !== undefined) {
      writer.uint32(106).string(message.rating);
    }
    if (message.ratingAgency !== undefined) {
      writer.uint32(114).string(message.ratingAgency);
    }
    if (message.ratingSupportingUrl !== undefined) {
      writer.uint32(122).string(message.ratingSupportingUrl);
    }
    if (message.bondType !== undefined) {
      writer.uint32(128).int32(message.bondType);
    }
    if (message.typeOfYield !== undefined) {
      writer.uint32(138).string(message.typeOfYield);
    }
    if (message.isActive !== undefined) {
      writer.uint32(144).bool(message.isActive);
    }
    if (message.natureOfBond !== undefined) {
      writer.uint32(154).string(message.natureOfBond);
    }
    if (message.issueSize !== undefined) {
      writer.uint32(162).string(message.issueSize);
    }
    if (message.issueDate !== undefined) {
      writer.uint32(170).string(message.issueDate);
    }
    if (message.issuePrice !== undefined) {
      writer.uint32(178).string(message.issuePrice);
    }
    if (message.issueFaceValue !== undefined) {
      writer.uint32(186).string(message.issueFaceValue);
    }
    if (message.bgColor !== undefined) {
      writer.uint32(194).string(message.bgColor);
    }
    if (message.informationMemorandum !== undefined) {
      writer.uint32(202).string(message.informationMemorandum);
    }
    if (message.debuntureTrustee !== undefined) {
      writer.uint32(210).string(message.debuntureTrustee);
    }
    if (message.ratingDate !== undefined) {
      writer.uint32(218).string(message.ratingDate);
    }
    if (message.issueMode !== undefined) {
      writer.uint32(226).string(message.issueMode);
    }
    if (message.nature !== undefined) {
      writer.uint32(234).string(message.nature);
    }
    if (message.seniority !== undefined) {
      writer.uint32(242).string(message.seniority);
    }
    if (message.couponType !== undefined) {
      writer.uint32(250).string(message.couponType);
    }
    if (message.yield !== undefined) {
      writer.uint32(258).string(message.yield);
    }
    if (message.perUserPurchaseLimit !== undefined) {
      writer.uint32(266).string(message.perUserPurchaseLimit);
    }
    if (message.minLotSize !== undefined) {
      writer.uint32(274).string(message.minLotSize);
    }
    if (message.investabilityStatus !== undefined) {
      writer.uint32(280).int32(message.investabilityStatus);
    }
    if (message.isTZeroSettlementSupported !== undefined) {
      writer.uint32(288).bool(message.isTZeroSettlementSupported);
    }
    if (message.includeInCollection !== undefined) {
      writer.uint32(296).bool(message.includeInCollection);
    }
    if (message.blockTrade !== undefined) {
      writer.uint32(304).bool(message.blockTrade);
    }
    if (message.sellSpread !== undefined) {
      writer.uint32(314).string(message.sellSpread);
    }
    if (message.includeInSearch !== undefined) {
      writer.uint32(320).bool(message.includeInSearch);
    }
    if (message.buyerPartyId !== undefined) {
      writer.uint32(330).string(message.buyerPartyId);
    }
    if (message.dailySellLimit !== undefined) {
      writer.uint32(338).string(message.dailySellLimit);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondDetailsUpdateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondDetailsUpdateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.issuer = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.couponRate = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.putCall = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.couponFrequency = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.principalRepaymentFrequency = reader.int32() as any;
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.principalRepaymentFrequencyDesc = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.maturityDate = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.displayTitle = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.isinCode = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.rating = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.ratingAgency = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.ratingSupportingUrl = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.bondType = reader.int32() as any;
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.typeOfYield = reader.string();
          continue;
        }
        case 18: {
          if (tag !== 144) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.natureOfBond = reader.string();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.issueSize = reader.string();
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.issueDate = reader.string();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.issuePrice = reader.string();
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.issueFaceValue = reader.string();
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.bgColor = reader.string();
          continue;
        }
        case 25: {
          if (tag !== 202) {
            break;
          }

          message.informationMemorandum = reader.string();
          continue;
        }
        case 26: {
          if (tag !== 210) {
            break;
          }

          message.debuntureTrustee = reader.string();
          continue;
        }
        case 27: {
          if (tag !== 218) {
            break;
          }

          message.ratingDate = reader.string();
          continue;
        }
        case 28: {
          if (tag !== 226) {
            break;
          }

          message.issueMode = reader.string();
          continue;
        }
        case 29: {
          if (tag !== 234) {
            break;
          }

          message.nature = reader.string();
          continue;
        }
        case 30: {
          if (tag !== 242) {
            break;
          }

          message.seniority = reader.string();
          continue;
        }
        case 31: {
          if (tag !== 250) {
            break;
          }

          message.couponType = reader.string();
          continue;
        }
        case 32: {
          if (tag !== 258) {
            break;
          }

          message.yield = reader.string();
          continue;
        }
        case 33: {
          if (tag !== 266) {
            break;
          }

          message.perUserPurchaseLimit = reader.string();
          continue;
        }
        case 34: {
          if (tag !== 274) {
            break;
          }

          message.minLotSize = reader.string();
          continue;
        }
        case 35: {
          if (tag !== 280) {
            break;
          }

          message.investabilityStatus = reader.int32() as any;
          continue;
        }
        case 36: {
          if (tag !== 288) {
            break;
          }

          message.isTZeroSettlementSupported = reader.bool();
          continue;
        }
        case 37: {
          if (tag !== 296) {
            break;
          }

          message.includeInCollection = reader.bool();
          continue;
        }
        case 38: {
          if (tag !== 304) {
            break;
          }

          message.blockTrade = reader.bool();
          continue;
        }
        case 39: {
          if (tag !== 314) {
            break;
          }

          message.sellSpread = reader.string();
          continue;
        }
        case 40: {
          if (tag !== 320) {
            break;
          }

          message.includeInSearch = reader.bool();
          continue;
        }
        case 41: {
          if (tag !== 330) {
            break;
          }

          message.buyerPartyId = reader.string();
          continue;
        }
        case 42: {
          if (tag !== 338) {
            break;
          }

          message.dailySellLimit = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondDetailsUpdateRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      type: isSet(object.type) ? globalThis.String(object.type) : undefined,
      issuer: isSet(object.issuer) ? globalThis.String(object.issuer) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
      couponRate: isSet(object.couponRate) ? globalThis.String(object.couponRate) : undefined,
      putCall: isSet(object.putCall) ? globalThis.String(object.putCall) : undefined,
      couponFrequency: isSet(object.couponFrequency) ? repaymentFrequencyFromJSON(object.couponFrequency) : undefined,
      principalRepaymentFrequency: isSet(object.principalRepaymentFrequency)
        ? repaymentFrequencyFromJSON(object.principalRepaymentFrequency)
        : undefined,
      principalRepaymentFrequencyDesc: isSet(object.principalRepaymentFrequencyDesc)
        ? globalThis.String(object.principalRepaymentFrequencyDesc)
        : undefined,
      maturityDate: isSet(object.maturityDate) ? globalThis.String(object.maturityDate) : undefined,
      displayTitle: isSet(object.displayTitle) ? globalThis.String(object.displayTitle) : undefined,
      isinCode: isSet(object.isinCode) ? globalThis.String(object.isinCode) : undefined,
      rating: isSet(object.rating) ? globalThis.String(object.rating) : undefined,
      ratingAgency: isSet(object.ratingAgency) ? globalThis.String(object.ratingAgency) : undefined,
      ratingSupportingUrl: isSet(object.ratingSupportingUrl)
        ? globalThis.String(object.ratingSupportingUrl)
        : undefined,
      bondType: isSet(object.bondType) ? bondTypeFromJSON(object.bondType) : undefined,
      typeOfYield: isSet(object.typeOfYield) ? globalThis.String(object.typeOfYield) : undefined,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : undefined,
      natureOfBond: isSet(object.natureOfBond) ? globalThis.String(object.natureOfBond) : undefined,
      issueSize: isSet(object.issueSize) ? globalThis.String(object.issueSize) : undefined,
      issueDate: isSet(object.issueDate) ? globalThis.String(object.issueDate) : undefined,
      issuePrice: isSet(object.issuePrice) ? globalThis.String(object.issuePrice) : undefined,
      issueFaceValue: isSet(object.issueFaceValue) ? globalThis.String(object.issueFaceValue) : undefined,
      bgColor: isSet(object.bgColor) ? globalThis.String(object.bgColor) : undefined,
      informationMemorandum: isSet(object.informationMemorandum)
        ? globalThis.String(object.informationMemorandum)
        : undefined,
      debuntureTrustee: isSet(object.debuntureTrustee) ? globalThis.String(object.debuntureTrustee) : undefined,
      ratingDate: isSet(object.ratingDate) ? globalThis.String(object.ratingDate) : undefined,
      issueMode: isSet(object.issueMode) ? globalThis.String(object.issueMode) : undefined,
      nature: isSet(object.nature) ? globalThis.String(object.nature) : undefined,
      seniority: isSet(object.seniority) ? globalThis.String(object.seniority) : undefined,
      couponType: isSet(object.couponType) ? globalThis.String(object.couponType) : undefined,
      yield: isSet(object.yield) ? globalThis.String(object.yield) : undefined,
      perUserPurchaseLimit: isSet(object.perUserPurchaseLimit)
        ? globalThis.String(object.perUserPurchaseLimit)
        : undefined,
      minLotSize: isSet(object.minLotSize) ? globalThis.String(object.minLotSize) : undefined,
      investabilityStatus: isSet(object.investabilityStatus)
        ? investabilityStatusFromJSON(object.investabilityStatus)
        : undefined,
      isTZeroSettlementSupported: isSet(object.isTZeroSettlementSupported)
        ? globalThis.Boolean(object.isTZeroSettlementSupported)
        : undefined,
      includeInCollection: isSet(object.includeInCollection)
        ? globalThis.Boolean(object.includeInCollection)
        : undefined,
      blockTrade: isSet(object.blockTrade) ? globalThis.Boolean(object.blockTrade) : undefined,
      sellSpread: isSet(object.sellSpread) ? globalThis.String(object.sellSpread) : undefined,
      includeInSearch: isSet(object.includeInSearch) ? globalThis.Boolean(object.includeInSearch) : undefined,
      buyerPartyId: isSet(object.buyerPartyId) ? globalThis.String(object.buyerPartyId) : undefined,
      dailySellLimit: isSet(object.dailySellLimit) ? globalThis.String(object.dailySellLimit) : undefined,
    };
  },

  toJSON(message: BondDetailsUpdateRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.type !== undefined) {
      obj.type = message.type;
    }
    if (message.issuer !== undefined) {
      obj.issuer = message.issuer;
    }
    if (message.name !== undefined) {
      obj.name = message.name;
    }
    if (message.couponRate !== undefined) {
      obj.couponRate = message.couponRate;
    }
    if (message.putCall !== undefined) {
      obj.putCall = message.putCall;
    }
    if (message.couponFrequency !== undefined) {
      obj.couponFrequency = repaymentFrequencyToJSON(message.couponFrequency);
    }
    if (message.principalRepaymentFrequency !== undefined) {
      obj.principalRepaymentFrequency = repaymentFrequencyToJSON(message.principalRepaymentFrequency);
    }
    if (message.principalRepaymentFrequencyDesc !== undefined) {
      obj.principalRepaymentFrequencyDesc = message.principalRepaymentFrequencyDesc;
    }
    if (message.maturityDate !== undefined) {
      obj.maturityDate = message.maturityDate;
    }
    if (message.displayTitle !== undefined) {
      obj.displayTitle = message.displayTitle;
    }
    if (message.isinCode !== undefined) {
      obj.isinCode = message.isinCode;
    }
    if (message.rating !== undefined) {
      obj.rating = message.rating;
    }
    if (message.ratingAgency !== undefined) {
      obj.ratingAgency = message.ratingAgency;
    }
    if (message.ratingSupportingUrl !== undefined) {
      obj.ratingSupportingUrl = message.ratingSupportingUrl;
    }
    if (message.bondType !== undefined) {
      obj.bondType = bondTypeToJSON(message.bondType);
    }
    if (message.typeOfYield !== undefined) {
      obj.typeOfYield = message.typeOfYield;
    }
    if (message.isActive !== undefined) {
      obj.isActive = message.isActive;
    }
    if (message.natureOfBond !== undefined) {
      obj.natureOfBond = message.natureOfBond;
    }
    if (message.issueSize !== undefined) {
      obj.issueSize = message.issueSize;
    }
    if (message.issueDate !== undefined) {
      obj.issueDate = message.issueDate;
    }
    if (message.issuePrice !== undefined) {
      obj.issuePrice = message.issuePrice;
    }
    if (message.issueFaceValue !== undefined) {
      obj.issueFaceValue = message.issueFaceValue;
    }
    if (message.bgColor !== undefined) {
      obj.bgColor = message.bgColor;
    }
    if (message.informationMemorandum !== undefined) {
      obj.informationMemorandum = message.informationMemorandum;
    }
    if (message.debuntureTrustee !== undefined) {
      obj.debuntureTrustee = message.debuntureTrustee;
    }
    if (message.ratingDate !== undefined) {
      obj.ratingDate = message.ratingDate;
    }
    if (message.issueMode !== undefined) {
      obj.issueMode = message.issueMode;
    }
    if (message.nature !== undefined) {
      obj.nature = message.nature;
    }
    if (message.seniority !== undefined) {
      obj.seniority = message.seniority;
    }
    if (message.couponType !== undefined) {
      obj.couponType = message.couponType;
    }
    if (message.yield !== undefined) {
      obj.yield = message.yield;
    }
    if (message.perUserPurchaseLimit !== undefined) {
      obj.perUserPurchaseLimit = message.perUserPurchaseLimit;
    }
    if (message.minLotSize !== undefined) {
      obj.minLotSize = message.minLotSize;
    }
    if (message.investabilityStatus !== undefined) {
      obj.investabilityStatus = investabilityStatusToJSON(message.investabilityStatus);
    }
    if (message.isTZeroSettlementSupported !== undefined) {
      obj.isTZeroSettlementSupported = message.isTZeroSettlementSupported;
    }
    if (message.includeInCollection !== undefined) {
      obj.includeInCollection = message.includeInCollection;
    }
    if (message.blockTrade !== undefined) {
      obj.blockTrade = message.blockTrade;
    }
    if (message.sellSpread !== undefined) {
      obj.sellSpread = message.sellSpread;
    }
    if (message.includeInSearch !== undefined) {
      obj.includeInSearch = message.includeInSearch;
    }
    if (message.buyerPartyId !== undefined) {
      obj.buyerPartyId = message.buyerPartyId;
    }
    if (message.dailySellLimit !== undefined) {
      obj.dailySellLimit = message.dailySellLimit;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondDetailsUpdateRequest>, I>>(base?: I): BondDetailsUpdateRequest {
    return BondDetailsUpdateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondDetailsUpdateRequest>, I>>(object: I): BondDetailsUpdateRequest {
    const message = createBaseBondDetailsUpdateRequest();
    message.id = object.id ?? "";
    message.type = object.type ?? undefined;
    message.issuer = object.issuer ?? undefined;
    message.name = object.name ?? undefined;
    message.couponRate = object.couponRate ?? undefined;
    message.putCall = object.putCall ?? undefined;
    message.couponFrequency = object.couponFrequency ?? undefined;
    message.principalRepaymentFrequency = object.principalRepaymentFrequency ?? undefined;
    message.principalRepaymentFrequencyDesc = object.principalRepaymentFrequencyDesc ?? undefined;
    message.maturityDate = object.maturityDate ?? undefined;
    message.displayTitle = object.displayTitle ?? undefined;
    message.isinCode = object.isinCode ?? undefined;
    message.rating = object.rating ?? undefined;
    message.ratingAgency = object.ratingAgency ?? undefined;
    message.ratingSupportingUrl = object.ratingSupportingUrl ?? undefined;
    message.bondType = object.bondType ?? undefined;
    message.typeOfYield = object.typeOfYield ?? undefined;
    message.isActive = object.isActive ?? undefined;
    message.natureOfBond = object.natureOfBond ?? undefined;
    message.issueSize = object.issueSize ?? undefined;
    message.issueDate = object.issueDate ?? undefined;
    message.issuePrice = object.issuePrice ?? undefined;
    message.issueFaceValue = object.issueFaceValue ?? undefined;
    message.bgColor = object.bgColor ?? undefined;
    message.informationMemorandum = object.informationMemorandum ?? undefined;
    message.debuntureTrustee = object.debuntureTrustee ?? undefined;
    message.ratingDate = object.ratingDate ?? undefined;
    message.issueMode = object.issueMode ?? undefined;
    message.nature = object.nature ?? undefined;
    message.seniority = object.seniority ?? undefined;
    message.couponType = object.couponType ?? undefined;
    message.yield = object.yield ?? undefined;
    message.perUserPurchaseLimit = object.perUserPurchaseLimit ?? undefined;
    message.minLotSize = object.minLotSize ?? undefined;
    message.investabilityStatus = object.investabilityStatus ?? undefined;
    message.isTZeroSettlementSupported = object.isTZeroSettlementSupported ?? undefined;
    message.includeInCollection = object.includeInCollection ?? undefined;
    message.blockTrade = object.blockTrade ?? undefined;
    message.sellSpread = object.sellSpread ?? undefined;
    message.includeInSearch = object.includeInSearch ?? undefined;
    message.buyerPartyId = object.buyerPartyId ?? undefined;
    message.dailySellLimit = object.dailySellLimit ?? undefined;
    return message;
  },
};

function createBaseBondDetailsUpdateResponse(): BondDetailsUpdateResponse {
  return {};
}

export const BondDetailsUpdateResponse: MessageFns<BondDetailsUpdateResponse> = {
  encode(_: BondDetailsUpdateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondDetailsUpdateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondDetailsUpdateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): BondDetailsUpdateResponse {
    return {};
  },

  toJSON(_: BondDetailsUpdateResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<BondDetailsUpdateResponse>, I>>(base?: I): BondDetailsUpdateResponse {
    return BondDetailsUpdateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondDetailsUpdateResponse>, I>>(_: I): BondDetailsUpdateResponse {
    const message = createBaseBondDetailsUpdateResponse();
    return message;
  },
};

function createBaseAllISINSRequest(): AllISINSRequest {
  return {};
}

export const AllISINSRequest: MessageFns<AllISINSRequest> = {
  encode(_: AllISINSRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllISINSRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllISINSRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): AllISINSRequest {
    return {};
  },

  toJSON(_: AllISINSRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<AllISINSRequest>, I>>(base?: I): AllISINSRequest {
    return AllISINSRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllISINSRequest>, I>>(_: I): AllISINSRequest {
    const message = createBaseAllISINSRequest();
    return message;
  },
};

function createBaseBondIssuingInstitution(): BondIssuingInstitution {
  return { id: "", name: "" };
}

export const BondIssuingInstitution: MessageFns<BondIssuingInstitution> = {
  encode(message: BondIssuingInstitution, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondIssuingInstitution {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondIssuingInstitution();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondIssuingInstitution {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: BondIssuingInstitution): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondIssuingInstitution>, I>>(base?: I): BondIssuingInstitution {
    return BondIssuingInstitution.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondIssuingInstitution>, I>>(object: I): BondIssuingInstitution {
    const message = createBaseBondIssuingInstitution();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseAllISINSResponse(): AllISINSResponse {
  return { bondIssuingInstitutions: [] };
}

export const AllISINSResponse: MessageFns<AllISINSResponse> = {
  encode(message: AllISINSResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bondIssuingInstitutions) {
      BondIssuingInstitution.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllISINSResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllISINSResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondIssuingInstitutions.push(BondIssuingInstitution.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllISINSResponse {
    return {
      bondIssuingInstitutions: globalThis.Array.isArray(object?.bondIssuingInstitutions)
        ? object.bondIssuingInstitutions.map((e: any) => BondIssuingInstitution.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AllISINSResponse): unknown {
    const obj: any = {};
    if (message.bondIssuingInstitutions?.length) {
      obj.bondIssuingInstitutions = message.bondIssuingInstitutions.map((e) => BondIssuingInstitution.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllISINSResponse>, I>>(base?: I): AllISINSResponse {
    return AllISINSResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllISINSResponse>, I>>(object: I): AllISINSResponse {
    const message = createBaseAllISINSResponse();
    message.bondIssuingInstitutions =
      object.bondIssuingInstitutions?.map((e) => BondIssuingInstitution.fromPartial(e)) || [];
    return message;
  },
};

function createBasePartyDetailsRequest(): PartyDetailsRequest {
  return {};
}

export const PartyDetailsRequest: MessageFns<PartyDetailsRequest> = {
  encode(_: PartyDetailsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PartyDetailsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePartyDetailsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): PartyDetailsRequest {
    return {};
  },

  toJSON(_: PartyDetailsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<PartyDetailsRequest>, I>>(base?: I): PartyDetailsRequest {
    return PartyDetailsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PartyDetailsRequest>, I>>(_: I): PartyDetailsRequest {
    const message = createBasePartyDetailsRequest();
    return message;
  },
};

function createBasePartyDetail(): PartyDetail {
  return { id: "", name: "" };
}

export const PartyDetail: MessageFns<PartyDetail> = {
  encode(message: PartyDetail, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PartyDetail {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePartyDetail();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PartyDetail {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: PartyDetail): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PartyDetail>, I>>(base?: I): PartyDetail {
    return PartyDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PartyDetail>, I>>(object: I): PartyDetail {
    const message = createBasePartyDetail();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBasePartyDetailsResponse(): PartyDetailsResponse {
  return { partyDetails: [] };
}

export const PartyDetailsResponse: MessageFns<PartyDetailsResponse> = {
  encode(message: PartyDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.partyDetails) {
      PartyDetail.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PartyDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePartyDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.partyDetails.push(PartyDetail.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PartyDetailsResponse {
    return {
      partyDetails: globalThis.Array.isArray(object?.partyDetails)
        ? object.partyDetails.map((e: any) => PartyDetail.fromJSON(e))
        : [],
    };
  },

  toJSON(message: PartyDetailsResponse): unknown {
    const obj: any = {};
    if (message.partyDetails?.length) {
      obj.partyDetails = message.partyDetails.map((e) => PartyDetail.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PartyDetailsResponse>, I>>(base?: I): PartyDetailsResponse {
    return PartyDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PartyDetailsResponse>, I>>(object: I): PartyDetailsResponse {
    const message = createBasePartyDetailsResponse();
    message.partyDetails = object.partyDetails?.map((e) => PartyDetail.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMediaItemRequest(): MediaItemRequest {
  return { id: "", parentType: 0 };
}

export const MediaItemRequest: MessageFns<MediaItemRequest> = {
  encode(message: MediaItemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.parentType !== 0) {
      writer.uint32(16).int32(message.parentType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MediaItemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMediaItemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.parentType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MediaItemRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      parentType: isSet(object.parentType) ? bondMediaItemParentTypeFromJSON(object.parentType) : 0,
    };
  },

  toJSON(message: MediaItemRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.parentType !== 0) {
      obj.parentType = bondMediaItemParentTypeToJSON(message.parentType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MediaItemRequest>, I>>(base?: I): MediaItemRequest {
    return MediaItemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MediaItemRequest>, I>>(object: I): MediaItemRequest {
    const message = createBaseMediaItemRequest();
    message.id = object.id ?? "";
    message.parentType = object.parentType ?? 0;
    return message;
  },
};

function createBaseMediaItemDashboard(): MediaItemDashboard {
  return {
    id: "",
    parentId: "",
    parentType: 0,
    section: "",
    mediaType: 0,
    url: "",
    screenType: 0,
    redirectDeeplink: "",
    createdAt: "",
    updatedAt: "",
    isActive: false,
  };
}

export const MediaItemDashboard: MessageFns<MediaItemDashboard> = {
  encode(message: MediaItemDashboard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.parentId !== "") {
      writer.uint32(18).string(message.parentId);
    }
    if (message.parentType !== 0) {
      writer.uint32(24).int32(message.parentType);
    }
    if (message.section !== "") {
      writer.uint32(34).string(message.section);
    }
    if (message.mediaType !== 0) {
      writer.uint32(40).int32(message.mediaType);
    }
    if (message.url !== "") {
      writer.uint32(50).string(message.url);
    }
    if (message.screenType !== 0) {
      writer.uint32(56).int32(message.screenType);
    }
    if (message.redirectDeeplink !== "") {
      writer.uint32(66).string(message.redirectDeeplink);
    }
    if (message.createdAt !== "") {
      writer.uint32(74).string(message.createdAt);
    }
    if (message.updatedAt !== "") {
      writer.uint32(82).string(message.updatedAt);
    }
    if (message.isActive !== false) {
      writer.uint32(88).bool(message.isActive);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MediaItemDashboard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMediaItemDashboard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.parentId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.parentType = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.section = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.mediaType = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.url = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.screenType = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.redirectDeeplink = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.createdAt = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.updatedAt = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MediaItemDashboard {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      parentId: isSet(object.parentId) ? globalThis.String(object.parentId) : "",
      parentType: isSet(object.parentType) ? bondMediaItemParentTypeFromJSON(object.parentType) : 0,
      section: isSet(object.section) ? globalThis.String(object.section) : "",
      mediaType: isSet(object.mediaType) ? mediaTypeFromJSON(object.mediaType) : 0,
      url: isSet(object.url) ? globalThis.String(object.url) : "",
      screenType: isSet(object.screenType) ? screenTypeFromJSON(object.screenType) : 0,
      redirectDeeplink: isSet(object.redirectDeeplink) ? globalThis.String(object.redirectDeeplink) : "",
      createdAt: isSet(object.createdAt) ? globalThis.String(object.createdAt) : "",
      updatedAt: isSet(object.updatedAt) ? globalThis.String(object.updatedAt) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
    };
  },

  toJSON(message: MediaItemDashboard): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.parentId !== "") {
      obj.parentId = message.parentId;
    }
    if (message.parentType !== 0) {
      obj.parentType = bondMediaItemParentTypeToJSON(message.parentType);
    }
    if (message.section !== "") {
      obj.section = message.section;
    }
    if (message.mediaType !== 0) {
      obj.mediaType = mediaTypeToJSON(message.mediaType);
    }
    if (message.url !== "") {
      obj.url = message.url;
    }
    if (message.screenType !== 0) {
      obj.screenType = screenTypeToJSON(message.screenType);
    }
    if (message.redirectDeeplink !== "") {
      obj.redirectDeeplink = message.redirectDeeplink;
    }
    if (message.createdAt !== "") {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== "") {
      obj.updatedAt = message.updatedAt;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MediaItemDashboard>, I>>(base?: I): MediaItemDashboard {
    return MediaItemDashboard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MediaItemDashboard>, I>>(object: I): MediaItemDashboard {
    const message = createBaseMediaItemDashboard();
    message.id = object.id ?? "";
    message.parentId = object.parentId ?? "";
    message.parentType = object.parentType ?? 0;
    message.section = object.section ?? "";
    message.mediaType = object.mediaType ?? 0;
    message.url = object.url ?? "";
    message.screenType = object.screenType ?? 0;
    message.redirectDeeplink = object.redirectDeeplink ?? "";
    message.createdAt = object.createdAt ?? "";
    message.updatedAt = object.updatedAt ?? "";
    message.isActive = object.isActive ?? false;
    return message;
  },
};

function createBaseMediaItemResponse(): MediaItemResponse {
  return { mediaItems: [] };
}

export const MediaItemResponse: MessageFns<MediaItemResponse> = {
  encode(message: MediaItemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.mediaItems) {
      MediaItemDashboard.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MediaItemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMediaItemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.mediaItems.push(MediaItemDashboard.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MediaItemResponse {
    return {
      mediaItems: globalThis.Array.isArray(object?.mediaItems)
        ? object.mediaItems.map((e: any) => MediaItemDashboard.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MediaItemResponse): unknown {
    const obj: any = {};
    if (message.mediaItems?.length) {
      obj.mediaItems = message.mediaItems.map((e) => MediaItemDashboard.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MediaItemResponse>, I>>(base?: I): MediaItemResponse {
    return MediaItemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MediaItemResponse>, I>>(object: I): MediaItemResponse {
    const message = createBaseMediaItemResponse();
    message.mediaItems = object.mediaItems?.map((e) => MediaItemDashboard.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMediaItemUpdateRequest(): MediaItemUpdateRequest {
  return {
    id: "",
    parentType: 0,
    section: "",
    mediaType: 0,
    mediaUrl: "",
    screenType: 0,
    redirectDeeplink: "",
    isActive: false,
  };
}

export const MediaItemUpdateRequest: MessageFns<MediaItemUpdateRequest> = {
  encode(message: MediaItemUpdateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.parentType !== 0) {
      writer.uint32(24).int32(message.parentType);
    }
    if (message.section !== "") {
      writer.uint32(34).string(message.section);
    }
    if (message.mediaType !== 0) {
      writer.uint32(40).int32(message.mediaType);
    }
    if (message.mediaUrl !== "") {
      writer.uint32(50).string(message.mediaUrl);
    }
    if (message.screenType !== 0) {
      writer.uint32(56).int32(message.screenType);
    }
    if (message.redirectDeeplink !== "") {
      writer.uint32(66).string(message.redirectDeeplink);
    }
    if (message.isActive !== false) {
      writer.uint32(72).bool(message.isActive);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MediaItemUpdateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMediaItemUpdateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.parentType = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.section = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.mediaType = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.mediaUrl = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.screenType = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.redirectDeeplink = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MediaItemUpdateRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      parentType: isSet(object.parentType) ? bondMediaItemParentTypeFromJSON(object.parentType) : 0,
      section: isSet(object.section) ? globalThis.String(object.section) : "",
      mediaType: isSet(object.mediaType) ? mediaTypeFromJSON(object.mediaType) : 0,
      mediaUrl: isSet(object.mediaUrl) ? globalThis.String(object.mediaUrl) : "",
      screenType: isSet(object.screenType) ? screenTypeFromJSON(object.screenType) : 0,
      redirectDeeplink: isSet(object.redirectDeeplink) ? globalThis.String(object.redirectDeeplink) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
    };
  },

  toJSON(message: MediaItemUpdateRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.parentType !== 0) {
      obj.parentType = bondMediaItemParentTypeToJSON(message.parentType);
    }
    if (message.section !== "") {
      obj.section = message.section;
    }
    if (message.mediaType !== 0) {
      obj.mediaType = mediaTypeToJSON(message.mediaType);
    }
    if (message.mediaUrl !== "") {
      obj.mediaUrl = message.mediaUrl;
    }
    if (message.screenType !== 0) {
      obj.screenType = screenTypeToJSON(message.screenType);
    }
    if (message.redirectDeeplink !== "") {
      obj.redirectDeeplink = message.redirectDeeplink;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MediaItemUpdateRequest>, I>>(base?: I): MediaItemUpdateRequest {
    return MediaItemUpdateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MediaItemUpdateRequest>, I>>(object: I): MediaItemUpdateRequest {
    const message = createBaseMediaItemUpdateRequest();
    message.id = object.id ?? "";
    message.parentType = object.parentType ?? 0;
    message.section = object.section ?? "";
    message.mediaType = object.mediaType ?? 0;
    message.mediaUrl = object.mediaUrl ?? "";
    message.screenType = object.screenType ?? 0;
    message.redirectDeeplink = object.redirectDeeplink ?? "";
    message.isActive = object.isActive ?? false;
    return message;
  },
};

function createBaseMediaItemUpdateResponse(): MediaItemUpdateResponse {
  return {};
}

export const MediaItemUpdateResponse: MessageFns<MediaItemUpdateResponse> = {
  encode(_: MediaItemUpdateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MediaItemUpdateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMediaItemUpdateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): MediaItemUpdateResponse {
    return {};
  },

  toJSON(_: MediaItemUpdateResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<MediaItemUpdateResponse>, I>>(base?: I): MediaItemUpdateResponse {
    return MediaItemUpdateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MediaItemUpdateResponse>, I>>(_: I): MediaItemUpdateResponse {
    const message = createBaseMediaItemUpdateResponse();
    return message;
  },
};

function createBaseBondDetailRequest(): BondDetailRequest {
  return { isisnCode: "" };
}

export const BondDetailRequest: MessageFns<BondDetailRequest> = {
  encode(message: BondDetailRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isisnCode !== "") {
      writer.uint32(10).string(message.isisnCode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondDetailRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondDetailRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.isisnCode = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondDetailRequest {
    return { isisnCode: isSet(object.isisnCode) ? globalThis.String(object.isisnCode) : "" };
  },

  toJSON(message: BondDetailRequest): unknown {
    const obj: any = {};
    if (message.isisnCode !== "") {
      obj.isisnCode = message.isisnCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondDetailRequest>, I>>(base?: I): BondDetailRequest {
    return BondDetailRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondDetailRequest>, I>>(object: I): BondDetailRequest {
    const message = createBaseBondDetailRequest();
    message.isisnCode = object.isisnCode ?? "";
    return message;
  },
};

function createBaseAllBond(): AllBond {
  return {
    id: "",
    displayTitle: "",
    isActive: false,
    name: "",
    investabilityStatus: 0,
    isinCode: "",
    maturityDate: "",
    couponRate: 0,
    couponFrequency: 0,
    perUserPurchaseLimit: 0,
    bondType: 0,
    includeInCollection: false,
    isSoldOut: false,
    recordDate: "",
    isRecordDateVerified: false,
    bondCashflowScheduleId: "",
  };
}

export const AllBond: MessageFns<AllBond> = {
  encode(message: AllBond, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.displayTitle !== "") {
      writer.uint32(18).string(message.displayTitle);
    }
    if (message.isActive !== false) {
      writer.uint32(24).bool(message.isActive);
    }
    if (message.name !== "") {
      writer.uint32(34).string(message.name);
    }
    if (message.investabilityStatus !== 0) {
      writer.uint32(40).int32(message.investabilityStatus);
    }
    if (message.isinCode !== "") {
      writer.uint32(50).string(message.isinCode);
    }
    if (message.maturityDate !== "") {
      writer.uint32(58).string(message.maturityDate);
    }
    if (message.couponRate !== 0) {
      writer.uint32(65).double(message.couponRate);
    }
    if (message.couponFrequency !== 0) {
      writer.uint32(72).int32(message.couponFrequency);
    }
    if (message.perUserPurchaseLimit !== 0) {
      writer.uint32(80).int32(message.perUserPurchaseLimit);
    }
    if (message.bondType !== 0) {
      writer.uint32(88).int32(message.bondType);
    }
    if (message.includeInCollection !== false) {
      writer.uint32(96).bool(message.includeInCollection);
    }
    if (message.isSoldOut !== false) {
      writer.uint32(104).bool(message.isSoldOut);
    }
    if (message.recordDate !== "") {
      writer.uint32(114).string(message.recordDate);
    }
    if (message.isRecordDateVerified !== false) {
      writer.uint32(120).bool(message.isRecordDateVerified);
    }
    if (message.bondCashflowScheduleId !== "") {
      writer.uint32(130).string(message.bondCashflowScheduleId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllBond {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllBond();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.displayTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.investabilityStatus = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.isinCode = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.maturityDate = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.couponRate = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.couponFrequency = reader.int32() as any;
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.perUserPurchaseLimit = reader.int32();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.bondType = reader.int32() as any;
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.includeInCollection = reader.bool();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.isSoldOut = reader.bool();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.recordDate = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.isRecordDateVerified = reader.bool();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.bondCashflowScheduleId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllBond {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      displayTitle: isSet(object.displayTitle) ? globalThis.String(object.displayTitle) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      investabilityStatus: isSet(object.investabilityStatus)
        ? investabilityStatusFromJSON(object.investabilityStatus)
        : 0,
      isinCode: isSet(object.isinCode) ? globalThis.String(object.isinCode) : "",
      maturityDate: isSet(object.maturityDate) ? globalThis.String(object.maturityDate) : "",
      couponRate: isSet(object.couponRate) ? globalThis.Number(object.couponRate) : 0,
      couponFrequency: isSet(object.couponFrequency) ? repaymentFrequencyFromJSON(object.couponFrequency) : 0,
      perUserPurchaseLimit: isSet(object.perUserPurchaseLimit) ? globalThis.Number(object.perUserPurchaseLimit) : 0,
      bondType: isSet(object.bondType) ? bondTypeFromJSON(object.bondType) : 0,
      includeInCollection: isSet(object.includeInCollection) ? globalThis.Boolean(object.includeInCollection) : false,
      isSoldOut: isSet(object.isSoldOut) ? globalThis.Boolean(object.isSoldOut) : false,
      recordDate: isSet(object.recordDate) ? globalThis.String(object.recordDate) : "",
      isRecordDateVerified: isSet(object.isRecordDateVerified)
        ? globalThis.Boolean(object.isRecordDateVerified)
        : false,
      bondCashflowScheduleId: isSet(object.bondCashflowScheduleId)
        ? globalThis.String(object.bondCashflowScheduleId)
        : "",
    };
  },

  toJSON(message: AllBond): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.displayTitle !== "") {
      obj.displayTitle = message.displayTitle;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.investabilityStatus !== 0) {
      obj.investabilityStatus = investabilityStatusToJSON(message.investabilityStatus);
    }
    if (message.isinCode !== "") {
      obj.isinCode = message.isinCode;
    }
    if (message.maturityDate !== "") {
      obj.maturityDate = message.maturityDate;
    }
    if (message.couponRate !== 0) {
      obj.couponRate = message.couponRate;
    }
    if (message.couponFrequency !== 0) {
      obj.couponFrequency = repaymentFrequencyToJSON(message.couponFrequency);
    }
    if (message.perUserPurchaseLimit !== 0) {
      obj.perUserPurchaseLimit = Math.round(message.perUserPurchaseLimit);
    }
    if (message.bondType !== 0) {
      obj.bondType = bondTypeToJSON(message.bondType);
    }
    if (message.includeInCollection !== false) {
      obj.includeInCollection = message.includeInCollection;
    }
    if (message.isSoldOut !== false) {
      obj.isSoldOut = message.isSoldOut;
    }
    if (message.recordDate !== "") {
      obj.recordDate = message.recordDate;
    }
    if (message.isRecordDateVerified !== false) {
      obj.isRecordDateVerified = message.isRecordDateVerified;
    }
    if (message.bondCashflowScheduleId !== "") {
      obj.bondCashflowScheduleId = message.bondCashflowScheduleId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllBond>, I>>(base?: I): AllBond {
    return AllBond.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllBond>, I>>(object: I): AllBond {
    const message = createBaseAllBond();
    message.id = object.id ?? "";
    message.displayTitle = object.displayTitle ?? "";
    message.isActive = object.isActive ?? false;
    message.name = object.name ?? "";
    message.investabilityStatus = object.investabilityStatus ?? 0;
    message.isinCode = object.isinCode ?? "";
    message.maturityDate = object.maturityDate ?? "";
    message.couponRate = object.couponRate ?? 0;
    message.couponFrequency = object.couponFrequency ?? 0;
    message.perUserPurchaseLimit = object.perUserPurchaseLimit ?? 0;
    message.bondType = object.bondType ?? 0;
    message.includeInCollection = object.includeInCollection ?? false;
    message.isSoldOut = object.isSoldOut ?? false;
    message.recordDate = object.recordDate ?? "";
    message.isRecordDateVerified = object.isRecordDateVerified ?? false;
    message.bondCashflowScheduleId = object.bondCashflowScheduleId ?? "";
    return message;
  },
};

function createBaseAllBondDetailsRequest(): AllBondDetailsRequest {
  return {};
}

export const AllBondDetailsRequest: MessageFns<AllBondDetailsRequest> = {
  encode(_: AllBondDetailsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllBondDetailsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllBondDetailsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): AllBondDetailsRequest {
    return {};
  },

  toJSON(_: AllBondDetailsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<AllBondDetailsRequest>, I>>(base?: I): AllBondDetailsRequest {
    return AllBondDetailsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllBondDetailsRequest>, I>>(_: I): AllBondDetailsRequest {
    const message = createBaseAllBondDetailsRequest();
    return message;
  },
};

function createBaseAllBondDetailsResponse(): AllBondDetailsResponse {
  return { bondDetails: [] };
}

export const AllBondDetailsResponse: MessageFns<AllBondDetailsResponse> = {
  encode(message: AllBondDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bondDetails) {
      AllBond.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllBondDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllBondDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondDetails.push(AllBond.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllBondDetailsResponse {
    return {
      bondDetails: globalThis.Array.isArray(object?.bondDetails)
        ? object.bondDetails.map((e: any) => AllBond.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AllBondDetailsResponse): unknown {
    const obj: any = {};
    if (message.bondDetails?.length) {
      obj.bondDetails = message.bondDetails.map((e) => AllBond.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllBondDetailsResponse>, I>>(base?: I): AllBondDetailsResponse {
    return AllBondDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllBondDetailsResponse>, I>>(object: I): AllBondDetailsResponse {
    const message = createBaseAllBondDetailsResponse();
    message.bondDetails = object.bondDetails?.map((e) => AllBond.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUpdateOfferingActiveStatusRequest(): UpdateOfferingActiveStatusRequest {
  return { id: "", isActive: false };
}

export const UpdateOfferingActiveStatusRequest: MessageFns<UpdateOfferingActiveStatusRequest> = {
  encode(message: UpdateOfferingActiveStatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.isActive !== false) {
      writer.uint32(16).bool(message.isActive);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateOfferingActiveStatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateOfferingActiveStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateOfferingActiveStatusRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
    };
  },

  toJSON(message: UpdateOfferingActiveStatusRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateOfferingActiveStatusRequest>, I>>(
    base?: I,
  ): UpdateOfferingActiveStatusRequest {
    return UpdateOfferingActiveStatusRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateOfferingActiveStatusRequest>, I>>(
    object: I,
  ): UpdateOfferingActiveStatusRequest {
    const message = createBaseUpdateOfferingActiveStatusRequest();
    message.id = object.id ?? "";
    message.isActive = object.isActive ?? false;
    return message;
  },
};

function createBaseUpdateOfferingActiveStatusResponse(): UpdateOfferingActiveStatusResponse {
  return {};
}

export const UpdateOfferingActiveStatusResponse: MessageFns<UpdateOfferingActiveStatusResponse> = {
  encode(_: UpdateOfferingActiveStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateOfferingActiveStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateOfferingActiveStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateOfferingActiveStatusResponse {
    return {};
  },

  toJSON(_: UpdateOfferingActiveStatusResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateOfferingActiveStatusResponse>, I>>(
    base?: I,
  ): UpdateOfferingActiveStatusResponse {
    return UpdateOfferingActiveStatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateOfferingActiveStatusResponse>, I>>(
    _: I,
  ): UpdateOfferingActiveStatusResponse {
    const message = createBaseUpdateOfferingActiveStatusResponse();
    return message;
  },
};

function createBaseUpdateBondOfferingYieldRequest(): UpdateBondOfferingYieldRequest {
  return { bondOfferingId: "", yield: 0 };
}

export const UpdateBondOfferingYieldRequest: MessageFns<UpdateBondOfferingYieldRequest> = {
  encode(message: UpdateBondOfferingYieldRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bondOfferingId !== "") {
      writer.uint32(10).string(message.bondOfferingId);
    }
    if (message.yield !== 0) {
      writer.uint32(17).double(message.yield);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateBondOfferingYieldRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateBondOfferingYieldRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondOfferingId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.yield = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateBondOfferingYieldRequest {
    return {
      bondOfferingId: isSet(object.bondOfferingId) ? globalThis.String(object.bondOfferingId) : "",
      yield: isSet(object.yield) ? globalThis.Number(object.yield) : 0,
    };
  },

  toJSON(message: UpdateBondOfferingYieldRequest): unknown {
    const obj: any = {};
    if (message.bondOfferingId !== "") {
      obj.bondOfferingId = message.bondOfferingId;
    }
    if (message.yield !== 0) {
      obj.yield = message.yield;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateBondOfferingYieldRequest>, I>>(base?: I): UpdateBondOfferingYieldRequest {
    return UpdateBondOfferingYieldRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBondOfferingYieldRequest>, I>>(
    object: I,
  ): UpdateBondOfferingYieldRequest {
    const message = createBaseUpdateBondOfferingYieldRequest();
    message.bondOfferingId = object.bondOfferingId ?? "";
    message.yield = object.yield ?? 0;
    return message;
  },
};

function createBaseUpdateBondOfferingYieldResponse(): UpdateBondOfferingYieldResponse {
  return {};
}

export const UpdateBondOfferingYieldResponse: MessageFns<UpdateBondOfferingYieldResponse> = {
  encode(_: UpdateBondOfferingYieldResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateBondOfferingYieldResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateBondOfferingYieldResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateBondOfferingYieldResponse {
    return {};
  },

  toJSON(_: UpdateBondOfferingYieldResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateBondOfferingYieldResponse>, I>>(base?: I): UpdateBondOfferingYieldResponse {
    return UpdateBondOfferingYieldResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBondOfferingYieldResponse>, I>>(_: I): UpdateBondOfferingYieldResponse {
    const message = createBaseUpdateBondOfferingYieldResponse();
    return message;
  },
};

function createBaseBondCashflowSchedule(): BondCashflowSchedule {
  return {
    id: "",
    date: "",
    interestPaymentAmount: 0,
    principalRepaymentAmount: 0,
    recordDate: "",
    type: 0,
    faceValue: 0,
    isActive: false,
    maturityAmount: 0,
    isRecordDateVerified: false,
  };
}

export const BondCashflowSchedule: MessageFns<BondCashflowSchedule> = {
  encode(message: BondCashflowSchedule, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.date !== "") {
      writer.uint32(18).string(message.date);
    }
    if (message.interestPaymentAmount !== 0) {
      writer.uint32(25).double(message.interestPaymentAmount);
    }
    if (message.principalRepaymentAmount !== 0) {
      writer.uint32(33).double(message.principalRepaymentAmount);
    }
    if (message.recordDate !== "") {
      writer.uint32(42).string(message.recordDate);
    }
    if (message.type !== 0) {
      writer.uint32(48).int32(message.type);
    }
    if (message.faceValue !== 0) {
      writer.uint32(57).double(message.faceValue);
    }
    if (message.isActive !== false) {
      writer.uint32(64).bool(message.isActive);
    }
    if (message.maturityAmount !== 0) {
      writer.uint32(73).double(message.maturityAmount);
    }
    if (message.isRecordDateVerified !== false) {
      writer.uint32(80).bool(message.isRecordDateVerified);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondCashflowSchedule {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondCashflowSchedule();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.date = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.interestPaymentAmount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.principalRepaymentAmount = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.recordDate = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.faceValue = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 73) {
            break;
          }

          message.maturityAmount = reader.double();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.isRecordDateVerified = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondCashflowSchedule {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      date: isSet(object.date) ? globalThis.String(object.date) : "",
      interestPaymentAmount: isSet(object.interestPaymentAmount) ? globalThis.Number(object.interestPaymentAmount) : 0,
      principalRepaymentAmount: isSet(object.principalRepaymentAmount)
        ? globalThis.Number(object.principalRepaymentAmount)
        : 0,
      recordDate: isSet(object.recordDate) ? globalThis.String(object.recordDate) : "",
      type: isSet(object.type) ? repaymentTypeFromJSON(object.type) : 0,
      faceValue: isSet(object.faceValue) ? globalThis.Number(object.faceValue) : 0,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      maturityAmount: isSet(object.maturityAmount) ? globalThis.Number(object.maturityAmount) : 0,
      isRecordDateVerified: isSet(object.isRecordDateVerified)
        ? globalThis.Boolean(object.isRecordDateVerified)
        : false,
    };
  },

  toJSON(message: BondCashflowSchedule): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.date !== "") {
      obj.date = message.date;
    }
    if (message.interestPaymentAmount !== 0) {
      obj.interestPaymentAmount = message.interestPaymentAmount;
    }
    if (message.principalRepaymentAmount !== 0) {
      obj.principalRepaymentAmount = message.principalRepaymentAmount;
    }
    if (message.recordDate !== "") {
      obj.recordDate = message.recordDate;
    }
    if (message.type !== 0) {
      obj.type = repaymentTypeToJSON(message.type);
    }
    if (message.faceValue !== 0) {
      obj.faceValue = message.faceValue;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.maturityAmount !== 0) {
      obj.maturityAmount = message.maturityAmount;
    }
    if (message.isRecordDateVerified !== false) {
      obj.isRecordDateVerified = message.isRecordDateVerified;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondCashflowSchedule>, I>>(base?: I): BondCashflowSchedule {
    return BondCashflowSchedule.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondCashflowSchedule>, I>>(object: I): BondCashflowSchedule {
    const message = createBaseBondCashflowSchedule();
    message.id = object.id ?? "";
    message.date = object.date ?? "";
    message.interestPaymentAmount = object.interestPaymentAmount ?? 0;
    message.principalRepaymentAmount = object.principalRepaymentAmount ?? 0;
    message.recordDate = object.recordDate ?? "";
    message.type = object.type ?? 0;
    message.faceValue = object.faceValue ?? 0;
    message.isActive = object.isActive ?? false;
    message.maturityAmount = object.maturityAmount ?? 0;
    message.isRecordDateVerified = object.isRecordDateVerified ?? false;
    return message;
  },
};

function createBaseGetBondCashflowScheduleRequest(): GetBondCashflowScheduleRequest {
  return { bondDetailId: "" };
}

export const GetBondCashflowScheduleRequest: MessageFns<GetBondCashflowScheduleRequest> = {
  encode(message: GetBondCashflowScheduleRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bondDetailId !== "") {
      writer.uint32(10).string(message.bondDetailId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondCashflowScheduleRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondCashflowScheduleRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondDetailId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBondCashflowScheduleRequest {
    return { bondDetailId: isSet(object.bondDetailId) ? globalThis.String(object.bondDetailId) : "" };
  },

  toJSON(message: GetBondCashflowScheduleRequest): unknown {
    const obj: any = {};
    if (message.bondDetailId !== "") {
      obj.bondDetailId = message.bondDetailId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondCashflowScheduleRequest>, I>>(base?: I): GetBondCashflowScheduleRequest {
    return GetBondCashflowScheduleRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondCashflowScheduleRequest>, I>>(
    object: I,
  ): GetBondCashflowScheduleRequest {
    const message = createBaseGetBondCashflowScheduleRequest();
    message.bondDetailId = object.bondDetailId ?? "";
    return message;
  },
};

function createBaseGetBondCashflowScheduleResponse(): GetBondCashflowScheduleResponse {
  return { bondCashflowSchedule: [] };
}

export const GetBondCashflowScheduleResponse: MessageFns<GetBondCashflowScheduleResponse> = {
  encode(message: GetBondCashflowScheduleResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bondCashflowSchedule) {
      BondCashflowSchedule.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondCashflowScheduleResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondCashflowScheduleResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondCashflowSchedule.push(BondCashflowSchedule.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBondCashflowScheduleResponse {
    return {
      bondCashflowSchedule: globalThis.Array.isArray(object?.bondCashflowSchedule)
        ? object.bondCashflowSchedule.map((e: any) => BondCashflowSchedule.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetBondCashflowScheduleResponse): unknown {
    const obj: any = {};
    if (message.bondCashflowSchedule?.length) {
      obj.bondCashflowSchedule = message.bondCashflowSchedule.map((e) => BondCashflowSchedule.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondCashflowScheduleResponse>, I>>(base?: I): GetBondCashflowScheduleResponse {
    return GetBondCashflowScheduleResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondCashflowScheduleResponse>, I>>(
    object: I,
  ): GetBondCashflowScheduleResponse {
    const message = createBaseGetBondCashflowScheduleResponse();
    message.bondCashflowSchedule = object.bondCashflowSchedule?.map((e) => BondCashflowSchedule.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCreateTagRequest(): CreateTagRequest {
  return { name: "", color: "", bgColor: "", shimmerColor: "" };
}

export const CreateTagRequest: MessageFns<CreateTagRequest> = {
  encode(message: CreateTagRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.color !== "") {
      writer.uint32(18).string(message.color);
    }
    if (message.bgColor !== "") {
      writer.uint32(26).string(message.bgColor);
    }
    if (message.shimmerColor !== "") {
      writer.uint32(34).string(message.shimmerColor);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateTagRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateTagRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bgColor = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.shimmerColor = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateTagRequest {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      bgColor: isSet(object.bgColor) ? globalThis.String(object.bgColor) : "",
      shimmerColor: isSet(object.shimmerColor) ? globalThis.String(object.shimmerColor) : "",
    };
  },

  toJSON(message: CreateTagRequest): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.bgColor !== "") {
      obj.bgColor = message.bgColor;
    }
    if (message.shimmerColor !== "") {
      obj.shimmerColor = message.shimmerColor;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateTagRequest>, I>>(base?: I): CreateTagRequest {
    return CreateTagRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateTagRequest>, I>>(object: I): CreateTagRequest {
    const message = createBaseCreateTagRequest();
    message.name = object.name ?? "";
    message.color = object.color ?? "";
    message.bgColor = object.bgColor ?? "";
    message.shimmerColor = object.shimmerColor ?? "";
    return message;
  },
};

function createBaseCreateTagResponse(): CreateTagResponse {
  return {};
}

export const CreateTagResponse: MessageFns<CreateTagResponse> = {
  encode(_: CreateTagResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateTagResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateTagResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): CreateTagResponse {
    return {};
  },

  toJSON(_: CreateTagResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateTagResponse>, I>>(base?: I): CreateTagResponse {
    return CreateTagResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateTagResponse>, I>>(_: I): CreateTagResponse {
    const message = createBaseCreateTagResponse();
    return message;
  },
};

function createBaseGetAllTagsRequest(): GetAllTagsRequest {
  return {};
}

export const GetAllTagsRequest: MessageFns<GetAllTagsRequest> = {
  encode(_: GetAllTagsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllTagsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllTagsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): GetAllTagsRequest {
    return {};
  },

  toJSON(_: GetAllTagsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAllTagsRequest>, I>>(base?: I): GetAllTagsRequest {
    return GetAllTagsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAllTagsRequest>, I>>(_: I): GetAllTagsRequest {
    const message = createBaseGetAllTagsRequest();
    return message;
  },
};

function createBaseTag(): Tag {
  return { id: "", name: "", color: "", bgColor: "", shimmerColor: "" };
}

export const Tag: MessageFns<Tag> = {
  encode(message: Tag, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.color !== "") {
      writer.uint32(26).string(message.color);
    }
    if (message.bgColor !== "") {
      writer.uint32(34).string(message.bgColor);
    }
    if (message.shimmerColor !== "") {
      writer.uint32(42).string(message.shimmerColor);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tag {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTag();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bgColor = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.shimmerColor = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Tag {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      bgColor: isSet(object.bgColor) ? globalThis.String(object.bgColor) : "",
      shimmerColor: isSet(object.shimmerColor) ? globalThis.String(object.shimmerColor) : "",
    };
  },

  toJSON(message: Tag): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.bgColor !== "") {
      obj.bgColor = message.bgColor;
    }
    if (message.shimmerColor !== "") {
      obj.shimmerColor = message.shimmerColor;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Tag>, I>>(base?: I): Tag {
    return Tag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tag>, I>>(object: I): Tag {
    const message = createBaseTag();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.color = object.color ?? "";
    message.bgColor = object.bgColor ?? "";
    message.shimmerColor = object.shimmerColor ?? "";
    return message;
  },
};

function createBaseGetAllTagsResponse(): GetAllTagsResponse {
  return { tag: [] };
}

export const GetAllTagsResponse: MessageFns<GetAllTagsResponse> = {
  encode(message: GetAllTagsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.tag) {
      Tag.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllTagsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllTagsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tag.push(Tag.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAllTagsResponse {
    return { tag: globalThis.Array.isArray(object?.tag) ? object.tag.map((e: any) => Tag.fromJSON(e)) : [] };
  },

  toJSON(message: GetAllTagsResponse): unknown {
    const obj: any = {};
    if (message.tag?.length) {
      obj.tag = message.tag.map((e) => Tag.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAllTagsResponse>, I>>(base?: I): GetAllTagsResponse {
    return GetAllTagsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAllTagsResponse>, I>>(object: I): GetAllTagsResponse {
    const message = createBaseGetAllTagsResponse();
    message.tag = object.tag?.map((e) => Tag.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTagCollectionItem(): TagCollectionItem {
  return { id: "", tagId: "", collectionId: "", collectionItemIsinCode: "", tagName: "" };
}

export const TagCollectionItem: MessageFns<TagCollectionItem> = {
  encode(message: TagCollectionItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.tagId !== "") {
      writer.uint32(18).string(message.tagId);
    }
    if (message.collectionId !== "") {
      writer.uint32(26).string(message.collectionId);
    }
    if (message.collectionItemIsinCode !== "") {
      writer.uint32(34).string(message.collectionItemIsinCode);
    }
    if (message.tagName !== "") {
      writer.uint32(42).string(message.tagName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TagCollectionItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTagCollectionItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tagId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.collectionItemIsinCode = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.tagName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TagCollectionItem {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      tagId: isSet(object.tagId) ? globalThis.String(object.tagId) : "",
      collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "",
      collectionItemIsinCode: isSet(object.collectionItemIsinCode)
        ? globalThis.String(object.collectionItemIsinCode)
        : "",
      tagName: isSet(object.tagName) ? globalThis.String(object.tagName) : "",
    };
  },

  toJSON(message: TagCollectionItem): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.tagId !== "") {
      obj.tagId = message.tagId;
    }
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    if (message.collectionItemIsinCode !== "") {
      obj.collectionItemIsinCode = message.collectionItemIsinCode;
    }
    if (message.tagName !== "") {
      obj.tagName = message.tagName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TagCollectionItem>, I>>(base?: I): TagCollectionItem {
    return TagCollectionItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TagCollectionItem>, I>>(object: I): TagCollectionItem {
    const message = createBaseTagCollectionItem();
    message.id = object.id ?? "";
    message.tagId = object.tagId ?? "";
    message.collectionId = object.collectionId ?? "";
    message.collectionItemIsinCode = object.collectionItemIsinCode ?? "";
    message.tagName = object.tagName ?? "";
    return message;
  },
};

function createBaseCreateTagCollectionItemRequest(): CreateTagCollectionItemRequest {
  return { tagId: "", collectionId: "", collectionItemIsinCode: "" };
}

export const CreateTagCollectionItemRequest: MessageFns<CreateTagCollectionItemRequest> = {
  encode(message: CreateTagCollectionItemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tagId !== "") {
      writer.uint32(10).string(message.tagId);
    }
    if (message.collectionId !== "") {
      writer.uint32(18).string(message.collectionId);
    }
    if (message.collectionItemIsinCode !== "") {
      writer.uint32(26).string(message.collectionItemIsinCode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateTagCollectionItemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateTagCollectionItemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tagId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.collectionItemIsinCode = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateTagCollectionItemRequest {
    return {
      tagId: isSet(object.tagId) ? globalThis.String(object.tagId) : "",
      collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "",
      collectionItemIsinCode: isSet(object.collectionItemIsinCode)
        ? globalThis.String(object.collectionItemIsinCode)
        : "",
    };
  },

  toJSON(message: CreateTagCollectionItemRequest): unknown {
    const obj: any = {};
    if (message.tagId !== "") {
      obj.tagId = message.tagId;
    }
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    if (message.collectionItemIsinCode !== "") {
      obj.collectionItemIsinCode = message.collectionItemIsinCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateTagCollectionItemRequest>, I>>(base?: I): CreateTagCollectionItemRequest {
    return CreateTagCollectionItemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateTagCollectionItemRequest>, I>>(
    object: I,
  ): CreateTagCollectionItemRequest {
    const message = createBaseCreateTagCollectionItemRequest();
    message.tagId = object.tagId ?? "";
    message.collectionId = object.collectionId ?? "";
    message.collectionItemIsinCode = object.collectionItemIsinCode ?? "";
    return message;
  },
};

function createBaseCreateTagCollectionItemResponse(): CreateTagCollectionItemResponse {
  return {};
}

export const CreateTagCollectionItemResponse: MessageFns<CreateTagCollectionItemResponse> = {
  encode(_: CreateTagCollectionItemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateTagCollectionItemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateTagCollectionItemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): CreateTagCollectionItemResponse {
    return {};
  },

  toJSON(_: CreateTagCollectionItemResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateTagCollectionItemResponse>, I>>(base?: I): CreateTagCollectionItemResponse {
    return CreateTagCollectionItemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateTagCollectionItemResponse>, I>>(_: I): CreateTagCollectionItemResponse {
    const message = createBaseCreateTagCollectionItemResponse();
    return message;
  },
};

function createBaseGetAllTagCollectionItemRequest(): GetAllTagCollectionItemRequest {
  return {};
}

export const GetAllTagCollectionItemRequest: MessageFns<GetAllTagCollectionItemRequest> = {
  encode(_: GetAllTagCollectionItemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllTagCollectionItemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllTagCollectionItemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): GetAllTagCollectionItemRequest {
    return {};
  },

  toJSON(_: GetAllTagCollectionItemRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAllTagCollectionItemRequest>, I>>(base?: I): GetAllTagCollectionItemRequest {
    return GetAllTagCollectionItemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAllTagCollectionItemRequest>, I>>(_: I): GetAllTagCollectionItemRequest {
    const message = createBaseGetAllTagCollectionItemRequest();
    return message;
  },
};

function createBaseGetAllTagCollectionItemResponse(): GetAllTagCollectionItemResponse {
  return { tagCollectionItem: [] };
}

export const GetAllTagCollectionItemResponse: MessageFns<GetAllTagCollectionItemResponse> = {
  encode(message: GetAllTagCollectionItemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.tagCollectionItem) {
      TagCollectionItem.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllTagCollectionItemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllTagCollectionItemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tagCollectionItem.push(TagCollectionItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAllTagCollectionItemResponse {
    return {
      tagCollectionItem: globalThis.Array.isArray(object?.tagCollectionItem)
        ? object.tagCollectionItem.map((e: any) => TagCollectionItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetAllTagCollectionItemResponse): unknown {
    const obj: any = {};
    if (message.tagCollectionItem?.length) {
      obj.tagCollectionItem = message.tagCollectionItem.map((e) => TagCollectionItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAllTagCollectionItemResponse>, I>>(base?: I): GetAllTagCollectionItemResponse {
    return GetAllTagCollectionItemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAllTagCollectionItemResponse>, I>>(
    object: I,
  ): GetAllTagCollectionItemResponse {
    const message = createBaseGetAllTagCollectionItemResponse();
    message.tagCollectionItem = object.tagCollectionItem?.map((e) => TagCollectionItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseDeleteTagCollectionItemRequest(): DeleteTagCollectionItemRequest {
  return { id: "" };
}

export const DeleteTagCollectionItemRequest: MessageFns<DeleteTagCollectionItemRequest> = {
  encode(message: DeleteTagCollectionItemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteTagCollectionItemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteTagCollectionItemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteTagCollectionItemRequest {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: DeleteTagCollectionItemRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteTagCollectionItemRequest>, I>>(base?: I): DeleteTagCollectionItemRequest {
    return DeleteTagCollectionItemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteTagCollectionItemRequest>, I>>(
    object: I,
  ): DeleteTagCollectionItemRequest {
    const message = createBaseDeleteTagCollectionItemRequest();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseDeleteTagCollectionItemResponse(): DeleteTagCollectionItemResponse {
  return {};
}

export const DeleteTagCollectionItemResponse: MessageFns<DeleteTagCollectionItemResponse> = {
  encode(_: DeleteTagCollectionItemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteTagCollectionItemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteTagCollectionItemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeleteTagCollectionItemResponse {
    return {};
  },

  toJSON(_: DeleteTagCollectionItemResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteTagCollectionItemResponse>, I>>(base?: I): DeleteTagCollectionItemResponse {
    return DeleteTagCollectionItemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteTagCollectionItemResponse>, I>>(_: I): DeleteTagCollectionItemResponse {
    const message = createBaseDeleteTagCollectionItemResponse();
    return message;
  },
};

function createBaseUpdateTagResponse(): UpdateTagResponse {
  return {};
}

export const UpdateTagResponse: MessageFns<UpdateTagResponse> = {
  encode(_: UpdateTagResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateTagResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateTagResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateTagResponse {
    return {};
  },

  toJSON(_: UpdateTagResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateTagResponse>, I>>(base?: I): UpdateTagResponse {
    return UpdateTagResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateTagResponse>, I>>(_: I): UpdateTagResponse {
    const message = createBaseUpdateTagResponse();
    return message;
  },
};

function createBasegetTagRequest(): getTagRequest {
  return { tagId: "" };
}

export const getTagRequest: MessageFns<getTagRequest> = {
  encode(message: getTagRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tagId !== "") {
      writer.uint32(10).string(message.tagId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): getTagRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasegetTagRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tagId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): getTagRequest {
    return { tagId: isSet(object.tagId) ? globalThis.String(object.tagId) : "" };
  },

  toJSON(message: getTagRequest): unknown {
    const obj: any = {};
    if (message.tagId !== "") {
      obj.tagId = message.tagId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<getTagRequest>, I>>(base?: I): getTagRequest {
    return getTagRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<getTagRequest>, I>>(object: I): getTagRequest {
    const message = createBasegetTagRequest();
    message.tagId = object.tagId ?? "";
    return message;
  },
};

function createBaseBondOfferingUpdateRequest(): BondOfferingUpdateRequest {
  return {
    bondOfferingId: "",
    yield: 0,
    minLotSize: "",
    repeatUserDefaultQuantity: "",
    dealType: "",
    bidOffer: "",
    bondSettlementType: "",
    expiryTime: "",
    newUserDefaultQuantity: "",
    isTZeroSettlementSupported: false,
  };
}

export const BondOfferingUpdateRequest: MessageFns<BondOfferingUpdateRequest> = {
  encode(message: BondOfferingUpdateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bondOfferingId !== "") {
      writer.uint32(10).string(message.bondOfferingId);
    }
    if (message.yield !== 0) {
      writer.uint32(17).double(message.yield);
    }
    if (message.minLotSize !== "") {
      writer.uint32(26).string(message.minLotSize);
    }
    if (message.repeatUserDefaultQuantity !== "") {
      writer.uint32(34).string(message.repeatUserDefaultQuantity);
    }
    if (message.dealType !== "") {
      writer.uint32(42).string(message.dealType);
    }
    if (message.bidOffer !== "") {
      writer.uint32(50).string(message.bidOffer);
    }
    if (message.bondSettlementType !== "") {
      writer.uint32(58).string(message.bondSettlementType);
    }
    if (message.expiryTime !== "") {
      writer.uint32(66).string(message.expiryTime);
    }
    if (message.newUserDefaultQuantity !== "") {
      writer.uint32(74).string(message.newUserDefaultQuantity);
    }
    if (message.isTZeroSettlementSupported !== false) {
      writer.uint32(80).bool(message.isTZeroSettlementSupported);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondOfferingUpdateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondOfferingUpdateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondOfferingId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.yield = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.minLotSize = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.repeatUserDefaultQuantity = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.dealType = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.bidOffer = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.bondSettlementType = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.expiryTime = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.newUserDefaultQuantity = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.isTZeroSettlementSupported = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondOfferingUpdateRequest {
    return {
      bondOfferingId: isSet(object.bondOfferingId) ? globalThis.String(object.bondOfferingId) : "",
      yield: isSet(object.yield) ? globalThis.Number(object.yield) : 0,
      minLotSize: isSet(object.minLotSize) ? globalThis.String(object.minLotSize) : "",
      repeatUserDefaultQuantity: isSet(object.repeatUserDefaultQuantity)
        ? globalThis.String(object.repeatUserDefaultQuantity)
        : "",
      dealType: isSet(object.dealType) ? globalThis.String(object.dealType) : "",
      bidOffer: isSet(object.bidOffer) ? globalThis.String(object.bidOffer) : "",
      bondSettlementType: isSet(object.bondSettlementType) ? globalThis.String(object.bondSettlementType) : "",
      expiryTime: isSet(object.expiryTime) ? globalThis.String(object.expiryTime) : "",
      newUserDefaultQuantity: isSet(object.newUserDefaultQuantity)
        ? globalThis.String(object.newUserDefaultQuantity)
        : "",
      isTZeroSettlementSupported: isSet(object.isTZeroSettlementSupported)
        ? globalThis.Boolean(object.isTZeroSettlementSupported)
        : false,
    };
  },

  toJSON(message: BondOfferingUpdateRequest): unknown {
    const obj: any = {};
    if (message.bondOfferingId !== "") {
      obj.bondOfferingId = message.bondOfferingId;
    }
    if (message.yield !== 0) {
      obj.yield = message.yield;
    }
    if (message.minLotSize !== "") {
      obj.minLotSize = message.minLotSize;
    }
    if (message.repeatUserDefaultQuantity !== "") {
      obj.repeatUserDefaultQuantity = message.repeatUserDefaultQuantity;
    }
    if (message.dealType !== "") {
      obj.dealType = message.dealType;
    }
    if (message.bidOffer !== "") {
      obj.bidOffer = message.bidOffer;
    }
    if (message.bondSettlementType !== "") {
      obj.bondSettlementType = message.bondSettlementType;
    }
    if (message.expiryTime !== "") {
      obj.expiryTime = message.expiryTime;
    }
    if (message.newUserDefaultQuantity !== "") {
      obj.newUserDefaultQuantity = message.newUserDefaultQuantity;
    }
    if (message.isTZeroSettlementSupported !== false) {
      obj.isTZeroSettlementSupported = message.isTZeroSettlementSupported;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondOfferingUpdateRequest>, I>>(base?: I): BondOfferingUpdateRequest {
    return BondOfferingUpdateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondOfferingUpdateRequest>, I>>(object: I): BondOfferingUpdateRequest {
    const message = createBaseBondOfferingUpdateRequest();
    message.bondOfferingId = object.bondOfferingId ?? "";
    message.yield = object.yield ?? 0;
    message.minLotSize = object.minLotSize ?? "";
    message.repeatUserDefaultQuantity = object.repeatUserDefaultQuantity ?? "";
    message.dealType = object.dealType ?? "";
    message.bidOffer = object.bidOffer ?? "";
    message.bondSettlementType = object.bondSettlementType ?? "";
    message.expiryTime = object.expiryTime ?? "";
    message.newUserDefaultQuantity = object.newUserDefaultQuantity ?? "";
    message.isTZeroSettlementSupported = object.isTZeroSettlementSupported ?? false;
    return message;
  },
};

function createBaseBondOfferingUpdateResponse(): BondOfferingUpdateResponse {
  return {};
}

export const BondOfferingUpdateResponse: MessageFns<BondOfferingUpdateResponse> = {
  encode(_: BondOfferingUpdateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondOfferingUpdateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondOfferingUpdateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): BondOfferingUpdateResponse {
    return {};
  },

  toJSON(_: BondOfferingUpdateResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<BondOfferingUpdateResponse>, I>>(base?: I): BondOfferingUpdateResponse {
    return BondOfferingUpdateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondOfferingUpdateResponse>, I>>(_: I): BondOfferingUpdateResponse {
    const message = createBaseBondOfferingUpdateResponse();
    return message;
  },
};

function createBaseGetBondPricesRequest(): GetBondPricesRequest {
  return { bondOfferingId: "" };
}

export const GetBondPricesRequest: MessageFns<GetBondPricesRequest> = {
  encode(message: GetBondPricesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bondOfferingId !== "") {
      writer.uint32(10).string(message.bondOfferingId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondPricesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondPricesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondOfferingId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBondPricesRequest {
    return { bondOfferingId: isSet(object.bondOfferingId) ? globalThis.String(object.bondOfferingId) : "" };
  },

  toJSON(message: GetBondPricesRequest): unknown {
    const obj: any = {};
    if (message.bondOfferingId !== "") {
      obj.bondOfferingId = message.bondOfferingId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondPricesRequest>, I>>(base?: I): GetBondPricesRequest {
    return GetBondPricesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondPricesRequest>, I>>(object: I): GetBondPricesRequest {
    const message = createBaseGetBondPricesRequest();
    message.bondOfferingId = object.bondOfferingId ?? "";
    return message;
  },
};

function createBaseBondPrice(): BondPrice {
  return {
    id: "",
    accruedInterest: 0,
    cleanPrice: 0,
    dirtyPrice: 0,
    faceValue: 0,
    isActive: false,
    settlementDate: "",
  };
}

export const BondPrice: MessageFns<BondPrice> = {
  encode(message: BondPrice, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.accruedInterest !== 0) {
      writer.uint32(17).double(message.accruedInterest);
    }
    if (message.cleanPrice !== 0) {
      writer.uint32(25).double(message.cleanPrice);
    }
    if (message.dirtyPrice !== 0) {
      writer.uint32(33).double(message.dirtyPrice);
    }
    if (message.faceValue !== 0) {
      writer.uint32(41).double(message.faceValue);
    }
    if (message.isActive !== false) {
      writer.uint32(48).bool(message.isActive);
    }
    if (message.settlementDate !== "") {
      writer.uint32(58).string(message.settlementDate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondPrice {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondPrice();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.accruedInterest = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.cleanPrice = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.dirtyPrice = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.faceValue = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.settlementDate = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondPrice {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      accruedInterest: isSet(object.accruedInterest) ? globalThis.Number(object.accruedInterest) : 0,
      cleanPrice: isSet(object.cleanPrice) ? globalThis.Number(object.cleanPrice) : 0,
      dirtyPrice: isSet(object.dirtyPrice) ? globalThis.Number(object.dirtyPrice) : 0,
      faceValue: isSet(object.faceValue) ? globalThis.Number(object.faceValue) : 0,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      settlementDate: isSet(object.settlementDate) ? globalThis.String(object.settlementDate) : "",
    };
  },

  toJSON(message: BondPrice): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.accruedInterest !== 0) {
      obj.accruedInterest = message.accruedInterest;
    }
    if (message.cleanPrice !== 0) {
      obj.cleanPrice = message.cleanPrice;
    }
    if (message.dirtyPrice !== 0) {
      obj.dirtyPrice = message.dirtyPrice;
    }
    if (message.faceValue !== 0) {
      obj.faceValue = message.faceValue;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.settlementDate !== "") {
      obj.settlementDate = message.settlementDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondPrice>, I>>(base?: I): BondPrice {
    return BondPrice.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondPrice>, I>>(object: I): BondPrice {
    const message = createBaseBondPrice();
    message.id = object.id ?? "";
    message.accruedInterest = object.accruedInterest ?? 0;
    message.cleanPrice = object.cleanPrice ?? 0;
    message.dirtyPrice = object.dirtyPrice ?? 0;
    message.faceValue = object.faceValue ?? 0;
    message.isActive = object.isActive ?? false;
    message.settlementDate = object.settlementDate ?? "";
    return message;
  },
};

function createBaseGetBondPricesResponse(): GetBondPricesResponse {
  return { bondPrices: [] };
}

export const GetBondPricesResponse: MessageFns<GetBondPricesResponse> = {
  encode(message: GetBondPricesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bondPrices) {
      BondPrice.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondPricesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondPricesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondPrices.push(BondPrice.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBondPricesResponse {
    return {
      bondPrices: globalThis.Array.isArray(object?.bondPrices)
        ? object.bondPrices.map((e: any) => BondPrice.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetBondPricesResponse): unknown {
    const obj: any = {};
    if (message.bondPrices?.length) {
      obj.bondPrices = message.bondPrices.map((e) => BondPrice.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondPricesResponse>, I>>(base?: I): GetBondPricesResponse {
    return GetBondPricesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondPricesResponse>, I>>(object: I): GetBondPricesResponse {
    const message = createBaseGetBondPricesResponse();
    message.bondPrices = object.bondPrices?.map((e) => BondPrice.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUpdateBondInventoryRequest(): UpdateBondInventoryRequest {
  return {
    bondOfferingId: "",
    maxCount: "",
    orderLimit: "",
    alarmStatus: 0,
    validFrom: "",
    validTill: "",
    isActive: false,
    countDifference: "",
    totalCountDifference: "",
  };
}

export const UpdateBondInventoryRequest: MessageFns<UpdateBondInventoryRequest> = {
  encode(message: UpdateBondInventoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bondOfferingId !== "") {
      writer.uint32(10).string(message.bondOfferingId);
    }
    if (message.maxCount !== "") {
      writer.uint32(18).string(message.maxCount);
    }
    if (message.orderLimit !== "") {
      writer.uint32(26).string(message.orderLimit);
    }
    if (message.alarmStatus !== 0) {
      writer.uint32(32).int32(message.alarmStatus);
    }
    if (message.validFrom !== "") {
      writer.uint32(42).string(message.validFrom);
    }
    if (message.validTill !== "") {
      writer.uint32(50).string(message.validTill);
    }
    if (message.isActive !== false) {
      writer.uint32(56).bool(message.isActive);
    }
    if (message.countDifference !== "") {
      writer.uint32(66).string(message.countDifference);
    }
    if (message.totalCountDifference !== "") {
      writer.uint32(74).string(message.totalCountDifference);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateBondInventoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateBondInventoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondOfferingId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.maxCount = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.orderLimit = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.alarmStatus = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.validFrom = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.validTill = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.countDifference = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.totalCountDifference = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateBondInventoryRequest {
    return {
      bondOfferingId: isSet(object.bondOfferingId) ? globalThis.String(object.bondOfferingId) : "",
      maxCount: isSet(object.maxCount) ? globalThis.String(object.maxCount) : "",
      orderLimit: isSet(object.orderLimit) ? globalThis.String(object.orderLimit) : "",
      alarmStatus: isSet(object.alarmStatus) ? inventoryAlarmStatusFromJSON(object.alarmStatus) : 0,
      validFrom: isSet(object.validFrom) ? globalThis.String(object.validFrom) : "",
      validTill: isSet(object.validTill) ? globalThis.String(object.validTill) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      countDifference: isSet(object.countDifference) ? globalThis.String(object.countDifference) : "",
      totalCountDifference: isSet(object.totalCountDifference) ? globalThis.String(object.totalCountDifference) : "",
    };
  },

  toJSON(message: UpdateBondInventoryRequest): unknown {
    const obj: any = {};
    if (message.bondOfferingId !== "") {
      obj.bondOfferingId = message.bondOfferingId;
    }
    if (message.maxCount !== "") {
      obj.maxCount = message.maxCount;
    }
    if (message.orderLimit !== "") {
      obj.orderLimit = message.orderLimit;
    }
    if (message.alarmStatus !== 0) {
      obj.alarmStatus = inventoryAlarmStatusToJSON(message.alarmStatus);
    }
    if (message.validFrom !== "") {
      obj.validFrom = message.validFrom;
    }
    if (message.validTill !== "") {
      obj.validTill = message.validTill;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.countDifference !== "") {
      obj.countDifference = message.countDifference;
    }
    if (message.totalCountDifference !== "") {
      obj.totalCountDifference = message.totalCountDifference;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateBondInventoryRequest>, I>>(base?: I): UpdateBondInventoryRequest {
    return UpdateBondInventoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBondInventoryRequest>, I>>(object: I): UpdateBondInventoryRequest {
    const message = createBaseUpdateBondInventoryRequest();
    message.bondOfferingId = object.bondOfferingId ?? "";
    message.maxCount = object.maxCount ?? "";
    message.orderLimit = object.orderLimit ?? "";
    message.alarmStatus = object.alarmStatus ?? 0;
    message.validFrom = object.validFrom ?? "";
    message.validTill = object.validTill ?? "";
    message.isActive = object.isActive ?? false;
    message.countDifference = object.countDifference ?? "";
    message.totalCountDifference = object.totalCountDifference ?? "";
    return message;
  },
};

function createBaseUpdateBondInventoryResponse(): UpdateBondInventoryResponse {
  return {};
}

export const UpdateBondInventoryResponse: MessageFns<UpdateBondInventoryResponse> = {
  encode(_: UpdateBondInventoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateBondInventoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateBondInventoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateBondInventoryResponse {
    return {};
  },

  toJSON(_: UpdateBondInventoryResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateBondInventoryResponse>, I>>(base?: I): UpdateBondInventoryResponse {
    return UpdateBondInventoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBondInventoryResponse>, I>>(_: I): UpdateBondInventoryResponse {
    const message = createBaseUpdateBondInventoryResponse();
    return message;
  },
};

function createBaseCreateMediaItemRequest(): CreateMediaItemRequest {
  return {
    parentId: undefined,
    parentType: undefined,
    section: undefined,
    mediaType: undefined,
    mediaUrl: undefined,
    screenType: undefined,
    redirectDeeplink: undefined,
  };
}

export const CreateMediaItemRequest: MessageFns<CreateMediaItemRequest> = {
  encode(message: CreateMediaItemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.parentId !== undefined) {
      writer.uint32(10).string(message.parentId);
    }
    if (message.parentType !== undefined) {
      writer.uint32(16).int32(message.parentType);
    }
    if (message.section !== undefined) {
      writer.uint32(26).string(message.section);
    }
    if (message.mediaType !== undefined) {
      writer.uint32(32).int32(message.mediaType);
    }
    if (message.mediaUrl !== undefined) {
      writer.uint32(42).string(message.mediaUrl);
    }
    if (message.screenType !== undefined) {
      writer.uint32(48).int32(message.screenType);
    }
    if (message.redirectDeeplink !== undefined) {
      writer.uint32(58).string(message.redirectDeeplink);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateMediaItemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateMediaItemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.parentId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.parentType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.section = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.mediaType = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.mediaUrl = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.screenType = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.redirectDeeplink = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateMediaItemRequest {
    return {
      parentId: isSet(object.parentId) ? globalThis.String(object.parentId) : undefined,
      parentType: isSet(object.parentType) ? bondMediaItemParentTypeFromJSON(object.parentType) : undefined,
      section: isSet(object.section) ? globalThis.String(object.section) : undefined,
      mediaType: isSet(object.mediaType) ? mediaItem_MediaTypeFromJSON(object.mediaType) : undefined,
      mediaUrl: isSet(object.mediaUrl) ? globalThis.String(object.mediaUrl) : undefined,
      screenType: isSet(object.screenType) ? mediaItem_ScreenTypeFromJSON(object.screenType) : undefined,
      redirectDeeplink: isSet(object.redirectDeeplink) ? globalThis.String(object.redirectDeeplink) : undefined,
    };
  },

  toJSON(message: CreateMediaItemRequest): unknown {
    const obj: any = {};
    if (message.parentId !== undefined) {
      obj.parentId = message.parentId;
    }
    if (message.parentType !== undefined) {
      obj.parentType = bondMediaItemParentTypeToJSON(message.parentType);
    }
    if (message.section !== undefined) {
      obj.section = message.section;
    }
    if (message.mediaType !== undefined) {
      obj.mediaType = mediaItem_MediaTypeToJSON(message.mediaType);
    }
    if (message.mediaUrl !== undefined) {
      obj.mediaUrl = message.mediaUrl;
    }
    if (message.screenType !== undefined) {
      obj.screenType = mediaItem_ScreenTypeToJSON(message.screenType);
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = message.redirectDeeplink;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateMediaItemRequest>, I>>(base?: I): CreateMediaItemRequest {
    return CreateMediaItemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateMediaItemRequest>, I>>(object: I): CreateMediaItemRequest {
    const message = createBaseCreateMediaItemRequest();
    message.parentId = object.parentId ?? undefined;
    message.parentType = object.parentType ?? undefined;
    message.section = object.section ?? undefined;
    message.mediaType = object.mediaType ?? undefined;
    message.mediaUrl = object.mediaUrl ?? undefined;
    message.screenType = object.screenType ?? undefined;
    message.redirectDeeplink = object.redirectDeeplink ?? undefined;
    return message;
  },
};

function createBaseCreateMediaItemResponse(): CreateMediaItemResponse {
  return {};
}

export const CreateMediaItemResponse: MessageFns<CreateMediaItemResponse> = {
  encode(_: CreateMediaItemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateMediaItemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateMediaItemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): CreateMediaItemResponse {
    return {};
  },

  toJSON(_: CreateMediaItemResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateMediaItemResponse>, I>>(base?: I): CreateMediaItemResponse {
    return CreateMediaItemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateMediaItemResponse>, I>>(_: I): CreateMediaItemResponse {
    const message = createBaseCreateMediaItemResponse();
    return message;
  },
};

function createBaseVerifyRecordDateRequest(): VerifyRecordDateRequest {
  return { bondDetailId: "", cashflowScheduleId: "" };
}

export const VerifyRecordDateRequest: MessageFns<VerifyRecordDateRequest> = {
  encode(message: VerifyRecordDateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bondDetailId !== "") {
      writer.uint32(10).string(message.bondDetailId);
    }
    if (message.cashflowScheduleId !== "") {
      writer.uint32(18).string(message.cashflowScheduleId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VerifyRecordDateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVerifyRecordDateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bondDetailId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.cashflowScheduleId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VerifyRecordDateRequest {
    return {
      bondDetailId: isSet(object.bondDetailId) ? globalThis.String(object.bondDetailId) : "",
      cashflowScheduleId: isSet(object.cashflowScheduleId) ? globalThis.String(object.cashflowScheduleId) : "",
    };
  },

  toJSON(message: VerifyRecordDateRequest): unknown {
    const obj: any = {};
    if (message.bondDetailId !== "") {
      obj.bondDetailId = message.bondDetailId;
    }
    if (message.cashflowScheduleId !== "") {
      obj.cashflowScheduleId = message.cashflowScheduleId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VerifyRecordDateRequest>, I>>(base?: I): VerifyRecordDateRequest {
    return VerifyRecordDateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VerifyRecordDateRequest>, I>>(object: I): VerifyRecordDateRequest {
    const message = createBaseVerifyRecordDateRequest();
    message.bondDetailId = object.bondDetailId ?? "";
    message.cashflowScheduleId = object.cashflowScheduleId ?? "";
    return message;
  },
};

function createBaseVerifyRecordDateResponse(): VerifyRecordDateResponse {
  return {};
}

export const VerifyRecordDateResponse: MessageFns<VerifyRecordDateResponse> = {
  encode(_: VerifyRecordDateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VerifyRecordDateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVerifyRecordDateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): VerifyRecordDateResponse {
    return {};
  },

  toJSON(_: VerifyRecordDateResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<VerifyRecordDateResponse>, I>>(base?: I): VerifyRecordDateResponse {
    return VerifyRecordDateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VerifyRecordDateResponse>, I>>(_: I): VerifyRecordDateResponse {
    const message = createBaseVerifyRecordDateResponse();
    return message;
  },
};

function createBaseGetMediaItemRequest(): GetMediaItemRequest {
  return { mediaItemId: "" };
}

export const GetMediaItemRequest: MessageFns<GetMediaItemRequest> = {
  encode(message: GetMediaItemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mediaItemId !== "") {
      writer.uint32(10).string(message.mediaItemId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetMediaItemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetMediaItemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.mediaItemId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetMediaItemRequest {
    return { mediaItemId: isSet(object.mediaItemId) ? globalThis.String(object.mediaItemId) : "" };
  },

  toJSON(message: GetMediaItemRequest): unknown {
    const obj: any = {};
    if (message.mediaItemId !== "") {
      obj.mediaItemId = message.mediaItemId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetMediaItemRequest>, I>>(base?: I): GetMediaItemRequest {
    return GetMediaItemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMediaItemRequest>, I>>(object: I): GetMediaItemRequest {
    const message = createBaseGetMediaItemRequest();
    message.mediaItemId = object.mediaItemId ?? "";
    return message;
  },
};

function createBaseGetMediaItemResponse(): GetMediaItemResponse {
  return { mediaItem: undefined };
}

export const GetMediaItemResponse: MessageFns<GetMediaItemResponse> = {
  encode(message: GetMediaItemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mediaItem !== undefined) {
      MediaItemDashboard.encode(message.mediaItem, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetMediaItemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetMediaItemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.mediaItem = MediaItemDashboard.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetMediaItemResponse {
    return { mediaItem: isSet(object.mediaItem) ? MediaItemDashboard.fromJSON(object.mediaItem) : undefined };
  },

  toJSON(message: GetMediaItemResponse): unknown {
    const obj: any = {};
    if (message.mediaItem !== undefined) {
      obj.mediaItem = MediaItemDashboard.toJSON(message.mediaItem);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetMediaItemResponse>, I>>(base?: I): GetMediaItemResponse {
    return GetMediaItemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMediaItemResponse>, I>>(object: I): GetMediaItemResponse {
    const message = createBaseGetMediaItemResponse();
    message.mediaItem = (object.mediaItem !== undefined && object.mediaItem !== null)
      ? MediaItemDashboard.fromPartial(object.mediaItem)
      : undefined;
    return message;
  },
};

function createBaseUpdateRecordDateRequest(): UpdateRecordDateRequest {
  return { cashflowScheduleId: undefined, recordDate: undefined };
}

export const UpdateRecordDateRequest: MessageFns<UpdateRecordDateRequest> = {
  encode(message: UpdateRecordDateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cashflowScheduleId !== undefined) {
      writer.uint32(10).string(message.cashflowScheduleId);
    }
    if (message.recordDate !== undefined) {
      writer.uint32(18).string(message.recordDate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateRecordDateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateRecordDateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cashflowScheduleId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recordDate = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateRecordDateRequest {
    return {
      cashflowScheduleId: isSet(object.cashflowScheduleId) ? globalThis.String(object.cashflowScheduleId) : undefined,
      recordDate: isSet(object.recordDate) ? globalThis.String(object.recordDate) : undefined,
    };
  },

  toJSON(message: UpdateRecordDateRequest): unknown {
    const obj: any = {};
    if (message.cashflowScheduleId !== undefined) {
      obj.cashflowScheduleId = message.cashflowScheduleId;
    }
    if (message.recordDate !== undefined) {
      obj.recordDate = message.recordDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateRecordDateRequest>, I>>(base?: I): UpdateRecordDateRequest {
    return UpdateRecordDateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRecordDateRequest>, I>>(object: I): UpdateRecordDateRequest {
    const message = createBaseUpdateRecordDateRequest();
    message.cashflowScheduleId = object.cashflowScheduleId ?? undefined;
    message.recordDate = object.recordDate ?? undefined;
    return message;
  },
};

function createBaseUpdateRecordDateResponse(): UpdateRecordDateResponse {
  return {};
}

export const UpdateRecordDateResponse: MessageFns<UpdateRecordDateResponse> = {
  encode(_: UpdateRecordDateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateRecordDateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateRecordDateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateRecordDateResponse {
    return {};
  },

  toJSON(_: UpdateRecordDateResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateRecordDateResponse>, I>>(base?: I): UpdateRecordDateResponse {
    return UpdateRecordDateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRecordDateResponse>, I>>(_: I): UpdateRecordDateResponse {
    const message = createBaseUpdateRecordDateResponse();
    return message;
  },
};

export interface CatalogService {
  getAllBondsIsActive(request: GetAllBondsRequest): Promise<GetAllBondsResponse>;
  getAllBondsIsNotActive(request: GetAllBondsRequest): Promise<GetAllBondsResponse>;
  getBondIssuingInstitution(request: IdRequest): Promise<BondIssuingInstitutionResponse>;
  getBondOfferingDetail(request: IdRequest): Promise<BondOfferingDetailResponse>;
  getBondInventory(request: IdRequest): Promise<BondInventoryResponse>;
  updateBondDetails(request: BondDetailsUpdateRequest): Promise<BondDetailsUpdateResponse>;
  getPartyDetails(request: PartyDetailsRequest): Promise<PartyDetailsResponse>;
  getAllISINS(request: AllISINSRequest): Promise<AllISINSResponse>;
  getMediaItem(request: MediaItemRequest): Promise<MediaItemResponse>;
  updateMediaItem(request: MediaItemUpdateRequest): Promise<MediaItemUpdateResponse>;
  updateIssuingInstitution(request: BondIssuingInstitutionUpdateRequest): Promise<BondIssuingInstitutionUpdateResponse>;
  getBondDetailByISIN(request: BondDetailRequest): Promise<BondDetailsDashboard>;
  getAllBondDetails(request: AllBondDetailsRequest): Promise<AllBondDetailsResponse>;
  updateOfferingActiveStatus(request: UpdateOfferingActiveStatusRequest): Promise<UpdateOfferingActiveStatusResponse>;
  updateBondOfferingYield(request: UpdateBondOfferingYieldRequest): Promise<UpdateBondOfferingYieldResponse>;
  getBondCashflowSchedule(request: GetBondCashflowScheduleRequest): Promise<GetBondCashflowScheduleResponse>;
  createTag(request: CreateTagRequest): Promise<CreateTagResponse>;
  getAllTags(request: GetAllTagsRequest): Promise<GetAllTagsResponse>;
  createTagCollectionItem(request: CreateTagCollectionItemRequest): Promise<CreateTagCollectionItemResponse>;
  getAllTagCollectionItem(request: GetAllTagCollectionItemRequest): Promise<GetAllTagCollectionItemResponse>;
  deleteTagCollectionItem(request: DeleteTagCollectionItemRequest): Promise<DeleteTagCollectionItemResponse>;
  updateTag(request: Tag): Promise<UpdateTagResponse>;
  getTag(request: getTagRequest): Promise<Tag>;
  updateBondOffering(request: BondOfferingUpdateRequest): Promise<BondOfferingUpdateResponse>;
  verifyRecordDate(request: VerifyRecordDateRequest): Promise<VerifyRecordDateResponse>;
  getMediaItemById(request: GetMediaItemRequest): Promise<GetMediaItemResponse>;
  updateRecordDate(request: UpdateRecordDateRequest): Promise<UpdateRecordDateResponse>;
}

export const CatalogServiceServiceName = "com.stablemoney.api.broking.CatalogService";
export class CatalogServiceClientImpl implements CatalogService {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || CatalogServiceServiceName;
    this.rpc = rpc;
    this.getAllBondsIsActive = this.getAllBondsIsActive.bind(this);
    this.getAllBondsIsNotActive = this.getAllBondsIsNotActive.bind(this);
    this.getBondIssuingInstitution = this.getBondIssuingInstitution.bind(this);
    this.getBondOfferingDetail = this.getBondOfferingDetail.bind(this);
    this.getBondInventory = this.getBondInventory.bind(this);
    this.updateBondDetails = this.updateBondDetails.bind(this);
    this.getPartyDetails = this.getPartyDetails.bind(this);
    this.getAllISINS = this.getAllISINS.bind(this);
    this.getMediaItem = this.getMediaItem.bind(this);
    this.updateMediaItem = this.updateMediaItem.bind(this);
    this.updateIssuingInstitution = this.updateIssuingInstitution.bind(this);
    this.getBondDetailByISIN = this.getBondDetailByISIN.bind(this);
    this.getAllBondDetails = this.getAllBondDetails.bind(this);
    this.updateOfferingActiveStatus = this.updateOfferingActiveStatus.bind(this);
    this.updateBondOfferingYield = this.updateBondOfferingYield.bind(this);
    this.getBondCashflowSchedule = this.getBondCashflowSchedule.bind(this);
    this.createTag = this.createTag.bind(this);
    this.getAllTags = this.getAllTags.bind(this);
    this.createTagCollectionItem = this.createTagCollectionItem.bind(this);
    this.getAllTagCollectionItem = this.getAllTagCollectionItem.bind(this);
    this.deleteTagCollectionItem = this.deleteTagCollectionItem.bind(this);
    this.updateTag = this.updateTag.bind(this);
    this.getTag = this.getTag.bind(this);
    this.updateBondOffering = this.updateBondOffering.bind(this);
    this.verifyRecordDate = this.verifyRecordDate.bind(this);
    this.getMediaItemById = this.getMediaItemById.bind(this);
    this.updateRecordDate = this.updateRecordDate.bind(this);
  }
  getAllBondsIsActive(request: GetAllBondsRequest): Promise<GetAllBondsResponse> {
    const data = GetAllBondsRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getAllBondsIsActive", data);
    return promise.then((data) => GetAllBondsResponse.decode(new BinaryReader(data)));
  }

  getAllBondsIsNotActive(request: GetAllBondsRequest): Promise<GetAllBondsResponse> {
    const data = GetAllBondsRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getAllBondsIsNotActive", data);
    return promise.then((data) => GetAllBondsResponse.decode(new BinaryReader(data)));
  }

  getBondIssuingInstitution(request: IdRequest): Promise<BondIssuingInstitutionResponse> {
    const data = IdRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getBondIssuingInstitution", data);
    return promise.then((data) => BondIssuingInstitutionResponse.decode(new BinaryReader(data)));
  }

  getBondOfferingDetail(request: IdRequest): Promise<BondOfferingDetailResponse> {
    const data = IdRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getBondOfferingDetail", data);
    return promise.then((data) => BondOfferingDetailResponse.decode(new BinaryReader(data)));
  }

  getBondInventory(request: IdRequest): Promise<BondInventoryResponse> {
    const data = IdRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getBondInventory", data);
    return promise.then((data) => BondInventoryResponse.decode(new BinaryReader(data)));
  }

  updateBondDetails(request: BondDetailsUpdateRequest): Promise<BondDetailsUpdateResponse> {
    const data = BondDetailsUpdateRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "updateBondDetails", data);
    return promise.then((data) => BondDetailsUpdateResponse.decode(new BinaryReader(data)));
  }

  getPartyDetails(request: PartyDetailsRequest): Promise<PartyDetailsResponse> {
    const data = PartyDetailsRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getPartyDetails", data);
    return promise.then((data) => PartyDetailsResponse.decode(new BinaryReader(data)));
  }

  getAllISINS(request: AllISINSRequest): Promise<AllISINSResponse> {
    const data = AllISINSRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getAllISINS", data);
    return promise.then((data) => AllISINSResponse.decode(new BinaryReader(data)));
  }

  getMediaItem(request: MediaItemRequest): Promise<MediaItemResponse> {
    const data = MediaItemRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getMediaItem", data);
    return promise.then((data) => MediaItemResponse.decode(new BinaryReader(data)));
  }

  updateMediaItem(request: MediaItemUpdateRequest): Promise<MediaItemUpdateResponse> {
    const data = MediaItemUpdateRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "updateMediaItem", data);
    return promise.then((data) => MediaItemUpdateResponse.decode(new BinaryReader(data)));
  }

  updateIssuingInstitution(
    request: BondIssuingInstitutionUpdateRequest,
  ): Promise<BondIssuingInstitutionUpdateResponse> {
    const data = BondIssuingInstitutionUpdateRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "updateIssuingInstitution", data);
    return promise.then((data) => BondIssuingInstitutionUpdateResponse.decode(new BinaryReader(data)));
  }

  getBondDetailByISIN(request: BondDetailRequest): Promise<BondDetailsDashboard> {
    const data = BondDetailRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getBondDetailByISIN", data);
    return promise.then((data) => BondDetailsDashboard.decode(new BinaryReader(data)));
  }

  getAllBondDetails(request: AllBondDetailsRequest): Promise<AllBondDetailsResponse> {
    const data = AllBondDetailsRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getAllBondDetails", data);
    return promise.then((data) => AllBondDetailsResponse.decode(new BinaryReader(data)));
  }

  updateOfferingActiveStatus(request: UpdateOfferingActiveStatusRequest): Promise<UpdateOfferingActiveStatusResponse> {
    const data = UpdateOfferingActiveStatusRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "updateOfferingActiveStatus", data);
    return promise.then((data) => UpdateOfferingActiveStatusResponse.decode(new BinaryReader(data)));
  }

  updateBondOfferingYield(request: UpdateBondOfferingYieldRequest): Promise<UpdateBondOfferingYieldResponse> {
    const data = UpdateBondOfferingYieldRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "updateBondOfferingYield", data);
    return promise.then((data) => UpdateBondOfferingYieldResponse.decode(new BinaryReader(data)));
  }

  getBondCashflowSchedule(request: GetBondCashflowScheduleRequest): Promise<GetBondCashflowScheduleResponse> {
    const data = GetBondCashflowScheduleRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getBondCashflowSchedule", data);
    return promise.then((data) => GetBondCashflowScheduleResponse.decode(new BinaryReader(data)));
  }

  createTag(request: CreateTagRequest): Promise<CreateTagResponse> {
    const data = CreateTagRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "createTag", data);
    return promise.then((data) => CreateTagResponse.decode(new BinaryReader(data)));
  }

  getAllTags(request: GetAllTagsRequest): Promise<GetAllTagsResponse> {
    const data = GetAllTagsRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getAllTags", data);
    return promise.then((data) => GetAllTagsResponse.decode(new BinaryReader(data)));
  }

  createTagCollectionItem(request: CreateTagCollectionItemRequest): Promise<CreateTagCollectionItemResponse> {
    const data = CreateTagCollectionItemRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "createTagCollectionItem", data);
    return promise.then((data) => CreateTagCollectionItemResponse.decode(new BinaryReader(data)));
  }

  getAllTagCollectionItem(request: GetAllTagCollectionItemRequest): Promise<GetAllTagCollectionItemResponse> {
    const data = GetAllTagCollectionItemRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getAllTagCollectionItem", data);
    return promise.then((data) => GetAllTagCollectionItemResponse.decode(new BinaryReader(data)));
  }

  deleteTagCollectionItem(request: DeleteTagCollectionItemRequest): Promise<DeleteTagCollectionItemResponse> {
    const data = DeleteTagCollectionItemRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "deleteTagCollectionItem", data);
    return promise.then((data) => DeleteTagCollectionItemResponse.decode(new BinaryReader(data)));
  }

  updateTag(request: Tag): Promise<UpdateTagResponse> {
    const data = Tag.encode(request).finish();
    const promise = this.rpc.request(this.service, "updateTag", data);
    return promise.then((data) => UpdateTagResponse.decode(new BinaryReader(data)));
  }

  getTag(request: getTagRequest): Promise<Tag> {
    const data = getTagRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getTag", data);
    return promise.then((data) => Tag.decode(new BinaryReader(data)));
  }

  updateBondOffering(request: BondOfferingUpdateRequest): Promise<BondOfferingUpdateResponse> {
    const data = BondOfferingUpdateRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "updateBondOffering", data);
    return promise.then((data) => BondOfferingUpdateResponse.decode(new BinaryReader(data)));
  }

  verifyRecordDate(request: VerifyRecordDateRequest): Promise<VerifyRecordDateResponse> {
    const data = VerifyRecordDateRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "verifyRecordDate", data);
    return promise.then((data) => VerifyRecordDateResponse.decode(new BinaryReader(data)));
  }

  getMediaItemById(request: GetMediaItemRequest): Promise<GetMediaItemResponse> {
    const data = GetMediaItemRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "getMediaItemById", data);
    return promise.then((data) => GetMediaItemResponse.decode(new BinaryReader(data)));
  }

  updateRecordDate(request: UpdateRecordDateRequest): Promise<UpdateRecordDateResponse> {
    const data = UpdateRecordDateRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "updateRecordDate", data);
    return promise.then((data) => UpdateRecordDateResponse.decode(new BinaryReader(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
