// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: FixedDepositRecommendation.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Bank } from "./Bank";

export const protobufPackage = "com.stablemoney.api.business.recommendation";

export interface GetInsuredFixedDepositsRecommendation {
  investments: GetInsuredFixedDepositsRecommendation_Investment[];
}

export interface GetInsuredFixedDepositsRecommendation_Investment {
  bank: Bank | undefined;
  investedAmount: number;
  maxAmount: number;
}

export interface GetNextBestFixedDepositsRecommendation {
  fixedDeposits: GetNextBestFixedDepositsRecommendation_FixedDeposit[];
}

export interface GetNextBestFixedDepositsRecommendation_FixedDeposit {
  bank: Bank | undefined;
  tenure: GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure | undefined;
  interestRate: number;
  benefits: string[];
  instantBooking: boolean;
  recommendationReason: string;
  minTenureInDays: number;
  maxTenureInDays: number;
}

export enum GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType {
  UNKNOWN = 0,
  SHORT_TERM = 1,
  MEDIUM_TERM = 2,
  LONG_TERM = 3,
  UNRECOGNIZED = -1,
}

export function getNextBestFixedDepositsRecommendation_FixedDeposit_DurationTypeFromJSON(
  object: any,
): GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType {
  switch (object) {
    case 0:
    case "UNKNOWN":
      return GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.UNKNOWN;
    case 1:
    case "SHORT_TERM":
      return GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.SHORT_TERM;
    case 2:
    case "MEDIUM_TERM":
      return GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.MEDIUM_TERM;
    case 3:
    case "LONG_TERM":
      return GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.LONG_TERM;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.UNRECOGNIZED;
  }
}

export function getNextBestFixedDepositsRecommendation_FixedDeposit_DurationTypeToJSON(
  object: GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType,
): string {
  switch (object) {
    case GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.UNKNOWN:
      return "UNKNOWN";
    case GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.SHORT_TERM:
      return "SHORT_TERM";
    case GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.MEDIUM_TERM:
      return "MEDIUM_TERM";
    case GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.LONG_TERM:
      return "LONG_TERM";
    case GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure {
  tenure: string;
  days: number;
  months: number;
  years: number;
}

export interface OneClickFixedDepositsRecommendation {
  banks: OneClickFixedDepositsRecommendation_OneClickInvestmentBank[];
}

export interface OneClickFixedDepositsRecommendation_OneClickInvestmentBank {
  bank: Bank | undefined;
  lastInvestedOn: string;
  tags: string[];
}

function createBaseGetInsuredFixedDepositsRecommendation(): GetInsuredFixedDepositsRecommendation {
  return { investments: [] };
}

export const GetInsuredFixedDepositsRecommendation: MessageFns<GetInsuredFixedDepositsRecommendation> = {
  encode(message: GetInsuredFixedDepositsRecommendation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.investments) {
      GetInsuredFixedDepositsRecommendation_Investment.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetInsuredFixedDepositsRecommendation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetInsuredFixedDepositsRecommendation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.investments.push(GetInsuredFixedDepositsRecommendation_Investment.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetInsuredFixedDepositsRecommendation {
    return {
      investments: globalThis.Array.isArray(object?.investments)
        ? object.investments.map((e: any) => GetInsuredFixedDepositsRecommendation_Investment.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetInsuredFixedDepositsRecommendation): unknown {
    const obj: any = {};
    if (message.investments?.length) {
      obj.investments = message.investments.map((e) => GetInsuredFixedDepositsRecommendation_Investment.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetInsuredFixedDepositsRecommendation>, I>>(
    base?: I,
  ): GetInsuredFixedDepositsRecommendation {
    return GetInsuredFixedDepositsRecommendation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetInsuredFixedDepositsRecommendation>, I>>(
    object: I,
  ): GetInsuredFixedDepositsRecommendation {
    const message = createBaseGetInsuredFixedDepositsRecommendation();
    message.investments =
      object.investments?.map((e) => GetInsuredFixedDepositsRecommendation_Investment.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetInsuredFixedDepositsRecommendation_Investment(): GetInsuredFixedDepositsRecommendation_Investment {
  return { bank: undefined, investedAmount: 0, maxAmount: 0 };
}

export const GetInsuredFixedDepositsRecommendation_Investment: MessageFns<
  GetInsuredFixedDepositsRecommendation_Investment
> = {
  encode(
    message: GetInsuredFixedDepositsRecommendation_Investment,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.bank !== undefined) {
      Bank.encode(message.bank, writer.uint32(10).fork()).join();
    }
    if (message.investedAmount !== 0) {
      writer.uint32(17).double(message.investedAmount);
    }
    if (message.maxAmount !== 0) {
      writer.uint32(25).double(message.maxAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetInsuredFixedDepositsRecommendation_Investment {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetInsuredFixedDepositsRecommendation_Investment();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bank = Bank.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.investedAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.maxAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetInsuredFixedDepositsRecommendation_Investment {
    return {
      bank: isSet(object.bank) ? Bank.fromJSON(object.bank) : undefined,
      investedAmount: isSet(object.investedAmount) ? globalThis.Number(object.investedAmount) : 0,
      maxAmount: isSet(object.maxAmount) ? globalThis.Number(object.maxAmount) : 0,
    };
  },

  toJSON(message: GetInsuredFixedDepositsRecommendation_Investment): unknown {
    const obj: any = {};
    if (message.bank !== undefined) {
      obj.bank = Bank.toJSON(message.bank);
    }
    if (message.investedAmount !== 0) {
      obj.investedAmount = message.investedAmount;
    }
    if (message.maxAmount !== 0) {
      obj.maxAmount = message.maxAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetInsuredFixedDepositsRecommendation_Investment>, I>>(
    base?: I,
  ): GetInsuredFixedDepositsRecommendation_Investment {
    return GetInsuredFixedDepositsRecommendation_Investment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetInsuredFixedDepositsRecommendation_Investment>, I>>(
    object: I,
  ): GetInsuredFixedDepositsRecommendation_Investment {
    const message = createBaseGetInsuredFixedDepositsRecommendation_Investment();
    message.bank = (object.bank !== undefined && object.bank !== null) ? Bank.fromPartial(object.bank) : undefined;
    message.investedAmount = object.investedAmount ?? 0;
    message.maxAmount = object.maxAmount ?? 0;
    return message;
  },
};

function createBaseGetNextBestFixedDepositsRecommendation(): GetNextBestFixedDepositsRecommendation {
  return { fixedDeposits: [] };
}

export const GetNextBestFixedDepositsRecommendation: MessageFns<GetNextBestFixedDepositsRecommendation> = {
  encode(message: GetNextBestFixedDepositsRecommendation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.fixedDeposits) {
      GetNextBestFixedDepositsRecommendation_FixedDeposit.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextBestFixedDepositsRecommendation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextBestFixedDepositsRecommendation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fixedDeposits.push(
            GetNextBestFixedDepositsRecommendation_FixedDeposit.decode(reader, reader.uint32()),
          );
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextBestFixedDepositsRecommendation {
    return {
      fixedDeposits: globalThis.Array.isArray(object?.fixedDeposits)
        ? object.fixedDeposits.map((e: any) => GetNextBestFixedDepositsRecommendation_FixedDeposit.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetNextBestFixedDepositsRecommendation): unknown {
    const obj: any = {};
    if (message.fixedDeposits?.length) {
      obj.fixedDeposits = message.fixedDeposits.map((e) =>
        GetNextBestFixedDepositsRecommendation_FixedDeposit.toJSON(e)
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextBestFixedDepositsRecommendation>, I>>(
    base?: I,
  ): GetNextBestFixedDepositsRecommendation {
    return GetNextBestFixedDepositsRecommendation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextBestFixedDepositsRecommendation>, I>>(
    object: I,
  ): GetNextBestFixedDepositsRecommendation {
    const message = createBaseGetNextBestFixedDepositsRecommendation();
    message.fixedDeposits =
      object.fixedDeposits?.map((e) => GetNextBestFixedDepositsRecommendation_FixedDeposit.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetNextBestFixedDepositsRecommendation_FixedDeposit(): GetNextBestFixedDepositsRecommendation_FixedDeposit {
  return {
    bank: undefined,
    tenure: undefined,
    interestRate: 0,
    benefits: [],
    instantBooking: false,
    recommendationReason: "",
    minTenureInDays: 0,
    maxTenureInDays: 0,
  };
}

export const GetNextBestFixedDepositsRecommendation_FixedDeposit: MessageFns<
  GetNextBestFixedDepositsRecommendation_FixedDeposit
> = {
  encode(
    message: GetNextBestFixedDepositsRecommendation_FixedDeposit,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.bank !== undefined) {
      Bank.encode(message.bank, writer.uint32(10).fork()).join();
    }
    if (message.tenure !== undefined) {
      GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure.encode(message.tenure, writer.uint32(18).fork())
        .join();
    }
    if (message.interestRate !== 0) {
      writer.uint32(25).double(message.interestRate);
    }
    for (const v of message.benefits) {
      writer.uint32(42).string(v!);
    }
    if (message.instantBooking !== false) {
      writer.uint32(48).bool(message.instantBooking);
    }
    if (message.recommendationReason !== "") {
      writer.uint32(66).string(message.recommendationReason);
    }
    if (message.minTenureInDays !== 0) {
      writer.uint32(72).int32(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      writer.uint32(80).int32(message.maxTenureInDays);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetNextBestFixedDepositsRecommendation_FixedDeposit {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextBestFixedDepositsRecommendation_FixedDeposit();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bank = Bank.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenure = GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.interestRate = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.benefits.push(reader.string());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.instantBooking = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.recommendationReason = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.minTenureInDays = reader.int32();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.maxTenureInDays = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextBestFixedDepositsRecommendation_FixedDeposit {
    return {
      bank: isSet(object.bank) ? Bank.fromJSON(object.bank) : undefined,
      tenure: isSet(object.tenure)
        ? GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure.fromJSON(object.tenure)
        : undefined,
      interestRate: isSet(object.interestRate) ? globalThis.Number(object.interestRate) : 0,
      benefits: globalThis.Array.isArray(object?.benefits) ? object.benefits.map((e: any) => globalThis.String(e)) : [],
      instantBooking: isSet(object.instantBooking) ? globalThis.Boolean(object.instantBooking) : false,
      recommendationReason: isSet(object.recommendationReason) ? globalThis.String(object.recommendationReason) : "",
      minTenureInDays: isSet(object.minTenureInDays) ? globalThis.Number(object.minTenureInDays) : 0,
      maxTenureInDays: isSet(object.maxTenureInDays) ? globalThis.Number(object.maxTenureInDays) : 0,
    };
  },

  toJSON(message: GetNextBestFixedDepositsRecommendation_FixedDeposit): unknown {
    const obj: any = {};
    if (message.bank !== undefined) {
      obj.bank = Bank.toJSON(message.bank);
    }
    if (message.tenure !== undefined) {
      obj.tenure = GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure.toJSON(message.tenure);
    }
    if (message.interestRate !== 0) {
      obj.interestRate = message.interestRate;
    }
    if (message.benefits?.length) {
      obj.benefits = message.benefits;
    }
    if (message.instantBooking !== false) {
      obj.instantBooking = message.instantBooking;
    }
    if (message.recommendationReason !== "") {
      obj.recommendationReason = message.recommendationReason;
    }
    if (message.minTenureInDays !== 0) {
      obj.minTenureInDays = Math.round(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      obj.maxTenureInDays = Math.round(message.maxTenureInDays);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextBestFixedDepositsRecommendation_FixedDeposit>, I>>(
    base?: I,
  ): GetNextBestFixedDepositsRecommendation_FixedDeposit {
    return GetNextBestFixedDepositsRecommendation_FixedDeposit.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextBestFixedDepositsRecommendation_FixedDeposit>, I>>(
    object: I,
  ): GetNextBestFixedDepositsRecommendation_FixedDeposit {
    const message = createBaseGetNextBestFixedDepositsRecommendation_FixedDeposit();
    message.bank = (object.bank !== undefined && object.bank !== null) ? Bank.fromPartial(object.bank) : undefined;
    message.tenure = (object.tenure !== undefined && object.tenure !== null)
      ? GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure.fromPartial(object.tenure)
      : undefined;
    message.interestRate = object.interestRate ?? 0;
    message.benefits = object.benefits?.map((e) => e) || [];
    message.instantBooking = object.instantBooking ?? false;
    message.recommendationReason = object.recommendationReason ?? "";
    message.minTenureInDays = object.minTenureInDays ?? 0;
    message.maxTenureInDays = object.maxTenureInDays ?? 0;
    return message;
  },
};

function createBaseGetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure(): GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure {
  return { tenure: "", days: 0, months: 0, years: 0 };
}

export const GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure: MessageFns<
  GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure
> = {
  encode(
    message: GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.tenure !== "") {
      writer.uint32(10).string(message.tenure);
    }
    if (message.days !== 0) {
      writer.uint32(16).int32(message.days);
    }
    if (message.months !== 0) {
      writer.uint32(24).int32(message.months);
    }
    if (message.years !== 0) {
      writer.uint32(32).int32(message.years);
    }
    return writer;
  },

  decode(
    input: BinaryReader | Uint8Array,
    length?: number,
  ): GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.days = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.months = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.years = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure {
    return {
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      days: isSet(object.days) ? globalThis.Number(object.days) : 0,
      months: isSet(object.months) ? globalThis.Number(object.months) : 0,
      years: isSet(object.years) ? globalThis.Number(object.years) : 0,
    };
  },

  toJSON(message: GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure): unknown {
    const obj: any = {};
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.days !== 0) {
      obj.days = Math.round(message.days);
    }
    if (message.months !== 0) {
      obj.months = Math.round(message.months);
    }
    if (message.years !== 0) {
      obj.years = Math.round(message.years);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure>, I>>(
    base?: I,
  ): GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure {
    return GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure>, I>>(
    object: I,
  ): GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure {
    const message = createBaseGetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure();
    message.tenure = object.tenure ?? "";
    message.days = object.days ?? 0;
    message.months = object.months ?? 0;
    message.years = object.years ?? 0;
    return message;
  },
};

function createBaseOneClickFixedDepositsRecommendation(): OneClickFixedDepositsRecommendation {
  return { banks: [] };
}

export const OneClickFixedDepositsRecommendation: MessageFns<OneClickFixedDepositsRecommendation> = {
  encode(message: OneClickFixedDepositsRecommendation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.banks) {
      OneClickFixedDepositsRecommendation_OneClickInvestmentBank.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OneClickFixedDepositsRecommendation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOneClickFixedDepositsRecommendation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.banks.push(
            OneClickFixedDepositsRecommendation_OneClickInvestmentBank.decode(reader, reader.uint32()),
          );
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OneClickFixedDepositsRecommendation {
    return {
      banks: globalThis.Array.isArray(object?.banks)
        ? object.banks.map((e: any) => OneClickFixedDepositsRecommendation_OneClickInvestmentBank.fromJSON(e))
        : [],
    };
  },

  toJSON(message: OneClickFixedDepositsRecommendation): unknown {
    const obj: any = {};
    if (message.banks?.length) {
      obj.banks = message.banks.map((e) => OneClickFixedDepositsRecommendation_OneClickInvestmentBank.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OneClickFixedDepositsRecommendation>, I>>(
    base?: I,
  ): OneClickFixedDepositsRecommendation {
    return OneClickFixedDepositsRecommendation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OneClickFixedDepositsRecommendation>, I>>(
    object: I,
  ): OneClickFixedDepositsRecommendation {
    const message = createBaseOneClickFixedDepositsRecommendation();
    message.banks =
      object.banks?.map((e) => OneClickFixedDepositsRecommendation_OneClickInvestmentBank.fromPartial(e)) || [];
    return message;
  },
};

function createBaseOneClickFixedDepositsRecommendation_OneClickInvestmentBank(): OneClickFixedDepositsRecommendation_OneClickInvestmentBank {
  return { bank: undefined, lastInvestedOn: "", tags: [] };
}

export const OneClickFixedDepositsRecommendation_OneClickInvestmentBank: MessageFns<
  OneClickFixedDepositsRecommendation_OneClickInvestmentBank
> = {
  encode(
    message: OneClickFixedDepositsRecommendation_OneClickInvestmentBank,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.bank !== undefined) {
      Bank.encode(message.bank, writer.uint32(10).fork()).join();
    }
    if (message.lastInvestedOn !== "") {
      writer.uint32(18).string(message.lastInvestedOn);
    }
    for (const v of message.tags) {
      writer.uint32(26).string(v!);
    }
    return writer;
  },

  decode(
    input: BinaryReader | Uint8Array,
    length?: number,
  ): OneClickFixedDepositsRecommendation_OneClickInvestmentBank {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOneClickFixedDepositsRecommendation_OneClickInvestmentBank();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bank = Bank.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.lastInvestedOn = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tags.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OneClickFixedDepositsRecommendation_OneClickInvestmentBank {
    return {
      bank: isSet(object.bank) ? Bank.fromJSON(object.bank) : undefined,
      lastInvestedOn: isSet(object.lastInvestedOn) ? globalThis.String(object.lastInvestedOn) : "",
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: OneClickFixedDepositsRecommendation_OneClickInvestmentBank): unknown {
    const obj: any = {};
    if (message.bank !== undefined) {
      obj.bank = Bank.toJSON(message.bank);
    }
    if (message.lastInvestedOn !== "") {
      obj.lastInvestedOn = message.lastInvestedOn;
    }
    if (message.tags?.length) {
      obj.tags = message.tags;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OneClickFixedDepositsRecommendation_OneClickInvestmentBank>, I>>(
    base?: I,
  ): OneClickFixedDepositsRecommendation_OneClickInvestmentBank {
    return OneClickFixedDepositsRecommendation_OneClickInvestmentBank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OneClickFixedDepositsRecommendation_OneClickInvestmentBank>, I>>(
    object: I,
  ): OneClickFixedDepositsRecommendation_OneClickInvestmentBank {
    const message = createBaseOneClickFixedDepositsRecommendation_OneClickInvestmentBank();
    message.bank = (object.bank !== undefined && object.bank !== null) ? Bank.fromPartial(object.bank) : undefined;
    message.lastInvestedOn = object.lastInvestedOn ?? "";
    message.tags = object.tags?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
