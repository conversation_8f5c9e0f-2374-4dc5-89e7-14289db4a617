// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: PendingJourney.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RedirectDeeplink } from "./BusinessCommon";
import { BankResponse } from "./Collection";

export const protobufPackage = "com.stablemoney.api.identity";

export enum PendingJourneyCardType {
  UNKNOWN_PENDING_JOURNEY_CARD_TYPE = 0,
  SIMPLE_PENDING_JOURNEY_CARD_TYPE = 1,
  VKYC_ATTEMPT_CONFIRMATION_PENDING_JOURNEY_CARD_TYPE = 2,
  UNRECOGNIZED = -1,
}

export function pendingJourneyCardTypeFromJSON(object: any): PendingJourneyCardType {
  switch (object) {
    case 0:
    case "UNKNOWN_PENDING_JOURNEY_CARD_TYPE":
      return PendingJourneyCardType.UNKNOWN_PENDING_JOURNEY_CARD_TYPE;
    case 1:
    case "SIMPLE_PENDING_JOURNEY_CARD_TYPE":
      return PendingJourneyCardType.SIMPLE_PENDING_JOURNEY_CARD_TYPE;
    case 2:
    case "VKYC_ATTEMPT_CONFIRMATION_PENDING_JOURNEY_CARD_TYPE":
      return PendingJourneyCardType.VKYC_ATTEMPT_CONFIRMATION_PENDING_JOURNEY_CARD_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PendingJourneyCardType.UNRECOGNIZED;
  }
}

export function pendingJourneyCardTypeToJSON(object: PendingJourneyCardType): string {
  switch (object) {
    case PendingJourneyCardType.UNKNOWN_PENDING_JOURNEY_CARD_TYPE:
      return "UNKNOWN_PENDING_JOURNEY_CARD_TYPE";
    case PendingJourneyCardType.SIMPLE_PENDING_JOURNEY_CARD_TYPE:
      return "SIMPLE_PENDING_JOURNEY_CARD_TYPE";
    case PendingJourneyCardType.VKYC_ATTEMPT_CONFIRMATION_PENDING_JOURNEY_CARD_TYPE:
      return "VKYC_ATTEMPT_CONFIRMATION_PENDING_JOURNEY_CARD_TYPE";
    case PendingJourneyCardType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum VkycFailureEventType {
  UNKNOWN_VKYC_FAILURE_EVENT_TYPE = 0,
  SUCCESS_VKYC_FAILURE_EVENT_TYPE = 1,
  FAILURE_VKYC_FAILURE_EVENT_TYPE = 2,
  NEUTRAL_VKYC_FAILURE_EVENT_TYPE = 3,
  UNRECOGNIZED = -1,
}

export function vkycFailureEventTypeFromJSON(object: any): VkycFailureEventType {
  switch (object) {
    case 0:
    case "UNKNOWN_VKYC_FAILURE_EVENT_TYPE":
      return VkycFailureEventType.UNKNOWN_VKYC_FAILURE_EVENT_TYPE;
    case 1:
    case "SUCCESS_VKYC_FAILURE_EVENT_TYPE":
      return VkycFailureEventType.SUCCESS_VKYC_FAILURE_EVENT_TYPE;
    case 2:
    case "FAILURE_VKYC_FAILURE_EVENT_TYPE":
      return VkycFailureEventType.FAILURE_VKYC_FAILURE_EVENT_TYPE;
    case 3:
    case "NEUTRAL_VKYC_FAILURE_EVENT_TYPE":
      return VkycFailureEventType.NEUTRAL_VKYC_FAILURE_EVENT_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return VkycFailureEventType.UNRECOGNIZED;
  }
}

export function vkycFailureEventTypeToJSON(object: VkycFailureEventType): string {
  switch (object) {
    case VkycFailureEventType.UNKNOWN_VKYC_FAILURE_EVENT_TYPE:
      return "UNKNOWN_VKYC_FAILURE_EVENT_TYPE";
    case VkycFailureEventType.SUCCESS_VKYC_FAILURE_EVENT_TYPE:
      return "SUCCESS_VKYC_FAILURE_EVENT_TYPE";
    case VkycFailureEventType.FAILURE_VKYC_FAILURE_EVENT_TYPE:
      return "FAILURE_VKYC_FAILURE_EVENT_TYPE";
    case VkycFailureEventType.NEUTRAL_VKYC_FAILURE_EVENT_TYPE:
      return "NEUTRAL_VKYC_FAILURE_EVENT_TYPE";
    case VkycFailureEventType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum MediaTypes {
  UNKNOWN_MEDIA_TYPE = 0,
  IMAGE_MEDIA_TYPE = 1,
  TIMESTAMP_FOR_VKYC_COUNTDOWN_MEDIA_TYPE = 2,
  UNRECOGNIZED = -1,
}

export function mediaTypesFromJSON(object: any): MediaTypes {
  switch (object) {
    case 0:
    case "UNKNOWN_MEDIA_TYPE":
      return MediaTypes.UNKNOWN_MEDIA_TYPE;
    case 1:
    case "IMAGE_MEDIA_TYPE":
      return MediaTypes.IMAGE_MEDIA_TYPE;
    case 2:
    case "TIMESTAMP_FOR_VKYC_COUNTDOWN_MEDIA_TYPE":
      return MediaTypes.TIMESTAMP_FOR_VKYC_COUNTDOWN_MEDIA_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MediaTypes.UNRECOGNIZED;
  }
}

export function mediaTypesToJSON(object: MediaTypes): string {
  switch (object) {
    case MediaTypes.UNKNOWN_MEDIA_TYPE:
      return "UNKNOWN_MEDIA_TYPE";
    case MediaTypes.IMAGE_MEDIA_TYPE:
      return "IMAGE_MEDIA_TYPE";
    case MediaTypes.TIMESTAMP_FOR_VKYC_COUNTDOWN_MEDIA_TYPE:
      return "TIMESTAMP_FOR_VKYC_COUNTDOWN_MEDIA_TYPE";
    case MediaTypes.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface PendingJourney {
  id: string;
  iconUrl: string;
  title: RichTextResponse[];
  timestamp: string;
  redirectDeeplink: RedirectDeeplink | undefined;
  cardType: PendingJourneyCardType;
  bankId: string;
  journeyType: PendingJourney_JourneyType;
}

export enum PendingJourney_JourneyType {
  ACTION_UNKNOWN = 0,
  VKYC_FAILED = 2,
  VKYC_REQUIRED = 4,
  VKYC_RETRY_REQUIRED = 6,
  VKYC_INITIATED = 8,
  VKYC_EXPIRED = 10,
  UNRECOGNIZED = -1,
}

export function pendingJourney_JourneyTypeFromJSON(object: any): PendingJourney_JourneyType {
  switch (object) {
    case 0:
    case "ACTION_UNKNOWN":
      return PendingJourney_JourneyType.ACTION_UNKNOWN;
    case 2:
    case "VKYC_FAILED":
      return PendingJourney_JourneyType.VKYC_FAILED;
    case 4:
    case "VKYC_REQUIRED":
      return PendingJourney_JourneyType.VKYC_REQUIRED;
    case 6:
    case "VKYC_RETRY_REQUIRED":
      return PendingJourney_JourneyType.VKYC_RETRY_REQUIRED;
    case 8:
    case "VKYC_INITIATED":
      return PendingJourney_JourneyType.VKYC_INITIATED;
    case 10:
    case "VKYC_EXPIRED":
      return PendingJourney_JourneyType.VKYC_EXPIRED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PendingJourney_JourneyType.UNRECOGNIZED;
  }
}

export function pendingJourney_JourneyTypeToJSON(object: PendingJourney_JourneyType): string {
  switch (object) {
    case PendingJourney_JourneyType.ACTION_UNKNOWN:
      return "ACTION_UNKNOWN";
    case PendingJourney_JourneyType.VKYC_FAILED:
      return "VKYC_FAILED";
    case PendingJourney_JourneyType.VKYC_REQUIRED:
      return "VKYC_REQUIRED";
    case PendingJourney_JourneyType.VKYC_RETRY_REQUIRED:
      return "VKYC_RETRY_REQUIRED";
    case PendingJourney_JourneyType.VKYC_INITIATED:
      return "VKYC_INITIATED";
    case PendingJourney_JourneyType.VKYC_EXPIRED:
      return "VKYC_EXPIRED";
    case PendingJourney_JourneyType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface RichTextResponse {
  title: RichTextNode[];
  id: string;
}

export interface PendingJourneyResponse {
  bottomSheet?: BottomSheetResponse | undefined;
  pendingJourneys: PendingJourney[];
}

export interface BottomSheetResponse {
  title: RichTextNode[];
  subTitle: RichTextNode[];
  redirectDeeplink: RedirectDeeplink | undefined;
  iconUrl: string;
  buttonCta: string;
}

export interface VkycAttemptConfirmationResponse {
}

export interface VkycAttemptConfirmationRequest {
  id: string;
  wasSuccessful: boolean;
}

export interface RichTextNode {
  text: string;
  color: string;
}

export interface VkycSlotSelectionResponse {
}

export interface VkycSlotSelectionRequest {
  id: string;
  slotId: string;
}

export interface VkycDetailsResponse {
  isExpired: boolean;
  id: string;
  vkycPendingPageDetails?: VkycPendingPageDetails | undefined;
  vkycFailurePageDetails?: VkycFailurePageDetails | undefined;
  bank: BankResponse | undefined;
  faqCategory: string;
}

export interface VkycPendingPageDetails {
  title: RichTextNode[];
  logoUrl: string;
  bottomText: RichTextNode[];
  timestamp: string;
  steps: VKYCSteps | undefined;
  slots: VkycSchedule | undefined;
}

export interface VkycFailurePageDetails {
  title: RichTextNode[];
  logoUrl: string;
  subTitle: RichTextNode[];
  bottomText: RichTextNode[];
  failureEvents: VkycFailureEvents[];
  tipsForNextAttempt: TipsForNextAttempt[];
}

export interface TipsForNextAttempt {
  text: RichTextNode[];
  subtext: RichTextNode[];
  bankDescription: BankDescription[];
  redirectDeeplink: RedirectDeeplink | undefined;
}

export interface BankDescription {
  bank: BankResponse | undefined;
  description: string;
}

export interface VkycFailureEvents {
  timestamp: string;
  text: string;
  type: VkycFailureEventType;
}

export interface VkycSchedule {
  isAgentAvailable: boolean;
  bookingStatus: VkycBookingStatus | undefined;
  days: DaySchedule[];
  vkycRedirectDeeplink: RedirectDeeplink | undefined;
}

export interface VkycBookingStatus {
  isSlotBooked: boolean;
  description: RichTextNode[];
}

export interface DaySchedule {
  day: string;
  timestamp: string;
  slots: VkycSlot[];
}

export interface VkycSlot {
  id: string;
  displayText: string;
  isRecommended: boolean;
}

export interface VKYCSteps {
  title: RichTextNode[];
  steps: VKYCStep[];
}

export interface VKYCStep {
  title: RichTextNode[];
  shortTitle: RichTextNode[];
  media: Media | undefined;
}

export interface Media {
  url: string;
  type: MediaTypes;
}

function createBasePendingJourney(): PendingJourney {
  return {
    id: "",
    iconUrl: "",
    title: [],
    timestamp: "",
    redirectDeeplink: undefined,
    cardType: 0,
    bankId: "",
    journeyType: 0,
  };
}

export const PendingJourney: MessageFns<PendingJourney> = {
  encode(message: PendingJourney, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.iconUrl !== "") {
      writer.uint32(18).string(message.iconUrl);
    }
    for (const v of message.title) {
      RichTextResponse.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.timestamp !== "") {
      writer.uint32(34).string(message.timestamp);
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(42).fork()).join();
    }
    if (message.cardType !== 0) {
      writer.uint32(48).int32(message.cardType);
    }
    if (message.bankId !== "") {
      writer.uint32(58).string(message.bankId);
    }
    if (message.journeyType !== 0) {
      writer.uint32(72).int32(message.journeyType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PendingJourney {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePendingJourney();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.title.push(RichTextResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.timestamp = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.cardType = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.journeyType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PendingJourney {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      title: globalThis.Array.isArray(object?.title) ? object.title.map((e: any) => RichTextResponse.fromJSON(e)) : [],
      timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : "",
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      cardType: isSet(object.cardType) ? pendingJourneyCardTypeFromJSON(object.cardType) : 0,
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      journeyType: isSet(object.journeyType) ? pendingJourney_JourneyTypeFromJSON(object.journeyType) : 0,
    };
  },

  toJSON(message: PendingJourney): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.title?.length) {
      obj.title = message.title.map((e) => RichTextResponse.toJSON(e));
    }
    if (message.timestamp !== "") {
      obj.timestamp = message.timestamp;
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.cardType !== 0) {
      obj.cardType = pendingJourneyCardTypeToJSON(message.cardType);
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.journeyType !== 0) {
      obj.journeyType = pendingJourney_JourneyTypeToJSON(message.journeyType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PendingJourney>, I>>(base?: I): PendingJourney {
    return PendingJourney.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PendingJourney>, I>>(object: I): PendingJourney {
    const message = createBasePendingJourney();
    message.id = object.id ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.title = object.title?.map((e) => RichTextResponse.fromPartial(e)) || [];
    message.timestamp = object.timestamp ?? "";
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.cardType = object.cardType ?? 0;
    message.bankId = object.bankId ?? "";
    message.journeyType = object.journeyType ?? 0;
    return message;
  },
};

function createBaseRichTextResponse(): RichTextResponse {
  return { title: [], id: "" };
}

export const RichTextResponse: MessageFns<RichTextResponse> = {
  encode(message: RichTextResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.title) {
      RichTextNode.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RichTextResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRichTextResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RichTextResponse {
    return {
      title: globalThis.Array.isArray(object?.title) ? object.title.map((e: any) => RichTextNode.fromJSON(e)) : [],
      id: isSet(object.id) ? globalThis.String(object.id) : "",
    };
  },

  toJSON(message: RichTextResponse): unknown {
    const obj: any = {};
    if (message.title?.length) {
      obj.title = message.title.map((e) => RichTextNode.toJSON(e));
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RichTextResponse>, I>>(base?: I): RichTextResponse {
    return RichTextResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RichTextResponse>, I>>(object: I): RichTextResponse {
    const message = createBaseRichTextResponse();
    message.title = object.title?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.id = object.id ?? "";
    return message;
  },
};

function createBasePendingJourneyResponse(): PendingJourneyResponse {
  return { bottomSheet: undefined, pendingJourneys: [] };
}

export const PendingJourneyResponse: MessageFns<PendingJourneyResponse> = {
  encode(message: PendingJourneyResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bottomSheet !== undefined) {
      BottomSheetResponse.encode(message.bottomSheet, writer.uint32(10).fork()).join();
    }
    for (const v of message.pendingJourneys) {
      PendingJourney.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PendingJourneyResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePendingJourneyResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bottomSheet = BottomSheetResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pendingJourneys.push(PendingJourney.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PendingJourneyResponse {
    return {
      bottomSheet: isSet(object.bottomSheet) ? BottomSheetResponse.fromJSON(object.bottomSheet) : undefined,
      pendingJourneys: globalThis.Array.isArray(object?.pendingJourneys)
        ? object.pendingJourneys.map((e: any) => PendingJourney.fromJSON(e))
        : [],
    };
  },

  toJSON(message: PendingJourneyResponse): unknown {
    const obj: any = {};
    if (message.bottomSheet !== undefined) {
      obj.bottomSheet = BottomSheetResponse.toJSON(message.bottomSheet);
    }
    if (message.pendingJourneys?.length) {
      obj.pendingJourneys = message.pendingJourneys.map((e) => PendingJourney.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PendingJourneyResponse>, I>>(base?: I): PendingJourneyResponse {
    return PendingJourneyResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PendingJourneyResponse>, I>>(object: I): PendingJourneyResponse {
    const message = createBasePendingJourneyResponse();
    message.bottomSheet = (object.bottomSheet !== undefined && object.bottomSheet !== null)
      ? BottomSheetResponse.fromPartial(object.bottomSheet)
      : undefined;
    message.pendingJourneys = object.pendingJourneys?.map((e) => PendingJourney.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBottomSheetResponse(): BottomSheetResponse {
  return { title: [], subTitle: [], redirectDeeplink: undefined, iconUrl: "", buttonCta: "" };
}

export const BottomSheetResponse: MessageFns<BottomSheetResponse> = {
  encode(message: BottomSheetResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.title) {
      RichTextNode.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.subTitle) {
      RichTextNode.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(26).fork()).join();
    }
    if (message.iconUrl !== "") {
      writer.uint32(34).string(message.iconUrl);
    }
    if (message.buttonCta !== "") {
      writer.uint32(42).string(message.buttonCta);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BottomSheetResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBottomSheetResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subTitle.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.buttonCta = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BottomSheetResponse {
    return {
      title: globalThis.Array.isArray(object?.title) ? object.title.map((e: any) => RichTextNode.fromJSON(e)) : [],
      subTitle: globalThis.Array.isArray(object?.subTitle)
        ? object.subTitle.map((e: any) => RichTextNode.fromJSON(e))
        : [],
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      buttonCta: isSet(object.buttonCta) ? globalThis.String(object.buttonCta) : "",
    };
  },

  toJSON(message: BottomSheetResponse): unknown {
    const obj: any = {};
    if (message.title?.length) {
      obj.title = message.title.map((e) => RichTextNode.toJSON(e));
    }
    if (message.subTitle?.length) {
      obj.subTitle = message.subTitle.map((e) => RichTextNode.toJSON(e));
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.buttonCta !== "") {
      obj.buttonCta = message.buttonCta;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BottomSheetResponse>, I>>(base?: I): BottomSheetResponse {
    return BottomSheetResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BottomSheetResponse>, I>>(object: I): BottomSheetResponse {
    const message = createBaseBottomSheetResponse();
    message.title = object.title?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.subTitle = object.subTitle?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.iconUrl = object.iconUrl ?? "";
    message.buttonCta = object.buttonCta ?? "";
    return message;
  },
};

function createBaseVkycAttemptConfirmationResponse(): VkycAttemptConfirmationResponse {
  return {};
}

export const VkycAttemptConfirmationResponse: MessageFns<VkycAttemptConfirmationResponse> = {
  encode(_: VkycAttemptConfirmationResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycAttemptConfirmationResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycAttemptConfirmationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): VkycAttemptConfirmationResponse {
    return {};
  },

  toJSON(_: VkycAttemptConfirmationResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycAttemptConfirmationResponse>, I>>(base?: I): VkycAttemptConfirmationResponse {
    return VkycAttemptConfirmationResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycAttemptConfirmationResponse>, I>>(_: I): VkycAttemptConfirmationResponse {
    const message = createBaseVkycAttemptConfirmationResponse();
    return message;
  },
};

function createBaseVkycAttemptConfirmationRequest(): VkycAttemptConfirmationRequest {
  return { id: "", wasSuccessful: false };
}

export const VkycAttemptConfirmationRequest: MessageFns<VkycAttemptConfirmationRequest> = {
  encode(message: VkycAttemptConfirmationRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.wasSuccessful !== false) {
      writer.uint32(16).bool(message.wasSuccessful);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycAttemptConfirmationRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycAttemptConfirmationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.wasSuccessful = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycAttemptConfirmationRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      wasSuccessful: isSet(object.wasSuccessful) ? globalThis.Boolean(object.wasSuccessful) : false,
    };
  },

  toJSON(message: VkycAttemptConfirmationRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.wasSuccessful !== false) {
      obj.wasSuccessful = message.wasSuccessful;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycAttemptConfirmationRequest>, I>>(base?: I): VkycAttemptConfirmationRequest {
    return VkycAttemptConfirmationRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycAttemptConfirmationRequest>, I>>(
    object: I,
  ): VkycAttemptConfirmationRequest {
    const message = createBaseVkycAttemptConfirmationRequest();
    message.id = object.id ?? "";
    message.wasSuccessful = object.wasSuccessful ?? false;
    return message;
  },
};

function createBaseRichTextNode(): RichTextNode {
  return { text: "", color: "" };
}

export const RichTextNode: MessageFns<RichTextNode> = {
  encode(message: RichTextNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.text !== "") {
      writer.uint32(10).string(message.text);
    }
    if (message.color !== "") {
      writer.uint32(18).string(message.color);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RichTextNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRichTextNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.text = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.color = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RichTextNode {
    return {
      text: isSet(object.text) ? globalThis.String(object.text) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
    };
  },

  toJSON(message: RichTextNode): unknown {
    const obj: any = {};
    if (message.text !== "") {
      obj.text = message.text;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RichTextNode>, I>>(base?: I): RichTextNode {
    return RichTextNode.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RichTextNode>, I>>(object: I): RichTextNode {
    const message = createBaseRichTextNode();
    message.text = object.text ?? "";
    message.color = object.color ?? "";
    return message;
  },
};

function createBaseVkycSlotSelectionResponse(): VkycSlotSelectionResponse {
  return {};
}

export const VkycSlotSelectionResponse: MessageFns<VkycSlotSelectionResponse> = {
  encode(_: VkycSlotSelectionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycSlotSelectionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycSlotSelectionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): VkycSlotSelectionResponse {
    return {};
  },

  toJSON(_: VkycSlotSelectionResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycSlotSelectionResponse>, I>>(base?: I): VkycSlotSelectionResponse {
    return VkycSlotSelectionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycSlotSelectionResponse>, I>>(_: I): VkycSlotSelectionResponse {
    const message = createBaseVkycSlotSelectionResponse();
    return message;
  },
};

function createBaseVkycSlotSelectionRequest(): VkycSlotSelectionRequest {
  return { id: "", slotId: "" };
}

export const VkycSlotSelectionRequest: MessageFns<VkycSlotSelectionRequest> = {
  encode(message: VkycSlotSelectionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.slotId !== "") {
      writer.uint32(18).string(message.slotId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycSlotSelectionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycSlotSelectionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.slotId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycSlotSelectionRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      slotId: isSet(object.slotId) ? globalThis.String(object.slotId) : "",
    };
  },

  toJSON(message: VkycSlotSelectionRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.slotId !== "") {
      obj.slotId = message.slotId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycSlotSelectionRequest>, I>>(base?: I): VkycSlotSelectionRequest {
    return VkycSlotSelectionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycSlotSelectionRequest>, I>>(object: I): VkycSlotSelectionRequest {
    const message = createBaseVkycSlotSelectionRequest();
    message.id = object.id ?? "";
    message.slotId = object.slotId ?? "";
    return message;
  },
};

function createBaseVkycDetailsResponse(): VkycDetailsResponse {
  return {
    isExpired: false,
    id: "",
    vkycPendingPageDetails: undefined,
    vkycFailurePageDetails: undefined,
    bank: undefined,
    faqCategory: "",
  };
}

export const VkycDetailsResponse: MessageFns<VkycDetailsResponse> = {
  encode(message: VkycDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isExpired !== false) {
      writer.uint32(8).bool(message.isExpired);
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    if (message.vkycPendingPageDetails !== undefined) {
      VkycPendingPageDetails.encode(message.vkycPendingPageDetails, writer.uint32(26).fork()).join();
    }
    if (message.vkycFailurePageDetails !== undefined) {
      VkycFailurePageDetails.encode(message.vkycFailurePageDetails, writer.uint32(34).fork()).join();
    }
    if (message.bank !== undefined) {
      BankResponse.encode(message.bank, writer.uint32(42).fork()).join();
    }
    if (message.faqCategory !== "") {
      writer.uint32(50).string(message.faqCategory);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isExpired = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.vkycPendingPageDetails = VkycPendingPageDetails.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.vkycFailurePageDetails = VkycFailurePageDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bank = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.faqCategory = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycDetailsResponse {
    return {
      isExpired: isSet(object.isExpired) ? globalThis.Boolean(object.isExpired) : false,
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      vkycPendingPageDetails: isSet(object.vkycPendingPageDetails)
        ? VkycPendingPageDetails.fromJSON(object.vkycPendingPageDetails)
        : undefined,
      vkycFailurePageDetails: isSet(object.vkycFailurePageDetails)
        ? VkycFailurePageDetails.fromJSON(object.vkycFailurePageDetails)
        : undefined,
      bank: isSet(object.bank) ? BankResponse.fromJSON(object.bank) : undefined,
      faqCategory: isSet(object.faqCategory) ? globalThis.String(object.faqCategory) : "",
    };
  },

  toJSON(message: VkycDetailsResponse): unknown {
    const obj: any = {};
    if (message.isExpired !== false) {
      obj.isExpired = message.isExpired;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.vkycPendingPageDetails !== undefined) {
      obj.vkycPendingPageDetails = VkycPendingPageDetails.toJSON(message.vkycPendingPageDetails);
    }
    if (message.vkycFailurePageDetails !== undefined) {
      obj.vkycFailurePageDetails = VkycFailurePageDetails.toJSON(message.vkycFailurePageDetails);
    }
    if (message.bank !== undefined) {
      obj.bank = BankResponse.toJSON(message.bank);
    }
    if (message.faqCategory !== "") {
      obj.faqCategory = message.faqCategory;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycDetailsResponse>, I>>(base?: I): VkycDetailsResponse {
    return VkycDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycDetailsResponse>, I>>(object: I): VkycDetailsResponse {
    const message = createBaseVkycDetailsResponse();
    message.isExpired = object.isExpired ?? false;
    message.id = object.id ?? "";
    message.vkycPendingPageDetails =
      (object.vkycPendingPageDetails !== undefined && object.vkycPendingPageDetails !== null)
        ? VkycPendingPageDetails.fromPartial(object.vkycPendingPageDetails)
        : undefined;
    message.vkycFailurePageDetails =
      (object.vkycFailurePageDetails !== undefined && object.vkycFailurePageDetails !== null)
        ? VkycFailurePageDetails.fromPartial(object.vkycFailurePageDetails)
        : undefined;
    message.bank = (object.bank !== undefined && object.bank !== null)
      ? BankResponse.fromPartial(object.bank)
      : undefined;
    message.faqCategory = object.faqCategory ?? "";
    return message;
  },
};

function createBaseVkycPendingPageDetails(): VkycPendingPageDetails {
  return { title: [], logoUrl: "", bottomText: [], timestamp: "", steps: undefined, slots: undefined };
}

export const VkycPendingPageDetails: MessageFns<VkycPendingPageDetails> = {
  encode(message: VkycPendingPageDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.title) {
      RichTextNode.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.logoUrl !== "") {
      writer.uint32(18).string(message.logoUrl);
    }
    for (const v of message.bottomText) {
      RichTextNode.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.timestamp !== "") {
      writer.uint32(34).string(message.timestamp);
    }
    if (message.steps !== undefined) {
      VKYCSteps.encode(message.steps, writer.uint32(42).fork()).join();
    }
    if (message.slots !== undefined) {
      VkycSchedule.encode(message.slots, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycPendingPageDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycPendingPageDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bottomText.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.timestamp = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.steps = VKYCSteps.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.slots = VkycSchedule.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycPendingPageDetails {
    return {
      title: globalThis.Array.isArray(object?.title) ? object.title.map((e: any) => RichTextNode.fromJSON(e)) : [],
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
      bottomText: globalThis.Array.isArray(object?.bottomText)
        ? object.bottomText.map((e: any) => RichTextNode.fromJSON(e))
        : [],
      timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : "",
      steps: isSet(object.steps) ? VKYCSteps.fromJSON(object.steps) : undefined,
      slots: isSet(object.slots) ? VkycSchedule.fromJSON(object.slots) : undefined,
    };
  },

  toJSON(message: VkycPendingPageDetails): unknown {
    const obj: any = {};
    if (message.title?.length) {
      obj.title = message.title.map((e) => RichTextNode.toJSON(e));
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    if (message.bottomText?.length) {
      obj.bottomText = message.bottomText.map((e) => RichTextNode.toJSON(e));
    }
    if (message.timestamp !== "") {
      obj.timestamp = message.timestamp;
    }
    if (message.steps !== undefined) {
      obj.steps = VKYCSteps.toJSON(message.steps);
    }
    if (message.slots !== undefined) {
      obj.slots = VkycSchedule.toJSON(message.slots);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycPendingPageDetails>, I>>(base?: I): VkycPendingPageDetails {
    return VkycPendingPageDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycPendingPageDetails>, I>>(object: I): VkycPendingPageDetails {
    const message = createBaseVkycPendingPageDetails();
    message.title = object.title?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.logoUrl = object.logoUrl ?? "";
    message.bottomText = object.bottomText?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.timestamp = object.timestamp ?? "";
    message.steps = (object.steps !== undefined && object.steps !== null)
      ? VKYCSteps.fromPartial(object.steps)
      : undefined;
    message.slots = (object.slots !== undefined && object.slots !== null)
      ? VkycSchedule.fromPartial(object.slots)
      : undefined;
    return message;
  },
};

function createBaseVkycFailurePageDetails(): VkycFailurePageDetails {
  return { title: [], logoUrl: "", subTitle: [], bottomText: [], failureEvents: [], tipsForNextAttempt: [] };
}

export const VkycFailurePageDetails: MessageFns<VkycFailurePageDetails> = {
  encode(message: VkycFailurePageDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.title) {
      RichTextNode.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.logoUrl !== "") {
      writer.uint32(18).string(message.logoUrl);
    }
    for (const v of message.subTitle) {
      RichTextNode.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.bottomText) {
      RichTextNode.encode(v!, writer.uint32(34).fork()).join();
    }
    for (const v of message.failureEvents) {
      VkycFailureEvents.encode(v!, writer.uint32(58).fork()).join();
    }
    for (const v of message.tipsForNextAttempt) {
      TipsForNextAttempt.encode(v!, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycFailurePageDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycFailurePageDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subTitle.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bottomText.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.failureEvents.push(VkycFailureEvents.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.tipsForNextAttempt.push(TipsForNextAttempt.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycFailurePageDetails {
    return {
      title: globalThis.Array.isArray(object?.title) ? object.title.map((e: any) => RichTextNode.fromJSON(e)) : [],
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
      subTitle: globalThis.Array.isArray(object?.subTitle)
        ? object.subTitle.map((e: any) => RichTextNode.fromJSON(e))
        : [],
      bottomText: globalThis.Array.isArray(object?.bottomText)
        ? object.bottomText.map((e: any) => RichTextNode.fromJSON(e))
        : [],
      failureEvents: globalThis.Array.isArray(object?.failureEvents)
        ? object.failureEvents.map((e: any) => VkycFailureEvents.fromJSON(e))
        : [],
      tipsForNextAttempt: globalThis.Array.isArray(object?.tipsForNextAttempt)
        ? object.tipsForNextAttempt.map((e: any) => TipsForNextAttempt.fromJSON(e))
        : [],
    };
  },

  toJSON(message: VkycFailurePageDetails): unknown {
    const obj: any = {};
    if (message.title?.length) {
      obj.title = message.title.map((e) => RichTextNode.toJSON(e));
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    if (message.subTitle?.length) {
      obj.subTitle = message.subTitle.map((e) => RichTextNode.toJSON(e));
    }
    if (message.bottomText?.length) {
      obj.bottomText = message.bottomText.map((e) => RichTextNode.toJSON(e));
    }
    if (message.failureEvents?.length) {
      obj.failureEvents = message.failureEvents.map((e) => VkycFailureEvents.toJSON(e));
    }
    if (message.tipsForNextAttempt?.length) {
      obj.tipsForNextAttempt = message.tipsForNextAttempt.map((e) => TipsForNextAttempt.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycFailurePageDetails>, I>>(base?: I): VkycFailurePageDetails {
    return VkycFailurePageDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycFailurePageDetails>, I>>(object: I): VkycFailurePageDetails {
    const message = createBaseVkycFailurePageDetails();
    message.title = object.title?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.logoUrl = object.logoUrl ?? "";
    message.subTitle = object.subTitle?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.bottomText = object.bottomText?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.failureEvents = object.failureEvents?.map((e) => VkycFailureEvents.fromPartial(e)) || [];
    message.tipsForNextAttempt = object.tipsForNextAttempt?.map((e) => TipsForNextAttempt.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTipsForNextAttempt(): TipsForNextAttempt {
  return { text: [], subtext: [], bankDescription: [], redirectDeeplink: undefined };
}

export const TipsForNextAttempt: MessageFns<TipsForNextAttempt> = {
  encode(message: TipsForNextAttempt, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.text) {
      RichTextNode.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.subtext) {
      RichTextNode.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.bankDescription) {
      BankDescription.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TipsForNextAttempt {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTipsForNextAttempt();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.text.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subtext.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankDescription.push(BankDescription.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TipsForNextAttempt {
    return {
      text: globalThis.Array.isArray(object?.text) ? object.text.map((e: any) => RichTextNode.fromJSON(e)) : [],
      subtext: globalThis.Array.isArray(object?.subtext)
        ? object.subtext.map((e: any) => RichTextNode.fromJSON(e))
        : [],
      bankDescription: globalThis.Array.isArray(object?.bankDescription)
        ? object.bankDescription.map((e: any) => BankDescription.fromJSON(e))
        : [],
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
    };
  },

  toJSON(message: TipsForNextAttempt): unknown {
    const obj: any = {};
    if (message.text?.length) {
      obj.text = message.text.map((e) => RichTextNode.toJSON(e));
    }
    if (message.subtext?.length) {
      obj.subtext = message.subtext.map((e) => RichTextNode.toJSON(e));
    }
    if (message.bankDescription?.length) {
      obj.bankDescription = message.bankDescription.map((e) => BankDescription.toJSON(e));
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TipsForNextAttempt>, I>>(base?: I): TipsForNextAttempt {
    return TipsForNextAttempt.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TipsForNextAttempt>, I>>(object: I): TipsForNextAttempt {
    const message = createBaseTipsForNextAttempt();
    message.text = object.text?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.subtext = object.subtext?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.bankDescription = object.bankDescription?.map((e) => BankDescription.fromPartial(e)) || [];
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    return message;
  },
};

function createBaseBankDescription(): BankDescription {
  return { bank: undefined, description: "" };
}

export const BankDescription: MessageFns<BankDescription> = {
  encode(message: BankDescription, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bank !== undefined) {
      BankResponse.encode(message.bank, writer.uint32(10).fork()).join();
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankDescription {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankDescription();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bank = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankDescription {
    return {
      bank: isSet(object.bank) ? BankResponse.fromJSON(object.bank) : undefined,
      description: isSet(object.description) ? globalThis.String(object.description) : "",
    };
  },

  toJSON(message: BankDescription): unknown {
    const obj: any = {};
    if (message.bank !== undefined) {
      obj.bank = BankResponse.toJSON(message.bank);
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankDescription>, I>>(base?: I): BankDescription {
    return BankDescription.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankDescription>, I>>(object: I): BankDescription {
    const message = createBaseBankDescription();
    message.bank = (object.bank !== undefined && object.bank !== null)
      ? BankResponse.fromPartial(object.bank)
      : undefined;
    message.description = object.description ?? "";
    return message;
  },
};

function createBaseVkycFailureEvents(): VkycFailureEvents {
  return { timestamp: "", text: "", type: 0 };
}

export const VkycFailureEvents: MessageFns<VkycFailureEvents> = {
  encode(message: VkycFailureEvents, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timestamp !== "") {
      writer.uint32(10).string(message.timestamp);
    }
    if (message.text !== "") {
      writer.uint32(18).string(message.text);
    }
    if (message.type !== 0) {
      writer.uint32(24).int32(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycFailureEvents {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycFailureEvents();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.timestamp = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.text = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycFailureEvents {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : "",
      text: isSet(object.text) ? globalThis.String(object.text) : "",
      type: isSet(object.type) ? vkycFailureEventTypeFromJSON(object.type) : 0,
    };
  },

  toJSON(message: VkycFailureEvents): unknown {
    const obj: any = {};
    if (message.timestamp !== "") {
      obj.timestamp = message.timestamp;
    }
    if (message.text !== "") {
      obj.text = message.text;
    }
    if (message.type !== 0) {
      obj.type = vkycFailureEventTypeToJSON(message.type);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycFailureEvents>, I>>(base?: I): VkycFailureEvents {
    return VkycFailureEvents.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycFailureEvents>, I>>(object: I): VkycFailureEvents {
    const message = createBaseVkycFailureEvents();
    message.timestamp = object.timestamp ?? "";
    message.text = object.text ?? "";
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseVkycSchedule(): VkycSchedule {
  return { isAgentAvailable: false, bookingStatus: undefined, days: [], vkycRedirectDeeplink: undefined };
}

export const VkycSchedule: MessageFns<VkycSchedule> = {
  encode(message: VkycSchedule, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isAgentAvailable !== false) {
      writer.uint32(8).bool(message.isAgentAvailable);
    }
    if (message.bookingStatus !== undefined) {
      VkycBookingStatus.encode(message.bookingStatus, writer.uint32(18).fork()).join();
    }
    for (const v of message.days) {
      DaySchedule.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.vkycRedirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.vkycRedirectDeeplink, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycSchedule {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycSchedule();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isAgentAvailable = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bookingStatus = VkycBookingStatus.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.days.push(DaySchedule.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.vkycRedirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycSchedule {
    return {
      isAgentAvailable: isSet(object.isAgentAvailable) ? globalThis.Boolean(object.isAgentAvailable) : false,
      bookingStatus: isSet(object.bookingStatus) ? VkycBookingStatus.fromJSON(object.bookingStatus) : undefined,
      days: globalThis.Array.isArray(object?.days) ? object.days.map((e: any) => DaySchedule.fromJSON(e)) : [],
      vkycRedirectDeeplink: isSet(object.vkycRedirectDeeplink)
        ? RedirectDeeplink.fromJSON(object.vkycRedirectDeeplink)
        : undefined,
    };
  },

  toJSON(message: VkycSchedule): unknown {
    const obj: any = {};
    if (message.isAgentAvailable !== false) {
      obj.isAgentAvailable = message.isAgentAvailable;
    }
    if (message.bookingStatus !== undefined) {
      obj.bookingStatus = VkycBookingStatus.toJSON(message.bookingStatus);
    }
    if (message.days?.length) {
      obj.days = message.days.map((e) => DaySchedule.toJSON(e));
    }
    if (message.vkycRedirectDeeplink !== undefined) {
      obj.vkycRedirectDeeplink = RedirectDeeplink.toJSON(message.vkycRedirectDeeplink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycSchedule>, I>>(base?: I): VkycSchedule {
    return VkycSchedule.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycSchedule>, I>>(object: I): VkycSchedule {
    const message = createBaseVkycSchedule();
    message.isAgentAvailable = object.isAgentAvailable ?? false;
    message.bookingStatus = (object.bookingStatus !== undefined && object.bookingStatus !== null)
      ? VkycBookingStatus.fromPartial(object.bookingStatus)
      : undefined;
    message.days = object.days?.map((e) => DaySchedule.fromPartial(e)) || [];
    message.vkycRedirectDeeplink = (object.vkycRedirectDeeplink !== undefined && object.vkycRedirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.vkycRedirectDeeplink)
      : undefined;
    return message;
  },
};

function createBaseVkycBookingStatus(): VkycBookingStatus {
  return { isSlotBooked: false, description: [] };
}

export const VkycBookingStatus: MessageFns<VkycBookingStatus> = {
  encode(message: VkycBookingStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isSlotBooked !== false) {
      writer.uint32(8).bool(message.isSlotBooked);
    }
    for (const v of message.description) {
      RichTextNode.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycBookingStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycBookingStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isSlotBooked = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycBookingStatus {
    return {
      isSlotBooked: isSet(object.isSlotBooked) ? globalThis.Boolean(object.isSlotBooked) : false,
      description: globalThis.Array.isArray(object?.description)
        ? object.description.map((e: any) => RichTextNode.fromJSON(e))
        : [],
    };
  },

  toJSON(message: VkycBookingStatus): unknown {
    const obj: any = {};
    if (message.isSlotBooked !== false) {
      obj.isSlotBooked = message.isSlotBooked;
    }
    if (message.description?.length) {
      obj.description = message.description.map((e) => RichTextNode.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycBookingStatus>, I>>(base?: I): VkycBookingStatus {
    return VkycBookingStatus.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycBookingStatus>, I>>(object: I): VkycBookingStatus {
    const message = createBaseVkycBookingStatus();
    message.isSlotBooked = object.isSlotBooked ?? false;
    message.description = object.description?.map((e) => RichTextNode.fromPartial(e)) || [];
    return message;
  },
};

function createBaseDaySchedule(): DaySchedule {
  return { day: "", timestamp: "", slots: [] };
}

export const DaySchedule: MessageFns<DaySchedule> = {
  encode(message: DaySchedule, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.day !== "") {
      writer.uint32(10).string(message.day);
    }
    if (message.timestamp !== "") {
      writer.uint32(18).string(message.timestamp);
    }
    for (const v of message.slots) {
      VkycSlot.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DaySchedule {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDaySchedule();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.day = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.timestamp = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.slots.push(VkycSlot.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DaySchedule {
    return {
      day: isSet(object.day) ? globalThis.String(object.day) : "",
      timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : "",
      slots: globalThis.Array.isArray(object?.slots) ? object.slots.map((e: any) => VkycSlot.fromJSON(e)) : [],
    };
  },

  toJSON(message: DaySchedule): unknown {
    const obj: any = {};
    if (message.day !== "") {
      obj.day = message.day;
    }
    if (message.timestamp !== "") {
      obj.timestamp = message.timestamp;
    }
    if (message.slots?.length) {
      obj.slots = message.slots.map((e) => VkycSlot.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DaySchedule>, I>>(base?: I): DaySchedule {
    return DaySchedule.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DaySchedule>, I>>(object: I): DaySchedule {
    const message = createBaseDaySchedule();
    message.day = object.day ?? "";
    message.timestamp = object.timestamp ?? "";
    message.slots = object.slots?.map((e) => VkycSlot.fromPartial(e)) || [];
    return message;
  },
};

function createBaseVkycSlot(): VkycSlot {
  return { id: "", displayText: "", isRecommended: false };
}

export const VkycSlot: MessageFns<VkycSlot> = {
  encode(message: VkycSlot, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.displayText !== "") {
      writer.uint32(18).string(message.displayText);
    }
    if (message.isRecommended !== false) {
      writer.uint32(24).bool(message.isRecommended);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycSlot {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycSlot();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.displayText = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isRecommended = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycSlot {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      displayText: isSet(object.displayText) ? globalThis.String(object.displayText) : "",
      isRecommended: isSet(object.isRecommended) ? globalThis.Boolean(object.isRecommended) : false,
    };
  },

  toJSON(message: VkycSlot): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.displayText !== "") {
      obj.displayText = message.displayText;
    }
    if (message.isRecommended !== false) {
      obj.isRecommended = message.isRecommended;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycSlot>, I>>(base?: I): VkycSlot {
    return VkycSlot.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycSlot>, I>>(object: I): VkycSlot {
    const message = createBaseVkycSlot();
    message.id = object.id ?? "";
    message.displayText = object.displayText ?? "";
    message.isRecommended = object.isRecommended ?? false;
    return message;
  },
};

function createBaseVKYCSteps(): VKYCSteps {
  return { title: [], steps: [] };
}

export const VKYCSteps: MessageFns<VKYCSteps> = {
  encode(message: VKYCSteps, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.title) {
      RichTextNode.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.steps) {
      VKYCStep.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VKYCSteps {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVKYCSteps();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.steps.push(VKYCStep.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VKYCSteps {
    return {
      title: globalThis.Array.isArray(object?.title) ? object.title.map((e: any) => RichTextNode.fromJSON(e)) : [],
      steps: globalThis.Array.isArray(object?.steps) ? object.steps.map((e: any) => VKYCStep.fromJSON(e)) : [],
    };
  },

  toJSON(message: VKYCSteps): unknown {
    const obj: any = {};
    if (message.title?.length) {
      obj.title = message.title.map((e) => RichTextNode.toJSON(e));
    }
    if (message.steps?.length) {
      obj.steps = message.steps.map((e) => VKYCStep.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VKYCSteps>, I>>(base?: I): VKYCSteps {
    return VKYCSteps.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VKYCSteps>, I>>(object: I): VKYCSteps {
    const message = createBaseVKYCSteps();
    message.title = object.title?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.steps = object.steps?.map((e) => VKYCStep.fromPartial(e)) || [];
    return message;
  },
};

function createBaseVKYCStep(): VKYCStep {
  return { title: [], shortTitle: [], media: undefined };
}

export const VKYCStep: MessageFns<VKYCStep> = {
  encode(message: VKYCStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.title) {
      RichTextNode.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.shortTitle) {
      RichTextNode.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.media !== undefined) {
      Media.encode(message.media, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VKYCStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVKYCStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.shortTitle.push(RichTextNode.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.media = Media.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VKYCStep {
    return {
      title: globalThis.Array.isArray(object?.title) ? object.title.map((e: any) => RichTextNode.fromJSON(e)) : [],
      shortTitle: globalThis.Array.isArray(object?.shortTitle)
        ? object.shortTitle.map((e: any) => RichTextNode.fromJSON(e))
        : [],
      media: isSet(object.media) ? Media.fromJSON(object.media) : undefined,
    };
  },

  toJSON(message: VKYCStep): unknown {
    const obj: any = {};
    if (message.title?.length) {
      obj.title = message.title.map((e) => RichTextNode.toJSON(e));
    }
    if (message.shortTitle?.length) {
      obj.shortTitle = message.shortTitle.map((e) => RichTextNode.toJSON(e));
    }
    if (message.media !== undefined) {
      obj.media = Media.toJSON(message.media);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VKYCStep>, I>>(base?: I): VKYCStep {
    return VKYCStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VKYCStep>, I>>(object: I): VKYCStep {
    const message = createBaseVKYCStep();
    message.title = object.title?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.shortTitle = object.shortTitle?.map((e) => RichTextNode.fromPartial(e)) || [];
    message.media = (object.media !== undefined && object.media !== null) ? Media.fromPartial(object.media) : undefined;
    return message;
  },
};

function createBaseMedia(): Media {
  return { url: "", type: 0 };
}

export const Media: MessageFns<Media> = {
  encode(message: Media, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.url !== "") {
      writer.uint32(10).string(message.url);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Media {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMedia();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.url = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Media {
    return {
      url: isSet(object.url) ? globalThis.String(object.url) : "",
      type: isSet(object.type) ? mediaTypesFromJSON(object.type) : 0,
    };
  },

  toJSON(message: Media): unknown {
    const obj: any = {};
    if (message.url !== "") {
      obj.url = message.url;
    }
    if (message.type !== 0) {
      obj.type = mediaTypesToJSON(message.type);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Media>, I>>(base?: I): Media {
    return Media.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Media>, I>>(object: I): Media {
    const message = createBaseMedia();
    message.url = object.url ?? "";
    message.type = object.type ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
