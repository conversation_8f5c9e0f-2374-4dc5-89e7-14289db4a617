// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: BusinessCommon.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum BankType {
  UNKNOWN_BANK_TYPE = 0,
  NBFC_BANK_TYPE = 1,
  PRIVATE_BANK_TYPE = 2,
  PUBLIC_BANK_TYPE = 3,
  COOPERATIVE_BANK_TYPE = 4,
  SMALL_FINANCE_BANK_TYPE = 5,
  POST_OFFICE_SAVINGS_BANK_TYPE = 6,
  FOREIGN_BANK_TYPE = 7,
  PAYMENTS_BANK_TYPE = 8,
  UNRECOGNIZED = -1,
}

export function bankTypeFromJSON(object: any): BankType {
  switch (object) {
    case 0:
    case "UNKNOWN_BANK_TYPE":
      return BankType.UNKNOWN_BANK_TYPE;
    case 1:
    case "NBFC_BANK_TYPE":
      return BankType.NBFC_BANK_TYPE;
    case 2:
    case "PRIVATE_BANK_TYPE":
      return BankType.PRIVATE_BANK_TYPE;
    case 3:
    case "PUBLIC_BANK_TYPE":
      return BankType.PUBLIC_BANK_TYPE;
    case 4:
    case "COOPERATIVE_BANK_TYPE":
      return BankType.COOPERATIVE_BANK_TYPE;
    case 5:
    case "SMALL_FINANCE_BANK_TYPE":
      return BankType.SMALL_FINANCE_BANK_TYPE;
    case 6:
    case "POST_OFFICE_SAVINGS_BANK_TYPE":
      return BankType.POST_OFFICE_SAVINGS_BANK_TYPE;
    case 7:
    case "FOREIGN_BANK_TYPE":
      return BankType.FOREIGN_BANK_TYPE;
    case 8:
    case "PAYMENTS_BANK_TYPE":
      return BankType.PAYMENTS_BANK_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BankType.UNRECOGNIZED;
  }
}

export function bankTypeToJSON(object: BankType): string {
  switch (object) {
    case BankType.UNKNOWN_BANK_TYPE:
      return "UNKNOWN_BANK_TYPE";
    case BankType.NBFC_BANK_TYPE:
      return "NBFC_BANK_TYPE";
    case BankType.PRIVATE_BANK_TYPE:
      return "PRIVATE_BANK_TYPE";
    case BankType.PUBLIC_BANK_TYPE:
      return "PUBLIC_BANK_TYPE";
    case BankType.COOPERATIVE_BANK_TYPE:
      return "COOPERATIVE_BANK_TYPE";
    case BankType.SMALL_FINANCE_BANK_TYPE:
      return "SMALL_FINANCE_BANK_TYPE";
    case BankType.POST_OFFICE_SAVINGS_BANK_TYPE:
      return "POST_OFFICE_SAVINGS_BANK_TYPE";
    case BankType.FOREIGN_BANK_TYPE:
      return "FOREIGN_BANK_TYPE";
    case BankType.PAYMENTS_BANK_TYPE:
      return "PAYMENTS_BANK_TYPE";
    case BankType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum InvestorType {
  UNKNOWN_INVESTOR_TYPE = 0,
  GENERAL_PUBLIC_INVESTOR_TYPE = 1,
  SENIOR_CITIZEN_INVESTOR_TYPE = 2,
  WOMEN_INVESTOR_TYPE = 3,
  WOMEN_SENIOR_CITIZEN_INVESTOR_TYPE = 4,
  UNRECOGNIZED = -1,
}

export function investorTypeFromJSON(object: any): InvestorType {
  switch (object) {
    case 0:
    case "UNKNOWN_INVESTOR_TYPE":
      return InvestorType.UNKNOWN_INVESTOR_TYPE;
    case 1:
    case "GENERAL_PUBLIC_INVESTOR_TYPE":
      return InvestorType.GENERAL_PUBLIC_INVESTOR_TYPE;
    case 2:
    case "SENIOR_CITIZEN_INVESTOR_TYPE":
      return InvestorType.SENIOR_CITIZEN_INVESTOR_TYPE;
    case 3:
    case "WOMEN_INVESTOR_TYPE":
      return InvestorType.WOMEN_INVESTOR_TYPE;
    case 4:
    case "WOMEN_SENIOR_CITIZEN_INVESTOR_TYPE":
      return InvestorType.WOMEN_SENIOR_CITIZEN_INVESTOR_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return InvestorType.UNRECOGNIZED;
  }
}

export function investorTypeToJSON(object: InvestorType): string {
  switch (object) {
    case InvestorType.UNKNOWN_INVESTOR_TYPE:
      return "UNKNOWN_INVESTOR_TYPE";
    case InvestorType.GENERAL_PUBLIC_INVESTOR_TYPE:
      return "GENERAL_PUBLIC_INVESTOR_TYPE";
    case InvestorType.SENIOR_CITIZEN_INVESTOR_TYPE:
      return "SENIOR_CITIZEN_INVESTOR_TYPE";
    case InvestorType.WOMEN_INVESTOR_TYPE:
      return "WOMEN_INVESTOR_TYPE";
    case InvestorType.WOMEN_SENIOR_CITIZEN_INVESTOR_TYPE:
      return "WOMEN_SENIOR_CITIZEN_INVESTOR_TYPE";
    case InvestorType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum OfferingStatus {
  UNKNOWN_OFFERING_STATUS = 0,
  ACTIVE_OFFERING_STATUS = 1,
  INACTIVE_OFFERING_STATUS = 2,
  COMING_SOON_OFFERING_STATUS = 3,
  DELETED_OFFERING_STATUS = 4,
  UNRECOGNIZED = -1,
}

export function offeringStatusFromJSON(object: any): OfferingStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_OFFERING_STATUS":
      return OfferingStatus.UNKNOWN_OFFERING_STATUS;
    case 1:
    case "ACTIVE_OFFERING_STATUS":
      return OfferingStatus.ACTIVE_OFFERING_STATUS;
    case 2:
    case "INACTIVE_OFFERING_STATUS":
      return OfferingStatus.INACTIVE_OFFERING_STATUS;
    case 3:
    case "COMING_SOON_OFFERING_STATUS":
      return OfferingStatus.COMING_SOON_OFFERING_STATUS;
    case 4:
    case "DELETED_OFFERING_STATUS":
      return OfferingStatus.DELETED_OFFERING_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return OfferingStatus.UNRECOGNIZED;
  }
}

export function offeringStatusToJSON(object: OfferingStatus): string {
  switch (object) {
    case OfferingStatus.UNKNOWN_OFFERING_STATUS:
      return "UNKNOWN_OFFERING_STATUS";
    case OfferingStatus.ACTIVE_OFFERING_STATUS:
      return "ACTIVE_OFFERING_STATUS";
    case OfferingStatus.INACTIVE_OFFERING_STATUS:
      return "INACTIVE_OFFERING_STATUS";
    case OfferingStatus.COMING_SOON_OFFERING_STATUS:
      return "COMING_SOON_OFFERING_STATUS";
    case OfferingStatus.DELETED_OFFERING_STATUS:
      return "DELETED_OFFERING_STATUS";
    case OfferingStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum InterestPayoutType {
  UNKNOWN_INTEREST_PAYOUT_TYPE = 0,
  MONTHLY_INTEREST_PAYOUT_TYPE = 1,
  QUARTERLY_INTEREST_PAYOUT_TYPE = 2,
  HALF_YEARLY_INTEREST_PAYOUT_TYPE = 3,
  YEARLY_INTEREST_PAYOUT_TYPE = 4,
  MATURITY_INTEREST_PAYOUT_TYPE = 5,
  UNRECOGNIZED = -1,
}

export function interestPayoutTypeFromJSON(object: any): InterestPayoutType {
  switch (object) {
    case 0:
    case "UNKNOWN_INTEREST_PAYOUT_TYPE":
      return InterestPayoutType.UNKNOWN_INTEREST_PAYOUT_TYPE;
    case 1:
    case "MONTHLY_INTEREST_PAYOUT_TYPE":
      return InterestPayoutType.MONTHLY_INTEREST_PAYOUT_TYPE;
    case 2:
    case "QUARTERLY_INTEREST_PAYOUT_TYPE":
      return InterestPayoutType.QUARTERLY_INTEREST_PAYOUT_TYPE;
    case 3:
    case "HALF_YEARLY_INTEREST_PAYOUT_TYPE":
      return InterestPayoutType.HALF_YEARLY_INTEREST_PAYOUT_TYPE;
    case 4:
    case "YEARLY_INTEREST_PAYOUT_TYPE":
      return InterestPayoutType.YEARLY_INTEREST_PAYOUT_TYPE;
    case 5:
    case "MATURITY_INTEREST_PAYOUT_TYPE":
      return InterestPayoutType.MATURITY_INTEREST_PAYOUT_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return InterestPayoutType.UNRECOGNIZED;
  }
}

export function interestPayoutTypeToJSON(object: InterestPayoutType): string {
  switch (object) {
    case InterestPayoutType.UNKNOWN_INTEREST_PAYOUT_TYPE:
      return "UNKNOWN_INTEREST_PAYOUT_TYPE";
    case InterestPayoutType.MONTHLY_INTEREST_PAYOUT_TYPE:
      return "MONTHLY_INTEREST_PAYOUT_TYPE";
    case InterestPayoutType.QUARTERLY_INTEREST_PAYOUT_TYPE:
      return "QUARTERLY_INTEREST_PAYOUT_TYPE";
    case InterestPayoutType.HALF_YEARLY_INTEREST_PAYOUT_TYPE:
      return "HALF_YEARLY_INTEREST_PAYOUT_TYPE";
    case InterestPayoutType.YEARLY_INTEREST_PAYOUT_TYPE:
      return "YEARLY_INTEREST_PAYOUT_TYPE";
    case InterestPayoutType.MATURITY_INTEREST_PAYOUT_TYPE:
      return "MATURITY_INTEREST_PAYOUT_TYPE";
    case InterestPayoutType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CompoundingFrequencyType {
  UNKNOWN_COMPOUNDING_FREQUENCY_TYPE = 0,
  MONTHLY_COMPOUNDING_FREQUENCY_TYPE = 1,
  QUARTERLY_COMPOUNDING_FREQUENCY_TYPE = 2,
  HALF_YEARLY_COMPOUNDING_FREQUENCY_TYPE = 3,
  YEARLY_COMPOUNDING_FREQUENCY_TYPE = 4,
  UNRECOGNIZED = -1,
}

export function compoundingFrequencyTypeFromJSON(object: any): CompoundingFrequencyType {
  switch (object) {
    case 0:
    case "UNKNOWN_COMPOUNDING_FREQUENCY_TYPE":
      return CompoundingFrequencyType.UNKNOWN_COMPOUNDING_FREQUENCY_TYPE;
    case 1:
    case "MONTHLY_COMPOUNDING_FREQUENCY_TYPE":
      return CompoundingFrequencyType.MONTHLY_COMPOUNDING_FREQUENCY_TYPE;
    case 2:
    case "QUARTERLY_COMPOUNDING_FREQUENCY_TYPE":
      return CompoundingFrequencyType.QUARTERLY_COMPOUNDING_FREQUENCY_TYPE;
    case 3:
    case "HALF_YEARLY_COMPOUNDING_FREQUENCY_TYPE":
      return CompoundingFrequencyType.HALF_YEARLY_COMPOUNDING_FREQUENCY_TYPE;
    case 4:
    case "YEARLY_COMPOUNDING_FREQUENCY_TYPE":
      return CompoundingFrequencyType.YEARLY_COMPOUNDING_FREQUENCY_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CompoundingFrequencyType.UNRECOGNIZED;
  }
}

export function compoundingFrequencyTypeToJSON(object: CompoundingFrequencyType): string {
  switch (object) {
    case CompoundingFrequencyType.UNKNOWN_COMPOUNDING_FREQUENCY_TYPE:
      return "UNKNOWN_COMPOUNDING_FREQUENCY_TYPE";
    case CompoundingFrequencyType.MONTHLY_COMPOUNDING_FREQUENCY_TYPE:
      return "MONTHLY_COMPOUNDING_FREQUENCY_TYPE";
    case CompoundingFrequencyType.QUARTERLY_COMPOUNDING_FREQUENCY_TYPE:
      return "QUARTERLY_COMPOUNDING_FREQUENCY_TYPE";
    case CompoundingFrequencyType.HALF_YEARLY_COMPOUNDING_FREQUENCY_TYPE:
      return "HALF_YEARLY_COMPOUNDING_FREQUENCY_TYPE";
    case CompoundingFrequencyType.YEARLY_COMPOUNDING_FREQUENCY_TYPE:
      return "YEARLY_COMPOUNDING_FREQUENCY_TYPE";
    case CompoundingFrequencyType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum UiScreenType {
  UNKNOWN_UI_SCREEN = 0,
  HOME_SCREEN = 1,
  EXPLORE_SCREEN = 2,
  UNRECOGNIZED = -1,
}

export function uiScreenTypeFromJSON(object: any): UiScreenType {
  switch (object) {
    case 0:
    case "UNKNOWN_UI_SCREEN":
      return UiScreenType.UNKNOWN_UI_SCREEN;
    case 1:
    case "HOME_SCREEN":
      return UiScreenType.HOME_SCREEN;
    case 2:
    case "EXPLORE_SCREEN":
      return UiScreenType.EXPLORE_SCREEN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return UiScreenType.UNRECOGNIZED;
  }
}

export function uiScreenTypeToJSON(object: UiScreenType): string {
  switch (object) {
    case UiScreenType.UNKNOWN_UI_SCREEN:
      return "UNKNOWN_UI_SCREEN";
    case UiScreenType.HOME_SCREEN:
      return "HOME_SCREEN";
    case UiScreenType.EXPLORE_SCREEN:
      return "EXPLORE_SCREEN";
    case UiScreenType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum InvestabilityStatus {
  UNKNOWN_INVESTABILITY_STATUS = 0,
  ACTIVE_INVESTABILITY_STATUS = 1,
  INACTIVE_INVESTABILITY_STATUS = 2,
  COMING_SOON_INVESTABILITY_STATUS = 3,
  UNRECOGNIZED = -1,
}

export function investabilityStatusFromJSON(object: any): InvestabilityStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_INVESTABILITY_STATUS":
      return InvestabilityStatus.UNKNOWN_INVESTABILITY_STATUS;
    case 1:
    case "ACTIVE_INVESTABILITY_STATUS":
      return InvestabilityStatus.ACTIVE_INVESTABILITY_STATUS;
    case 2:
    case "INACTIVE_INVESTABILITY_STATUS":
      return InvestabilityStatus.INACTIVE_INVESTABILITY_STATUS;
    case 3:
    case "COMING_SOON_INVESTABILITY_STATUS":
      return InvestabilityStatus.COMING_SOON_INVESTABILITY_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return InvestabilityStatus.UNRECOGNIZED;
  }
}

export function investabilityStatusToJSON(object: InvestabilityStatus): string {
  switch (object) {
    case InvestabilityStatus.UNKNOWN_INVESTABILITY_STATUS:
      return "UNKNOWN_INVESTABILITY_STATUS";
    case InvestabilityStatus.ACTIVE_INVESTABILITY_STATUS:
      return "ACTIVE_INVESTABILITY_STATUS";
    case InvestabilityStatus.INACTIVE_INVESTABILITY_STATUS:
      return "INACTIVE_INVESTABILITY_STATUS";
    case InvestabilityStatus.COMING_SOON_INVESTABILITY_STATUS:
      return "COMING_SOON_INVESTABILITY_STATUS";
    case InvestabilityStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum InvestabilityRolloutStatus {
  UNKNOWN_INVESTABILITY_ROLLOUT_STATUS = 0,
  COMPLETE_ROLLOUT_STATUS = 1,
  PARTIALLY_ROLLOUT_STATUS = 2,
  UNRECOGNIZED = -1,
}

export function investabilityRolloutStatusFromJSON(object: any): InvestabilityRolloutStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_INVESTABILITY_ROLLOUT_STATUS":
      return InvestabilityRolloutStatus.UNKNOWN_INVESTABILITY_ROLLOUT_STATUS;
    case 1:
    case "COMPLETE_ROLLOUT_STATUS":
      return InvestabilityRolloutStatus.COMPLETE_ROLLOUT_STATUS;
    case 2:
    case "PARTIALLY_ROLLOUT_STATUS":
      return InvestabilityRolloutStatus.PARTIALLY_ROLLOUT_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return InvestabilityRolloutStatus.UNRECOGNIZED;
  }
}

export function investabilityRolloutStatusToJSON(object: InvestabilityRolloutStatus): string {
  switch (object) {
    case InvestabilityRolloutStatus.UNKNOWN_INVESTABILITY_ROLLOUT_STATUS:
      return "UNKNOWN_INVESTABILITY_ROLLOUT_STATUS";
    case InvestabilityRolloutStatus.COMPLETE_ROLLOUT_STATUS:
      return "COMPLETE_ROLLOUT_STATUS";
    case InvestabilityRolloutStatus.PARTIALLY_ROLLOUT_STATUS:
      return "PARTIALLY_ROLLOUT_STATUS";
    case InvestabilityRolloutStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum TenureFormatType {
  UNKNOWN_TENURE_FORMAT_TYPE = 0,
  DAYS_TENURE_FORMAT_TYPE = 1,
  YEAR_MONTH_DAY_TENURE_FORMAT_TYPE = 2,
  UNRECOGNIZED = -1,
}

export function tenureFormatTypeFromJSON(object: any): TenureFormatType {
  switch (object) {
    case 0:
    case "UNKNOWN_TENURE_FORMAT_TYPE":
      return TenureFormatType.UNKNOWN_TENURE_FORMAT_TYPE;
    case 1:
    case "DAYS_TENURE_FORMAT_TYPE":
      return TenureFormatType.DAYS_TENURE_FORMAT_TYPE;
    case 2:
    case "YEAR_MONTH_DAY_TENURE_FORMAT_TYPE":
      return TenureFormatType.YEAR_MONTH_DAY_TENURE_FORMAT_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TenureFormatType.UNRECOGNIZED;
  }
}

export function tenureFormatTypeToJSON(object: TenureFormatType): string {
  switch (object) {
    case TenureFormatType.UNKNOWN_TENURE_FORMAT_TYPE:
      return "UNKNOWN_TENURE_FORMAT_TYPE";
    case TenureFormatType.DAYS_TENURE_FORMAT_TYPE:
      return "DAYS_TENURE_FORMAT_TYPE";
    case TenureFormatType.YEAR_MONTH_DAY_TENURE_FORMAT_TYPE:
      return "YEAR_MONTH_DAY_TENURE_FORMAT_TYPE";
    case TenureFormatType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum BusinessProvider {
  UNKNOWN_BUSINESS_PROVIDER = 0,
  TARRAKKI_BUSINESS_PROVIDER = 1,
  UPSWING_BUSINESS_PROVIDER = 2,
  UNRECOGNIZED = -1,
}

export function businessProviderFromJSON(object: any): BusinessProvider {
  switch (object) {
    case 0:
    case "UNKNOWN_BUSINESS_PROVIDER":
      return BusinessProvider.UNKNOWN_BUSINESS_PROVIDER;
    case 1:
    case "TARRAKKI_BUSINESS_PROVIDER":
      return BusinessProvider.TARRAKKI_BUSINESS_PROVIDER;
    case 2:
    case "UPSWING_BUSINESS_PROVIDER":
      return BusinessProvider.UPSWING_BUSINESS_PROVIDER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BusinessProvider.UNRECOGNIZED;
  }
}

export function businessProviderToJSON(object: BusinessProvider): string {
  switch (object) {
    case BusinessProvider.UNKNOWN_BUSINESS_PROVIDER:
      return "UNKNOWN_BUSINESS_PROVIDER";
    case BusinessProvider.TARRAKKI_BUSINESS_PROVIDER:
      return "TARRAKKI_BUSINESS_PROVIDER";
    case BusinessProvider.UPSWING_BUSINESS_PROVIDER:
      return "UPSWING_BUSINESS_PROVIDER";
    case BusinessProvider.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface RedirectDeeplink {
  path: string;
  pathType: string;
}

function createBaseRedirectDeeplink(): RedirectDeeplink {
  return { path: "", pathType: "" };
}

export const RedirectDeeplink: MessageFns<RedirectDeeplink> = {
  encode(message: RedirectDeeplink, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.path !== "") {
      writer.uint32(10).string(message.path);
    }
    if (message.pathType !== "") {
      writer.uint32(18).string(message.pathType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RedirectDeeplink {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRedirectDeeplink();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.path = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pathType = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RedirectDeeplink {
    return {
      path: isSet(object.path) ? globalThis.String(object.path) : "",
      pathType: isSet(object.pathType) ? globalThis.String(object.pathType) : "",
    };
  },

  toJSON(message: RedirectDeeplink): unknown {
    const obj: any = {};
    if (message.path !== "") {
      obj.path = message.path;
    }
    if (message.pathType !== "") {
      obj.pathType = message.pathType;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RedirectDeeplink>, I>>(base?: I): RedirectDeeplink {
    return RedirectDeeplink.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RedirectDeeplink>, I>>(object: I): RedirectDeeplink {
    const message = createBaseRedirectDeeplink();
    message.path = object.path ?? "";
    message.pathType = object.pathType ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
