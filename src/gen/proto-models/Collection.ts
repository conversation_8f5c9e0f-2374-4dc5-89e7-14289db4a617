// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Collection.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  BankType,
  bankTypeFromJSON,
  bankTypeToJSON,
  InterestPayoutType,
  interestPayoutTypeFromJSON,
  interestPayoutTypeToJSON,
  InvestabilityRolloutStatus,
  investabilityRolloutStatusFromJSON,
  investabilityRolloutStatusToJSON,
  InvestabilityStatus,
  investabilityStatusFromJSON,
  investabilityStatusToJSON,
  InvestorType,
  investorTypeFromJSON,
  investorTypeToJSON,
  RedirectDeeplink,
  TenureFormatType,
  tenureFormatTypeFromJSON,
  tenureFormatTypeToJSON,
} from "./BusinessCommon";

export const protobufPackage = "com.stablemoney.api.identity";

export enum CollectionDataType {
  UNKNOWN_COLLECTION_DATA_TYPE = 0,
  AGGREGATED_BANK_DATA = 1,
  HIGHEST_INTEREST_RATE = 2,
  HIGHEST_INTEREST_RATE_SHORT_TERM = 3,
  HIGHEST_INTEREST_RATE_MEDIUM_TERM = 4,
  HIGHEST_INTEREST_RATE_LONG_TERM = 5,
  HIGHEST_INTEREST_RATE_TAX_SAVING = 6,
  HIGHEST_INTEREST_RATE_1_YEAR_WITH_DELTA = 7,
  HIGHEST_INTEREST_RATE_2_YEAR_WITH_DELTA = 8,
  HIGHEST_INTEREST_RATE_3_YEAR_WITH_DELTA = 9,
  HIGHEST_INTEREST_RATE_4_YEAR_WITH_DELTA = 10,
  HIGHEST_INTEREST_RATE_5_YEAR_WITH_DELTA = 11,
  HIGHEST_INTEREST_RATE_1_YEAR_WITHOUT_DELTA = 12,
  HIGHEST_INTEREST_RATE_2_YEAR_WITHOUT_DELTA = 13,
  HIGHEST_INTEREST_RATE_3_YEAR_WITHOUT_DELTA = 14,
  HIGHEST_INTEREST_RATE_4_YEAR_WITHOUT_DELTA = 15,
  HIGHEST_INTEREST_RATE_5_YEAR_WITHOUT_DELTA = 16,
  HIGHEST_INTEREST_SPECIAL_RATE_FD = 17,
  HIGHEST_INTEREST_ZERO_LOCK_IN_PERIOD_FD = 18,
  HIGHEST_INTEREST_RATE_FOR_WOMEN_FD = 19,
  HIGHEST_INTEREST_RATE_FOR_SENIOR_CITIZEN_FD = 20,
  HIGHEST_INTEREST_RATE_FOR_GENERAL_CITIZEN = 21,
  UNRECOGNIZED = -1,
}

export function collectionDataTypeFromJSON(object: any): CollectionDataType {
  switch (object) {
    case 0:
    case "UNKNOWN_COLLECTION_DATA_TYPE":
      return CollectionDataType.UNKNOWN_COLLECTION_DATA_TYPE;
    case 1:
    case "AGGREGATED_BANK_DATA":
      return CollectionDataType.AGGREGATED_BANK_DATA;
    case 2:
    case "HIGHEST_INTEREST_RATE":
      return CollectionDataType.HIGHEST_INTEREST_RATE;
    case 3:
    case "HIGHEST_INTEREST_RATE_SHORT_TERM":
      return CollectionDataType.HIGHEST_INTEREST_RATE_SHORT_TERM;
    case 4:
    case "HIGHEST_INTEREST_RATE_MEDIUM_TERM":
      return CollectionDataType.HIGHEST_INTEREST_RATE_MEDIUM_TERM;
    case 5:
    case "HIGHEST_INTEREST_RATE_LONG_TERM":
      return CollectionDataType.HIGHEST_INTEREST_RATE_LONG_TERM;
    case 6:
    case "HIGHEST_INTEREST_RATE_TAX_SAVING":
      return CollectionDataType.HIGHEST_INTEREST_RATE_TAX_SAVING;
    case 7:
    case "HIGHEST_INTEREST_RATE_1_YEAR_WITH_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_1_YEAR_WITH_DELTA;
    case 8:
    case "HIGHEST_INTEREST_RATE_2_YEAR_WITH_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_2_YEAR_WITH_DELTA;
    case 9:
    case "HIGHEST_INTEREST_RATE_3_YEAR_WITH_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_3_YEAR_WITH_DELTA;
    case 10:
    case "HIGHEST_INTEREST_RATE_4_YEAR_WITH_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_4_YEAR_WITH_DELTA;
    case 11:
    case "HIGHEST_INTEREST_RATE_5_YEAR_WITH_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_5_YEAR_WITH_DELTA;
    case 12:
    case "HIGHEST_INTEREST_RATE_1_YEAR_WITHOUT_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_1_YEAR_WITHOUT_DELTA;
    case 13:
    case "HIGHEST_INTEREST_RATE_2_YEAR_WITHOUT_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_2_YEAR_WITHOUT_DELTA;
    case 14:
    case "HIGHEST_INTEREST_RATE_3_YEAR_WITHOUT_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_3_YEAR_WITHOUT_DELTA;
    case 15:
    case "HIGHEST_INTEREST_RATE_4_YEAR_WITHOUT_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_4_YEAR_WITHOUT_DELTA;
    case 16:
    case "HIGHEST_INTEREST_RATE_5_YEAR_WITHOUT_DELTA":
      return CollectionDataType.HIGHEST_INTEREST_RATE_5_YEAR_WITHOUT_DELTA;
    case 17:
    case "HIGHEST_INTEREST_SPECIAL_RATE_FD":
      return CollectionDataType.HIGHEST_INTEREST_SPECIAL_RATE_FD;
    case 18:
    case "HIGHEST_INTEREST_ZERO_LOCK_IN_PERIOD_FD":
      return CollectionDataType.HIGHEST_INTEREST_ZERO_LOCK_IN_PERIOD_FD;
    case 19:
    case "HIGHEST_INTEREST_RATE_FOR_WOMEN_FD":
      return CollectionDataType.HIGHEST_INTEREST_RATE_FOR_WOMEN_FD;
    case 20:
    case "HIGHEST_INTEREST_RATE_FOR_SENIOR_CITIZEN_FD":
      return CollectionDataType.HIGHEST_INTEREST_RATE_FOR_SENIOR_CITIZEN_FD;
    case 21:
    case "HIGHEST_INTEREST_RATE_FOR_GENERAL_CITIZEN":
      return CollectionDataType.HIGHEST_INTEREST_RATE_FOR_GENERAL_CITIZEN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CollectionDataType.UNRECOGNIZED;
  }
}

export function collectionDataTypeToJSON(object: CollectionDataType): string {
  switch (object) {
    case CollectionDataType.UNKNOWN_COLLECTION_DATA_TYPE:
      return "UNKNOWN_COLLECTION_DATA_TYPE";
    case CollectionDataType.AGGREGATED_BANK_DATA:
      return "AGGREGATED_BANK_DATA";
    case CollectionDataType.HIGHEST_INTEREST_RATE:
      return "HIGHEST_INTEREST_RATE";
    case CollectionDataType.HIGHEST_INTEREST_RATE_SHORT_TERM:
      return "HIGHEST_INTEREST_RATE_SHORT_TERM";
    case CollectionDataType.HIGHEST_INTEREST_RATE_MEDIUM_TERM:
      return "HIGHEST_INTEREST_RATE_MEDIUM_TERM";
    case CollectionDataType.HIGHEST_INTEREST_RATE_LONG_TERM:
      return "HIGHEST_INTEREST_RATE_LONG_TERM";
    case CollectionDataType.HIGHEST_INTEREST_RATE_TAX_SAVING:
      return "HIGHEST_INTEREST_RATE_TAX_SAVING";
    case CollectionDataType.HIGHEST_INTEREST_RATE_1_YEAR_WITH_DELTA:
      return "HIGHEST_INTEREST_RATE_1_YEAR_WITH_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_RATE_2_YEAR_WITH_DELTA:
      return "HIGHEST_INTEREST_RATE_2_YEAR_WITH_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_RATE_3_YEAR_WITH_DELTA:
      return "HIGHEST_INTEREST_RATE_3_YEAR_WITH_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_RATE_4_YEAR_WITH_DELTA:
      return "HIGHEST_INTEREST_RATE_4_YEAR_WITH_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_RATE_5_YEAR_WITH_DELTA:
      return "HIGHEST_INTEREST_RATE_5_YEAR_WITH_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_RATE_1_YEAR_WITHOUT_DELTA:
      return "HIGHEST_INTEREST_RATE_1_YEAR_WITHOUT_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_RATE_2_YEAR_WITHOUT_DELTA:
      return "HIGHEST_INTEREST_RATE_2_YEAR_WITHOUT_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_RATE_3_YEAR_WITHOUT_DELTA:
      return "HIGHEST_INTEREST_RATE_3_YEAR_WITHOUT_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_RATE_4_YEAR_WITHOUT_DELTA:
      return "HIGHEST_INTEREST_RATE_4_YEAR_WITHOUT_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_RATE_5_YEAR_WITHOUT_DELTA:
      return "HIGHEST_INTEREST_RATE_5_YEAR_WITHOUT_DELTA";
    case CollectionDataType.HIGHEST_INTEREST_SPECIAL_RATE_FD:
      return "HIGHEST_INTEREST_SPECIAL_RATE_FD";
    case CollectionDataType.HIGHEST_INTEREST_ZERO_LOCK_IN_PERIOD_FD:
      return "HIGHEST_INTEREST_ZERO_LOCK_IN_PERIOD_FD";
    case CollectionDataType.HIGHEST_INTEREST_RATE_FOR_WOMEN_FD:
      return "HIGHEST_INTEREST_RATE_FOR_WOMEN_FD";
    case CollectionDataType.HIGHEST_INTEREST_RATE_FOR_SENIOR_CITIZEN_FD:
      return "HIGHEST_INTEREST_RATE_FOR_SENIOR_CITIZEN_FD";
    case CollectionDataType.HIGHEST_INTEREST_RATE_FOR_GENERAL_CITIZEN:
      return "HIGHEST_INTEREST_RATE_FOR_GENERAL_CITIZEN";
    case CollectionDataType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum SortTypes {
  UNKNOWN_SORT_TYPE = 0,
  INTEREST_RATE_SORT = 1,
  DEFAULT_SORT = 2,
  UNRECOGNIZED = -1,
}

export function sortTypesFromJSON(object: any): SortTypes {
  switch (object) {
    case 0:
    case "UNKNOWN_SORT_TYPE":
      return SortTypes.UNKNOWN_SORT_TYPE;
    case 1:
    case "INTEREST_RATE_SORT":
      return SortTypes.INTEREST_RATE_SORT;
    case 2:
    case "DEFAULT_SORT":
      return SortTypes.DEFAULT_SORT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SortTypes.UNRECOGNIZED;
  }
}

export function sortTypesToJSON(object: SortTypes): string {
  switch (object) {
    case SortTypes.UNKNOWN_SORT_TYPE:
      return "UNKNOWN_SORT_TYPE";
    case SortTypes.INTEREST_RATE_SORT:
      return "INTEREST_RATE_SORT";
    case SortTypes.DEFAULT_SORT:
      return "DEFAULT_SORT";
    case SortTypes.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum MaturityInstruction {
  UNKNOWN_MATURITY_INSTRUCTION = 0,
  AUTO_RENEWAL_WITH_MATURITY_AMOUNT = 1,
  AUTO_RENEWAL_WITH_PRINCIPAL_AMOUNT = 2,
  CLOSE_ACCOUNT = 3,
  UNRECOGNIZED = -1,
}

export function maturityInstructionFromJSON(object: any): MaturityInstruction {
  switch (object) {
    case 0:
    case "UNKNOWN_MATURITY_INSTRUCTION":
      return MaturityInstruction.UNKNOWN_MATURITY_INSTRUCTION;
    case 1:
    case "AUTO_RENEWAL_WITH_MATURITY_AMOUNT":
      return MaturityInstruction.AUTO_RENEWAL_WITH_MATURITY_AMOUNT;
    case 2:
    case "AUTO_RENEWAL_WITH_PRINCIPAL_AMOUNT":
      return MaturityInstruction.AUTO_RENEWAL_WITH_PRINCIPAL_AMOUNT;
    case 3:
    case "CLOSE_ACCOUNT":
      return MaturityInstruction.CLOSE_ACCOUNT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MaturityInstruction.UNRECOGNIZED;
  }
}

export function maturityInstructionToJSON(object: MaturityInstruction): string {
  switch (object) {
    case MaturityInstruction.UNKNOWN_MATURITY_INSTRUCTION:
      return "UNKNOWN_MATURITY_INSTRUCTION";
    case MaturityInstruction.AUTO_RENEWAL_WITH_MATURITY_AMOUNT:
      return "AUTO_RENEWAL_WITH_MATURITY_AMOUNT";
    case MaturityInstruction.AUTO_RENEWAL_WITH_PRINCIPAL_AMOUNT:
      return "AUTO_RENEWAL_WITH_PRINCIPAL_AMOUNT";
    case MaturityInstruction.CLOSE_ACCOUNT:
      return "CLOSE_ACCOUNT";
    case MaturityInstruction.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CollectionDataStrategyType {
  UNKNOWN_COLLECTION_DATA_STRATEGY_TYPE = 0,
  SIMPLE_COLLECTION_DATA_STRATEGY = 1,
  AGGREGATED_COLLECTION_DATA_STRATEGY = 2,
  UNRECOGNIZED = -1,
}

export function collectionDataStrategyTypeFromJSON(object: any): CollectionDataStrategyType {
  switch (object) {
    case 0:
    case "UNKNOWN_COLLECTION_DATA_STRATEGY_TYPE":
      return CollectionDataStrategyType.UNKNOWN_COLLECTION_DATA_STRATEGY_TYPE;
    case 1:
    case "SIMPLE_COLLECTION_DATA_STRATEGY":
      return CollectionDataStrategyType.SIMPLE_COLLECTION_DATA_STRATEGY;
    case 2:
    case "AGGREGATED_COLLECTION_DATA_STRATEGY":
      return CollectionDataStrategyType.AGGREGATED_COLLECTION_DATA_STRATEGY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CollectionDataStrategyType.UNRECOGNIZED;
  }
}

export function collectionDataStrategyTypeToJSON(object: CollectionDataStrategyType): string {
  switch (object) {
    case CollectionDataStrategyType.UNKNOWN_COLLECTION_DATA_STRATEGY_TYPE:
      return "UNKNOWN_COLLECTION_DATA_STRATEGY_TYPE";
    case CollectionDataStrategyType.SIMPLE_COLLECTION_DATA_STRATEGY:
      return "SIMPLE_COLLECTION_DATA_STRATEGY";
    case CollectionDataStrategyType.AGGREGATED_COLLECTION_DATA_STRATEGY:
      return "AGGREGATED_COLLECTION_DATA_STRATEGY";
    case CollectionDataStrategyType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CollectionWidgetType {
  UNKNOWN_WIDGET_TYPE = 0,
  FD_CARD = 1,
  BANK_CARD = 2,
  UNRECOGNIZED = -1,
}

export function collectionWidgetTypeFromJSON(object: any): CollectionWidgetType {
  switch (object) {
    case 0:
    case "UNKNOWN_WIDGET_TYPE":
      return CollectionWidgetType.UNKNOWN_WIDGET_TYPE;
    case 1:
    case "FD_CARD":
      return CollectionWidgetType.FD_CARD;
    case 2:
    case "BANK_CARD":
      return CollectionWidgetType.BANK_CARD;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CollectionWidgetType.UNRECOGNIZED;
  }
}

export function collectionWidgetTypeToJSON(object: CollectionWidgetType): string {
  switch (object) {
    case CollectionWidgetType.UNKNOWN_WIDGET_TYPE:
      return "UNKNOWN_WIDGET_TYPE";
    case CollectionWidgetType.FD_CARD:
      return "FD_CARD";
    case CollectionWidgetType.BANK_CARD:
      return "BANK_CARD";
    case CollectionWidgetType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum OngoingBookingStatus {
  UNKNOWN_BOOKING_STATUS = 0,
  VKYC_PENDING_STATUS = 1,
  BOOKING_IN_PROGRESS = 2,
  VKYC_SUCCESS_STATUS = 3,
  RESUME_JOURNEY = 4,
  UNRECOGNIZED = -1,
}

export function ongoingBookingStatusFromJSON(object: any): OngoingBookingStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_BOOKING_STATUS":
      return OngoingBookingStatus.UNKNOWN_BOOKING_STATUS;
    case 1:
    case "VKYC_PENDING_STATUS":
      return OngoingBookingStatus.VKYC_PENDING_STATUS;
    case 2:
    case "BOOKING_IN_PROGRESS":
      return OngoingBookingStatus.BOOKING_IN_PROGRESS;
    case 3:
    case "VKYC_SUCCESS_STATUS":
      return OngoingBookingStatus.VKYC_SUCCESS_STATUS;
    case 4:
    case "RESUME_JOURNEY":
      return OngoingBookingStatus.RESUME_JOURNEY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return OngoingBookingStatus.UNRECOGNIZED;
  }
}

export function ongoingBookingStatusToJSON(object: OngoingBookingStatus): string {
  switch (object) {
    case OngoingBookingStatus.UNKNOWN_BOOKING_STATUS:
      return "UNKNOWN_BOOKING_STATUS";
    case OngoingBookingStatus.VKYC_PENDING_STATUS:
      return "VKYC_PENDING_STATUS";
    case OngoingBookingStatus.BOOKING_IN_PROGRESS:
      return "BOOKING_IN_PROGRESS";
    case OngoingBookingStatus.VKYC_SUCCESS_STATUS:
      return "VKYC_SUCCESS_STATUS";
    case OngoingBookingStatus.RESUME_JOURNEY:
      return "RESUME_JOURNEY";
    case OngoingBookingStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum UserCancellationAction {
  UNKNOWN_CANCELLATION_ACTION = 0,
  NO_ACTION = 1,
  CANCEL_CANCELLATION = 2,
  DO_NOT_CANCEL_CANCEL_CANCELLATION = 3,
  UNRECOGNIZED = -1,
}

export function userCancellationActionFromJSON(object: any): UserCancellationAction {
  switch (object) {
    case 0:
    case "UNKNOWN_CANCELLATION_ACTION":
      return UserCancellationAction.UNKNOWN_CANCELLATION_ACTION;
    case 1:
    case "NO_ACTION":
      return UserCancellationAction.NO_ACTION;
    case 2:
    case "CANCEL_CANCELLATION":
      return UserCancellationAction.CANCEL_CANCELLATION;
    case 3:
    case "DO_NOT_CANCEL_CANCEL_CANCELLATION":
      return UserCancellationAction.DO_NOT_CANCEL_CANCEL_CANCELLATION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return UserCancellationAction.UNRECOGNIZED;
  }
}

export function userCancellationActionToJSON(object: UserCancellationAction): string {
  switch (object) {
    case UserCancellationAction.UNKNOWN_CANCELLATION_ACTION:
      return "UNKNOWN_CANCELLATION_ACTION";
    case UserCancellationAction.NO_ACTION:
      return "NO_ACTION";
    case UserCancellationAction.CANCEL_CANCELLATION:
      return "CANCEL_CANCELLATION";
    case UserCancellationAction.DO_NOT_CANCEL_CANCEL_CANCELLATION:
      return "DO_NOT_CANCEL_CANCEL_CANCELLATION";
    case UserCancellationAction.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum WithdrawalStatus {
  UNKNOWN_WITHDRAWAL_STATUS = 0,
  WITHDRAWAL_INITIATED = 1,
  WITHDRAWAL_IN_PROCESSING = 2,
  WITHDRAWAL_PROCESSED = 3,
  UNRECOGNIZED = -1,
}

export function withdrawalStatusFromJSON(object: any): WithdrawalStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_WITHDRAWAL_STATUS":
      return WithdrawalStatus.UNKNOWN_WITHDRAWAL_STATUS;
    case 1:
    case "WITHDRAWAL_INITIATED":
      return WithdrawalStatus.WITHDRAWAL_INITIATED;
    case 2:
    case "WITHDRAWAL_IN_PROCESSING":
      return WithdrawalStatus.WITHDRAWAL_IN_PROCESSING;
    case 3:
    case "WITHDRAWAL_PROCESSED":
      return WithdrawalStatus.WITHDRAWAL_PROCESSED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WithdrawalStatus.UNRECOGNIZED;
  }
}

export function withdrawalStatusToJSON(object: WithdrawalStatus): string {
  switch (object) {
    case WithdrawalStatus.UNKNOWN_WITHDRAWAL_STATUS:
      return "UNKNOWN_WITHDRAWAL_STATUS";
    case WithdrawalStatus.WITHDRAWAL_INITIATED:
      return "WITHDRAWAL_INITIATED";
    case WithdrawalStatus.WITHDRAWAL_IN_PROCESSING:
      return "WITHDRAWAL_IN_PROCESSING";
    case WithdrawalStatus.WITHDRAWAL_PROCESSED:
      return "WITHDRAWAL_PROCESSED";
    case WithdrawalStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface CollectionResponse {
  id: string;
  title: string;
  description: string;
  shortTitle: string;
  shortDescription: string;
  iconUrl: string;
  collectionItem: CollectionItem[];
  totalCount: number;
  sortOptions: SortOption[];
  filterByInstitutionTypeOptions: FilterByInstitutionTypeOption[];
  collectionWidgetType: CollectionWidgetType;
}

export interface SortOption {
  displayText: string;
  isSelected: boolean;
  sortKey: string;
}

export interface FilterByInstitutionTypeOption {
  displayText: string;
  isSelected: boolean;
  filterKey: string;
  count: number;
}

export interface CollectionItem {
  description: string;
  fixedDeposit?: FixedDepositResponse | undefined;
  bankResponseWithFdData?:
    | BankResponseWithFdData
    | undefined;
  /** @deprecated */
  tag: Tag | undefined;
  deeplink: RedirectDeeplink | undefined;
}

export interface Tag {
  name: string;
  color: string;
  backgroundColor: string;
  width: number;
}

export interface FixedDepositResponse {
  fdId: string;
  rawTenure: string;
  tenureFormatType: TenureFormatType;
  bank: BankResponse | undefined;
  rate: number;
  annualizedRate: number;
  minDeposit: number;
  maxDeposit: number;
  investorType: InvestorType;
  lockInPeriodInDays: number;
  isPreMatureWithdrawalAllowed: boolean;
  interestPayoutType: InterestPayoutType;
  breakageChargesAndDescription: string;
  isLoanAgainstFdAllowed: boolean;
  isPartialWithdrawalAllowed: boolean;
  minTenureInDays: number;
  maxTenureInDays: number;
  inDenominationOf: number;
  isTaxSaving: boolean;
  tenure: string;
  tenureInDays: number;
  tenureInMonths: number;
  tenureInYears: number;
  maturityInstructions: MaturityInstruction[];
  interestPayoutTypes: InterestPayoutType[];
  tag: string;
}

export interface BankResponse {
  id: string;
  name: string;
  logoUrl: string;
  websiteUrl: string;
  isInsured: boolean;
  bankType: BankType;
  investabilityStatus: InvestabilityStatus;
  isPopular: boolean;
  operatingBankId: string;
  customerCareNumber: string;
  establishmentYear: number;
  rbiLicenseUrl: string;
  displayName: string;
  color: string;
  tagConfig: TagConfig | undefined;
  coverImageUrl: string;
  iconBgColor: string;
  investabilityRolloutStatus: InvestabilityRolloutStatus;
  properties: { [key: string]: string };
  shortName: string;
}

export interface BankResponse_PropertiesEntry {
  key: string;
  value: string;
}

export interface TagConfig {
  name: string;
  iconUrl: string;
  color: string;
  bgColor: string;
  type: string;
  shimmerColor: string;
}

export interface BankResponseWithFdData {
  bank: BankResponse | undefined;
  fdsForBankCard: FixedDepositResponse[];
  highestInterestRateFd: FixedDepositResponse | undefined;
}

export interface BulkCollectionsResponse {
  collection: CollectionResponse[];
}

export interface AllCollectionsInfo {
  collection: CollectionResponse[];
}

export interface UserOngoingBookingStatusResponse {
  ongoingBookingStatus: OngoingBookingStatusData[];
}

export interface OngoingBookingStatusData {
  title: string;
  subTitle: string;
  isAgentsActive: boolean;
  agentTitle: string;
  agentSubTitle: string;
  bank: BankResponse | undefined;
  cta: string;
  redirectDeeplink: RedirectDeeplink | undefined;
  state: OngoingBookingStatus;
  paymentSuccessTime: string;
  latestStatus: string;
  message: string;
  amount: number;
  interestRate: number;
  tenureDays: string;
  distinctId: string;
  isWithdrawalCard: boolean;
  withdrawalDate: string;
}

export interface OngoingBookingStatusDataV2 {
  title: string;
  subTitle: string;
  isAgentsActive: boolean;
  agentTitle: string;
  agentSubTitle: string;
  bank: BankResponse | undefined;
  state: OngoingBookingStatus;
  paymentSuccessTime: string;
  primaryCta?: string | undefined;
  primaryRedirectDeeplink?: RedirectDeeplink | undefined;
  secondaryCta?: string | undefined;
  secondaryRedirectDeeplink?: RedirectDeeplink | undefined;
  message?: string | undefined;
  bankId: string;
  amount: number;
  interestRate: number;
  tenureDays: string;
  tag: string;
  tagColor: string;
  distinctId: string;
  isWithdrawalCard: boolean;
  withdrawalDate: string;
}

export interface UserOngoingBookingStatusResponseV2 {
  ongoingBookingStatus: OngoingBookingStatusDataV2[];
}

export interface PaymentFailureItem {
  id: string;
  amount: number;
  bankName?: string | undefined;
  bankId?: string | undefined;
}

export interface PaymentFailureCardResponse {
  paymentFailureItemList: PaymentFailureItem[];
  hasPaymentSuccess: boolean;
}

export interface WithdrawalData {
  id: string;
  userCancellationAction: UserCancellationAction;
  withdrawalStatus: WithdrawalStatus;
  withdrawalTime: string;
  withdrawalCancellationDeadline: string;
  withdrawalTimeLine: WithdrawalTimeLineNode[];
  bottomInfoText: string;
  hasMoneyCredited: boolean;
  hasAnsweredCancellationQuestions: boolean;
  withdrawalReason: string;
}

export interface WithdrawalTimeLineNode {
  title: string;
  subTitle: string;
  isCompleted: boolean;
}

export interface SubmitWithdrawalReasonRequest {
  withdrawalId: string;
  reason: string;
}

export interface SubmitWithdrawalReasonResponse {
}

/** @deprecated */
export interface WithdrawalCancellationRequest {
  withdrawalId: string;
  qa: WithdrawalCancellationQuestionAndAnswer[];
}

export interface UserWithdrawalCancellationActionRequest {
  withdrawalId: string;
  userCancellationAction: UserCancellationAction;
}

export interface UserWithdrawalCancellationActionResponse {
}

export interface WithdrawalCancellationQuestionAndAnswer {
  question: string;
  answer: string;
}

/** @deprecated */
export interface WithdrawalCancellationResponse {
}

export interface BankDetails {
  bankId: string;
  logo: string;
  bankName: string;
}

export interface LocationBasedRecommendedFd {
  rate: number;
  tenure: string;
  bankDetails: BankDetails | undefined;
}

function createBaseCollectionResponse(): CollectionResponse {
  return {
    id: "",
    title: "",
    description: "",
    shortTitle: "",
    shortDescription: "",
    iconUrl: "",
    collectionItem: [],
    totalCount: 0,
    sortOptions: [],
    filterByInstitutionTypeOptions: [],
    collectionWidgetType: 0,
  };
}

export const CollectionResponse: MessageFns<CollectionResponse> = {
  encode(message: CollectionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.title !== "") {
      writer.uint32(18).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.shortTitle !== "") {
      writer.uint32(34).string(message.shortTitle);
    }
    if (message.shortDescription !== "") {
      writer.uint32(42).string(message.shortDescription);
    }
    if (message.iconUrl !== "") {
      writer.uint32(50).string(message.iconUrl);
    }
    for (const v of message.collectionItem) {
      CollectionItem.encode(v!, writer.uint32(58).fork()).join();
    }
    if (message.totalCount !== 0) {
      writer.uint32(64).int32(message.totalCount);
    }
    for (const v of message.sortOptions) {
      SortOption.encode(v!, writer.uint32(74).fork()).join();
    }
    for (const v of message.filterByInstitutionTypeOptions) {
      FilterByInstitutionTypeOption.encode(v!, writer.uint32(82).fork()).join();
    }
    if (message.collectionWidgetType !== 0) {
      writer.uint32(88).int32(message.collectionWidgetType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.shortTitle = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.shortDescription = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.collectionItem.push(CollectionItem.decode(reader, reader.uint32()));
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.totalCount = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.sortOptions.push(SortOption.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.filterByInstitutionTypeOptions.push(FilterByInstitutionTypeOption.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.collectionWidgetType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      shortTitle: isSet(object.shortTitle) ? globalThis.String(object.shortTitle) : "",
      shortDescription: isSet(object.shortDescription) ? globalThis.String(object.shortDescription) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      collectionItem: globalThis.Array.isArray(object?.collectionItem)
        ? object.collectionItem.map((e: any) => CollectionItem.fromJSON(e))
        : [],
      totalCount: isSet(object.totalCount) ? globalThis.Number(object.totalCount) : 0,
      sortOptions: globalThis.Array.isArray(object?.sortOptions)
        ? object.sortOptions.map((e: any) => SortOption.fromJSON(e))
        : [],
      filterByInstitutionTypeOptions: globalThis.Array.isArray(object?.filterByInstitutionTypeOptions)
        ? object.filterByInstitutionTypeOptions.map((e: any) => FilterByInstitutionTypeOption.fromJSON(e))
        : [],
      collectionWidgetType: isSet(object.collectionWidgetType)
        ? collectionWidgetTypeFromJSON(object.collectionWidgetType)
        : 0,
    };
  },

  toJSON(message: CollectionResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.shortTitle !== "") {
      obj.shortTitle = message.shortTitle;
    }
    if (message.shortDescription !== "") {
      obj.shortDescription = message.shortDescription;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.collectionItem?.length) {
      obj.collectionItem = message.collectionItem.map((e) => CollectionItem.toJSON(e));
    }
    if (message.totalCount !== 0) {
      obj.totalCount = Math.round(message.totalCount);
    }
    if (message.sortOptions?.length) {
      obj.sortOptions = message.sortOptions.map((e) => SortOption.toJSON(e));
    }
    if (message.filterByInstitutionTypeOptions?.length) {
      obj.filterByInstitutionTypeOptions = message.filterByInstitutionTypeOptions.map((e) =>
        FilterByInstitutionTypeOption.toJSON(e)
      );
    }
    if (message.collectionWidgetType !== 0) {
      obj.collectionWidgetType = collectionWidgetTypeToJSON(message.collectionWidgetType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionResponse>, I>>(base?: I): CollectionResponse {
    return CollectionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionResponse>, I>>(object: I): CollectionResponse {
    const message = createBaseCollectionResponse();
    message.id = object.id ?? "";
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.shortTitle = object.shortTitle ?? "";
    message.shortDescription = object.shortDescription ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.collectionItem = object.collectionItem?.map((e) => CollectionItem.fromPartial(e)) || [];
    message.totalCount = object.totalCount ?? 0;
    message.sortOptions = object.sortOptions?.map((e) => SortOption.fromPartial(e)) || [];
    message.filterByInstitutionTypeOptions =
      object.filterByInstitutionTypeOptions?.map((e) => FilterByInstitutionTypeOption.fromPartial(e)) || [];
    message.collectionWidgetType = object.collectionWidgetType ?? 0;
    return message;
  },
};

function createBaseSortOption(): SortOption {
  return { displayText: "", isSelected: false, sortKey: "" };
}

export const SortOption: MessageFns<SortOption> = {
  encode(message: SortOption, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.displayText !== "") {
      writer.uint32(10).string(message.displayText);
    }
    if (message.isSelected !== false) {
      writer.uint32(16).bool(message.isSelected);
    }
    if (message.sortKey !== "") {
      writer.uint32(26).string(message.sortKey);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SortOption {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSortOption();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.displayText = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isSelected = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.sortKey = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SortOption {
    return {
      displayText: isSet(object.displayText) ? globalThis.String(object.displayText) : "",
      isSelected: isSet(object.isSelected) ? globalThis.Boolean(object.isSelected) : false,
      sortKey: isSet(object.sortKey) ? globalThis.String(object.sortKey) : "",
    };
  },

  toJSON(message: SortOption): unknown {
    const obj: any = {};
    if (message.displayText !== "") {
      obj.displayText = message.displayText;
    }
    if (message.isSelected !== false) {
      obj.isSelected = message.isSelected;
    }
    if (message.sortKey !== "") {
      obj.sortKey = message.sortKey;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SortOption>, I>>(base?: I): SortOption {
    return SortOption.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SortOption>, I>>(object: I): SortOption {
    const message = createBaseSortOption();
    message.displayText = object.displayText ?? "";
    message.isSelected = object.isSelected ?? false;
    message.sortKey = object.sortKey ?? "";
    return message;
  },
};

function createBaseFilterByInstitutionTypeOption(): FilterByInstitutionTypeOption {
  return { displayText: "", isSelected: false, filterKey: "", count: 0 };
}

export const FilterByInstitutionTypeOption: MessageFns<FilterByInstitutionTypeOption> = {
  encode(message: FilterByInstitutionTypeOption, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.displayText !== "") {
      writer.uint32(10).string(message.displayText);
    }
    if (message.isSelected !== false) {
      writer.uint32(16).bool(message.isSelected);
    }
    if (message.filterKey !== "") {
      writer.uint32(26).string(message.filterKey);
    }
    if (message.count !== 0) {
      writer.uint32(32).int64(message.count);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FilterByInstitutionTypeOption {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFilterByInstitutionTypeOption();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.displayText = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isSelected = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.filterKey = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.count = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FilterByInstitutionTypeOption {
    return {
      displayText: isSet(object.displayText) ? globalThis.String(object.displayText) : "",
      isSelected: isSet(object.isSelected) ? globalThis.Boolean(object.isSelected) : false,
      filterKey: isSet(object.filterKey) ? globalThis.String(object.filterKey) : "",
      count: isSet(object.count) ? globalThis.Number(object.count) : 0,
    };
  },

  toJSON(message: FilterByInstitutionTypeOption): unknown {
    const obj: any = {};
    if (message.displayText !== "") {
      obj.displayText = message.displayText;
    }
    if (message.isSelected !== false) {
      obj.isSelected = message.isSelected;
    }
    if (message.filterKey !== "") {
      obj.filterKey = message.filterKey;
    }
    if (message.count !== 0) {
      obj.count = Math.round(message.count);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FilterByInstitutionTypeOption>, I>>(base?: I): FilterByInstitutionTypeOption {
    return FilterByInstitutionTypeOption.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FilterByInstitutionTypeOption>, I>>(
    object: I,
  ): FilterByInstitutionTypeOption {
    const message = createBaseFilterByInstitutionTypeOption();
    message.displayText = object.displayText ?? "";
    message.isSelected = object.isSelected ?? false;
    message.filterKey = object.filterKey ?? "";
    message.count = object.count ?? 0;
    return message;
  },
};

function createBaseCollectionItem(): CollectionItem {
  return {
    description: "",
    fixedDeposit: undefined,
    bankResponseWithFdData: undefined,
    tag: undefined,
    deeplink: undefined,
  };
}

export const CollectionItem: MessageFns<CollectionItem> = {
  encode(message: CollectionItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.description !== "") {
      writer.uint32(10).string(message.description);
    }
    if (message.fixedDeposit !== undefined) {
      FixedDepositResponse.encode(message.fixedDeposit, writer.uint32(18).fork()).join();
    }
    if (message.bankResponseWithFdData !== undefined) {
      BankResponseWithFdData.encode(message.bankResponseWithFdData, writer.uint32(26).fork()).join();
    }
    if (message.tag !== undefined) {
      Tag.encode(message.tag, writer.uint32(34).fork()).join();
    }
    if (message.deeplink !== undefined) {
      RedirectDeeplink.encode(message.deeplink, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fixedDeposit = FixedDepositResponse.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankResponseWithFdData = BankResponseWithFdData.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.tag = Tag.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.deeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionItem {
    return {
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      fixedDeposit: isSet(object.fixedDeposit) ? FixedDepositResponse.fromJSON(object.fixedDeposit) : undefined,
      bankResponseWithFdData: isSet(object.bankResponseWithFdData)
        ? BankResponseWithFdData.fromJSON(object.bankResponseWithFdData)
        : undefined,
      tag: isSet(object.tag) ? Tag.fromJSON(object.tag) : undefined,
      deeplink: isSet(object.deeplink) ? RedirectDeeplink.fromJSON(object.deeplink) : undefined,
    };
  },

  toJSON(message: CollectionItem): unknown {
    const obj: any = {};
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.fixedDeposit !== undefined) {
      obj.fixedDeposit = FixedDepositResponse.toJSON(message.fixedDeposit);
    }
    if (message.bankResponseWithFdData !== undefined) {
      obj.bankResponseWithFdData = BankResponseWithFdData.toJSON(message.bankResponseWithFdData);
    }
    if (message.tag !== undefined) {
      obj.tag = Tag.toJSON(message.tag);
    }
    if (message.deeplink !== undefined) {
      obj.deeplink = RedirectDeeplink.toJSON(message.deeplink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionItem>, I>>(base?: I): CollectionItem {
    return CollectionItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionItem>, I>>(object: I): CollectionItem {
    const message = createBaseCollectionItem();
    message.description = object.description ?? "";
    message.fixedDeposit = (object.fixedDeposit !== undefined && object.fixedDeposit !== null)
      ? FixedDepositResponse.fromPartial(object.fixedDeposit)
      : undefined;
    message.bankResponseWithFdData =
      (object.bankResponseWithFdData !== undefined && object.bankResponseWithFdData !== null)
        ? BankResponseWithFdData.fromPartial(object.bankResponseWithFdData)
        : undefined;
    message.tag = (object.tag !== undefined && object.tag !== null) ? Tag.fromPartial(object.tag) : undefined;
    message.deeplink = (object.deeplink !== undefined && object.deeplink !== null)
      ? RedirectDeeplink.fromPartial(object.deeplink)
      : undefined;
    return message;
  },
};

function createBaseTag(): Tag {
  return { name: "", color: "", backgroundColor: "", width: 0 };
}

export const Tag: MessageFns<Tag> = {
  encode(message: Tag, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.color !== "") {
      writer.uint32(18).string(message.color);
    }
    if (message.backgroundColor !== "") {
      writer.uint32(26).string(message.backgroundColor);
    }
    if (message.width !== 0) {
      writer.uint32(33).double(message.width);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tag {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTag();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.backgroundColor = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.width = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Tag {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      backgroundColor: isSet(object.backgroundColor) ? globalThis.String(object.backgroundColor) : "",
      width: isSet(object.width) ? globalThis.Number(object.width) : 0,
    };
  },

  toJSON(message: Tag): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.backgroundColor !== "") {
      obj.backgroundColor = message.backgroundColor;
    }
    if (message.width !== 0) {
      obj.width = message.width;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Tag>, I>>(base?: I): Tag {
    return Tag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tag>, I>>(object: I): Tag {
    const message = createBaseTag();
    message.name = object.name ?? "";
    message.color = object.color ?? "";
    message.backgroundColor = object.backgroundColor ?? "";
    message.width = object.width ?? 0;
    return message;
  },
};

function createBaseFixedDepositResponse(): FixedDepositResponse {
  return {
    fdId: "",
    rawTenure: "",
    tenureFormatType: 0,
    bank: undefined,
    rate: 0,
    annualizedRate: 0,
    minDeposit: 0,
    maxDeposit: 0,
    investorType: 0,
    lockInPeriodInDays: 0,
    isPreMatureWithdrawalAllowed: false,
    interestPayoutType: 0,
    breakageChargesAndDescription: "",
    isLoanAgainstFdAllowed: false,
    isPartialWithdrawalAllowed: false,
    minTenureInDays: 0,
    maxTenureInDays: 0,
    inDenominationOf: 0,
    isTaxSaving: false,
    tenure: "",
    tenureInDays: 0,
    tenureInMonths: 0,
    tenureInYears: 0,
    maturityInstructions: [],
    interestPayoutTypes: [],
    tag: "",
  };
}

export const FixedDepositResponse: MessageFns<FixedDepositResponse> = {
  encode(message: FixedDepositResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdId !== "") {
      writer.uint32(10).string(message.fdId);
    }
    if (message.rawTenure !== "") {
      writer.uint32(18).string(message.rawTenure);
    }
    if (message.tenureFormatType !== 0) {
      writer.uint32(24).int32(message.tenureFormatType);
    }
    if (message.bank !== undefined) {
      BankResponse.encode(message.bank, writer.uint32(34).fork()).join();
    }
    if (message.rate !== 0) {
      writer.uint32(41).double(message.rate);
    }
    if (message.annualizedRate !== 0) {
      writer.uint32(49).double(message.annualizedRate);
    }
    if (message.minDeposit !== 0) {
      writer.uint32(57).double(message.minDeposit);
    }
    if (message.maxDeposit !== 0) {
      writer.uint32(65).double(message.maxDeposit);
    }
    if (message.investorType !== 0) {
      writer.uint32(80).int32(message.investorType);
    }
    if (message.lockInPeriodInDays !== 0) {
      writer.uint32(88).int32(message.lockInPeriodInDays);
    }
    if (message.isPreMatureWithdrawalAllowed !== false) {
      writer.uint32(96).bool(message.isPreMatureWithdrawalAllowed);
    }
    if (message.interestPayoutType !== 0) {
      writer.uint32(112).int32(message.interestPayoutType);
    }
    if (message.breakageChargesAndDescription !== "") {
      writer.uint32(122).string(message.breakageChargesAndDescription);
    }
    if (message.isLoanAgainstFdAllowed !== false) {
      writer.uint32(128).bool(message.isLoanAgainstFdAllowed);
    }
    if (message.isPartialWithdrawalAllowed !== false) {
      writer.uint32(136).bool(message.isPartialWithdrawalAllowed);
    }
    if (message.minTenureInDays !== 0) {
      writer.uint32(144).int32(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      writer.uint32(152).int32(message.maxTenureInDays);
    }
    if (message.inDenominationOf !== 0) {
      writer.uint32(160).int32(message.inDenominationOf);
    }
    if (message.isTaxSaving !== false) {
      writer.uint32(168).bool(message.isTaxSaving);
    }
    if (message.tenure !== "") {
      writer.uint32(178).string(message.tenure);
    }
    if (message.tenureInDays !== 0) {
      writer.uint32(184).int32(message.tenureInDays);
    }
    if (message.tenureInMonths !== 0) {
      writer.uint32(192).int32(message.tenureInMonths);
    }
    if (message.tenureInYears !== 0) {
      writer.uint32(200).int32(message.tenureInYears);
    }
    writer.uint32(210).fork();
    for (const v of message.maturityInstructions) {
      writer.int32(v);
    }
    writer.join();
    writer.uint32(218).fork();
    for (const v of message.interestPayoutTypes) {
      writer.int32(v);
    }
    writer.join();
    if (message.tag !== "") {
      writer.uint32(226).string(message.tag);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FixedDepositResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFixedDepositResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fdId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rawTenure = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.tenureFormatType = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bank = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.annualizedRate = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.minDeposit = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.maxDeposit = reader.double();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.investorType = reader.int32() as any;
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.lockInPeriodInDays = reader.int32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.isPreMatureWithdrawalAllowed = reader.bool();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.interestPayoutType = reader.int32() as any;
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.breakageChargesAndDescription = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.isLoanAgainstFdAllowed = reader.bool();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.isPartialWithdrawalAllowed = reader.bool();
          continue;
        }
        case 18: {
          if (tag !== 144) {
            break;
          }

          message.minTenureInDays = reader.int32();
          continue;
        }
        case 19: {
          if (tag !== 152) {
            break;
          }

          message.maxTenureInDays = reader.int32();
          continue;
        }
        case 20: {
          if (tag !== 160) {
            break;
          }

          message.inDenominationOf = reader.int32();
          continue;
        }
        case 21: {
          if (tag !== 168) {
            break;
          }

          message.isTaxSaving = reader.bool();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 23: {
          if (tag !== 184) {
            break;
          }

          message.tenureInDays = reader.int32();
          continue;
        }
        case 24: {
          if (tag !== 192) {
            break;
          }

          message.tenureInMonths = reader.int32();
          continue;
        }
        case 25: {
          if (tag !== 200) {
            break;
          }

          message.tenureInYears = reader.int32();
          continue;
        }
        case 26: {
          if (tag === 208) {
            message.maturityInstructions.push(reader.int32() as any);

            continue;
          }

          if (tag === 210) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.maturityInstructions.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
        case 27: {
          if (tag === 216) {
            message.interestPayoutTypes.push(reader.int32() as any);

            continue;
          }

          if (tag === 218) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.interestPayoutTypes.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
        case 28: {
          if (tag !== 226) {
            break;
          }

          message.tag = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FixedDepositResponse {
    return {
      fdId: isSet(object.fdId) ? globalThis.String(object.fdId) : "",
      rawTenure: isSet(object.rawTenure) ? globalThis.String(object.rawTenure) : "",
      tenureFormatType: isSet(object.tenureFormatType) ? tenureFormatTypeFromJSON(object.tenureFormatType) : 0,
      bank: isSet(object.bank) ? BankResponse.fromJSON(object.bank) : undefined,
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      annualizedRate: isSet(object.annualizedRate) ? globalThis.Number(object.annualizedRate) : 0,
      minDeposit: isSet(object.minDeposit) ? globalThis.Number(object.minDeposit) : 0,
      maxDeposit: isSet(object.maxDeposit) ? globalThis.Number(object.maxDeposit) : 0,
      investorType: isSet(object.investorType) ? investorTypeFromJSON(object.investorType) : 0,
      lockInPeriodInDays: isSet(object.lockInPeriodInDays) ? globalThis.Number(object.lockInPeriodInDays) : 0,
      isPreMatureWithdrawalAllowed: isSet(object.isPreMatureWithdrawalAllowed)
        ? globalThis.Boolean(object.isPreMatureWithdrawalAllowed)
        : false,
      interestPayoutType: isSet(object.interestPayoutType) ? interestPayoutTypeFromJSON(object.interestPayoutType) : 0,
      breakageChargesAndDescription: isSet(object.breakageChargesAndDescription)
        ? globalThis.String(object.breakageChargesAndDescription)
        : "",
      isLoanAgainstFdAllowed: isSet(object.isLoanAgainstFdAllowed)
        ? globalThis.Boolean(object.isLoanAgainstFdAllowed)
        : false,
      isPartialWithdrawalAllowed: isSet(object.isPartialWithdrawalAllowed)
        ? globalThis.Boolean(object.isPartialWithdrawalAllowed)
        : false,
      minTenureInDays: isSet(object.minTenureInDays) ? globalThis.Number(object.minTenureInDays) : 0,
      maxTenureInDays: isSet(object.maxTenureInDays) ? globalThis.Number(object.maxTenureInDays) : 0,
      inDenominationOf: isSet(object.inDenominationOf) ? globalThis.Number(object.inDenominationOf) : 0,
      isTaxSaving: isSet(object.isTaxSaving) ? globalThis.Boolean(object.isTaxSaving) : false,
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      tenureInDays: isSet(object.tenureInDays) ? globalThis.Number(object.tenureInDays) : 0,
      tenureInMonths: isSet(object.tenureInMonths) ? globalThis.Number(object.tenureInMonths) : 0,
      tenureInYears: isSet(object.tenureInYears) ? globalThis.Number(object.tenureInYears) : 0,
      maturityInstructions: globalThis.Array.isArray(object?.maturityInstructions)
        ? object.maturityInstructions.map((e: any) => maturityInstructionFromJSON(e))
        : [],
      interestPayoutTypes: globalThis.Array.isArray(object?.interestPayoutTypes)
        ? object.interestPayoutTypes.map((e: any) => interestPayoutTypeFromJSON(e))
        : [],
      tag: isSet(object.tag) ? globalThis.String(object.tag) : "",
    };
  },

  toJSON(message: FixedDepositResponse): unknown {
    const obj: any = {};
    if (message.fdId !== "") {
      obj.fdId = message.fdId;
    }
    if (message.rawTenure !== "") {
      obj.rawTenure = message.rawTenure;
    }
    if (message.tenureFormatType !== 0) {
      obj.tenureFormatType = tenureFormatTypeToJSON(message.tenureFormatType);
    }
    if (message.bank !== undefined) {
      obj.bank = BankResponse.toJSON(message.bank);
    }
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.annualizedRate !== 0) {
      obj.annualizedRate = message.annualizedRate;
    }
    if (message.minDeposit !== 0) {
      obj.minDeposit = message.minDeposit;
    }
    if (message.maxDeposit !== 0) {
      obj.maxDeposit = message.maxDeposit;
    }
    if (message.investorType !== 0) {
      obj.investorType = investorTypeToJSON(message.investorType);
    }
    if (message.lockInPeriodInDays !== 0) {
      obj.lockInPeriodInDays = Math.round(message.lockInPeriodInDays);
    }
    if (message.isPreMatureWithdrawalAllowed !== false) {
      obj.isPreMatureWithdrawalAllowed = message.isPreMatureWithdrawalAllowed;
    }
    if (message.interestPayoutType !== 0) {
      obj.interestPayoutType = interestPayoutTypeToJSON(message.interestPayoutType);
    }
    if (message.breakageChargesAndDescription !== "") {
      obj.breakageChargesAndDescription = message.breakageChargesAndDescription;
    }
    if (message.isLoanAgainstFdAllowed !== false) {
      obj.isLoanAgainstFdAllowed = message.isLoanAgainstFdAllowed;
    }
    if (message.isPartialWithdrawalAllowed !== false) {
      obj.isPartialWithdrawalAllowed = message.isPartialWithdrawalAllowed;
    }
    if (message.minTenureInDays !== 0) {
      obj.minTenureInDays = Math.round(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      obj.maxTenureInDays = Math.round(message.maxTenureInDays);
    }
    if (message.inDenominationOf !== 0) {
      obj.inDenominationOf = Math.round(message.inDenominationOf);
    }
    if (message.isTaxSaving !== false) {
      obj.isTaxSaving = message.isTaxSaving;
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.tenureInDays !== 0) {
      obj.tenureInDays = Math.round(message.tenureInDays);
    }
    if (message.tenureInMonths !== 0) {
      obj.tenureInMonths = Math.round(message.tenureInMonths);
    }
    if (message.tenureInYears !== 0) {
      obj.tenureInYears = Math.round(message.tenureInYears);
    }
    if (message.maturityInstructions?.length) {
      obj.maturityInstructions = message.maturityInstructions.map((e) => maturityInstructionToJSON(e));
    }
    if (message.interestPayoutTypes?.length) {
      obj.interestPayoutTypes = message.interestPayoutTypes.map((e) => interestPayoutTypeToJSON(e));
    }
    if (message.tag !== "") {
      obj.tag = message.tag;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FixedDepositResponse>, I>>(base?: I): FixedDepositResponse {
    return FixedDepositResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FixedDepositResponse>, I>>(object: I): FixedDepositResponse {
    const message = createBaseFixedDepositResponse();
    message.fdId = object.fdId ?? "";
    message.rawTenure = object.rawTenure ?? "";
    message.tenureFormatType = object.tenureFormatType ?? 0;
    message.bank = (object.bank !== undefined && object.bank !== null)
      ? BankResponse.fromPartial(object.bank)
      : undefined;
    message.rate = object.rate ?? 0;
    message.annualizedRate = object.annualizedRate ?? 0;
    message.minDeposit = object.minDeposit ?? 0;
    message.maxDeposit = object.maxDeposit ?? 0;
    message.investorType = object.investorType ?? 0;
    message.lockInPeriodInDays = object.lockInPeriodInDays ?? 0;
    message.isPreMatureWithdrawalAllowed = object.isPreMatureWithdrawalAllowed ?? false;
    message.interestPayoutType = object.interestPayoutType ?? 0;
    message.breakageChargesAndDescription = object.breakageChargesAndDescription ?? "";
    message.isLoanAgainstFdAllowed = object.isLoanAgainstFdAllowed ?? false;
    message.isPartialWithdrawalAllowed = object.isPartialWithdrawalAllowed ?? false;
    message.minTenureInDays = object.minTenureInDays ?? 0;
    message.maxTenureInDays = object.maxTenureInDays ?? 0;
    message.inDenominationOf = object.inDenominationOf ?? 0;
    message.isTaxSaving = object.isTaxSaving ?? false;
    message.tenure = object.tenure ?? "";
    message.tenureInDays = object.tenureInDays ?? 0;
    message.tenureInMonths = object.tenureInMonths ?? 0;
    message.tenureInYears = object.tenureInYears ?? 0;
    message.maturityInstructions = object.maturityInstructions?.map((e) => e) || [];
    message.interestPayoutTypes = object.interestPayoutTypes?.map((e) => e) || [];
    message.tag = object.tag ?? "";
    return message;
  },
};

function createBaseBankResponse(): BankResponse {
  return {
    id: "",
    name: "",
    logoUrl: "",
    websiteUrl: "",
    isInsured: false,
    bankType: 0,
    investabilityStatus: 0,
    isPopular: false,
    operatingBankId: "",
    customerCareNumber: "",
    establishmentYear: 0,
    rbiLicenseUrl: "",
    displayName: "",
    color: "",
    tagConfig: undefined,
    coverImageUrl: "",
    iconBgColor: "",
    investabilityRolloutStatus: 0,
    properties: {},
    shortName: "",
  };
}

export const BankResponse: MessageFns<BankResponse> = {
  encode(message: BankResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.logoUrl !== "") {
      writer.uint32(26).string(message.logoUrl);
    }
    if (message.websiteUrl !== "") {
      writer.uint32(34).string(message.websiteUrl);
    }
    if (message.isInsured !== false) {
      writer.uint32(40).bool(message.isInsured);
    }
    if (message.bankType !== 0) {
      writer.uint32(48).int32(message.bankType);
    }
    if (message.investabilityStatus !== 0) {
      writer.uint32(56).int32(message.investabilityStatus);
    }
    if (message.isPopular !== false) {
      writer.uint32(64).bool(message.isPopular);
    }
    if (message.operatingBankId !== "") {
      writer.uint32(74).string(message.operatingBankId);
    }
    if (message.customerCareNumber !== "") {
      writer.uint32(82).string(message.customerCareNumber);
    }
    if (message.establishmentYear !== 0) {
      writer.uint32(88).int32(message.establishmentYear);
    }
    if (message.rbiLicenseUrl !== "") {
      writer.uint32(98).string(message.rbiLicenseUrl);
    }
    if (message.displayName !== "") {
      writer.uint32(106).string(message.displayName);
    }
    if (message.color !== "") {
      writer.uint32(114).string(message.color);
    }
    if (message.tagConfig !== undefined) {
      TagConfig.encode(message.tagConfig, writer.uint32(122).fork()).join();
    }
    if (message.coverImageUrl !== "") {
      writer.uint32(130).string(message.coverImageUrl);
    }
    if (message.iconBgColor !== "") {
      writer.uint32(138).string(message.iconBgColor);
    }
    if (message.investabilityRolloutStatus !== 0) {
      writer.uint32(144).int32(message.investabilityRolloutStatus);
    }
    Object.entries(message.properties).forEach(([key, value]) => {
      BankResponse_PropertiesEntry.encode({ key: key as any, value }, writer.uint32(162).fork()).join();
    });
    if (message.shortName !== "") {
      writer.uint32(178).string(message.shortName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.websiteUrl = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isInsured = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.bankType = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.investabilityStatus = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isPopular = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.operatingBankId = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.customerCareNumber = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.establishmentYear = reader.int32();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.rbiLicenseUrl = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.displayName = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.tagConfig = TagConfig.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.coverImageUrl = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.iconBgColor = reader.string();
          continue;
        }
        case 18: {
          if (tag !== 144) {
            break;
          }

          message.investabilityRolloutStatus = reader.int32() as any;
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          const entry20 = BankResponse_PropertiesEntry.decode(reader, reader.uint32());
          if (entry20.value !== undefined) {
            message.properties[entry20.key] = entry20.value;
          }
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.shortName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
      websiteUrl: isSet(object.websiteUrl) ? globalThis.String(object.websiteUrl) : "",
      isInsured: isSet(object.isInsured) ? globalThis.Boolean(object.isInsured) : false,
      bankType: isSet(object.bankType) ? bankTypeFromJSON(object.bankType) : 0,
      investabilityStatus: isSet(object.investabilityStatus)
        ? investabilityStatusFromJSON(object.investabilityStatus)
        : 0,
      isPopular: isSet(object.isPopular) ? globalThis.Boolean(object.isPopular) : false,
      operatingBankId: isSet(object.operatingBankId) ? globalThis.String(object.operatingBankId) : "",
      customerCareNumber: isSet(object.customerCareNumber) ? globalThis.String(object.customerCareNumber) : "",
      establishmentYear: isSet(object.establishmentYear) ? globalThis.Number(object.establishmentYear) : 0,
      rbiLicenseUrl: isSet(object.rbiLicenseUrl) ? globalThis.String(object.rbiLicenseUrl) : "",
      displayName: isSet(object.displayName) ? globalThis.String(object.displayName) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      tagConfig: isSet(object.tagConfig) ? TagConfig.fromJSON(object.tagConfig) : undefined,
      coverImageUrl: isSet(object.coverImageUrl) ? globalThis.String(object.coverImageUrl) : "",
      iconBgColor: isSet(object.iconBgColor) ? globalThis.String(object.iconBgColor) : "",
      investabilityRolloutStatus: isSet(object.investabilityRolloutStatus)
        ? investabilityRolloutStatusFromJSON(object.investabilityRolloutStatus)
        : 0,
      properties: isObject(object.properties)
        ? Object.entries(object.properties).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
      shortName: isSet(object.shortName) ? globalThis.String(object.shortName) : "",
    };
  },

  toJSON(message: BankResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    if (message.websiteUrl !== "") {
      obj.websiteUrl = message.websiteUrl;
    }
    if (message.isInsured !== false) {
      obj.isInsured = message.isInsured;
    }
    if (message.bankType !== 0) {
      obj.bankType = bankTypeToJSON(message.bankType);
    }
    if (message.investabilityStatus !== 0) {
      obj.investabilityStatus = investabilityStatusToJSON(message.investabilityStatus);
    }
    if (message.isPopular !== false) {
      obj.isPopular = message.isPopular;
    }
    if (message.operatingBankId !== "") {
      obj.operatingBankId = message.operatingBankId;
    }
    if (message.customerCareNumber !== "") {
      obj.customerCareNumber = message.customerCareNumber;
    }
    if (message.establishmentYear !== 0) {
      obj.establishmentYear = Math.round(message.establishmentYear);
    }
    if (message.rbiLicenseUrl !== "") {
      obj.rbiLicenseUrl = message.rbiLicenseUrl;
    }
    if (message.displayName !== "") {
      obj.displayName = message.displayName;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.tagConfig !== undefined) {
      obj.tagConfig = TagConfig.toJSON(message.tagConfig);
    }
    if (message.coverImageUrl !== "") {
      obj.coverImageUrl = message.coverImageUrl;
    }
    if (message.iconBgColor !== "") {
      obj.iconBgColor = message.iconBgColor;
    }
    if (message.investabilityRolloutStatus !== 0) {
      obj.investabilityRolloutStatus = investabilityRolloutStatusToJSON(message.investabilityRolloutStatus);
    }
    if (message.properties) {
      const entries = Object.entries(message.properties);
      if (entries.length > 0) {
        obj.properties = {};
        entries.forEach(([k, v]) => {
          obj.properties[k] = v;
        });
      }
    }
    if (message.shortName !== "") {
      obj.shortName = message.shortName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankResponse>, I>>(base?: I): BankResponse {
    return BankResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankResponse>, I>>(object: I): BankResponse {
    const message = createBaseBankResponse();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.logoUrl = object.logoUrl ?? "";
    message.websiteUrl = object.websiteUrl ?? "";
    message.isInsured = object.isInsured ?? false;
    message.bankType = object.bankType ?? 0;
    message.investabilityStatus = object.investabilityStatus ?? 0;
    message.isPopular = object.isPopular ?? false;
    message.operatingBankId = object.operatingBankId ?? "";
    message.customerCareNumber = object.customerCareNumber ?? "";
    message.establishmentYear = object.establishmentYear ?? 0;
    message.rbiLicenseUrl = object.rbiLicenseUrl ?? "";
    message.displayName = object.displayName ?? "";
    message.color = object.color ?? "";
    message.tagConfig = (object.tagConfig !== undefined && object.tagConfig !== null)
      ? TagConfig.fromPartial(object.tagConfig)
      : undefined;
    message.coverImageUrl = object.coverImageUrl ?? "";
    message.iconBgColor = object.iconBgColor ?? "";
    message.investabilityRolloutStatus = object.investabilityRolloutStatus ?? 0;
    message.properties = Object.entries(object.properties ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    message.shortName = object.shortName ?? "";
    return message;
  },
};

function createBaseBankResponse_PropertiesEntry(): BankResponse_PropertiesEntry {
  return { key: "", value: "" };
}

export const BankResponse_PropertiesEntry: MessageFns<BankResponse_PropertiesEntry> = {
  encode(message: BankResponse_PropertiesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankResponse_PropertiesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankResponse_PropertiesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankResponse_PropertiesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: BankResponse_PropertiesEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankResponse_PropertiesEntry>, I>>(base?: I): BankResponse_PropertiesEntry {
    return BankResponse_PropertiesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankResponse_PropertiesEntry>, I>>(object: I): BankResponse_PropertiesEntry {
    const message = createBaseBankResponse_PropertiesEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseTagConfig(): TagConfig {
  return { name: "", iconUrl: "", color: "", bgColor: "", type: "", shimmerColor: "" };
}

export const TagConfig: MessageFns<TagConfig> = {
  encode(message: TagConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.iconUrl !== "") {
      writer.uint32(18).string(message.iconUrl);
    }
    if (message.color !== "") {
      writer.uint32(26).string(message.color);
    }
    if (message.bgColor !== "") {
      writer.uint32(34).string(message.bgColor);
    }
    if (message.type !== "") {
      writer.uint32(42).string(message.type);
    }
    if (message.shimmerColor !== "") {
      writer.uint32(50).string(message.shimmerColor);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TagConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTagConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bgColor = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.shimmerColor = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TagConfig {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      bgColor: isSet(object.bgColor) ? globalThis.String(object.bgColor) : "",
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      shimmerColor: isSet(object.shimmerColor) ? globalThis.String(object.shimmerColor) : "",
    };
  },

  toJSON(message: TagConfig): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.bgColor !== "") {
      obj.bgColor = message.bgColor;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.shimmerColor !== "") {
      obj.shimmerColor = message.shimmerColor;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TagConfig>, I>>(base?: I): TagConfig {
    return TagConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TagConfig>, I>>(object: I): TagConfig {
    const message = createBaseTagConfig();
    message.name = object.name ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.color = object.color ?? "";
    message.bgColor = object.bgColor ?? "";
    message.type = object.type ?? "";
    message.shimmerColor = object.shimmerColor ?? "";
    return message;
  },
};

function createBaseBankResponseWithFdData(): BankResponseWithFdData {
  return { bank: undefined, fdsForBankCard: [], highestInterestRateFd: undefined };
}

export const BankResponseWithFdData: MessageFns<BankResponseWithFdData> = {
  encode(message: BankResponseWithFdData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bank !== undefined) {
      BankResponse.encode(message.bank, writer.uint32(10).fork()).join();
    }
    for (const v of message.fdsForBankCard) {
      FixedDepositResponse.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.highestInterestRateFd !== undefined) {
      FixedDepositResponse.encode(message.highestInterestRateFd, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankResponseWithFdData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankResponseWithFdData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bank = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fdsForBankCard.push(FixedDepositResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.highestInterestRateFd = FixedDepositResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankResponseWithFdData {
    return {
      bank: isSet(object.bank) ? BankResponse.fromJSON(object.bank) : undefined,
      fdsForBankCard: globalThis.Array.isArray(object?.fdsForBankCard)
        ? object.fdsForBankCard.map((e: any) => FixedDepositResponse.fromJSON(e))
        : [],
      highestInterestRateFd: isSet(object.highestInterestRateFd)
        ? FixedDepositResponse.fromJSON(object.highestInterestRateFd)
        : undefined,
    };
  },

  toJSON(message: BankResponseWithFdData): unknown {
    const obj: any = {};
    if (message.bank !== undefined) {
      obj.bank = BankResponse.toJSON(message.bank);
    }
    if (message.fdsForBankCard?.length) {
      obj.fdsForBankCard = message.fdsForBankCard.map((e) => FixedDepositResponse.toJSON(e));
    }
    if (message.highestInterestRateFd !== undefined) {
      obj.highestInterestRateFd = FixedDepositResponse.toJSON(message.highestInterestRateFd);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankResponseWithFdData>, I>>(base?: I): BankResponseWithFdData {
    return BankResponseWithFdData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankResponseWithFdData>, I>>(object: I): BankResponseWithFdData {
    const message = createBaseBankResponseWithFdData();
    message.bank = (object.bank !== undefined && object.bank !== null)
      ? BankResponse.fromPartial(object.bank)
      : undefined;
    message.fdsForBankCard = object.fdsForBankCard?.map((e) => FixedDepositResponse.fromPartial(e)) || [];
    message.highestInterestRateFd =
      (object.highestInterestRateFd !== undefined && object.highestInterestRateFd !== null)
        ? FixedDepositResponse.fromPartial(object.highestInterestRateFd)
        : undefined;
    return message;
  },
};

function createBaseBulkCollectionsResponse(): BulkCollectionsResponse {
  return { collection: [] };
}

export const BulkCollectionsResponse: MessageFns<BulkCollectionsResponse> = {
  encode(message: BulkCollectionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.collection) {
      CollectionResponse.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkCollectionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkCollectionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collection.push(CollectionResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkCollectionsResponse {
    return {
      collection: globalThis.Array.isArray(object?.collection)
        ? object.collection.map((e: any) => CollectionResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BulkCollectionsResponse): unknown {
    const obj: any = {};
    if (message.collection?.length) {
      obj.collection = message.collection.map((e) => CollectionResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkCollectionsResponse>, I>>(base?: I): BulkCollectionsResponse {
    return BulkCollectionsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkCollectionsResponse>, I>>(object: I): BulkCollectionsResponse {
    const message = createBaseBulkCollectionsResponse();
    message.collection = object.collection?.map((e) => CollectionResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseAllCollectionsInfo(): AllCollectionsInfo {
  return { collection: [] };
}

export const AllCollectionsInfo: MessageFns<AllCollectionsInfo> = {
  encode(message: AllCollectionsInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.collection) {
      CollectionResponse.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllCollectionsInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllCollectionsInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collection.push(CollectionResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllCollectionsInfo {
    return {
      collection: globalThis.Array.isArray(object?.collection)
        ? object.collection.map((e: any) => CollectionResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AllCollectionsInfo): unknown {
    const obj: any = {};
    if (message.collection?.length) {
      obj.collection = message.collection.map((e) => CollectionResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllCollectionsInfo>, I>>(base?: I): AllCollectionsInfo {
    return AllCollectionsInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllCollectionsInfo>, I>>(object: I): AllCollectionsInfo {
    const message = createBaseAllCollectionsInfo();
    message.collection = object.collection?.map((e) => CollectionResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserOngoingBookingStatusResponse(): UserOngoingBookingStatusResponse {
  return { ongoingBookingStatus: [] };
}

export const UserOngoingBookingStatusResponse: MessageFns<UserOngoingBookingStatusResponse> = {
  encode(message: UserOngoingBookingStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.ongoingBookingStatus) {
      OngoingBookingStatusData.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserOngoingBookingStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserOngoingBookingStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ongoingBookingStatus.push(OngoingBookingStatusData.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserOngoingBookingStatusResponse {
    return {
      ongoingBookingStatus: globalThis.Array.isArray(object?.ongoingBookingStatus)
        ? object.ongoingBookingStatus.map((e: any) => OngoingBookingStatusData.fromJSON(e))
        : [],
    };
  },

  toJSON(message: UserOngoingBookingStatusResponse): unknown {
    const obj: any = {};
    if (message.ongoingBookingStatus?.length) {
      obj.ongoingBookingStatus = message.ongoingBookingStatus.map((e) => OngoingBookingStatusData.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserOngoingBookingStatusResponse>, I>>(
    base?: I,
  ): UserOngoingBookingStatusResponse {
    return UserOngoingBookingStatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserOngoingBookingStatusResponse>, I>>(
    object: I,
  ): UserOngoingBookingStatusResponse {
    const message = createBaseUserOngoingBookingStatusResponse();
    message.ongoingBookingStatus = object.ongoingBookingStatus?.map((e) => OngoingBookingStatusData.fromPartial(e)) ||
      [];
    return message;
  },
};

function createBaseOngoingBookingStatusData(): OngoingBookingStatusData {
  return {
    title: "",
    subTitle: "",
    isAgentsActive: false,
    agentTitle: "",
    agentSubTitle: "",
    bank: undefined,
    cta: "",
    redirectDeeplink: undefined,
    state: 0,
    paymentSuccessTime: "",
    latestStatus: "",
    message: "",
    amount: 0,
    interestRate: 0,
    tenureDays: "",
    distinctId: "",
    isWithdrawalCard: false,
    withdrawalDate: "",
  };
}

export const OngoingBookingStatusData: MessageFns<OngoingBookingStatusData> = {
  encode(message: OngoingBookingStatusData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.subTitle !== "") {
      writer.uint32(18).string(message.subTitle);
    }
    if (message.isAgentsActive !== false) {
      writer.uint32(24).bool(message.isAgentsActive);
    }
    if (message.agentTitle !== "") {
      writer.uint32(34).string(message.agentTitle);
    }
    if (message.agentSubTitle !== "") {
      writer.uint32(42).string(message.agentSubTitle);
    }
    if (message.bank !== undefined) {
      BankResponse.encode(message.bank, writer.uint32(50).fork()).join();
    }
    if (message.cta !== "") {
      writer.uint32(58).string(message.cta);
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(66).fork()).join();
    }
    if (message.state !== 0) {
      writer.uint32(72).int32(message.state);
    }
    if (message.paymentSuccessTime !== "") {
      writer.uint32(82).string(message.paymentSuccessTime);
    }
    if (message.latestStatus !== "") {
      writer.uint32(90).string(message.latestStatus);
    }
    if (message.message !== "") {
      writer.uint32(98).string(message.message);
    }
    if (message.amount !== 0) {
      writer.uint32(105).double(message.amount);
    }
    if (message.interestRate !== 0) {
      writer.uint32(113).double(message.interestRate);
    }
    if (message.tenureDays !== "") {
      writer.uint32(122).string(message.tenureDays);
    }
    if (message.distinctId !== "") {
      writer.uint32(130).string(message.distinctId);
    }
    if (message.isWithdrawalCard !== false) {
      writer.uint32(136).bool(message.isWithdrawalCard);
    }
    if (message.withdrawalDate !== "") {
      writer.uint32(146).string(message.withdrawalDate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OngoingBookingStatusData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOngoingBookingStatusData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isAgentsActive = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.agentTitle = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.agentSubTitle = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.bank = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.cta = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.paymentSuccessTime = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.latestStatus = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 105) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 14: {
          if (tag !== 113) {
            break;
          }

          message.interestRate = reader.double();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.tenureDays = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.distinctId = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.isWithdrawalCard = reader.bool();
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.withdrawalDate = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OngoingBookingStatusData {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle: isSet(object.subTitle) ? globalThis.String(object.subTitle) : "",
      isAgentsActive: isSet(object.isAgentsActive) ? globalThis.Boolean(object.isAgentsActive) : false,
      agentTitle: isSet(object.agentTitle) ? globalThis.String(object.agentTitle) : "",
      agentSubTitle: isSet(object.agentSubTitle) ? globalThis.String(object.agentSubTitle) : "",
      bank: isSet(object.bank) ? BankResponse.fromJSON(object.bank) : undefined,
      cta: isSet(object.cta) ? globalThis.String(object.cta) : "",
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      state: isSet(object.state) ? ongoingBookingStatusFromJSON(object.state) : 0,
      paymentSuccessTime: isSet(object.paymentSuccessTime) ? globalThis.String(object.paymentSuccessTime) : "",
      latestStatus: isSet(object.latestStatus) ? globalThis.String(object.latestStatus) : "",
      message: isSet(object.message) ? globalThis.String(object.message) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      interestRate: isSet(object.interestRate) ? globalThis.Number(object.interestRate) : 0,
      tenureDays: isSet(object.tenureDays) ? globalThis.String(object.tenureDays) : "",
      distinctId: isSet(object.distinctId) ? globalThis.String(object.distinctId) : "",
      isWithdrawalCard: isSet(object.isWithdrawalCard) ? globalThis.Boolean(object.isWithdrawalCard) : false,
      withdrawalDate: isSet(object.withdrawalDate) ? globalThis.String(object.withdrawalDate) : "",
    };
  },

  toJSON(message: OngoingBookingStatusData): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle !== "") {
      obj.subTitle = message.subTitle;
    }
    if (message.isAgentsActive !== false) {
      obj.isAgentsActive = message.isAgentsActive;
    }
    if (message.agentTitle !== "") {
      obj.agentTitle = message.agentTitle;
    }
    if (message.agentSubTitle !== "") {
      obj.agentSubTitle = message.agentSubTitle;
    }
    if (message.bank !== undefined) {
      obj.bank = BankResponse.toJSON(message.bank);
    }
    if (message.cta !== "") {
      obj.cta = message.cta;
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.state !== 0) {
      obj.state = ongoingBookingStatusToJSON(message.state);
    }
    if (message.paymentSuccessTime !== "") {
      obj.paymentSuccessTime = message.paymentSuccessTime;
    }
    if (message.latestStatus !== "") {
      obj.latestStatus = message.latestStatus;
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.interestRate !== 0) {
      obj.interestRate = message.interestRate;
    }
    if (message.tenureDays !== "") {
      obj.tenureDays = message.tenureDays;
    }
    if (message.distinctId !== "") {
      obj.distinctId = message.distinctId;
    }
    if (message.isWithdrawalCard !== false) {
      obj.isWithdrawalCard = message.isWithdrawalCard;
    }
    if (message.withdrawalDate !== "") {
      obj.withdrawalDate = message.withdrawalDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OngoingBookingStatusData>, I>>(base?: I): OngoingBookingStatusData {
    return OngoingBookingStatusData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OngoingBookingStatusData>, I>>(object: I): OngoingBookingStatusData {
    const message = createBaseOngoingBookingStatusData();
    message.title = object.title ?? "";
    message.subTitle = object.subTitle ?? "";
    message.isAgentsActive = object.isAgentsActive ?? false;
    message.agentTitle = object.agentTitle ?? "";
    message.agentSubTitle = object.agentSubTitle ?? "";
    message.bank = (object.bank !== undefined && object.bank !== null)
      ? BankResponse.fromPartial(object.bank)
      : undefined;
    message.cta = object.cta ?? "";
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.state = object.state ?? 0;
    message.paymentSuccessTime = object.paymentSuccessTime ?? "";
    message.latestStatus = object.latestStatus ?? "";
    message.message = object.message ?? "";
    message.amount = object.amount ?? 0;
    message.interestRate = object.interestRate ?? 0;
    message.tenureDays = object.tenureDays ?? "";
    message.distinctId = object.distinctId ?? "";
    message.isWithdrawalCard = object.isWithdrawalCard ?? false;
    message.withdrawalDate = object.withdrawalDate ?? "";
    return message;
  },
};

function createBaseOngoingBookingStatusDataV2(): OngoingBookingStatusDataV2 {
  return {
    title: "",
    subTitle: "",
    isAgentsActive: false,
    agentTitle: "",
    agentSubTitle: "",
    bank: undefined,
    state: 0,
    paymentSuccessTime: "",
    primaryCta: undefined,
    primaryRedirectDeeplink: undefined,
    secondaryCta: undefined,
    secondaryRedirectDeeplink: undefined,
    message: undefined,
    bankId: "",
    amount: 0,
    interestRate: 0,
    tenureDays: "",
    tag: "",
    tagColor: "",
    distinctId: "",
    isWithdrawalCard: false,
    withdrawalDate: "",
  };
}

export const OngoingBookingStatusDataV2: MessageFns<OngoingBookingStatusDataV2> = {
  encode(message: OngoingBookingStatusDataV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.subTitle !== "") {
      writer.uint32(18).string(message.subTitle);
    }
    if (message.isAgentsActive !== false) {
      writer.uint32(24).bool(message.isAgentsActive);
    }
    if (message.agentTitle !== "") {
      writer.uint32(34).string(message.agentTitle);
    }
    if (message.agentSubTitle !== "") {
      writer.uint32(42).string(message.agentSubTitle);
    }
    if (message.bank !== undefined) {
      BankResponse.encode(message.bank, writer.uint32(50).fork()).join();
    }
    if (message.state !== 0) {
      writer.uint32(56).int32(message.state);
    }
    if (message.paymentSuccessTime !== "") {
      writer.uint32(66).string(message.paymentSuccessTime);
    }
    if (message.primaryCta !== undefined) {
      writer.uint32(74).string(message.primaryCta);
    }
    if (message.primaryRedirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.primaryRedirectDeeplink, writer.uint32(82).fork()).join();
    }
    if (message.secondaryCta !== undefined) {
      writer.uint32(90).string(message.secondaryCta);
    }
    if (message.secondaryRedirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.secondaryRedirectDeeplink, writer.uint32(98).fork()).join();
    }
    if (message.message !== undefined) {
      writer.uint32(106).string(message.message);
    }
    if (message.bankId !== "") {
      writer.uint32(114).string(message.bankId);
    }
    if (message.amount !== 0) {
      writer.uint32(121).double(message.amount);
    }
    if (message.interestRate !== 0) {
      writer.uint32(129).double(message.interestRate);
    }
    if (message.tenureDays !== "") {
      writer.uint32(138).string(message.tenureDays);
    }
    if (message.tag !== "") {
      writer.uint32(146).string(message.tag);
    }
    if (message.tagColor !== "") {
      writer.uint32(154).string(message.tagColor);
    }
    if (message.distinctId !== "") {
      writer.uint32(162).string(message.distinctId);
    }
    if (message.isWithdrawalCard !== false) {
      writer.uint32(168).bool(message.isWithdrawalCard);
    }
    if (message.withdrawalDate !== "") {
      writer.uint32(178).string(message.withdrawalDate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OngoingBookingStatusDataV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOngoingBookingStatusDataV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isAgentsActive = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.agentTitle = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.agentSubTitle = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.bank = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.state = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.paymentSuccessTime = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.primaryCta = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.primaryRedirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.secondaryCta = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.secondaryRedirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 121) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 16: {
          if (tag !== 129) {
            break;
          }

          message.interestRate = reader.double();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.tenureDays = reader.string();
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.tag = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.tagColor = reader.string();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.distinctId = reader.string();
          continue;
        }
        case 21: {
          if (tag !== 168) {
            break;
          }

          message.isWithdrawalCard = reader.bool();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.withdrawalDate = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OngoingBookingStatusDataV2 {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle: isSet(object.subTitle) ? globalThis.String(object.subTitle) : "",
      isAgentsActive: isSet(object.isAgentsActive) ? globalThis.Boolean(object.isAgentsActive) : false,
      agentTitle: isSet(object.agentTitle) ? globalThis.String(object.agentTitle) : "",
      agentSubTitle: isSet(object.agentSubTitle) ? globalThis.String(object.agentSubTitle) : "",
      bank: isSet(object.bank) ? BankResponse.fromJSON(object.bank) : undefined,
      state: isSet(object.state) ? ongoingBookingStatusFromJSON(object.state) : 0,
      paymentSuccessTime: isSet(object.paymentSuccessTime) ? globalThis.String(object.paymentSuccessTime) : "",
      primaryCta: isSet(object.primaryCta) ? globalThis.String(object.primaryCta) : undefined,
      primaryRedirectDeeplink: isSet(object.primaryRedirectDeeplink)
        ? RedirectDeeplink.fromJSON(object.primaryRedirectDeeplink)
        : undefined,
      secondaryCta: isSet(object.secondaryCta) ? globalThis.String(object.secondaryCta) : undefined,
      secondaryRedirectDeeplink: isSet(object.secondaryRedirectDeeplink)
        ? RedirectDeeplink.fromJSON(object.secondaryRedirectDeeplink)
        : undefined,
      message: isSet(object.message) ? globalThis.String(object.message) : undefined,
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      interestRate: isSet(object.interestRate) ? globalThis.Number(object.interestRate) : 0,
      tenureDays: isSet(object.tenureDays) ? globalThis.String(object.tenureDays) : "",
      tag: isSet(object.tag) ? globalThis.String(object.tag) : "",
      tagColor: isSet(object.tagColor) ? globalThis.String(object.tagColor) : "",
      distinctId: isSet(object.distinctId) ? globalThis.String(object.distinctId) : "",
      isWithdrawalCard: isSet(object.isWithdrawalCard) ? globalThis.Boolean(object.isWithdrawalCard) : false,
      withdrawalDate: isSet(object.withdrawalDate) ? globalThis.String(object.withdrawalDate) : "",
    };
  },

  toJSON(message: OngoingBookingStatusDataV2): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle !== "") {
      obj.subTitle = message.subTitle;
    }
    if (message.isAgentsActive !== false) {
      obj.isAgentsActive = message.isAgentsActive;
    }
    if (message.agentTitle !== "") {
      obj.agentTitle = message.agentTitle;
    }
    if (message.agentSubTitle !== "") {
      obj.agentSubTitle = message.agentSubTitle;
    }
    if (message.bank !== undefined) {
      obj.bank = BankResponse.toJSON(message.bank);
    }
    if (message.state !== 0) {
      obj.state = ongoingBookingStatusToJSON(message.state);
    }
    if (message.paymentSuccessTime !== "") {
      obj.paymentSuccessTime = message.paymentSuccessTime;
    }
    if (message.primaryCta !== undefined) {
      obj.primaryCta = message.primaryCta;
    }
    if (message.primaryRedirectDeeplink !== undefined) {
      obj.primaryRedirectDeeplink = RedirectDeeplink.toJSON(message.primaryRedirectDeeplink);
    }
    if (message.secondaryCta !== undefined) {
      obj.secondaryCta = message.secondaryCta;
    }
    if (message.secondaryRedirectDeeplink !== undefined) {
      obj.secondaryRedirectDeeplink = RedirectDeeplink.toJSON(message.secondaryRedirectDeeplink);
    }
    if (message.message !== undefined) {
      obj.message = message.message;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.interestRate !== 0) {
      obj.interestRate = message.interestRate;
    }
    if (message.tenureDays !== "") {
      obj.tenureDays = message.tenureDays;
    }
    if (message.tag !== "") {
      obj.tag = message.tag;
    }
    if (message.tagColor !== "") {
      obj.tagColor = message.tagColor;
    }
    if (message.distinctId !== "") {
      obj.distinctId = message.distinctId;
    }
    if (message.isWithdrawalCard !== false) {
      obj.isWithdrawalCard = message.isWithdrawalCard;
    }
    if (message.withdrawalDate !== "") {
      obj.withdrawalDate = message.withdrawalDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OngoingBookingStatusDataV2>, I>>(base?: I): OngoingBookingStatusDataV2 {
    return OngoingBookingStatusDataV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OngoingBookingStatusDataV2>, I>>(object: I): OngoingBookingStatusDataV2 {
    const message = createBaseOngoingBookingStatusDataV2();
    message.title = object.title ?? "";
    message.subTitle = object.subTitle ?? "";
    message.isAgentsActive = object.isAgentsActive ?? false;
    message.agentTitle = object.agentTitle ?? "";
    message.agentSubTitle = object.agentSubTitle ?? "";
    message.bank = (object.bank !== undefined && object.bank !== null)
      ? BankResponse.fromPartial(object.bank)
      : undefined;
    message.state = object.state ?? 0;
    message.paymentSuccessTime = object.paymentSuccessTime ?? "";
    message.primaryCta = object.primaryCta ?? undefined;
    message.primaryRedirectDeeplink =
      (object.primaryRedirectDeeplink !== undefined && object.primaryRedirectDeeplink !== null)
        ? RedirectDeeplink.fromPartial(object.primaryRedirectDeeplink)
        : undefined;
    message.secondaryCta = object.secondaryCta ?? undefined;
    message.secondaryRedirectDeeplink =
      (object.secondaryRedirectDeeplink !== undefined && object.secondaryRedirectDeeplink !== null)
        ? RedirectDeeplink.fromPartial(object.secondaryRedirectDeeplink)
        : undefined;
    message.message = object.message ?? undefined;
    message.bankId = object.bankId ?? "";
    message.amount = object.amount ?? 0;
    message.interestRate = object.interestRate ?? 0;
    message.tenureDays = object.tenureDays ?? "";
    message.tag = object.tag ?? "";
    message.tagColor = object.tagColor ?? "";
    message.distinctId = object.distinctId ?? "";
    message.isWithdrawalCard = object.isWithdrawalCard ?? false;
    message.withdrawalDate = object.withdrawalDate ?? "";
    return message;
  },
};

function createBaseUserOngoingBookingStatusResponseV2(): UserOngoingBookingStatusResponseV2 {
  return { ongoingBookingStatus: [] };
}

export const UserOngoingBookingStatusResponseV2: MessageFns<UserOngoingBookingStatusResponseV2> = {
  encode(message: UserOngoingBookingStatusResponseV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.ongoingBookingStatus) {
      OngoingBookingStatusDataV2.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserOngoingBookingStatusResponseV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserOngoingBookingStatusResponseV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ongoingBookingStatus.push(OngoingBookingStatusDataV2.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserOngoingBookingStatusResponseV2 {
    return {
      ongoingBookingStatus: globalThis.Array.isArray(object?.ongoingBookingStatus)
        ? object.ongoingBookingStatus.map((e: any) => OngoingBookingStatusDataV2.fromJSON(e))
        : [],
    };
  },

  toJSON(message: UserOngoingBookingStatusResponseV2): unknown {
    const obj: any = {};
    if (message.ongoingBookingStatus?.length) {
      obj.ongoingBookingStatus = message.ongoingBookingStatus.map((e) => OngoingBookingStatusDataV2.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserOngoingBookingStatusResponseV2>, I>>(
    base?: I,
  ): UserOngoingBookingStatusResponseV2 {
    return UserOngoingBookingStatusResponseV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserOngoingBookingStatusResponseV2>, I>>(
    object: I,
  ): UserOngoingBookingStatusResponseV2 {
    const message = createBaseUserOngoingBookingStatusResponseV2();
    message.ongoingBookingStatus = object.ongoingBookingStatus?.map((e) => OngoingBookingStatusDataV2.fromPartial(e)) ||
      [];
    return message;
  },
};

function createBasePaymentFailureItem(): PaymentFailureItem {
  return { id: "", amount: 0, bankName: undefined, bankId: undefined };
}

export const PaymentFailureItem: MessageFns<PaymentFailureItem> = {
  encode(message: PaymentFailureItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.amount !== 0) {
      writer.uint32(17).double(message.amount);
    }
    if (message.bankName !== undefined) {
      writer.uint32(26).string(message.bankName);
    }
    if (message.bankId !== undefined) {
      writer.uint32(34).string(message.bankId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PaymentFailureItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePaymentFailureItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PaymentFailureItem {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : undefined,
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : undefined,
    };
  },

  toJSON(message: PaymentFailureItem): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.bankName !== undefined) {
      obj.bankName = message.bankName;
    }
    if (message.bankId !== undefined) {
      obj.bankId = message.bankId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PaymentFailureItem>, I>>(base?: I): PaymentFailureItem {
    return PaymentFailureItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaymentFailureItem>, I>>(object: I): PaymentFailureItem {
    const message = createBasePaymentFailureItem();
    message.id = object.id ?? "";
    message.amount = object.amount ?? 0;
    message.bankName = object.bankName ?? undefined;
    message.bankId = object.bankId ?? undefined;
    return message;
  },
};

function createBasePaymentFailureCardResponse(): PaymentFailureCardResponse {
  return { paymentFailureItemList: [], hasPaymentSuccess: false };
}

export const PaymentFailureCardResponse: MessageFns<PaymentFailureCardResponse> = {
  encode(message: PaymentFailureCardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.paymentFailureItemList) {
      PaymentFailureItem.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.hasPaymentSuccess !== false) {
      writer.uint32(16).bool(message.hasPaymentSuccess);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PaymentFailureCardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePaymentFailureCardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.paymentFailureItemList.push(PaymentFailureItem.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasPaymentSuccess = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PaymentFailureCardResponse {
    return {
      paymentFailureItemList: globalThis.Array.isArray(object?.paymentFailureItemList)
        ? object.paymentFailureItemList.map((e: any) => PaymentFailureItem.fromJSON(e))
        : [],
      hasPaymentSuccess: isSet(object.hasPaymentSuccess) ? globalThis.Boolean(object.hasPaymentSuccess) : false,
    };
  },

  toJSON(message: PaymentFailureCardResponse): unknown {
    const obj: any = {};
    if (message.paymentFailureItemList?.length) {
      obj.paymentFailureItemList = message.paymentFailureItemList.map((e) => PaymentFailureItem.toJSON(e));
    }
    if (message.hasPaymentSuccess !== false) {
      obj.hasPaymentSuccess = message.hasPaymentSuccess;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PaymentFailureCardResponse>, I>>(base?: I): PaymentFailureCardResponse {
    return PaymentFailureCardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaymentFailureCardResponse>, I>>(object: I): PaymentFailureCardResponse {
    const message = createBasePaymentFailureCardResponse();
    message.paymentFailureItemList = object.paymentFailureItemList?.map((e) => PaymentFailureItem.fromPartial(e)) || [];
    message.hasPaymentSuccess = object.hasPaymentSuccess ?? false;
    return message;
  },
};

function createBaseWithdrawalData(): WithdrawalData {
  return {
    id: "",
    userCancellationAction: 0,
    withdrawalStatus: 0,
    withdrawalTime: "",
    withdrawalCancellationDeadline: "",
    withdrawalTimeLine: [],
    bottomInfoText: "",
    hasMoneyCredited: false,
    hasAnsweredCancellationQuestions: false,
    withdrawalReason: "",
  };
}

export const WithdrawalData: MessageFns<WithdrawalData> = {
  encode(message: WithdrawalData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.userCancellationAction !== 0) {
      writer.uint32(16).int32(message.userCancellationAction);
    }
    if (message.withdrawalStatus !== 0) {
      writer.uint32(24).int32(message.withdrawalStatus);
    }
    if (message.withdrawalTime !== "") {
      writer.uint32(34).string(message.withdrawalTime);
    }
    if (message.withdrawalCancellationDeadline !== "") {
      writer.uint32(42).string(message.withdrawalCancellationDeadline);
    }
    for (const v of message.withdrawalTimeLine) {
      WithdrawalTimeLineNode.encode(v!, writer.uint32(50).fork()).join();
    }
    if (message.bottomInfoText !== "") {
      writer.uint32(58).string(message.bottomInfoText);
    }
    if (message.hasMoneyCredited !== false) {
      writer.uint32(64).bool(message.hasMoneyCredited);
    }
    if (message.hasAnsweredCancellationQuestions !== false) {
      writer.uint32(72).bool(message.hasAnsweredCancellationQuestions);
    }
    if (message.withdrawalReason !== "") {
      writer.uint32(82).string(message.withdrawalReason);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.userCancellationAction = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.withdrawalStatus = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.withdrawalTime = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.withdrawalCancellationDeadline = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.withdrawalTimeLine.push(WithdrawalTimeLineNode.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.bottomInfoText = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.hasMoneyCredited = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.hasAnsweredCancellationQuestions = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.withdrawalReason = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalData {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      userCancellationAction: isSet(object.userCancellationAction)
        ? userCancellationActionFromJSON(object.userCancellationAction)
        : 0,
      withdrawalStatus: isSet(object.withdrawalStatus) ? withdrawalStatusFromJSON(object.withdrawalStatus) : 0,
      withdrawalTime: isSet(object.withdrawalTime) ? globalThis.String(object.withdrawalTime) : "",
      withdrawalCancellationDeadline: isSet(object.withdrawalCancellationDeadline)
        ? globalThis.String(object.withdrawalCancellationDeadline)
        : "",
      withdrawalTimeLine: globalThis.Array.isArray(object?.withdrawalTimeLine)
        ? object.withdrawalTimeLine.map((e: any) => WithdrawalTimeLineNode.fromJSON(e))
        : [],
      bottomInfoText: isSet(object.bottomInfoText) ? globalThis.String(object.bottomInfoText) : "",
      hasMoneyCredited: isSet(object.hasMoneyCredited) ? globalThis.Boolean(object.hasMoneyCredited) : false,
      hasAnsweredCancellationQuestions: isSet(object.hasAnsweredCancellationQuestions)
        ? globalThis.Boolean(object.hasAnsweredCancellationQuestions)
        : false,
      withdrawalReason: isSet(object.withdrawalReason) ? globalThis.String(object.withdrawalReason) : "",
    };
  },

  toJSON(message: WithdrawalData): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.userCancellationAction !== 0) {
      obj.userCancellationAction = userCancellationActionToJSON(message.userCancellationAction);
    }
    if (message.withdrawalStatus !== 0) {
      obj.withdrawalStatus = withdrawalStatusToJSON(message.withdrawalStatus);
    }
    if (message.withdrawalTime !== "") {
      obj.withdrawalTime = message.withdrawalTime;
    }
    if (message.withdrawalCancellationDeadline !== "") {
      obj.withdrawalCancellationDeadline = message.withdrawalCancellationDeadline;
    }
    if (message.withdrawalTimeLine?.length) {
      obj.withdrawalTimeLine = message.withdrawalTimeLine.map((e) => WithdrawalTimeLineNode.toJSON(e));
    }
    if (message.bottomInfoText !== "") {
      obj.bottomInfoText = message.bottomInfoText;
    }
    if (message.hasMoneyCredited !== false) {
      obj.hasMoneyCredited = message.hasMoneyCredited;
    }
    if (message.hasAnsweredCancellationQuestions !== false) {
      obj.hasAnsweredCancellationQuestions = message.hasAnsweredCancellationQuestions;
    }
    if (message.withdrawalReason !== "") {
      obj.withdrawalReason = message.withdrawalReason;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalData>, I>>(base?: I): WithdrawalData {
    return WithdrawalData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalData>, I>>(object: I): WithdrawalData {
    const message = createBaseWithdrawalData();
    message.id = object.id ?? "";
    message.userCancellationAction = object.userCancellationAction ?? 0;
    message.withdrawalStatus = object.withdrawalStatus ?? 0;
    message.withdrawalTime = object.withdrawalTime ?? "";
    message.withdrawalCancellationDeadline = object.withdrawalCancellationDeadline ?? "";
    message.withdrawalTimeLine = object.withdrawalTimeLine?.map((e) => WithdrawalTimeLineNode.fromPartial(e)) || [];
    message.bottomInfoText = object.bottomInfoText ?? "";
    message.hasMoneyCredited = object.hasMoneyCredited ?? false;
    message.hasAnsweredCancellationQuestions = object.hasAnsweredCancellationQuestions ?? false;
    message.withdrawalReason = object.withdrawalReason ?? "";
    return message;
  },
};

function createBaseWithdrawalTimeLineNode(): WithdrawalTimeLineNode {
  return { title: "", subTitle: "", isCompleted: false };
}

export const WithdrawalTimeLineNode: MessageFns<WithdrawalTimeLineNode> = {
  encode(message: WithdrawalTimeLineNode, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.subTitle !== "") {
      writer.uint32(18).string(message.subTitle);
    }
    if (message.isCompleted !== false) {
      writer.uint32(24).bool(message.isCompleted);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalTimeLineNode {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalTimeLineNode();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.subTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isCompleted = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalTimeLineNode {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      subTitle: isSet(object.subTitle) ? globalThis.String(object.subTitle) : "",
      isCompleted: isSet(object.isCompleted) ? globalThis.Boolean(object.isCompleted) : false,
    };
  },

  toJSON(message: WithdrawalTimeLineNode): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.subTitle !== "") {
      obj.subTitle = message.subTitle;
    }
    if (message.isCompleted !== false) {
      obj.isCompleted = message.isCompleted;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalTimeLineNode>, I>>(base?: I): WithdrawalTimeLineNode {
    return WithdrawalTimeLineNode.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalTimeLineNode>, I>>(object: I): WithdrawalTimeLineNode {
    const message = createBaseWithdrawalTimeLineNode();
    message.title = object.title ?? "";
    message.subTitle = object.subTitle ?? "";
    message.isCompleted = object.isCompleted ?? false;
    return message;
  },
};

function createBaseSubmitWithdrawalReasonRequest(): SubmitWithdrawalReasonRequest {
  return { withdrawalId: "", reason: "" };
}

export const SubmitWithdrawalReasonRequest: MessageFns<SubmitWithdrawalReasonRequest> = {
  encode(message: SubmitWithdrawalReasonRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.withdrawalId !== "") {
      writer.uint32(10).string(message.withdrawalId);
    }
    if (message.reason !== "") {
      writer.uint32(18).string(message.reason);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubmitWithdrawalReasonRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitWithdrawalReasonRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.withdrawalId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.reason = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitWithdrawalReasonRequest {
    return {
      withdrawalId: isSet(object.withdrawalId) ? globalThis.String(object.withdrawalId) : "",
      reason: isSet(object.reason) ? globalThis.String(object.reason) : "",
    };
  },

  toJSON(message: SubmitWithdrawalReasonRequest): unknown {
    const obj: any = {};
    if (message.withdrawalId !== "") {
      obj.withdrawalId = message.withdrawalId;
    }
    if (message.reason !== "") {
      obj.reason = message.reason;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitWithdrawalReasonRequest>, I>>(base?: I): SubmitWithdrawalReasonRequest {
    return SubmitWithdrawalReasonRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitWithdrawalReasonRequest>, I>>(
    object: I,
  ): SubmitWithdrawalReasonRequest {
    const message = createBaseSubmitWithdrawalReasonRequest();
    message.withdrawalId = object.withdrawalId ?? "";
    message.reason = object.reason ?? "";
    return message;
  },
};

function createBaseSubmitWithdrawalReasonResponse(): SubmitWithdrawalReasonResponse {
  return {};
}

export const SubmitWithdrawalReasonResponse: MessageFns<SubmitWithdrawalReasonResponse> = {
  encode(_: SubmitWithdrawalReasonResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubmitWithdrawalReasonResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitWithdrawalReasonResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): SubmitWithdrawalReasonResponse {
    return {};
  },

  toJSON(_: SubmitWithdrawalReasonResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitWithdrawalReasonResponse>, I>>(base?: I): SubmitWithdrawalReasonResponse {
    return SubmitWithdrawalReasonResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitWithdrawalReasonResponse>, I>>(_: I): SubmitWithdrawalReasonResponse {
    const message = createBaseSubmitWithdrawalReasonResponse();
    return message;
  },
};

function createBaseWithdrawalCancellationRequest(): WithdrawalCancellationRequest {
  return { withdrawalId: "", qa: [] };
}

export const WithdrawalCancellationRequest: MessageFns<WithdrawalCancellationRequest> = {
  encode(message: WithdrawalCancellationRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.withdrawalId !== "") {
      writer.uint32(10).string(message.withdrawalId);
    }
    for (const v of message.qa) {
      WithdrawalCancellationQuestionAndAnswer.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalCancellationRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalCancellationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.withdrawalId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.qa.push(WithdrawalCancellationQuestionAndAnswer.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalCancellationRequest {
    return {
      withdrawalId: isSet(object.withdrawalId) ? globalThis.String(object.withdrawalId) : "",
      qa: globalThis.Array.isArray(object?.qa)
        ? object.qa.map((e: any) => WithdrawalCancellationQuestionAndAnswer.fromJSON(e))
        : [],
    };
  },

  toJSON(message: WithdrawalCancellationRequest): unknown {
    const obj: any = {};
    if (message.withdrawalId !== "") {
      obj.withdrawalId = message.withdrawalId;
    }
    if (message.qa?.length) {
      obj.qa = message.qa.map((e) => WithdrawalCancellationQuestionAndAnswer.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalCancellationRequest>, I>>(base?: I): WithdrawalCancellationRequest {
    return WithdrawalCancellationRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalCancellationRequest>, I>>(
    object: I,
  ): WithdrawalCancellationRequest {
    const message = createBaseWithdrawalCancellationRequest();
    message.withdrawalId = object.withdrawalId ?? "";
    message.qa = object.qa?.map((e) => WithdrawalCancellationQuestionAndAnswer.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserWithdrawalCancellationActionRequest(): UserWithdrawalCancellationActionRequest {
  return { withdrawalId: "", userCancellationAction: 0 };
}

export const UserWithdrawalCancellationActionRequest: MessageFns<UserWithdrawalCancellationActionRequest> = {
  encode(message: UserWithdrawalCancellationActionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.withdrawalId !== "") {
      writer.uint32(10).string(message.withdrawalId);
    }
    if (message.userCancellationAction !== 0) {
      writer.uint32(16).int32(message.userCancellationAction);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserWithdrawalCancellationActionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserWithdrawalCancellationActionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.withdrawalId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.userCancellationAction = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserWithdrawalCancellationActionRequest {
    return {
      withdrawalId: isSet(object.withdrawalId) ? globalThis.String(object.withdrawalId) : "",
      userCancellationAction: isSet(object.userCancellationAction)
        ? userCancellationActionFromJSON(object.userCancellationAction)
        : 0,
    };
  },

  toJSON(message: UserWithdrawalCancellationActionRequest): unknown {
    const obj: any = {};
    if (message.withdrawalId !== "") {
      obj.withdrawalId = message.withdrawalId;
    }
    if (message.userCancellationAction !== 0) {
      obj.userCancellationAction = userCancellationActionToJSON(message.userCancellationAction);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserWithdrawalCancellationActionRequest>, I>>(
    base?: I,
  ): UserWithdrawalCancellationActionRequest {
    return UserWithdrawalCancellationActionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserWithdrawalCancellationActionRequest>, I>>(
    object: I,
  ): UserWithdrawalCancellationActionRequest {
    const message = createBaseUserWithdrawalCancellationActionRequest();
    message.withdrawalId = object.withdrawalId ?? "";
    message.userCancellationAction = object.userCancellationAction ?? 0;
    return message;
  },
};

function createBaseUserWithdrawalCancellationActionResponse(): UserWithdrawalCancellationActionResponse {
  return {};
}

export const UserWithdrawalCancellationActionResponse: MessageFns<UserWithdrawalCancellationActionResponse> = {
  encode(_: UserWithdrawalCancellationActionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserWithdrawalCancellationActionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserWithdrawalCancellationActionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UserWithdrawalCancellationActionResponse {
    return {};
  },

  toJSON(_: UserWithdrawalCancellationActionResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UserWithdrawalCancellationActionResponse>, I>>(
    base?: I,
  ): UserWithdrawalCancellationActionResponse {
    return UserWithdrawalCancellationActionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserWithdrawalCancellationActionResponse>, I>>(
    _: I,
  ): UserWithdrawalCancellationActionResponse {
    const message = createBaseUserWithdrawalCancellationActionResponse();
    return message;
  },
};

function createBaseWithdrawalCancellationQuestionAndAnswer(): WithdrawalCancellationQuestionAndAnswer {
  return { question: "", answer: "" };
}

export const WithdrawalCancellationQuestionAndAnswer: MessageFns<WithdrawalCancellationQuestionAndAnswer> = {
  encode(message: WithdrawalCancellationQuestionAndAnswer, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.question !== "") {
      writer.uint32(10).string(message.question);
    }
    if (message.answer !== "") {
      writer.uint32(18).string(message.answer);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalCancellationQuestionAndAnswer {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalCancellationQuestionAndAnswer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.question = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.answer = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalCancellationQuestionAndAnswer {
    return {
      question: isSet(object.question) ? globalThis.String(object.question) : "",
      answer: isSet(object.answer) ? globalThis.String(object.answer) : "",
    };
  },

  toJSON(message: WithdrawalCancellationQuestionAndAnswer): unknown {
    const obj: any = {};
    if (message.question !== "") {
      obj.question = message.question;
    }
    if (message.answer !== "") {
      obj.answer = message.answer;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalCancellationQuestionAndAnswer>, I>>(
    base?: I,
  ): WithdrawalCancellationQuestionAndAnswer {
    return WithdrawalCancellationQuestionAndAnswer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalCancellationQuestionAndAnswer>, I>>(
    object: I,
  ): WithdrawalCancellationQuestionAndAnswer {
    const message = createBaseWithdrawalCancellationQuestionAndAnswer();
    message.question = object.question ?? "";
    message.answer = object.answer ?? "";
    return message;
  },
};

function createBaseWithdrawalCancellationResponse(): WithdrawalCancellationResponse {
  return {};
}

export const WithdrawalCancellationResponse: MessageFns<WithdrawalCancellationResponse> = {
  encode(_: WithdrawalCancellationResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalCancellationResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalCancellationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): WithdrawalCancellationResponse {
    return {};
  },

  toJSON(_: WithdrawalCancellationResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalCancellationResponse>, I>>(base?: I): WithdrawalCancellationResponse {
    return WithdrawalCancellationResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalCancellationResponse>, I>>(_: I): WithdrawalCancellationResponse {
    const message = createBaseWithdrawalCancellationResponse();
    return message;
  },
};

function createBaseBankDetails(): BankDetails {
  return { bankId: "", logo: "", bankName: "" };
}

export const BankDetails: MessageFns<BankDetails> = {
  encode(message: BankDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    if (message.logo !== "") {
      writer.uint32(18).string(message.logo);
    }
    if (message.bankName !== "") {
      writer.uint32(26).string(message.bankName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.logo = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankDetails {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      logo: isSet(object.logo) ? globalThis.String(object.logo) : "",
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
    };
  },

  toJSON(message: BankDetails): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.logo !== "") {
      obj.logo = message.logo;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankDetails>, I>>(base?: I): BankDetails {
    return BankDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankDetails>, I>>(object: I): BankDetails {
    const message = createBaseBankDetails();
    message.bankId = object.bankId ?? "";
    message.logo = object.logo ?? "";
    message.bankName = object.bankName ?? "";
    return message;
  },
};

function createBaseLocationBasedRecommendedFd(): LocationBasedRecommendedFd {
  return { rate: 0, tenure: "", bankDetails: undefined };
}

export const LocationBasedRecommendedFd: MessageFns<LocationBasedRecommendedFd> = {
  encode(message: LocationBasedRecommendedFd, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rate !== 0) {
      writer.uint32(9).double(message.rate);
    }
    if (message.tenure !== "") {
      writer.uint32(18).string(message.tenure);
    }
    if (message.bankDetails !== undefined) {
      BankDetails.encode(message.bankDetails, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LocationBasedRecommendedFd {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLocationBasedRecommendedFd();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankDetails = BankDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LocationBasedRecommendedFd {
    return {
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      bankDetails: isSet(object.bankDetails) ? BankDetails.fromJSON(object.bankDetails) : undefined,
    };
  },

  toJSON(message: LocationBasedRecommendedFd): unknown {
    const obj: any = {};
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.bankDetails !== undefined) {
      obj.bankDetails = BankDetails.toJSON(message.bankDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LocationBasedRecommendedFd>, I>>(base?: I): LocationBasedRecommendedFd {
    return LocationBasedRecommendedFd.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LocationBasedRecommendedFd>, I>>(object: I): LocationBasedRecommendedFd {
    const message = createBaseLocationBasedRecommendedFd();
    message.rate = object.rate ?? 0;
    message.tenure = object.tenure ?? "";
    message.bankDetails = (object.bankDetails !== undefined && object.bankDetails !== null)
      ? BankDetails.fromPartial(object.bankDetails)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
