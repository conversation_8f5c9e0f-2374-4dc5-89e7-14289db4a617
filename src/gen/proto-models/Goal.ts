// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Goal.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RedirectDeeplink } from "./BusinessCommon";
import { BankResponse, FixedDepositResponse } from "./Collection";

export const protobufPackage = "com.stablemoney.api.identity";

export enum GoalType {
  UNKNOWN_GOAL_TYPE = 0,
  EMERGENCY_FUND = 1,
  UNRECOGNIZED = -1,
}

export function goalTypeFromJSON(object: any): GoalType {
  switch (object) {
    case 0:
    case "UNKNOWN_GOAL_TYPE":
      return GoalType.UNKNOWN_GOAL_TYPE;
    case 1:
    case "EMERGENCY_FUND":
      return GoalType.EMERGENCY_FUND;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoalType.UNRECOGNIZED;
  }
}

export function goalTypeToJSON(object: GoalType): string {
  switch (object) {
    case GoalType.UNKNOWN_GOAL_TYPE:
      return "UNKNOWN_GOAL_TYPE";
    case GoalType.EMERGENCY_FUND:
      return "EMERGENCY_FUND";
    case GoalType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum EmergencyFundExpenseCategory {
  HOUSEHOLD_EXPENSES = 0,
  KIDS_EXPENSES = 1,
  TRAVEL_EXPENSES = 2,
  PARENTS_HEALTH_EXPENSES = 3,
  KIDS_EDUCATION_EXPENSES = 4,
  JOB_LOSS_EXPENSES = 5,
  MEDICAL_EXPENSES = 6,
  REPAIRS_EXPENSES = 7,
  OTHER_EXPENSES = 8,
  UNRECOGNIZED = -1,
}

export function emergencyFundExpenseCategoryFromJSON(object: any): EmergencyFundExpenseCategory {
  switch (object) {
    case 0:
    case "HOUSEHOLD_EXPENSES":
      return EmergencyFundExpenseCategory.HOUSEHOLD_EXPENSES;
    case 1:
    case "KIDS_EXPENSES":
      return EmergencyFundExpenseCategory.KIDS_EXPENSES;
    case 2:
    case "TRAVEL_EXPENSES":
      return EmergencyFundExpenseCategory.TRAVEL_EXPENSES;
    case 3:
    case "PARENTS_HEALTH_EXPENSES":
      return EmergencyFundExpenseCategory.PARENTS_HEALTH_EXPENSES;
    case 4:
    case "KIDS_EDUCATION_EXPENSES":
      return EmergencyFundExpenseCategory.KIDS_EDUCATION_EXPENSES;
    case 5:
    case "JOB_LOSS_EXPENSES":
      return EmergencyFundExpenseCategory.JOB_LOSS_EXPENSES;
    case 6:
    case "MEDICAL_EXPENSES":
      return EmergencyFundExpenseCategory.MEDICAL_EXPENSES;
    case 7:
    case "REPAIRS_EXPENSES":
      return EmergencyFundExpenseCategory.REPAIRS_EXPENSES;
    case 8:
    case "OTHER_EXPENSES":
      return EmergencyFundExpenseCategory.OTHER_EXPENSES;
    case -1:
    case "UNRECOGNIZED":
    default:
      return EmergencyFundExpenseCategory.UNRECOGNIZED;
  }
}

export function emergencyFundExpenseCategoryToJSON(object: EmergencyFundExpenseCategory): string {
  switch (object) {
    case EmergencyFundExpenseCategory.HOUSEHOLD_EXPENSES:
      return "HOUSEHOLD_EXPENSES";
    case EmergencyFundExpenseCategory.KIDS_EXPENSES:
      return "KIDS_EXPENSES";
    case EmergencyFundExpenseCategory.TRAVEL_EXPENSES:
      return "TRAVEL_EXPENSES";
    case EmergencyFundExpenseCategory.PARENTS_HEALTH_EXPENSES:
      return "PARENTS_HEALTH_EXPENSES";
    case EmergencyFundExpenseCategory.KIDS_EDUCATION_EXPENSES:
      return "KIDS_EDUCATION_EXPENSES";
    case EmergencyFundExpenseCategory.JOB_LOSS_EXPENSES:
      return "JOB_LOSS_EXPENSES";
    case EmergencyFundExpenseCategory.MEDICAL_EXPENSES:
      return "MEDICAL_EXPENSES";
    case EmergencyFundExpenseCategory.REPAIRS_EXPENSES:
      return "REPAIRS_EXPENSES";
    case EmergencyFundExpenseCategory.OTHER_EXPENSES:
      return "OTHER_EXPENSES";
    case EmergencyFundExpenseCategory.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum EmergencyFundUserCohort {
  HAS_KIDS_AND_PARENTS_COHORT = 0,
  HAS_KIDS_COHORT = 1,
  HAS_PARENTS_COHORT = 2,
  MARRIED_COHORT = 3,
  SINGLE_COHORT = 4,
  UNRECOGNIZED = -1,
}

export function emergencyFundUserCohortFromJSON(object: any): EmergencyFundUserCohort {
  switch (object) {
    case 0:
    case "HAS_KIDS_AND_PARENTS_COHORT":
      return EmergencyFundUserCohort.HAS_KIDS_AND_PARENTS_COHORT;
    case 1:
    case "HAS_KIDS_COHORT":
      return EmergencyFundUserCohort.HAS_KIDS_COHORT;
    case 2:
    case "HAS_PARENTS_COHORT":
      return EmergencyFundUserCohort.HAS_PARENTS_COHORT;
    case 3:
    case "MARRIED_COHORT":
      return EmergencyFundUserCohort.MARRIED_COHORT;
    case 4:
    case "SINGLE_COHORT":
      return EmergencyFundUserCohort.SINGLE_COHORT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return EmergencyFundUserCohort.UNRECOGNIZED;
  }
}

export function emergencyFundUserCohortToJSON(object: EmergencyFundUserCohort): string {
  switch (object) {
    case EmergencyFundUserCohort.HAS_KIDS_AND_PARENTS_COHORT:
      return "HAS_KIDS_AND_PARENTS_COHORT";
    case EmergencyFundUserCohort.HAS_KIDS_COHORT:
      return "HAS_KIDS_COHORT";
    case EmergencyFundUserCohort.HAS_PARENTS_COHORT:
      return "HAS_PARENTS_COHORT";
    case EmergencyFundUserCohort.MARRIED_COHORT:
      return "MARRIED_COHORT";
    case EmergencyFundUserCohort.SINGLE_COHORT:
      return "SINGLE_COHORT";
    case EmergencyFundUserCohort.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ContributionStatus {
  UNKNOWN_CONTRIBUTION_STATUS = 0,
  PENDING_CONTRIBUTION_STATUS = 1,
  COMPLETED_CONTRIBUTION_STATUS = 2,
  MISSED_CONTRIBUTION_STATUS = 3,
  UNRECOGNIZED = -1,
}

export function contributionStatusFromJSON(object: any): ContributionStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_CONTRIBUTION_STATUS":
      return ContributionStatus.UNKNOWN_CONTRIBUTION_STATUS;
    case 1:
    case "PENDING_CONTRIBUTION_STATUS":
      return ContributionStatus.PENDING_CONTRIBUTION_STATUS;
    case 2:
    case "COMPLETED_CONTRIBUTION_STATUS":
      return ContributionStatus.COMPLETED_CONTRIBUTION_STATUS;
    case 3:
    case "MISSED_CONTRIBUTION_STATUS":
      return ContributionStatus.MISSED_CONTRIBUTION_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ContributionStatus.UNRECOGNIZED;
  }
}

export function contributionStatusToJSON(object: ContributionStatus): string {
  switch (object) {
    case ContributionStatus.UNKNOWN_CONTRIBUTION_STATUS:
      return "UNKNOWN_CONTRIBUTION_STATUS";
    case ContributionStatus.PENDING_CONTRIBUTION_STATUS:
      return "PENDING_CONTRIBUTION_STATUS";
    case ContributionStatus.COMPLETED_CONTRIBUTION_STATUS:
      return "COMPLETED_CONTRIBUTION_STATUS";
    case ContributionStatus.MISSED_CONTRIBUTION_STATUS:
      return "MISSED_CONTRIBUTION_STATUS";
    case ContributionStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface GoalDetails {
  id: string;
  targetAmount: number;
  currentAmount: number;
  progressPercentage: number;
  progressDescription: string;
  progressDescriptionDashboard: string;
  isReminderDateSelected: boolean;
  reminderDateOfMonth: number;
  gains: number;
  targetDurationMonths: number;
  achievementBadge: string;
  shareText: string;
  progressSubDescriptionDashboard: string;
  fdGoal: number;
  savingsAccountGoal: number;
  savingsMinLimit: number;
  savingsMaxLimit: number;
  savingsAccountAmount: number;
  fdAmount: number;
}

export interface GetGoalDetailsResponse {
  isActive: boolean;
  isGoalCreated: boolean;
  goalDetails: GoalDetails | undefined;
}

export interface GoalCreateRequest {
  goalType: GoalType;
  targetDurationMonths: number;
  income: number;
}

export interface GoalCreateResponse {
  goalDetails: GoalDetails | undefined;
  recommendedBank: RecommendedBankForGoalResponse | undefined;
}

export interface RecommendedBankForGoalResponse {
  hasDicgcLimitReached: boolean;
  isActive: boolean;
  sellingPoints: RecommendedBankSellingPointsResponse[];
  bank: BankResponse | undefined;
  highestInterestRateFd: FixedDepositResponse | undefined;
  redirectDeeplink: RedirectDeeplink | undefined;
  recommendedAmount: number;
  currentInvestedAmount: number;
}

export interface RecommendedBankSellingPointsResponse {
  iconUrl: string;
  description: string;
}

export interface GoalNotificationRequest {
  goalType: GoalType;
  dateOfMonth: number;
}

export interface GoalNotificationResponse {
  goalDetails: GoalDetails | undefined;
}

export interface RecommendedBankListResponse {
  recommendedBanks: RecommendedBankForGoalResponse[];
}

export interface GoalInvestmentListResponse {
  investments: GoalInvestmentInfo[];
}

export interface GoalInvestmentInfo {
  bank: BankResponse | undefined;
  maturityDate: string;
  bookingDate: string;
  amount: number;
  interestRate: number;
  id: string;
}

export interface GoalSetInvestmentListRequest {
  goalType: GoalType;
  investmentIds: string[];
}

export interface GoalSetInvestmentListResponse {
  goalDetails: GoalDetails | undefined;
}

export interface SelectBankRequest {
  goalType: GoalType;
  bankId: string;
}

export interface SelectBankResponse {
}

export interface UpdateSavingsAmountResponse {
}

export interface UpdateSavingsAmountRequest {
  savingsAccountAmount: number;
}

export interface EmergencyFundStepsResponse {
  steps: EmergencyFundSteps[];
  nextStep: string;
  isCompleted: boolean;
  completionPercentage: number;
}

export interface EmergencyFundSteps {
  stepName: string;
  isCompleted: boolean;
  priority: number;
}

export interface EmergencyFundQuestionnaireRequest {
  step: string;
  cityStep?: CityStep | undefined;
  familyDetailsStep?: FamilyDetailsStep | undefined;
  monthlyExpenseStep?: MonthlyExpenseStep | undefined;
  insuranceStep?: InsuranceStep | undefined;
  modifyPlanStep?: ModifyPlanStep | undefined;
  acceptPlanStep?: AcceptPlanStep | undefined;
  monthlyPledge?: MonthlyPledgeStep | undefined;
  planRecommendation?: GetPlanRecommendation | undefined;
}

export interface GetEmergencyFundResponse {
  cityId: string;
  isPayingEmi: boolean;
  isPayingRent: boolean;
  isMarried: boolean;
  hasKids: boolean;
  doParentsDependOnYou: boolean;
  monthlyExpense: number;
  hasHealthInsurance: boolean;
  hasLifeInsurance: boolean;
  goalAmount: number;
  monthlyPledge: number;
  recommendedAmount: number;
  isActive: boolean;
  city: string;
}

export interface EmergencyFundQuestionnaireResponse {
  goalAmount: number;
  recommendedBanks: RecommendedBankForGoalResponse[];
  fdGoal: number;
  savingsAccountGoal: number;
  mappedInvestmentsAmount: number;
  fdContributionAmount: number;
  savingsRecommendedPrompt: string;
  savingsMinPercentage: number;
  savingsMaxPercentage: number;
  hasMappableInvestments: boolean;
  highestFdReturn: number;
  savingsAccountAmount: number;
}

export interface GetPlanRecommendation {
  goalAmount: number;
}

export interface CityStep {
  cityId: string;
  isPayingEmi: boolean;
  isPayingRent: boolean;
}

export interface FamilyDetailsStep {
  isMarried: boolean;
  hasKids: boolean;
  doParentsDependOnYou: boolean;
}

export interface MonthlyExpenseStep {
  monthlyExpense: number;
  emiAmount: number;
}

export interface InsuranceStep {
  hasHealthInsurance: boolean;
  hasLifeInsurance: boolean;
}

export interface EmergencyFundGoal {
  goalAmount?: number | undefined;
  emiAmount: number;
  monthlyExpense: number;
  hasHealthInsurance: boolean;
  hasKids: boolean;
  isMarried: boolean;
  hasDependentParents: boolean;
  cityId: string;
}

export interface EmergencyFundMilestone {
  name: string;
  amount?: number | undefined;
  isCompleted: boolean;
}

export interface EmergencyFundResponse {
  goal: EmergencyFundGoal | undefined;
  investedAmount: number;
  milestones: EmergencyFundMilestone[];
}

export interface EmergencyFundReferralData {
  referralLink: string;
  rewards: EmergencyFundReferralReward[];
  referees: EmergencyFundReferralProgramMember[];
  referrer: EmergencyFundReferralProgramMember | undefined;
}

export interface EmergencyFundReferralReward {
  referralCount: number;
  rewardAmount: number;
}

export interface EmergencyFundReferralProgramMember {
  id: string;
  firstName: string;
  lastName: string;
  goalProgress: number;
  milestones: EmergencyFundMilestone[];
}

export interface GetRecommendedGoalAmountResponse {
  recommendedAmount: number;
}

export interface ModifyPlanStep {
  goalAmount: number;
  fdGoal: number;
  savingsAccountGoal: number;
}

export interface AcceptPlanStep {
  fdGoal: number;
  savingsAccountGoal: number;
  mapExistingInvestments: boolean;
}

export interface MonthlyPledgeStep {
  monthlyPledgeAmount: number;
}

export interface EmergencyFundDashboardResponse {
  hasDoneFirstInvestment: boolean;
  goalDetails: GetGoalDetailsResponse | undefined;
  pledge: PledgeDetail | undefined;
  recommendedBanks: RecommendedBankForGoalResponse[];
  chosenBanks: RecommendedBankForGoalResponse[];
  otherBanks: RecommendedBankForGoalResponse[];
  isNewEfUser: boolean;
  recommendedSavingsPrompt: string;
}

export interface PledgeDetail {
  monthlyPledge: number;
  contributions: Contribution[];
  nextContributionDays: number;
}

export interface Contribution {
  month: string;
  year: string;
  status: ContributionStatus;
}

function createBaseGoalDetails(): GoalDetails {
  return {
    id: "",
    targetAmount: 0,
    currentAmount: 0,
    progressPercentage: 0,
    progressDescription: "",
    progressDescriptionDashboard: "",
    isReminderDateSelected: false,
    reminderDateOfMonth: 0,
    gains: 0,
    targetDurationMonths: 0,
    achievementBadge: "",
    shareText: "",
    progressSubDescriptionDashboard: "",
    fdGoal: 0,
    savingsAccountGoal: 0,
    savingsMinLimit: 0,
    savingsMaxLimit: 0,
    savingsAccountAmount: 0,
    fdAmount: 0,
  };
}

export const GoalDetails: MessageFns<GoalDetails> = {
  encode(message: GoalDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.targetAmount !== 0) {
      writer.uint32(17).double(message.targetAmount);
    }
    if (message.currentAmount !== 0) {
      writer.uint32(25).double(message.currentAmount);
    }
    if (message.progressPercentage !== 0) {
      writer.uint32(33).double(message.progressPercentage);
    }
    if (message.progressDescription !== "") {
      writer.uint32(42).string(message.progressDescription);
    }
    if (message.progressDescriptionDashboard !== "") {
      writer.uint32(50).string(message.progressDescriptionDashboard);
    }
    if (message.isReminderDateSelected !== false) {
      writer.uint32(56).bool(message.isReminderDateSelected);
    }
    if (message.reminderDateOfMonth !== 0) {
      writer.uint32(64).int32(message.reminderDateOfMonth);
    }
    if (message.gains !== 0) {
      writer.uint32(73).double(message.gains);
    }
    if (message.targetDurationMonths !== 0) {
      writer.uint32(80).int32(message.targetDurationMonths);
    }
    if (message.achievementBadge !== "") {
      writer.uint32(90).string(message.achievementBadge);
    }
    if (message.shareText !== "") {
      writer.uint32(98).string(message.shareText);
    }
    if (message.progressSubDescriptionDashboard !== "") {
      writer.uint32(106).string(message.progressSubDescriptionDashboard);
    }
    if (message.fdGoal !== 0) {
      writer.uint32(113).double(message.fdGoal);
    }
    if (message.savingsAccountGoal !== 0) {
      writer.uint32(121).double(message.savingsAccountGoal);
    }
    if (message.savingsMinLimit !== 0) {
      writer.uint32(129).double(message.savingsMinLimit);
    }
    if (message.savingsMaxLimit !== 0) {
      writer.uint32(137).double(message.savingsMaxLimit);
    }
    if (message.savingsAccountAmount !== 0) {
      writer.uint32(145).double(message.savingsAccountAmount);
    }
    if (message.fdAmount !== 0) {
      writer.uint32(153).double(message.fdAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoalDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoalDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.targetAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.currentAmount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.progressPercentage = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.progressDescription = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.progressDescriptionDashboard = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.isReminderDateSelected = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.reminderDateOfMonth = reader.int32();
          continue;
        }
        case 9: {
          if (tag !== 73) {
            break;
          }

          message.gains = reader.double();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.targetDurationMonths = reader.int32();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.achievementBadge = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.shareText = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.progressSubDescriptionDashboard = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 113) {
            break;
          }

          message.fdGoal = reader.double();
          continue;
        }
        case 15: {
          if (tag !== 121) {
            break;
          }

          message.savingsAccountGoal = reader.double();
          continue;
        }
        case 16: {
          if (tag !== 129) {
            break;
          }

          message.savingsMinLimit = reader.double();
          continue;
        }
        case 17: {
          if (tag !== 137) {
            break;
          }

          message.savingsMaxLimit = reader.double();
          continue;
        }
        case 18: {
          if (tag !== 145) {
            break;
          }

          message.savingsAccountAmount = reader.double();
          continue;
        }
        case 19: {
          if (tag !== 153) {
            break;
          }

          message.fdAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoalDetails {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      targetAmount: isSet(object.targetAmount) ? globalThis.Number(object.targetAmount) : 0,
      currentAmount: isSet(object.currentAmount) ? globalThis.Number(object.currentAmount) : 0,
      progressPercentage: isSet(object.progressPercentage) ? globalThis.Number(object.progressPercentage) : 0,
      progressDescription: isSet(object.progressDescription) ? globalThis.String(object.progressDescription) : "",
      progressDescriptionDashboard: isSet(object.progressDescriptionDashboard)
        ? globalThis.String(object.progressDescriptionDashboard)
        : "",
      isReminderDateSelected: isSet(object.isReminderDateSelected)
        ? globalThis.Boolean(object.isReminderDateSelected)
        : false,
      reminderDateOfMonth: isSet(object.reminderDateOfMonth) ? globalThis.Number(object.reminderDateOfMonth) : 0,
      gains: isSet(object.gains) ? globalThis.Number(object.gains) : 0,
      targetDurationMonths: isSet(object.targetDurationMonths) ? globalThis.Number(object.targetDurationMonths) : 0,
      achievementBadge: isSet(object.achievementBadge) ? globalThis.String(object.achievementBadge) : "",
      shareText: isSet(object.shareText) ? globalThis.String(object.shareText) : "",
      progressSubDescriptionDashboard: isSet(object.progressSubDescriptionDashboard)
        ? globalThis.String(object.progressSubDescriptionDashboard)
        : "",
      fdGoal: isSet(object.fdGoal) ? globalThis.Number(object.fdGoal) : 0,
      savingsAccountGoal: isSet(object.savingsAccountGoal) ? globalThis.Number(object.savingsAccountGoal) : 0,
      savingsMinLimit: isSet(object.savingsMinLimit) ? globalThis.Number(object.savingsMinLimit) : 0,
      savingsMaxLimit: isSet(object.savingsMaxLimit) ? globalThis.Number(object.savingsMaxLimit) : 0,
      savingsAccountAmount: isSet(object.savingsAccountAmount) ? globalThis.Number(object.savingsAccountAmount) : 0,
      fdAmount: isSet(object.fdAmount) ? globalThis.Number(object.fdAmount) : 0,
    };
  },

  toJSON(message: GoalDetails): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.targetAmount !== 0) {
      obj.targetAmount = message.targetAmount;
    }
    if (message.currentAmount !== 0) {
      obj.currentAmount = message.currentAmount;
    }
    if (message.progressPercentage !== 0) {
      obj.progressPercentage = message.progressPercentage;
    }
    if (message.progressDescription !== "") {
      obj.progressDescription = message.progressDescription;
    }
    if (message.progressDescriptionDashboard !== "") {
      obj.progressDescriptionDashboard = message.progressDescriptionDashboard;
    }
    if (message.isReminderDateSelected !== false) {
      obj.isReminderDateSelected = message.isReminderDateSelected;
    }
    if (message.reminderDateOfMonth !== 0) {
      obj.reminderDateOfMonth = Math.round(message.reminderDateOfMonth);
    }
    if (message.gains !== 0) {
      obj.gains = message.gains;
    }
    if (message.targetDurationMonths !== 0) {
      obj.targetDurationMonths = Math.round(message.targetDurationMonths);
    }
    if (message.achievementBadge !== "") {
      obj.achievementBadge = message.achievementBadge;
    }
    if (message.shareText !== "") {
      obj.shareText = message.shareText;
    }
    if (message.progressSubDescriptionDashboard !== "") {
      obj.progressSubDescriptionDashboard = message.progressSubDescriptionDashboard;
    }
    if (message.fdGoal !== 0) {
      obj.fdGoal = message.fdGoal;
    }
    if (message.savingsAccountGoal !== 0) {
      obj.savingsAccountGoal = message.savingsAccountGoal;
    }
    if (message.savingsMinLimit !== 0) {
      obj.savingsMinLimit = message.savingsMinLimit;
    }
    if (message.savingsMaxLimit !== 0) {
      obj.savingsMaxLimit = message.savingsMaxLimit;
    }
    if (message.savingsAccountAmount !== 0) {
      obj.savingsAccountAmount = message.savingsAccountAmount;
    }
    if (message.fdAmount !== 0) {
      obj.fdAmount = message.fdAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoalDetails>, I>>(base?: I): GoalDetails {
    return GoalDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoalDetails>, I>>(object: I): GoalDetails {
    const message = createBaseGoalDetails();
    message.id = object.id ?? "";
    message.targetAmount = object.targetAmount ?? 0;
    message.currentAmount = object.currentAmount ?? 0;
    message.progressPercentage = object.progressPercentage ?? 0;
    message.progressDescription = object.progressDescription ?? "";
    message.progressDescriptionDashboard = object.progressDescriptionDashboard ?? "";
    message.isReminderDateSelected = object.isReminderDateSelected ?? false;
    message.reminderDateOfMonth = object.reminderDateOfMonth ?? 0;
    message.gains = object.gains ?? 0;
    message.targetDurationMonths = object.targetDurationMonths ?? 0;
    message.achievementBadge = object.achievementBadge ?? "";
    message.shareText = object.shareText ?? "";
    message.progressSubDescriptionDashboard = object.progressSubDescriptionDashboard ?? "";
    message.fdGoal = object.fdGoal ?? 0;
    message.savingsAccountGoal = object.savingsAccountGoal ?? 0;
    message.savingsMinLimit = object.savingsMinLimit ?? 0;
    message.savingsMaxLimit = object.savingsMaxLimit ?? 0;
    message.savingsAccountAmount = object.savingsAccountAmount ?? 0;
    message.fdAmount = object.fdAmount ?? 0;
    return message;
  },
};

function createBaseGetGoalDetailsResponse(): GetGoalDetailsResponse {
  return { isActive: false, isGoalCreated: false, goalDetails: undefined };
}

export const GetGoalDetailsResponse: MessageFns<GetGoalDetailsResponse> = {
  encode(message: GetGoalDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isActive !== false) {
      writer.uint32(8).bool(message.isActive);
    }
    if (message.isGoalCreated !== false) {
      writer.uint32(16).bool(message.isGoalCreated);
    }
    if (message.goalDetails !== undefined) {
      GoalDetails.encode(message.goalDetails, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetGoalDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetGoalDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isGoalCreated = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.goalDetails = GoalDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetGoalDetailsResponse {
    return {
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      isGoalCreated: isSet(object.isGoalCreated) ? globalThis.Boolean(object.isGoalCreated) : false,
      goalDetails: isSet(object.goalDetails) ? GoalDetails.fromJSON(object.goalDetails) : undefined,
    };
  },

  toJSON(message: GetGoalDetailsResponse): unknown {
    const obj: any = {};
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.isGoalCreated !== false) {
      obj.isGoalCreated = message.isGoalCreated;
    }
    if (message.goalDetails !== undefined) {
      obj.goalDetails = GoalDetails.toJSON(message.goalDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetGoalDetailsResponse>, I>>(base?: I): GetGoalDetailsResponse {
    return GetGoalDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGoalDetailsResponse>, I>>(object: I): GetGoalDetailsResponse {
    const message = createBaseGetGoalDetailsResponse();
    message.isActive = object.isActive ?? false;
    message.isGoalCreated = object.isGoalCreated ?? false;
    message.goalDetails = (object.goalDetails !== undefined && object.goalDetails !== null)
      ? GoalDetails.fromPartial(object.goalDetails)
      : undefined;
    return message;
  },
};

function createBaseGoalCreateRequest(): GoalCreateRequest {
  return { goalType: 0, targetDurationMonths: 0, income: 0 };
}

export const GoalCreateRequest: MessageFns<GoalCreateRequest> = {
  encode(message: GoalCreateRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalType !== 0) {
      writer.uint32(8).int32(message.goalType);
    }
    if (message.targetDurationMonths !== 0) {
      writer.uint32(16).int32(message.targetDurationMonths);
    }
    if (message.income !== 0) {
      writer.uint32(25).double(message.income);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoalCreateRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoalCreateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.goalType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.targetDurationMonths = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.income = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoalCreateRequest {
    return {
      goalType: isSet(object.goalType) ? goalTypeFromJSON(object.goalType) : 0,
      targetDurationMonths: isSet(object.targetDurationMonths) ? globalThis.Number(object.targetDurationMonths) : 0,
      income: isSet(object.income) ? globalThis.Number(object.income) : 0,
    };
  },

  toJSON(message: GoalCreateRequest): unknown {
    const obj: any = {};
    if (message.goalType !== 0) {
      obj.goalType = goalTypeToJSON(message.goalType);
    }
    if (message.targetDurationMonths !== 0) {
      obj.targetDurationMonths = Math.round(message.targetDurationMonths);
    }
    if (message.income !== 0) {
      obj.income = message.income;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoalCreateRequest>, I>>(base?: I): GoalCreateRequest {
    return GoalCreateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoalCreateRequest>, I>>(object: I): GoalCreateRequest {
    const message = createBaseGoalCreateRequest();
    message.goalType = object.goalType ?? 0;
    message.targetDurationMonths = object.targetDurationMonths ?? 0;
    message.income = object.income ?? 0;
    return message;
  },
};

function createBaseGoalCreateResponse(): GoalCreateResponse {
  return { goalDetails: undefined, recommendedBank: undefined };
}

export const GoalCreateResponse: MessageFns<GoalCreateResponse> = {
  encode(message: GoalCreateResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalDetails !== undefined) {
      GoalDetails.encode(message.goalDetails, writer.uint32(10).fork()).join();
    }
    if (message.recommendedBank !== undefined) {
      RecommendedBankForGoalResponse.encode(message.recommendedBank, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoalCreateResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoalCreateResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.goalDetails = GoalDetails.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recommendedBank = RecommendedBankForGoalResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoalCreateResponse {
    return {
      goalDetails: isSet(object.goalDetails) ? GoalDetails.fromJSON(object.goalDetails) : undefined,
      recommendedBank: isSet(object.recommendedBank)
        ? RecommendedBankForGoalResponse.fromJSON(object.recommendedBank)
        : undefined,
    };
  },

  toJSON(message: GoalCreateResponse): unknown {
    const obj: any = {};
    if (message.goalDetails !== undefined) {
      obj.goalDetails = GoalDetails.toJSON(message.goalDetails);
    }
    if (message.recommendedBank !== undefined) {
      obj.recommendedBank = RecommendedBankForGoalResponse.toJSON(message.recommendedBank);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoalCreateResponse>, I>>(base?: I): GoalCreateResponse {
    return GoalCreateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoalCreateResponse>, I>>(object: I): GoalCreateResponse {
    const message = createBaseGoalCreateResponse();
    message.goalDetails = (object.goalDetails !== undefined && object.goalDetails !== null)
      ? GoalDetails.fromPartial(object.goalDetails)
      : undefined;
    message.recommendedBank = (object.recommendedBank !== undefined && object.recommendedBank !== null)
      ? RecommendedBankForGoalResponse.fromPartial(object.recommendedBank)
      : undefined;
    return message;
  },
};

function createBaseRecommendedBankForGoalResponse(): RecommendedBankForGoalResponse {
  return {
    hasDicgcLimitReached: false,
    isActive: false,
    sellingPoints: [],
    bank: undefined,
    highestInterestRateFd: undefined,
    redirectDeeplink: undefined,
    recommendedAmount: 0,
    currentInvestedAmount: 0,
  };
}

export const RecommendedBankForGoalResponse: MessageFns<RecommendedBankForGoalResponse> = {
  encode(message: RecommendedBankForGoalResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hasDicgcLimitReached !== false) {
      writer.uint32(8).bool(message.hasDicgcLimitReached);
    }
    if (message.isActive !== false) {
      writer.uint32(16).bool(message.isActive);
    }
    for (const v of message.sellingPoints) {
      RecommendedBankSellingPointsResponse.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.bank !== undefined) {
      BankResponse.encode(message.bank, writer.uint32(34).fork()).join();
    }
    if (message.highestInterestRateFd !== undefined) {
      FixedDepositResponse.encode(message.highestInterestRateFd, writer.uint32(42).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(50).fork()).join();
    }
    if (message.recommendedAmount !== 0) {
      writer.uint32(57).double(message.recommendedAmount);
    }
    if (message.currentInvestedAmount !== 0) {
      writer.uint32(65).double(message.currentInvestedAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendedBankForGoalResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendedBankForGoalResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.hasDicgcLimitReached = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.sellingPoints.push(RecommendedBankSellingPointsResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bank = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.highestInterestRateFd = FixedDepositResponse.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.recommendedAmount = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.currentInvestedAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecommendedBankForGoalResponse {
    return {
      hasDicgcLimitReached: isSet(object.hasDicgcLimitReached)
        ? globalThis.Boolean(object.hasDicgcLimitReached)
        : false,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      sellingPoints: globalThis.Array.isArray(object?.sellingPoints)
        ? object.sellingPoints.map((e: any) => RecommendedBankSellingPointsResponse.fromJSON(e))
        : [],
      bank: isSet(object.bank) ? BankResponse.fromJSON(object.bank) : undefined,
      highestInterestRateFd: isSet(object.highestInterestRateFd)
        ? FixedDepositResponse.fromJSON(object.highestInterestRateFd)
        : undefined,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      recommendedAmount: isSet(object.recommendedAmount) ? globalThis.Number(object.recommendedAmount) : 0,
      currentInvestedAmount: isSet(object.currentInvestedAmount) ? globalThis.Number(object.currentInvestedAmount) : 0,
    };
  },

  toJSON(message: RecommendedBankForGoalResponse): unknown {
    const obj: any = {};
    if (message.hasDicgcLimitReached !== false) {
      obj.hasDicgcLimitReached = message.hasDicgcLimitReached;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.sellingPoints?.length) {
      obj.sellingPoints = message.sellingPoints.map((e) => RecommendedBankSellingPointsResponse.toJSON(e));
    }
    if (message.bank !== undefined) {
      obj.bank = BankResponse.toJSON(message.bank);
    }
    if (message.highestInterestRateFd !== undefined) {
      obj.highestInterestRateFd = FixedDepositResponse.toJSON(message.highestInterestRateFd);
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.recommendedAmount !== 0) {
      obj.recommendedAmount = message.recommendedAmount;
    }
    if (message.currentInvestedAmount !== 0) {
      obj.currentInvestedAmount = message.currentInvestedAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendedBankForGoalResponse>, I>>(base?: I): RecommendedBankForGoalResponse {
    return RecommendedBankForGoalResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendedBankForGoalResponse>, I>>(
    object: I,
  ): RecommendedBankForGoalResponse {
    const message = createBaseRecommendedBankForGoalResponse();
    message.hasDicgcLimitReached = object.hasDicgcLimitReached ?? false;
    message.isActive = object.isActive ?? false;
    message.sellingPoints = object.sellingPoints?.map((e) => RecommendedBankSellingPointsResponse.fromPartial(e)) || [];
    message.bank = (object.bank !== undefined && object.bank !== null)
      ? BankResponse.fromPartial(object.bank)
      : undefined;
    message.highestInterestRateFd =
      (object.highestInterestRateFd !== undefined && object.highestInterestRateFd !== null)
        ? FixedDepositResponse.fromPartial(object.highestInterestRateFd)
        : undefined;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.recommendedAmount = object.recommendedAmount ?? 0;
    message.currentInvestedAmount = object.currentInvestedAmount ?? 0;
    return message;
  },
};

function createBaseRecommendedBankSellingPointsResponse(): RecommendedBankSellingPointsResponse {
  return { iconUrl: "", description: "" };
}

export const RecommendedBankSellingPointsResponse: MessageFns<RecommendedBankSellingPointsResponse> = {
  encode(message: RecommendedBankSellingPointsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.iconUrl !== "") {
      writer.uint32(10).string(message.iconUrl);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendedBankSellingPointsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendedBankSellingPointsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecommendedBankSellingPointsResponse {
    return {
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
    };
  },

  toJSON(message: RecommendedBankSellingPointsResponse): unknown {
    const obj: any = {};
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendedBankSellingPointsResponse>, I>>(
    base?: I,
  ): RecommendedBankSellingPointsResponse {
    return RecommendedBankSellingPointsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendedBankSellingPointsResponse>, I>>(
    object: I,
  ): RecommendedBankSellingPointsResponse {
    const message = createBaseRecommendedBankSellingPointsResponse();
    message.iconUrl = object.iconUrl ?? "";
    message.description = object.description ?? "";
    return message;
  },
};

function createBaseGoalNotificationRequest(): GoalNotificationRequest {
  return { goalType: 0, dateOfMonth: 0 };
}

export const GoalNotificationRequest: MessageFns<GoalNotificationRequest> = {
  encode(message: GoalNotificationRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalType !== 0) {
      writer.uint32(8).int32(message.goalType);
    }
    if (message.dateOfMonth !== 0) {
      writer.uint32(16).int32(message.dateOfMonth);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoalNotificationRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoalNotificationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.goalType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.dateOfMonth = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoalNotificationRequest {
    return {
      goalType: isSet(object.goalType) ? goalTypeFromJSON(object.goalType) : 0,
      dateOfMonth: isSet(object.dateOfMonth) ? globalThis.Number(object.dateOfMonth) : 0,
    };
  },

  toJSON(message: GoalNotificationRequest): unknown {
    const obj: any = {};
    if (message.goalType !== 0) {
      obj.goalType = goalTypeToJSON(message.goalType);
    }
    if (message.dateOfMonth !== 0) {
      obj.dateOfMonth = Math.round(message.dateOfMonth);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoalNotificationRequest>, I>>(base?: I): GoalNotificationRequest {
    return GoalNotificationRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoalNotificationRequest>, I>>(object: I): GoalNotificationRequest {
    const message = createBaseGoalNotificationRequest();
    message.goalType = object.goalType ?? 0;
    message.dateOfMonth = object.dateOfMonth ?? 0;
    return message;
  },
};

function createBaseGoalNotificationResponse(): GoalNotificationResponse {
  return { goalDetails: undefined };
}

export const GoalNotificationResponse: MessageFns<GoalNotificationResponse> = {
  encode(message: GoalNotificationResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalDetails !== undefined) {
      GoalDetails.encode(message.goalDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoalNotificationResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoalNotificationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.goalDetails = GoalDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoalNotificationResponse {
    return { goalDetails: isSet(object.goalDetails) ? GoalDetails.fromJSON(object.goalDetails) : undefined };
  },

  toJSON(message: GoalNotificationResponse): unknown {
    const obj: any = {};
    if (message.goalDetails !== undefined) {
      obj.goalDetails = GoalDetails.toJSON(message.goalDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoalNotificationResponse>, I>>(base?: I): GoalNotificationResponse {
    return GoalNotificationResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoalNotificationResponse>, I>>(object: I): GoalNotificationResponse {
    const message = createBaseGoalNotificationResponse();
    message.goalDetails = (object.goalDetails !== undefined && object.goalDetails !== null)
      ? GoalDetails.fromPartial(object.goalDetails)
      : undefined;
    return message;
  },
};

function createBaseRecommendedBankListResponse(): RecommendedBankListResponse {
  return { recommendedBanks: [] };
}

export const RecommendedBankListResponse: MessageFns<RecommendedBankListResponse> = {
  encode(message: RecommendedBankListResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.recommendedBanks) {
      RecommendedBankForGoalResponse.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendedBankListResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendedBankListResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recommendedBanks.push(RecommendedBankForGoalResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecommendedBankListResponse {
    return {
      recommendedBanks: globalThis.Array.isArray(object?.recommendedBanks)
        ? object.recommendedBanks.map((e: any) => RecommendedBankForGoalResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: RecommendedBankListResponse): unknown {
    const obj: any = {};
    if (message.recommendedBanks?.length) {
      obj.recommendedBanks = message.recommendedBanks.map((e) => RecommendedBankForGoalResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendedBankListResponse>, I>>(base?: I): RecommendedBankListResponse {
    return RecommendedBankListResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendedBankListResponse>, I>>(object: I): RecommendedBankListResponse {
    const message = createBaseRecommendedBankListResponse();
    message.recommendedBanks = object.recommendedBanks?.map((e) => RecommendedBankForGoalResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGoalInvestmentListResponse(): GoalInvestmentListResponse {
  return { investments: [] };
}

export const GoalInvestmentListResponse: MessageFns<GoalInvestmentListResponse> = {
  encode(message: GoalInvestmentListResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.investments) {
      GoalInvestmentInfo.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoalInvestmentListResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoalInvestmentListResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.investments.push(GoalInvestmentInfo.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoalInvestmentListResponse {
    return {
      investments: globalThis.Array.isArray(object?.investments)
        ? object.investments.map((e: any) => GoalInvestmentInfo.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GoalInvestmentListResponse): unknown {
    const obj: any = {};
    if (message.investments?.length) {
      obj.investments = message.investments.map((e) => GoalInvestmentInfo.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoalInvestmentListResponse>, I>>(base?: I): GoalInvestmentListResponse {
    return GoalInvestmentListResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoalInvestmentListResponse>, I>>(object: I): GoalInvestmentListResponse {
    const message = createBaseGoalInvestmentListResponse();
    message.investments = object.investments?.map((e) => GoalInvestmentInfo.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGoalInvestmentInfo(): GoalInvestmentInfo {
  return { bank: undefined, maturityDate: "", bookingDate: "", amount: 0, interestRate: 0, id: "" };
}

export const GoalInvestmentInfo: MessageFns<GoalInvestmentInfo> = {
  encode(message: GoalInvestmentInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bank !== undefined) {
      BankResponse.encode(message.bank, writer.uint32(10).fork()).join();
    }
    if (message.maturityDate !== "") {
      writer.uint32(18).string(message.maturityDate);
    }
    if (message.bookingDate !== "") {
      writer.uint32(26).string(message.bookingDate);
    }
    if (message.amount !== 0) {
      writer.uint32(33).double(message.amount);
    }
    if (message.interestRate !== 0) {
      writer.uint32(41).double(message.interestRate);
    }
    if (message.id !== "") {
      writer.uint32(50).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoalInvestmentInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoalInvestmentInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bank = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.maturityDate = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bookingDate = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.interestRate = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoalInvestmentInfo {
    return {
      bank: isSet(object.bank) ? BankResponse.fromJSON(object.bank) : undefined,
      maturityDate: isSet(object.maturityDate) ? globalThis.String(object.maturityDate) : "",
      bookingDate: isSet(object.bookingDate) ? globalThis.String(object.bookingDate) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      interestRate: isSet(object.interestRate) ? globalThis.Number(object.interestRate) : 0,
      id: isSet(object.id) ? globalThis.String(object.id) : "",
    };
  },

  toJSON(message: GoalInvestmentInfo): unknown {
    const obj: any = {};
    if (message.bank !== undefined) {
      obj.bank = BankResponse.toJSON(message.bank);
    }
    if (message.maturityDate !== "") {
      obj.maturityDate = message.maturityDate;
    }
    if (message.bookingDate !== "") {
      obj.bookingDate = message.bookingDate;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.interestRate !== 0) {
      obj.interestRate = message.interestRate;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoalInvestmentInfo>, I>>(base?: I): GoalInvestmentInfo {
    return GoalInvestmentInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoalInvestmentInfo>, I>>(object: I): GoalInvestmentInfo {
    const message = createBaseGoalInvestmentInfo();
    message.bank = (object.bank !== undefined && object.bank !== null)
      ? BankResponse.fromPartial(object.bank)
      : undefined;
    message.maturityDate = object.maturityDate ?? "";
    message.bookingDate = object.bookingDate ?? "";
    message.amount = object.amount ?? 0;
    message.interestRate = object.interestRate ?? 0;
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseGoalSetInvestmentListRequest(): GoalSetInvestmentListRequest {
  return { goalType: 0, investmentIds: [] };
}

export const GoalSetInvestmentListRequest: MessageFns<GoalSetInvestmentListRequest> = {
  encode(message: GoalSetInvestmentListRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalType !== 0) {
      writer.uint32(8).int32(message.goalType);
    }
    for (const v of message.investmentIds) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoalSetInvestmentListRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoalSetInvestmentListRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.goalType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.investmentIds.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoalSetInvestmentListRequest {
    return {
      goalType: isSet(object.goalType) ? goalTypeFromJSON(object.goalType) : 0,
      investmentIds: globalThis.Array.isArray(object?.investmentIds)
        ? object.investmentIds.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: GoalSetInvestmentListRequest): unknown {
    const obj: any = {};
    if (message.goalType !== 0) {
      obj.goalType = goalTypeToJSON(message.goalType);
    }
    if (message.investmentIds?.length) {
      obj.investmentIds = message.investmentIds;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoalSetInvestmentListRequest>, I>>(base?: I): GoalSetInvestmentListRequest {
    return GoalSetInvestmentListRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoalSetInvestmentListRequest>, I>>(object: I): GoalSetInvestmentListRequest {
    const message = createBaseGoalSetInvestmentListRequest();
    message.goalType = object.goalType ?? 0;
    message.investmentIds = object.investmentIds?.map((e) => e) || [];
    return message;
  },
};

function createBaseGoalSetInvestmentListResponse(): GoalSetInvestmentListResponse {
  return { goalDetails: undefined };
}

export const GoalSetInvestmentListResponse: MessageFns<GoalSetInvestmentListResponse> = {
  encode(message: GoalSetInvestmentListResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalDetails !== undefined) {
      GoalDetails.encode(message.goalDetails, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GoalSetInvestmentListResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoalSetInvestmentListResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.goalDetails = GoalDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoalSetInvestmentListResponse {
    return { goalDetails: isSet(object.goalDetails) ? GoalDetails.fromJSON(object.goalDetails) : undefined };
  },

  toJSON(message: GoalSetInvestmentListResponse): unknown {
    const obj: any = {};
    if (message.goalDetails !== undefined) {
      obj.goalDetails = GoalDetails.toJSON(message.goalDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoalSetInvestmentListResponse>, I>>(base?: I): GoalSetInvestmentListResponse {
    return GoalSetInvestmentListResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoalSetInvestmentListResponse>, I>>(
    object: I,
  ): GoalSetInvestmentListResponse {
    const message = createBaseGoalSetInvestmentListResponse();
    message.goalDetails = (object.goalDetails !== undefined && object.goalDetails !== null)
      ? GoalDetails.fromPartial(object.goalDetails)
      : undefined;
    return message;
  },
};

function createBaseSelectBankRequest(): SelectBankRequest {
  return { goalType: 0, bankId: "" };
}

export const SelectBankRequest: MessageFns<SelectBankRequest> = {
  encode(message: SelectBankRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalType !== 0) {
      writer.uint32(8).int32(message.goalType);
    }
    if (message.bankId !== "") {
      writer.uint32(18).string(message.bankId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SelectBankRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSelectBankRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.goalType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SelectBankRequest {
    return {
      goalType: isSet(object.goalType) ? goalTypeFromJSON(object.goalType) : 0,
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
    };
  },

  toJSON(message: SelectBankRequest): unknown {
    const obj: any = {};
    if (message.goalType !== 0) {
      obj.goalType = goalTypeToJSON(message.goalType);
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SelectBankRequest>, I>>(base?: I): SelectBankRequest {
    return SelectBankRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SelectBankRequest>, I>>(object: I): SelectBankRequest {
    const message = createBaseSelectBankRequest();
    message.goalType = object.goalType ?? 0;
    message.bankId = object.bankId ?? "";
    return message;
  },
};

function createBaseSelectBankResponse(): SelectBankResponse {
  return {};
}

export const SelectBankResponse: MessageFns<SelectBankResponse> = {
  encode(_: SelectBankResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SelectBankResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSelectBankResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): SelectBankResponse {
    return {};
  },

  toJSON(_: SelectBankResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SelectBankResponse>, I>>(base?: I): SelectBankResponse {
    return SelectBankResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SelectBankResponse>, I>>(_: I): SelectBankResponse {
    const message = createBaseSelectBankResponse();
    return message;
  },
};

function createBaseUpdateSavingsAmountResponse(): UpdateSavingsAmountResponse {
  return {};
}

export const UpdateSavingsAmountResponse: MessageFns<UpdateSavingsAmountResponse> = {
  encode(_: UpdateSavingsAmountResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateSavingsAmountResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateSavingsAmountResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateSavingsAmountResponse {
    return {};
  },

  toJSON(_: UpdateSavingsAmountResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateSavingsAmountResponse>, I>>(base?: I): UpdateSavingsAmountResponse {
    return UpdateSavingsAmountResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateSavingsAmountResponse>, I>>(_: I): UpdateSavingsAmountResponse {
    const message = createBaseUpdateSavingsAmountResponse();
    return message;
  },
};

function createBaseUpdateSavingsAmountRequest(): UpdateSavingsAmountRequest {
  return { savingsAccountAmount: 0 };
}

export const UpdateSavingsAmountRequest: MessageFns<UpdateSavingsAmountRequest> = {
  encode(message: UpdateSavingsAmountRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.savingsAccountAmount !== 0) {
      writer.uint32(9).double(message.savingsAccountAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateSavingsAmountRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateSavingsAmountRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.savingsAccountAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateSavingsAmountRequest {
    return {
      savingsAccountAmount: isSet(object.savingsAccountAmount) ? globalThis.Number(object.savingsAccountAmount) : 0,
    };
  },

  toJSON(message: UpdateSavingsAmountRequest): unknown {
    const obj: any = {};
    if (message.savingsAccountAmount !== 0) {
      obj.savingsAccountAmount = message.savingsAccountAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateSavingsAmountRequest>, I>>(base?: I): UpdateSavingsAmountRequest {
    return UpdateSavingsAmountRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateSavingsAmountRequest>, I>>(object: I): UpdateSavingsAmountRequest {
    const message = createBaseUpdateSavingsAmountRequest();
    message.savingsAccountAmount = object.savingsAccountAmount ?? 0;
    return message;
  },
};

function createBaseEmergencyFundStepsResponse(): EmergencyFundStepsResponse {
  return { steps: [], nextStep: "", isCompleted: false, completionPercentage: 0 };
}

export const EmergencyFundStepsResponse: MessageFns<EmergencyFundStepsResponse> = {
  encode(message: EmergencyFundStepsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.steps) {
      EmergencyFundSteps.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextStep !== "") {
      writer.uint32(18).string(message.nextStep);
    }
    if (message.isCompleted !== false) {
      writer.uint32(24).bool(message.isCompleted);
    }
    if (message.completionPercentage !== 0) {
      writer.uint32(33).double(message.completionPercentage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundStepsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundStepsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.steps.push(EmergencyFundSteps.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextStep = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isCompleted = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.completionPercentage = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundStepsResponse {
    return {
      steps: globalThis.Array.isArray(object?.steps)
        ? object.steps.map((e: any) => EmergencyFundSteps.fromJSON(e))
        : [],
      nextStep: isSet(object.nextStep) ? globalThis.String(object.nextStep) : "",
      isCompleted: isSet(object.isCompleted) ? globalThis.Boolean(object.isCompleted) : false,
      completionPercentage: isSet(object.completionPercentage) ? globalThis.Number(object.completionPercentage) : 0,
    };
  },

  toJSON(message: EmergencyFundStepsResponse): unknown {
    const obj: any = {};
    if (message.steps?.length) {
      obj.steps = message.steps.map((e) => EmergencyFundSteps.toJSON(e));
    }
    if (message.nextStep !== "") {
      obj.nextStep = message.nextStep;
    }
    if (message.isCompleted !== false) {
      obj.isCompleted = message.isCompleted;
    }
    if (message.completionPercentage !== 0) {
      obj.completionPercentage = message.completionPercentage;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundStepsResponse>, I>>(base?: I): EmergencyFundStepsResponse {
    return EmergencyFundStepsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundStepsResponse>, I>>(object: I): EmergencyFundStepsResponse {
    const message = createBaseEmergencyFundStepsResponse();
    message.steps = object.steps?.map((e) => EmergencyFundSteps.fromPartial(e)) || [];
    message.nextStep = object.nextStep ?? "";
    message.isCompleted = object.isCompleted ?? false;
    message.completionPercentage = object.completionPercentage ?? 0;
    return message;
  },
};

function createBaseEmergencyFundSteps(): EmergencyFundSteps {
  return { stepName: "", isCompleted: false, priority: 0 };
}

export const EmergencyFundSteps: MessageFns<EmergencyFundSteps> = {
  encode(message: EmergencyFundSteps, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.stepName !== "") {
      writer.uint32(10).string(message.stepName);
    }
    if (message.isCompleted !== false) {
      writer.uint32(16).bool(message.isCompleted);
    }
    if (message.priority !== 0) {
      writer.uint32(24).int32(message.priority);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundSteps {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundSteps();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.stepName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isCompleted = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundSteps {
    return {
      stepName: isSet(object.stepName) ? globalThis.String(object.stepName) : "",
      isCompleted: isSet(object.isCompleted) ? globalThis.Boolean(object.isCompleted) : false,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
    };
  },

  toJSON(message: EmergencyFundSteps): unknown {
    const obj: any = {};
    if (message.stepName !== "") {
      obj.stepName = message.stepName;
    }
    if (message.isCompleted !== false) {
      obj.isCompleted = message.isCompleted;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundSteps>, I>>(base?: I): EmergencyFundSteps {
    return EmergencyFundSteps.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundSteps>, I>>(object: I): EmergencyFundSteps {
    const message = createBaseEmergencyFundSteps();
    message.stepName = object.stepName ?? "";
    message.isCompleted = object.isCompleted ?? false;
    message.priority = object.priority ?? 0;
    return message;
  },
};

function createBaseEmergencyFundQuestionnaireRequest(): EmergencyFundQuestionnaireRequest {
  return {
    step: "",
    cityStep: undefined,
    familyDetailsStep: undefined,
    monthlyExpenseStep: undefined,
    insuranceStep: undefined,
    modifyPlanStep: undefined,
    acceptPlanStep: undefined,
    monthlyPledge: undefined,
    planRecommendation: undefined,
  };
}

export const EmergencyFundQuestionnaireRequest: MessageFns<EmergencyFundQuestionnaireRequest> = {
  encode(message: EmergencyFundQuestionnaireRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.step !== "") {
      writer.uint32(10).string(message.step);
    }
    if (message.cityStep !== undefined) {
      CityStep.encode(message.cityStep, writer.uint32(18).fork()).join();
    }
    if (message.familyDetailsStep !== undefined) {
      FamilyDetailsStep.encode(message.familyDetailsStep, writer.uint32(26).fork()).join();
    }
    if (message.monthlyExpenseStep !== undefined) {
      MonthlyExpenseStep.encode(message.monthlyExpenseStep, writer.uint32(34).fork()).join();
    }
    if (message.insuranceStep !== undefined) {
      InsuranceStep.encode(message.insuranceStep, writer.uint32(42).fork()).join();
    }
    if (message.modifyPlanStep !== undefined) {
      ModifyPlanStep.encode(message.modifyPlanStep, writer.uint32(50).fork()).join();
    }
    if (message.acceptPlanStep !== undefined) {
      AcceptPlanStep.encode(message.acceptPlanStep, writer.uint32(58).fork()).join();
    }
    if (message.monthlyPledge !== undefined) {
      MonthlyPledgeStep.encode(message.monthlyPledge, writer.uint32(66).fork()).join();
    }
    if (message.planRecommendation !== undefined) {
      GetPlanRecommendation.encode(message.planRecommendation, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundQuestionnaireRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundQuestionnaireRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.step = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.cityStep = CityStep.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.familyDetailsStep = FamilyDetailsStep.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.monthlyExpenseStep = MonthlyExpenseStep.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.insuranceStep = InsuranceStep.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.modifyPlanStep = ModifyPlanStep.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.acceptPlanStep = AcceptPlanStep.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.monthlyPledge = MonthlyPledgeStep.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.planRecommendation = GetPlanRecommendation.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundQuestionnaireRequest {
    return {
      step: isSet(object.step) ? globalThis.String(object.step) : "",
      cityStep: isSet(object.cityStep) ? CityStep.fromJSON(object.cityStep) : undefined,
      familyDetailsStep: isSet(object.familyDetailsStep)
        ? FamilyDetailsStep.fromJSON(object.familyDetailsStep)
        : undefined,
      monthlyExpenseStep: isSet(object.monthlyExpenseStep)
        ? MonthlyExpenseStep.fromJSON(object.monthlyExpenseStep)
        : undefined,
      insuranceStep: isSet(object.insuranceStep) ? InsuranceStep.fromJSON(object.insuranceStep) : undefined,
      modifyPlanStep: isSet(object.modifyPlanStep) ? ModifyPlanStep.fromJSON(object.modifyPlanStep) : undefined,
      acceptPlanStep: isSet(object.acceptPlanStep) ? AcceptPlanStep.fromJSON(object.acceptPlanStep) : undefined,
      monthlyPledge: isSet(object.monthlyPledge) ? MonthlyPledgeStep.fromJSON(object.monthlyPledge) : undefined,
      planRecommendation: isSet(object.planRecommendation)
        ? GetPlanRecommendation.fromJSON(object.planRecommendation)
        : undefined,
    };
  },

  toJSON(message: EmergencyFundQuestionnaireRequest): unknown {
    const obj: any = {};
    if (message.step !== "") {
      obj.step = message.step;
    }
    if (message.cityStep !== undefined) {
      obj.cityStep = CityStep.toJSON(message.cityStep);
    }
    if (message.familyDetailsStep !== undefined) {
      obj.familyDetailsStep = FamilyDetailsStep.toJSON(message.familyDetailsStep);
    }
    if (message.monthlyExpenseStep !== undefined) {
      obj.monthlyExpenseStep = MonthlyExpenseStep.toJSON(message.monthlyExpenseStep);
    }
    if (message.insuranceStep !== undefined) {
      obj.insuranceStep = InsuranceStep.toJSON(message.insuranceStep);
    }
    if (message.modifyPlanStep !== undefined) {
      obj.modifyPlanStep = ModifyPlanStep.toJSON(message.modifyPlanStep);
    }
    if (message.acceptPlanStep !== undefined) {
      obj.acceptPlanStep = AcceptPlanStep.toJSON(message.acceptPlanStep);
    }
    if (message.monthlyPledge !== undefined) {
      obj.monthlyPledge = MonthlyPledgeStep.toJSON(message.monthlyPledge);
    }
    if (message.planRecommendation !== undefined) {
      obj.planRecommendation = GetPlanRecommendation.toJSON(message.planRecommendation);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundQuestionnaireRequest>, I>>(
    base?: I,
  ): EmergencyFundQuestionnaireRequest {
    return EmergencyFundQuestionnaireRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundQuestionnaireRequest>, I>>(
    object: I,
  ): EmergencyFundQuestionnaireRequest {
    const message = createBaseEmergencyFundQuestionnaireRequest();
    message.step = object.step ?? "";
    message.cityStep = (object.cityStep !== undefined && object.cityStep !== null)
      ? CityStep.fromPartial(object.cityStep)
      : undefined;
    message.familyDetailsStep = (object.familyDetailsStep !== undefined && object.familyDetailsStep !== null)
      ? FamilyDetailsStep.fromPartial(object.familyDetailsStep)
      : undefined;
    message.monthlyExpenseStep = (object.monthlyExpenseStep !== undefined && object.monthlyExpenseStep !== null)
      ? MonthlyExpenseStep.fromPartial(object.monthlyExpenseStep)
      : undefined;
    message.insuranceStep = (object.insuranceStep !== undefined && object.insuranceStep !== null)
      ? InsuranceStep.fromPartial(object.insuranceStep)
      : undefined;
    message.modifyPlanStep = (object.modifyPlanStep !== undefined && object.modifyPlanStep !== null)
      ? ModifyPlanStep.fromPartial(object.modifyPlanStep)
      : undefined;
    message.acceptPlanStep = (object.acceptPlanStep !== undefined && object.acceptPlanStep !== null)
      ? AcceptPlanStep.fromPartial(object.acceptPlanStep)
      : undefined;
    message.monthlyPledge = (object.monthlyPledge !== undefined && object.monthlyPledge !== null)
      ? MonthlyPledgeStep.fromPartial(object.monthlyPledge)
      : undefined;
    message.planRecommendation = (object.planRecommendation !== undefined && object.planRecommendation !== null)
      ? GetPlanRecommendation.fromPartial(object.planRecommendation)
      : undefined;
    return message;
  },
};

function createBaseGetEmergencyFundResponse(): GetEmergencyFundResponse {
  return {
    cityId: "",
    isPayingEmi: false,
    isPayingRent: false,
    isMarried: false,
    hasKids: false,
    doParentsDependOnYou: false,
    monthlyExpense: 0,
    hasHealthInsurance: false,
    hasLifeInsurance: false,
    goalAmount: 0,
    monthlyPledge: 0,
    recommendedAmount: 0,
    isActive: false,
    city: "",
  };
}

export const GetEmergencyFundResponse: MessageFns<GetEmergencyFundResponse> = {
  encode(message: GetEmergencyFundResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cityId !== "") {
      writer.uint32(10).string(message.cityId);
    }
    if (message.isPayingEmi !== false) {
      writer.uint32(16).bool(message.isPayingEmi);
    }
    if (message.isPayingRent !== false) {
      writer.uint32(24).bool(message.isPayingRent);
    }
    if (message.isMarried !== false) {
      writer.uint32(32).bool(message.isMarried);
    }
    if (message.hasKids !== false) {
      writer.uint32(40).bool(message.hasKids);
    }
    if (message.doParentsDependOnYou !== false) {
      writer.uint32(48).bool(message.doParentsDependOnYou);
    }
    if (message.monthlyExpense !== 0) {
      writer.uint32(57).double(message.monthlyExpense);
    }
    if (message.hasHealthInsurance !== false) {
      writer.uint32(64).bool(message.hasHealthInsurance);
    }
    if (message.hasLifeInsurance !== false) {
      writer.uint32(72).bool(message.hasLifeInsurance);
    }
    if (message.goalAmount !== 0) {
      writer.uint32(81).double(message.goalAmount);
    }
    if (message.monthlyPledge !== 0) {
      writer.uint32(97).double(message.monthlyPledge);
    }
    if (message.recommendedAmount !== 0) {
      writer.uint32(105).double(message.recommendedAmount);
    }
    if (message.isActive !== false) {
      writer.uint32(112).bool(message.isActive);
    }
    if (message.city !== "") {
      writer.uint32(122).string(message.city);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetEmergencyFundResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEmergencyFundResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cityId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isPayingEmi = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isPayingRent = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isMarried = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.hasKids = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.doParentsDependOnYou = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.monthlyExpense = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.hasHealthInsurance = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.hasLifeInsurance = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 81) {
            break;
          }

          message.goalAmount = reader.double();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.monthlyPledge = reader.double();
          continue;
        }
        case 13: {
          if (tag !== 105) {
            break;
          }

          message.recommendedAmount = reader.double();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.city = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetEmergencyFundResponse {
    return {
      cityId: isSet(object.cityId) ? globalThis.String(object.cityId) : "",
      isPayingEmi: isSet(object.isPayingEmi) ? globalThis.Boolean(object.isPayingEmi) : false,
      isPayingRent: isSet(object.isPayingRent) ? globalThis.Boolean(object.isPayingRent) : false,
      isMarried: isSet(object.isMarried) ? globalThis.Boolean(object.isMarried) : false,
      hasKids: isSet(object.hasKids) ? globalThis.Boolean(object.hasKids) : false,
      doParentsDependOnYou: isSet(object.doParentsDependOnYou)
        ? globalThis.Boolean(object.doParentsDependOnYou)
        : false,
      monthlyExpense: isSet(object.monthlyExpense) ? globalThis.Number(object.monthlyExpense) : 0,
      hasHealthInsurance: isSet(object.hasHealthInsurance) ? globalThis.Boolean(object.hasHealthInsurance) : false,
      hasLifeInsurance: isSet(object.hasLifeInsurance) ? globalThis.Boolean(object.hasLifeInsurance) : false,
      goalAmount: isSet(object.goalAmount) ? globalThis.Number(object.goalAmount) : 0,
      monthlyPledge: isSet(object.monthlyPledge) ? globalThis.Number(object.monthlyPledge) : 0,
      recommendedAmount: isSet(object.recommendedAmount) ? globalThis.Number(object.recommendedAmount) : 0,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      city: isSet(object.city) ? globalThis.String(object.city) : "",
    };
  },

  toJSON(message: GetEmergencyFundResponse): unknown {
    const obj: any = {};
    if (message.cityId !== "") {
      obj.cityId = message.cityId;
    }
    if (message.isPayingEmi !== false) {
      obj.isPayingEmi = message.isPayingEmi;
    }
    if (message.isPayingRent !== false) {
      obj.isPayingRent = message.isPayingRent;
    }
    if (message.isMarried !== false) {
      obj.isMarried = message.isMarried;
    }
    if (message.hasKids !== false) {
      obj.hasKids = message.hasKids;
    }
    if (message.doParentsDependOnYou !== false) {
      obj.doParentsDependOnYou = message.doParentsDependOnYou;
    }
    if (message.monthlyExpense !== 0) {
      obj.monthlyExpense = message.monthlyExpense;
    }
    if (message.hasHealthInsurance !== false) {
      obj.hasHealthInsurance = message.hasHealthInsurance;
    }
    if (message.hasLifeInsurance !== false) {
      obj.hasLifeInsurance = message.hasLifeInsurance;
    }
    if (message.goalAmount !== 0) {
      obj.goalAmount = message.goalAmount;
    }
    if (message.monthlyPledge !== 0) {
      obj.monthlyPledge = message.monthlyPledge;
    }
    if (message.recommendedAmount !== 0) {
      obj.recommendedAmount = message.recommendedAmount;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.city !== "") {
      obj.city = message.city;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetEmergencyFundResponse>, I>>(base?: I): GetEmergencyFundResponse {
    return GetEmergencyFundResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetEmergencyFundResponse>, I>>(object: I): GetEmergencyFundResponse {
    const message = createBaseGetEmergencyFundResponse();
    message.cityId = object.cityId ?? "";
    message.isPayingEmi = object.isPayingEmi ?? false;
    message.isPayingRent = object.isPayingRent ?? false;
    message.isMarried = object.isMarried ?? false;
    message.hasKids = object.hasKids ?? false;
    message.doParentsDependOnYou = object.doParentsDependOnYou ?? false;
    message.monthlyExpense = object.monthlyExpense ?? 0;
    message.hasHealthInsurance = object.hasHealthInsurance ?? false;
    message.hasLifeInsurance = object.hasLifeInsurance ?? false;
    message.goalAmount = object.goalAmount ?? 0;
    message.monthlyPledge = object.monthlyPledge ?? 0;
    message.recommendedAmount = object.recommendedAmount ?? 0;
    message.isActive = object.isActive ?? false;
    message.city = object.city ?? "";
    return message;
  },
};

function createBaseEmergencyFundQuestionnaireResponse(): EmergencyFundQuestionnaireResponse {
  return {
    goalAmount: 0,
    recommendedBanks: [],
    fdGoal: 0,
    savingsAccountGoal: 0,
    mappedInvestmentsAmount: 0,
    fdContributionAmount: 0,
    savingsRecommendedPrompt: "",
    savingsMinPercentage: 0,
    savingsMaxPercentage: 0,
    hasMappableInvestments: false,
    highestFdReturn: 0,
    savingsAccountAmount: 0,
  };
}

export const EmergencyFundQuestionnaireResponse: MessageFns<EmergencyFundQuestionnaireResponse> = {
  encode(message: EmergencyFundQuestionnaireResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalAmount !== 0) {
      writer.uint32(9).double(message.goalAmount);
    }
    for (const v of message.recommendedBanks) {
      RecommendedBankForGoalResponse.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.fdGoal !== 0) {
      writer.uint32(25).double(message.fdGoal);
    }
    if (message.savingsAccountGoal !== 0) {
      writer.uint32(33).double(message.savingsAccountGoal);
    }
    if (message.mappedInvestmentsAmount !== 0) {
      writer.uint32(41).double(message.mappedInvestmentsAmount);
    }
    if (message.fdContributionAmount !== 0) {
      writer.uint32(49).double(message.fdContributionAmount);
    }
    if (message.savingsRecommendedPrompt !== "") {
      writer.uint32(58).string(message.savingsRecommendedPrompt);
    }
    if (message.savingsMinPercentage !== 0) {
      writer.uint32(65).double(message.savingsMinPercentage);
    }
    if (message.savingsMaxPercentage !== 0) {
      writer.uint32(73).double(message.savingsMaxPercentage);
    }
    if (message.hasMappableInvestments !== false) {
      writer.uint32(80).bool(message.hasMappableInvestments);
    }
    if (message.highestFdReturn !== 0) {
      writer.uint32(97).double(message.highestFdReturn);
    }
    if (message.savingsAccountAmount !== 0) {
      writer.uint32(105).double(message.savingsAccountAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundQuestionnaireResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundQuestionnaireResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.goalAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recommendedBanks.push(RecommendedBankForGoalResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.fdGoal = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.savingsAccountGoal = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.mappedInvestmentsAmount = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.fdContributionAmount = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.savingsRecommendedPrompt = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.savingsMinPercentage = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 73) {
            break;
          }

          message.savingsMaxPercentage = reader.double();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.hasMappableInvestments = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.highestFdReturn = reader.double();
          continue;
        }
        case 13: {
          if (tag !== 105) {
            break;
          }

          message.savingsAccountAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundQuestionnaireResponse {
    return {
      goalAmount: isSet(object.goalAmount) ? globalThis.Number(object.goalAmount) : 0,
      recommendedBanks: globalThis.Array.isArray(object?.recommendedBanks)
        ? object.recommendedBanks.map((e: any) => RecommendedBankForGoalResponse.fromJSON(e))
        : [],
      fdGoal: isSet(object.fdGoal) ? globalThis.Number(object.fdGoal) : 0,
      savingsAccountGoal: isSet(object.savingsAccountGoal) ? globalThis.Number(object.savingsAccountGoal) : 0,
      mappedInvestmentsAmount: isSet(object.mappedInvestmentsAmount)
        ? globalThis.Number(object.mappedInvestmentsAmount)
        : 0,
      fdContributionAmount: isSet(object.fdContributionAmount) ? globalThis.Number(object.fdContributionAmount) : 0,
      savingsRecommendedPrompt: isSet(object.savingsRecommendedPrompt)
        ? globalThis.String(object.savingsRecommendedPrompt)
        : "",
      savingsMinPercentage: isSet(object.savingsMinPercentage) ? globalThis.Number(object.savingsMinPercentage) : 0,
      savingsMaxPercentage: isSet(object.savingsMaxPercentage) ? globalThis.Number(object.savingsMaxPercentage) : 0,
      hasMappableInvestments: isSet(object.hasMappableInvestments)
        ? globalThis.Boolean(object.hasMappableInvestments)
        : false,
      highestFdReturn: isSet(object.highestFdReturn) ? globalThis.Number(object.highestFdReturn) : 0,
      savingsAccountAmount: isSet(object.savingsAccountAmount) ? globalThis.Number(object.savingsAccountAmount) : 0,
    };
  },

  toJSON(message: EmergencyFundQuestionnaireResponse): unknown {
    const obj: any = {};
    if (message.goalAmount !== 0) {
      obj.goalAmount = message.goalAmount;
    }
    if (message.recommendedBanks?.length) {
      obj.recommendedBanks = message.recommendedBanks.map((e) => RecommendedBankForGoalResponse.toJSON(e));
    }
    if (message.fdGoal !== 0) {
      obj.fdGoal = message.fdGoal;
    }
    if (message.savingsAccountGoal !== 0) {
      obj.savingsAccountGoal = message.savingsAccountGoal;
    }
    if (message.mappedInvestmentsAmount !== 0) {
      obj.mappedInvestmentsAmount = message.mappedInvestmentsAmount;
    }
    if (message.fdContributionAmount !== 0) {
      obj.fdContributionAmount = message.fdContributionAmount;
    }
    if (message.savingsRecommendedPrompt !== "") {
      obj.savingsRecommendedPrompt = message.savingsRecommendedPrompt;
    }
    if (message.savingsMinPercentage !== 0) {
      obj.savingsMinPercentage = message.savingsMinPercentage;
    }
    if (message.savingsMaxPercentage !== 0) {
      obj.savingsMaxPercentage = message.savingsMaxPercentage;
    }
    if (message.hasMappableInvestments !== false) {
      obj.hasMappableInvestments = message.hasMappableInvestments;
    }
    if (message.highestFdReturn !== 0) {
      obj.highestFdReturn = message.highestFdReturn;
    }
    if (message.savingsAccountAmount !== 0) {
      obj.savingsAccountAmount = message.savingsAccountAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundQuestionnaireResponse>, I>>(
    base?: I,
  ): EmergencyFundQuestionnaireResponse {
    return EmergencyFundQuestionnaireResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundQuestionnaireResponse>, I>>(
    object: I,
  ): EmergencyFundQuestionnaireResponse {
    const message = createBaseEmergencyFundQuestionnaireResponse();
    message.goalAmount = object.goalAmount ?? 0;
    message.recommendedBanks = object.recommendedBanks?.map((e) => RecommendedBankForGoalResponse.fromPartial(e)) || [];
    message.fdGoal = object.fdGoal ?? 0;
    message.savingsAccountGoal = object.savingsAccountGoal ?? 0;
    message.mappedInvestmentsAmount = object.mappedInvestmentsAmount ?? 0;
    message.fdContributionAmount = object.fdContributionAmount ?? 0;
    message.savingsRecommendedPrompt = object.savingsRecommendedPrompt ?? "";
    message.savingsMinPercentage = object.savingsMinPercentage ?? 0;
    message.savingsMaxPercentage = object.savingsMaxPercentage ?? 0;
    message.hasMappableInvestments = object.hasMappableInvestments ?? false;
    message.highestFdReturn = object.highestFdReturn ?? 0;
    message.savingsAccountAmount = object.savingsAccountAmount ?? 0;
    return message;
  },
};

function createBaseGetPlanRecommendation(): GetPlanRecommendation {
  return { goalAmount: 0 };
}

export const GetPlanRecommendation: MessageFns<GetPlanRecommendation> = {
  encode(message: GetPlanRecommendation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalAmount !== 0) {
      writer.uint32(9).double(message.goalAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetPlanRecommendation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetPlanRecommendation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.goalAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetPlanRecommendation {
    return { goalAmount: isSet(object.goalAmount) ? globalThis.Number(object.goalAmount) : 0 };
  },

  toJSON(message: GetPlanRecommendation): unknown {
    const obj: any = {};
    if (message.goalAmount !== 0) {
      obj.goalAmount = message.goalAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetPlanRecommendation>, I>>(base?: I): GetPlanRecommendation {
    return GetPlanRecommendation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPlanRecommendation>, I>>(object: I): GetPlanRecommendation {
    const message = createBaseGetPlanRecommendation();
    message.goalAmount = object.goalAmount ?? 0;
    return message;
  },
};

function createBaseCityStep(): CityStep {
  return { cityId: "", isPayingEmi: false, isPayingRent: false };
}

export const CityStep: MessageFns<CityStep> = {
  encode(message: CityStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.cityId !== "") {
      writer.uint32(10).string(message.cityId);
    }
    if (message.isPayingEmi !== false) {
      writer.uint32(16).bool(message.isPayingEmi);
    }
    if (message.isPayingRent !== false) {
      writer.uint32(24).bool(message.isPayingRent);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cityId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isPayingEmi = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isPayingRent = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CityStep {
    return {
      cityId: isSet(object.cityId) ? globalThis.String(object.cityId) : "",
      isPayingEmi: isSet(object.isPayingEmi) ? globalThis.Boolean(object.isPayingEmi) : false,
      isPayingRent: isSet(object.isPayingRent) ? globalThis.Boolean(object.isPayingRent) : false,
    };
  },

  toJSON(message: CityStep): unknown {
    const obj: any = {};
    if (message.cityId !== "") {
      obj.cityId = message.cityId;
    }
    if (message.isPayingEmi !== false) {
      obj.isPayingEmi = message.isPayingEmi;
    }
    if (message.isPayingRent !== false) {
      obj.isPayingRent = message.isPayingRent;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CityStep>, I>>(base?: I): CityStep {
    return CityStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityStep>, I>>(object: I): CityStep {
    const message = createBaseCityStep();
    message.cityId = object.cityId ?? "";
    message.isPayingEmi = object.isPayingEmi ?? false;
    message.isPayingRent = object.isPayingRent ?? false;
    return message;
  },
};

function createBaseFamilyDetailsStep(): FamilyDetailsStep {
  return { isMarried: false, hasKids: false, doParentsDependOnYou: false };
}

export const FamilyDetailsStep: MessageFns<FamilyDetailsStep> = {
  encode(message: FamilyDetailsStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isMarried !== false) {
      writer.uint32(8).bool(message.isMarried);
    }
    if (message.hasKids !== false) {
      writer.uint32(16).bool(message.hasKids);
    }
    if (message.doParentsDependOnYou !== false) {
      writer.uint32(24).bool(message.doParentsDependOnYou);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FamilyDetailsStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFamilyDetailsStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isMarried = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasKids = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.doParentsDependOnYou = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FamilyDetailsStep {
    return {
      isMarried: isSet(object.isMarried) ? globalThis.Boolean(object.isMarried) : false,
      hasKids: isSet(object.hasKids) ? globalThis.Boolean(object.hasKids) : false,
      doParentsDependOnYou: isSet(object.doParentsDependOnYou)
        ? globalThis.Boolean(object.doParentsDependOnYou)
        : false,
    };
  },

  toJSON(message: FamilyDetailsStep): unknown {
    const obj: any = {};
    if (message.isMarried !== false) {
      obj.isMarried = message.isMarried;
    }
    if (message.hasKids !== false) {
      obj.hasKids = message.hasKids;
    }
    if (message.doParentsDependOnYou !== false) {
      obj.doParentsDependOnYou = message.doParentsDependOnYou;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FamilyDetailsStep>, I>>(base?: I): FamilyDetailsStep {
    return FamilyDetailsStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FamilyDetailsStep>, I>>(object: I): FamilyDetailsStep {
    const message = createBaseFamilyDetailsStep();
    message.isMarried = object.isMarried ?? false;
    message.hasKids = object.hasKids ?? false;
    message.doParentsDependOnYou = object.doParentsDependOnYou ?? false;
    return message;
  },
};

function createBaseMonthlyExpenseStep(): MonthlyExpenseStep {
  return { monthlyExpense: 0, emiAmount: 0 };
}

export const MonthlyExpenseStep: MessageFns<MonthlyExpenseStep> = {
  encode(message: MonthlyExpenseStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.monthlyExpense !== 0) {
      writer.uint32(9).double(message.monthlyExpense);
    }
    if (message.emiAmount !== 0) {
      writer.uint32(17).double(message.emiAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MonthlyExpenseStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMonthlyExpenseStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.monthlyExpense = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.emiAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MonthlyExpenseStep {
    return {
      monthlyExpense: isSet(object.monthlyExpense) ? globalThis.Number(object.monthlyExpense) : 0,
      emiAmount: isSet(object.emiAmount) ? globalThis.Number(object.emiAmount) : 0,
    };
  },

  toJSON(message: MonthlyExpenseStep): unknown {
    const obj: any = {};
    if (message.monthlyExpense !== 0) {
      obj.monthlyExpense = message.monthlyExpense;
    }
    if (message.emiAmount !== 0) {
      obj.emiAmount = message.emiAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MonthlyExpenseStep>, I>>(base?: I): MonthlyExpenseStep {
    return MonthlyExpenseStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MonthlyExpenseStep>, I>>(object: I): MonthlyExpenseStep {
    const message = createBaseMonthlyExpenseStep();
    message.monthlyExpense = object.monthlyExpense ?? 0;
    message.emiAmount = object.emiAmount ?? 0;
    return message;
  },
};

function createBaseInsuranceStep(): InsuranceStep {
  return { hasHealthInsurance: false, hasLifeInsurance: false };
}

export const InsuranceStep: MessageFns<InsuranceStep> = {
  encode(message: InsuranceStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hasHealthInsurance !== false) {
      writer.uint32(8).bool(message.hasHealthInsurance);
    }
    if (message.hasLifeInsurance !== false) {
      writer.uint32(16).bool(message.hasLifeInsurance);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InsuranceStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInsuranceStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.hasHealthInsurance = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasLifeInsurance = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InsuranceStep {
    return {
      hasHealthInsurance: isSet(object.hasHealthInsurance) ? globalThis.Boolean(object.hasHealthInsurance) : false,
      hasLifeInsurance: isSet(object.hasLifeInsurance) ? globalThis.Boolean(object.hasLifeInsurance) : false,
    };
  },

  toJSON(message: InsuranceStep): unknown {
    const obj: any = {};
    if (message.hasHealthInsurance !== false) {
      obj.hasHealthInsurance = message.hasHealthInsurance;
    }
    if (message.hasLifeInsurance !== false) {
      obj.hasLifeInsurance = message.hasLifeInsurance;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InsuranceStep>, I>>(base?: I): InsuranceStep {
    return InsuranceStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InsuranceStep>, I>>(object: I): InsuranceStep {
    const message = createBaseInsuranceStep();
    message.hasHealthInsurance = object.hasHealthInsurance ?? false;
    message.hasLifeInsurance = object.hasLifeInsurance ?? false;
    return message;
  },
};

function createBaseEmergencyFundGoal(): EmergencyFundGoal {
  return {
    goalAmount: undefined,
    emiAmount: 0,
    monthlyExpense: 0,
    hasHealthInsurance: false,
    hasKids: false,
    isMarried: false,
    hasDependentParents: false,
    cityId: "",
  };
}

export const EmergencyFundGoal: MessageFns<EmergencyFundGoal> = {
  encode(message: EmergencyFundGoal, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalAmount !== undefined) {
      writer.uint32(9).double(message.goalAmount);
    }
    if (message.emiAmount !== 0) {
      writer.uint32(17).double(message.emiAmount);
    }
    if (message.monthlyExpense !== 0) {
      writer.uint32(25).double(message.monthlyExpense);
    }
    if (message.hasHealthInsurance !== false) {
      writer.uint32(32).bool(message.hasHealthInsurance);
    }
    if (message.hasKids !== false) {
      writer.uint32(40).bool(message.hasKids);
    }
    if (message.isMarried !== false) {
      writer.uint32(48).bool(message.isMarried);
    }
    if (message.hasDependentParents !== false) {
      writer.uint32(56).bool(message.hasDependentParents);
    }
    if (message.cityId !== "") {
      writer.uint32(66).string(message.cityId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundGoal {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundGoal();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.goalAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.emiAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.monthlyExpense = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.hasHealthInsurance = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.hasKids = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isMarried = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.hasDependentParents = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.cityId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundGoal {
    return {
      goalAmount: isSet(object.goalAmount) ? globalThis.Number(object.goalAmount) : undefined,
      emiAmount: isSet(object.emiAmount) ? globalThis.Number(object.emiAmount) : 0,
      monthlyExpense: isSet(object.monthlyExpense) ? globalThis.Number(object.monthlyExpense) : 0,
      hasHealthInsurance: isSet(object.hasHealthInsurance) ? globalThis.Boolean(object.hasHealthInsurance) : false,
      hasKids: isSet(object.hasKids) ? globalThis.Boolean(object.hasKids) : false,
      isMarried: isSet(object.isMarried) ? globalThis.Boolean(object.isMarried) : false,
      hasDependentParents: isSet(object.hasDependentParents) ? globalThis.Boolean(object.hasDependentParents) : false,
      cityId: isSet(object.cityId) ? globalThis.String(object.cityId) : "",
    };
  },

  toJSON(message: EmergencyFundGoal): unknown {
    const obj: any = {};
    if (message.goalAmount !== undefined) {
      obj.goalAmount = message.goalAmount;
    }
    if (message.emiAmount !== 0) {
      obj.emiAmount = message.emiAmount;
    }
    if (message.monthlyExpense !== 0) {
      obj.monthlyExpense = message.monthlyExpense;
    }
    if (message.hasHealthInsurance !== false) {
      obj.hasHealthInsurance = message.hasHealthInsurance;
    }
    if (message.hasKids !== false) {
      obj.hasKids = message.hasKids;
    }
    if (message.isMarried !== false) {
      obj.isMarried = message.isMarried;
    }
    if (message.hasDependentParents !== false) {
      obj.hasDependentParents = message.hasDependentParents;
    }
    if (message.cityId !== "") {
      obj.cityId = message.cityId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundGoal>, I>>(base?: I): EmergencyFundGoal {
    return EmergencyFundGoal.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundGoal>, I>>(object: I): EmergencyFundGoal {
    const message = createBaseEmergencyFundGoal();
    message.goalAmount = object.goalAmount ?? undefined;
    message.emiAmount = object.emiAmount ?? 0;
    message.monthlyExpense = object.monthlyExpense ?? 0;
    message.hasHealthInsurance = object.hasHealthInsurance ?? false;
    message.hasKids = object.hasKids ?? false;
    message.isMarried = object.isMarried ?? false;
    message.hasDependentParents = object.hasDependentParents ?? false;
    message.cityId = object.cityId ?? "";
    return message;
  },
};

function createBaseEmergencyFundMilestone(): EmergencyFundMilestone {
  return { name: "", amount: undefined, isCompleted: false };
}

export const EmergencyFundMilestone: MessageFns<EmergencyFundMilestone> = {
  encode(message: EmergencyFundMilestone, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.amount !== undefined) {
      writer.uint32(17).double(message.amount);
    }
    if (message.isCompleted !== false) {
      writer.uint32(24).bool(message.isCompleted);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundMilestone {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundMilestone();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isCompleted = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundMilestone {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : undefined,
      isCompleted: isSet(object.isCompleted) ? globalThis.Boolean(object.isCompleted) : false,
    };
  },

  toJSON(message: EmergencyFundMilestone): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.amount !== undefined) {
      obj.amount = message.amount;
    }
    if (message.isCompleted !== false) {
      obj.isCompleted = message.isCompleted;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundMilestone>, I>>(base?: I): EmergencyFundMilestone {
    return EmergencyFundMilestone.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundMilestone>, I>>(object: I): EmergencyFundMilestone {
    const message = createBaseEmergencyFundMilestone();
    message.name = object.name ?? "";
    message.amount = object.amount ?? undefined;
    message.isCompleted = object.isCompleted ?? false;
    return message;
  },
};

function createBaseEmergencyFundResponse(): EmergencyFundResponse {
  return { goal: undefined, investedAmount: 0, milestones: [] };
}

export const EmergencyFundResponse: MessageFns<EmergencyFundResponse> = {
  encode(message: EmergencyFundResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goal !== undefined) {
      EmergencyFundGoal.encode(message.goal, writer.uint32(10).fork()).join();
    }
    if (message.investedAmount !== 0) {
      writer.uint32(17).double(message.investedAmount);
    }
    for (const v of message.milestones) {
      EmergencyFundMilestone.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.goal = EmergencyFundGoal.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.investedAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.milestones.push(EmergencyFundMilestone.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundResponse {
    return {
      goal: isSet(object.goal) ? EmergencyFundGoal.fromJSON(object.goal) : undefined,
      investedAmount: isSet(object.investedAmount) ? globalThis.Number(object.investedAmount) : 0,
      milestones: globalThis.Array.isArray(object?.milestones)
        ? object.milestones.map((e: any) => EmergencyFundMilestone.fromJSON(e))
        : [],
    };
  },

  toJSON(message: EmergencyFundResponse): unknown {
    const obj: any = {};
    if (message.goal !== undefined) {
      obj.goal = EmergencyFundGoal.toJSON(message.goal);
    }
    if (message.investedAmount !== 0) {
      obj.investedAmount = message.investedAmount;
    }
    if (message.milestones?.length) {
      obj.milestones = message.milestones.map((e) => EmergencyFundMilestone.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundResponse>, I>>(base?: I): EmergencyFundResponse {
    return EmergencyFundResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundResponse>, I>>(object: I): EmergencyFundResponse {
    const message = createBaseEmergencyFundResponse();
    message.goal = (object.goal !== undefined && object.goal !== null)
      ? EmergencyFundGoal.fromPartial(object.goal)
      : undefined;
    message.investedAmount = object.investedAmount ?? 0;
    message.milestones = object.milestones?.map((e) => EmergencyFundMilestone.fromPartial(e)) || [];
    return message;
  },
};

function createBaseEmergencyFundReferralData(): EmergencyFundReferralData {
  return { referralLink: "", rewards: [], referees: [], referrer: undefined };
}

export const EmergencyFundReferralData: MessageFns<EmergencyFundReferralData> = {
  encode(message: EmergencyFundReferralData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.referralLink !== "") {
      writer.uint32(10).string(message.referralLink);
    }
    for (const v of message.rewards) {
      EmergencyFundReferralReward.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.referees) {
      EmergencyFundReferralProgramMember.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.referrer !== undefined) {
      EmergencyFundReferralProgramMember.encode(message.referrer, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundReferralData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundReferralData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.referralLink = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rewards.push(EmergencyFundReferralReward.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.referees.push(EmergencyFundReferralProgramMember.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.referrer = EmergencyFundReferralProgramMember.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundReferralData {
    return {
      referralLink: isSet(object.referralLink) ? globalThis.String(object.referralLink) : "",
      rewards: globalThis.Array.isArray(object?.rewards)
        ? object.rewards.map((e: any) => EmergencyFundReferralReward.fromJSON(e))
        : [],
      referees: globalThis.Array.isArray(object?.referees)
        ? object.referees.map((e: any) => EmergencyFundReferralProgramMember.fromJSON(e))
        : [],
      referrer: isSet(object.referrer) ? EmergencyFundReferralProgramMember.fromJSON(object.referrer) : undefined,
    };
  },

  toJSON(message: EmergencyFundReferralData): unknown {
    const obj: any = {};
    if (message.referralLink !== "") {
      obj.referralLink = message.referralLink;
    }
    if (message.rewards?.length) {
      obj.rewards = message.rewards.map((e) => EmergencyFundReferralReward.toJSON(e));
    }
    if (message.referees?.length) {
      obj.referees = message.referees.map((e) => EmergencyFundReferralProgramMember.toJSON(e));
    }
    if (message.referrer !== undefined) {
      obj.referrer = EmergencyFundReferralProgramMember.toJSON(message.referrer);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundReferralData>, I>>(base?: I): EmergencyFundReferralData {
    return EmergencyFundReferralData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundReferralData>, I>>(object: I): EmergencyFundReferralData {
    const message = createBaseEmergencyFundReferralData();
    message.referralLink = object.referralLink ?? "";
    message.rewards = object.rewards?.map((e) => EmergencyFundReferralReward.fromPartial(e)) || [];
    message.referees = object.referees?.map((e) => EmergencyFundReferralProgramMember.fromPartial(e)) || [];
    message.referrer = (object.referrer !== undefined && object.referrer !== null)
      ? EmergencyFundReferralProgramMember.fromPartial(object.referrer)
      : undefined;
    return message;
  },
};

function createBaseEmergencyFundReferralReward(): EmergencyFundReferralReward {
  return { referralCount: 0, rewardAmount: 0 };
}

export const EmergencyFundReferralReward: MessageFns<EmergencyFundReferralReward> = {
  encode(message: EmergencyFundReferralReward, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.referralCount !== 0) {
      writer.uint32(8).int64(message.referralCount);
    }
    if (message.rewardAmount !== 0) {
      writer.uint32(17).double(message.rewardAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundReferralReward {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundReferralReward();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.referralCount = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.rewardAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundReferralReward {
    return {
      referralCount: isSet(object.referralCount) ? globalThis.Number(object.referralCount) : 0,
      rewardAmount: isSet(object.rewardAmount) ? globalThis.Number(object.rewardAmount) : 0,
    };
  },

  toJSON(message: EmergencyFundReferralReward): unknown {
    const obj: any = {};
    if (message.referralCount !== 0) {
      obj.referralCount = Math.round(message.referralCount);
    }
    if (message.rewardAmount !== 0) {
      obj.rewardAmount = message.rewardAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundReferralReward>, I>>(base?: I): EmergencyFundReferralReward {
    return EmergencyFundReferralReward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundReferralReward>, I>>(object: I): EmergencyFundReferralReward {
    const message = createBaseEmergencyFundReferralReward();
    message.referralCount = object.referralCount ?? 0;
    message.rewardAmount = object.rewardAmount ?? 0;
    return message;
  },
};

function createBaseEmergencyFundReferralProgramMember(): EmergencyFundReferralProgramMember {
  return { id: "", firstName: "", lastName: "", goalProgress: 0, milestones: [] };
}

export const EmergencyFundReferralProgramMember: MessageFns<EmergencyFundReferralProgramMember> = {
  encode(message: EmergencyFundReferralProgramMember, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.firstName !== "") {
      writer.uint32(18).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(26).string(message.lastName);
    }
    if (message.goalProgress !== 0) {
      writer.uint32(37).float(message.goalProgress);
    }
    for (const v of message.milestones) {
      EmergencyFundMilestone.encode(v!, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundReferralProgramMember {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundReferralProgramMember();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 37) {
            break;
          }

          message.goalProgress = reader.float();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.milestones.push(EmergencyFundMilestone.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundReferralProgramMember {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      firstName: isSet(object.firstName) ? globalThis.String(object.firstName) : "",
      lastName: isSet(object.lastName) ? globalThis.String(object.lastName) : "",
      goalProgress: isSet(object.goalProgress) ? globalThis.Number(object.goalProgress) : 0,
      milestones: globalThis.Array.isArray(object?.milestones)
        ? object.milestones.map((e: any) => EmergencyFundMilestone.fromJSON(e))
        : [],
    };
  },

  toJSON(message: EmergencyFundReferralProgramMember): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.firstName !== "") {
      obj.firstName = message.firstName;
    }
    if (message.lastName !== "") {
      obj.lastName = message.lastName;
    }
    if (message.goalProgress !== 0) {
      obj.goalProgress = message.goalProgress;
    }
    if (message.milestones?.length) {
      obj.milestones = message.milestones.map((e) => EmergencyFundMilestone.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundReferralProgramMember>, I>>(
    base?: I,
  ): EmergencyFundReferralProgramMember {
    return EmergencyFundReferralProgramMember.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundReferralProgramMember>, I>>(
    object: I,
  ): EmergencyFundReferralProgramMember {
    const message = createBaseEmergencyFundReferralProgramMember();
    message.id = object.id ?? "";
    message.firstName = object.firstName ?? "";
    message.lastName = object.lastName ?? "";
    message.goalProgress = object.goalProgress ?? 0;
    message.milestones = object.milestones?.map((e) => EmergencyFundMilestone.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetRecommendedGoalAmountResponse(): GetRecommendedGoalAmountResponse {
  return { recommendedAmount: 0 };
}

export const GetRecommendedGoalAmountResponse: MessageFns<GetRecommendedGoalAmountResponse> = {
  encode(message: GetRecommendedGoalAmountResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recommendedAmount !== 0) {
      writer.uint32(9).double(message.recommendedAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRecommendedGoalAmountResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRecommendedGoalAmountResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.recommendedAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetRecommendedGoalAmountResponse {
    return { recommendedAmount: isSet(object.recommendedAmount) ? globalThis.Number(object.recommendedAmount) : 0 };
  },

  toJSON(message: GetRecommendedGoalAmountResponse): unknown {
    const obj: any = {};
    if (message.recommendedAmount !== 0) {
      obj.recommendedAmount = message.recommendedAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetRecommendedGoalAmountResponse>, I>>(
    base?: I,
  ): GetRecommendedGoalAmountResponse {
    return GetRecommendedGoalAmountResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRecommendedGoalAmountResponse>, I>>(
    object: I,
  ): GetRecommendedGoalAmountResponse {
    const message = createBaseGetRecommendedGoalAmountResponse();
    message.recommendedAmount = object.recommendedAmount ?? 0;
    return message;
  },
};

function createBaseModifyPlanStep(): ModifyPlanStep {
  return { goalAmount: 0, fdGoal: 0, savingsAccountGoal: 0 };
}

export const ModifyPlanStep: MessageFns<ModifyPlanStep> = {
  encode(message: ModifyPlanStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.goalAmount !== 0) {
      writer.uint32(9).double(message.goalAmount);
    }
    if (message.fdGoal !== 0) {
      writer.uint32(17).double(message.fdGoal);
    }
    if (message.savingsAccountGoal !== 0) {
      writer.uint32(25).double(message.savingsAccountGoal);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ModifyPlanStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseModifyPlanStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.goalAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.fdGoal = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.savingsAccountGoal = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ModifyPlanStep {
    return {
      goalAmount: isSet(object.goalAmount) ? globalThis.Number(object.goalAmount) : 0,
      fdGoal: isSet(object.fdGoal) ? globalThis.Number(object.fdGoal) : 0,
      savingsAccountGoal: isSet(object.savingsAccountGoal) ? globalThis.Number(object.savingsAccountGoal) : 0,
    };
  },

  toJSON(message: ModifyPlanStep): unknown {
    const obj: any = {};
    if (message.goalAmount !== 0) {
      obj.goalAmount = message.goalAmount;
    }
    if (message.fdGoal !== 0) {
      obj.fdGoal = message.fdGoal;
    }
    if (message.savingsAccountGoal !== 0) {
      obj.savingsAccountGoal = message.savingsAccountGoal;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ModifyPlanStep>, I>>(base?: I): ModifyPlanStep {
    return ModifyPlanStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ModifyPlanStep>, I>>(object: I): ModifyPlanStep {
    const message = createBaseModifyPlanStep();
    message.goalAmount = object.goalAmount ?? 0;
    message.fdGoal = object.fdGoal ?? 0;
    message.savingsAccountGoal = object.savingsAccountGoal ?? 0;
    return message;
  },
};

function createBaseAcceptPlanStep(): AcceptPlanStep {
  return { fdGoal: 0, savingsAccountGoal: 0, mapExistingInvestments: false };
}

export const AcceptPlanStep: MessageFns<AcceptPlanStep> = {
  encode(message: AcceptPlanStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdGoal !== 0) {
      writer.uint32(9).double(message.fdGoal);
    }
    if (message.savingsAccountGoal !== 0) {
      writer.uint32(17).double(message.savingsAccountGoal);
    }
    if (message.mapExistingInvestments !== false) {
      writer.uint32(24).bool(message.mapExistingInvestments);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AcceptPlanStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAcceptPlanStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.fdGoal = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.savingsAccountGoal = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.mapExistingInvestments = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AcceptPlanStep {
    return {
      fdGoal: isSet(object.fdGoal) ? globalThis.Number(object.fdGoal) : 0,
      savingsAccountGoal: isSet(object.savingsAccountGoal) ? globalThis.Number(object.savingsAccountGoal) : 0,
      mapExistingInvestments: isSet(object.mapExistingInvestments)
        ? globalThis.Boolean(object.mapExistingInvestments)
        : false,
    };
  },

  toJSON(message: AcceptPlanStep): unknown {
    const obj: any = {};
    if (message.fdGoal !== 0) {
      obj.fdGoal = message.fdGoal;
    }
    if (message.savingsAccountGoal !== 0) {
      obj.savingsAccountGoal = message.savingsAccountGoal;
    }
    if (message.mapExistingInvestments !== false) {
      obj.mapExistingInvestments = message.mapExistingInvestments;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AcceptPlanStep>, I>>(base?: I): AcceptPlanStep {
    return AcceptPlanStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AcceptPlanStep>, I>>(object: I): AcceptPlanStep {
    const message = createBaseAcceptPlanStep();
    message.fdGoal = object.fdGoal ?? 0;
    message.savingsAccountGoal = object.savingsAccountGoal ?? 0;
    message.mapExistingInvestments = object.mapExistingInvestments ?? false;
    return message;
  },
};

function createBaseMonthlyPledgeStep(): MonthlyPledgeStep {
  return { monthlyPledgeAmount: 0 };
}

export const MonthlyPledgeStep: MessageFns<MonthlyPledgeStep> = {
  encode(message: MonthlyPledgeStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.monthlyPledgeAmount !== 0) {
      writer.uint32(9).double(message.monthlyPledgeAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MonthlyPledgeStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMonthlyPledgeStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.monthlyPledgeAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MonthlyPledgeStep {
    return {
      monthlyPledgeAmount: isSet(object.monthlyPledgeAmount) ? globalThis.Number(object.monthlyPledgeAmount) : 0,
    };
  },

  toJSON(message: MonthlyPledgeStep): unknown {
    const obj: any = {};
    if (message.monthlyPledgeAmount !== 0) {
      obj.monthlyPledgeAmount = message.monthlyPledgeAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MonthlyPledgeStep>, I>>(base?: I): MonthlyPledgeStep {
    return MonthlyPledgeStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MonthlyPledgeStep>, I>>(object: I): MonthlyPledgeStep {
    const message = createBaseMonthlyPledgeStep();
    message.monthlyPledgeAmount = object.monthlyPledgeAmount ?? 0;
    return message;
  },
};

function createBaseEmergencyFundDashboardResponse(): EmergencyFundDashboardResponse {
  return {
    hasDoneFirstInvestment: false,
    goalDetails: undefined,
    pledge: undefined,
    recommendedBanks: [],
    chosenBanks: [],
    otherBanks: [],
    isNewEfUser: false,
    recommendedSavingsPrompt: "",
  };
}

export const EmergencyFundDashboardResponse: MessageFns<EmergencyFundDashboardResponse> = {
  encode(message: EmergencyFundDashboardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hasDoneFirstInvestment !== false) {
      writer.uint32(8).bool(message.hasDoneFirstInvestment);
    }
    if (message.goalDetails !== undefined) {
      GetGoalDetailsResponse.encode(message.goalDetails, writer.uint32(18).fork()).join();
    }
    if (message.pledge !== undefined) {
      PledgeDetail.encode(message.pledge, writer.uint32(26).fork()).join();
    }
    for (const v of message.recommendedBanks) {
      RecommendedBankForGoalResponse.encode(v!, writer.uint32(34).fork()).join();
    }
    for (const v of message.chosenBanks) {
      RecommendedBankForGoalResponse.encode(v!, writer.uint32(42).fork()).join();
    }
    for (const v of message.otherBanks) {
      RecommendedBankForGoalResponse.encode(v!, writer.uint32(50).fork()).join();
    }
    if (message.isNewEfUser !== false) {
      writer.uint32(56).bool(message.isNewEfUser);
    }
    if (message.recommendedSavingsPrompt !== "") {
      writer.uint32(66).string(message.recommendedSavingsPrompt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundDashboardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundDashboardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.hasDoneFirstInvestment = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.goalDetails = GetGoalDetailsResponse.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pledge = PledgeDetail.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.recommendedBanks.push(RecommendedBankForGoalResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.chosenBanks.push(RecommendedBankForGoalResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.otherBanks.push(RecommendedBankForGoalResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.isNewEfUser = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.recommendedSavingsPrompt = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundDashboardResponse {
    return {
      hasDoneFirstInvestment: isSet(object.hasDoneFirstInvestment)
        ? globalThis.Boolean(object.hasDoneFirstInvestment)
        : false,
      goalDetails: isSet(object.goalDetails) ? GetGoalDetailsResponse.fromJSON(object.goalDetails) : undefined,
      pledge: isSet(object.pledge) ? PledgeDetail.fromJSON(object.pledge) : undefined,
      recommendedBanks: globalThis.Array.isArray(object?.recommendedBanks)
        ? object.recommendedBanks.map((e: any) => RecommendedBankForGoalResponse.fromJSON(e))
        : [],
      chosenBanks: globalThis.Array.isArray(object?.chosenBanks)
        ? object.chosenBanks.map((e: any) => RecommendedBankForGoalResponse.fromJSON(e))
        : [],
      otherBanks: globalThis.Array.isArray(object?.otherBanks)
        ? object.otherBanks.map((e: any) => RecommendedBankForGoalResponse.fromJSON(e))
        : [],
      isNewEfUser: isSet(object.isNewEfUser) ? globalThis.Boolean(object.isNewEfUser) : false,
      recommendedSavingsPrompt: isSet(object.recommendedSavingsPrompt)
        ? globalThis.String(object.recommendedSavingsPrompt)
        : "",
    };
  },

  toJSON(message: EmergencyFundDashboardResponse): unknown {
    const obj: any = {};
    if (message.hasDoneFirstInvestment !== false) {
      obj.hasDoneFirstInvestment = message.hasDoneFirstInvestment;
    }
    if (message.goalDetails !== undefined) {
      obj.goalDetails = GetGoalDetailsResponse.toJSON(message.goalDetails);
    }
    if (message.pledge !== undefined) {
      obj.pledge = PledgeDetail.toJSON(message.pledge);
    }
    if (message.recommendedBanks?.length) {
      obj.recommendedBanks = message.recommendedBanks.map((e) => RecommendedBankForGoalResponse.toJSON(e));
    }
    if (message.chosenBanks?.length) {
      obj.chosenBanks = message.chosenBanks.map((e) => RecommendedBankForGoalResponse.toJSON(e));
    }
    if (message.otherBanks?.length) {
      obj.otherBanks = message.otherBanks.map((e) => RecommendedBankForGoalResponse.toJSON(e));
    }
    if (message.isNewEfUser !== false) {
      obj.isNewEfUser = message.isNewEfUser;
    }
    if (message.recommendedSavingsPrompt !== "") {
      obj.recommendedSavingsPrompt = message.recommendedSavingsPrompt;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundDashboardResponse>, I>>(base?: I): EmergencyFundDashboardResponse {
    return EmergencyFundDashboardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundDashboardResponse>, I>>(
    object: I,
  ): EmergencyFundDashboardResponse {
    const message = createBaseEmergencyFundDashboardResponse();
    message.hasDoneFirstInvestment = object.hasDoneFirstInvestment ?? false;
    message.goalDetails = (object.goalDetails !== undefined && object.goalDetails !== null)
      ? GetGoalDetailsResponse.fromPartial(object.goalDetails)
      : undefined;
    message.pledge = (object.pledge !== undefined && object.pledge !== null)
      ? PledgeDetail.fromPartial(object.pledge)
      : undefined;
    message.recommendedBanks = object.recommendedBanks?.map((e) => RecommendedBankForGoalResponse.fromPartial(e)) || [];
    message.chosenBanks = object.chosenBanks?.map((e) => RecommendedBankForGoalResponse.fromPartial(e)) || [];
    message.otherBanks = object.otherBanks?.map((e) => RecommendedBankForGoalResponse.fromPartial(e)) || [];
    message.isNewEfUser = object.isNewEfUser ?? false;
    message.recommendedSavingsPrompt = object.recommendedSavingsPrompt ?? "";
    return message;
  },
};

function createBasePledgeDetail(): PledgeDetail {
  return { monthlyPledge: 0, contributions: [], nextContributionDays: 0 };
}

export const PledgeDetail: MessageFns<PledgeDetail> = {
  encode(message: PledgeDetail, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.monthlyPledge !== 0) {
      writer.uint32(9).double(message.monthlyPledge);
    }
    for (const v of message.contributions) {
      Contribution.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.nextContributionDays !== 0) {
      writer.uint32(24).int64(message.nextContributionDays);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PledgeDetail {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePledgeDetail();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.monthlyPledge = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.contributions.push(Contribution.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.nextContributionDays = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PledgeDetail {
    return {
      monthlyPledge: isSet(object.monthlyPledge) ? globalThis.Number(object.monthlyPledge) : 0,
      contributions: globalThis.Array.isArray(object?.contributions)
        ? object.contributions.map((e: any) => Contribution.fromJSON(e))
        : [],
      nextContributionDays: isSet(object.nextContributionDays) ? globalThis.Number(object.nextContributionDays) : 0,
    };
  },

  toJSON(message: PledgeDetail): unknown {
    const obj: any = {};
    if (message.monthlyPledge !== 0) {
      obj.monthlyPledge = message.monthlyPledge;
    }
    if (message.contributions?.length) {
      obj.contributions = message.contributions.map((e) => Contribution.toJSON(e));
    }
    if (message.nextContributionDays !== 0) {
      obj.nextContributionDays = Math.round(message.nextContributionDays);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PledgeDetail>, I>>(base?: I): PledgeDetail {
    return PledgeDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PledgeDetail>, I>>(object: I): PledgeDetail {
    const message = createBasePledgeDetail();
    message.monthlyPledge = object.monthlyPledge ?? 0;
    message.contributions = object.contributions?.map((e) => Contribution.fromPartial(e)) || [];
    message.nextContributionDays = object.nextContributionDays ?? 0;
    return message;
  },
};

function createBaseContribution(): Contribution {
  return { month: "", year: "", status: 0 };
}

export const Contribution: MessageFns<Contribution> = {
  encode(message: Contribution, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.month !== "") {
      writer.uint32(10).string(message.month);
    }
    if (message.year !== "") {
      writer.uint32(18).string(message.year);
    }
    if (message.status !== 0) {
      writer.uint32(24).int32(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Contribution {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseContribution();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.month = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.year = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.status = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Contribution {
    return {
      month: isSet(object.month) ? globalThis.String(object.month) : "",
      year: isSet(object.year) ? globalThis.String(object.year) : "",
      status: isSet(object.status) ? contributionStatusFromJSON(object.status) : 0,
    };
  },

  toJSON(message: Contribution): unknown {
    const obj: any = {};
    if (message.month !== "") {
      obj.month = message.month;
    }
    if (message.year !== "") {
      obj.year = message.year;
    }
    if (message.status !== 0) {
      obj.status = contributionStatusToJSON(message.status);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Contribution>, I>>(base?: I): Contribution {
    return Contribution.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Contribution>, I>>(object: I): Contribution {
    const message = createBaseContribution();
    message.month = object.month ?? "";
    message.year = object.year ?? "";
    message.status = object.status ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
