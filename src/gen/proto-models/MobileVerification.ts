// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: MobileVerification.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { OTPChallenge } from "./Auth";

export const protobufPackage = "com.stablemoney.api.identity";

export interface InitiateMobileVerificationRequest {
  mobile: string;
  countryCode: string;
}

export interface InitiateMobileVerificationResponse {
  otpChallenge: OTPChallenge | undefined;
}

export interface RespondToMobileVerificationChallenge {
  challengeId: string;
  answer: string;
}

export interface RespondToMobileVerificationChallengeResponse {
  expired: boolean;
  message: string;
}

function createBaseInitiateMobileVerificationRequest(): InitiateMobileVerificationRequest {
  return { mobile: "", countryCode: "" };
}

export const InitiateMobileVerificationRequest: MessageFns<InitiateMobileVerificationRequest> = {
  encode(message: InitiateMobileVerificationRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mobile !== "") {
      writer.uint32(10).string(message.mobile);
    }
    if (message.countryCode !== "") {
      writer.uint32(18).string(message.countryCode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateMobileVerificationRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateMobileVerificationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.mobile = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.countryCode = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateMobileVerificationRequest {
    return {
      mobile: isSet(object.mobile) ? globalThis.String(object.mobile) : "",
      countryCode: isSet(object.countryCode) ? globalThis.String(object.countryCode) : "",
    };
  },

  toJSON(message: InitiateMobileVerificationRequest): unknown {
    const obj: any = {};
    if (message.mobile !== "") {
      obj.mobile = message.mobile;
    }
    if (message.countryCode !== "") {
      obj.countryCode = message.countryCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateMobileVerificationRequest>, I>>(
    base?: I,
  ): InitiateMobileVerificationRequest {
    return InitiateMobileVerificationRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateMobileVerificationRequest>, I>>(
    object: I,
  ): InitiateMobileVerificationRequest {
    const message = createBaseInitiateMobileVerificationRequest();
    message.mobile = object.mobile ?? "";
    message.countryCode = object.countryCode ?? "";
    return message;
  },
};

function createBaseInitiateMobileVerificationResponse(): InitiateMobileVerificationResponse {
  return { otpChallenge: undefined };
}

export const InitiateMobileVerificationResponse: MessageFns<InitiateMobileVerificationResponse> = {
  encode(message: InitiateMobileVerificationResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.otpChallenge !== undefined) {
      OTPChallenge.encode(message.otpChallenge, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateMobileVerificationResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateMobileVerificationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.otpChallenge = OTPChallenge.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateMobileVerificationResponse {
    return { otpChallenge: isSet(object.otpChallenge) ? OTPChallenge.fromJSON(object.otpChallenge) : undefined };
  },

  toJSON(message: InitiateMobileVerificationResponse): unknown {
    const obj: any = {};
    if (message.otpChallenge !== undefined) {
      obj.otpChallenge = OTPChallenge.toJSON(message.otpChallenge);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateMobileVerificationResponse>, I>>(
    base?: I,
  ): InitiateMobileVerificationResponse {
    return InitiateMobileVerificationResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateMobileVerificationResponse>, I>>(
    object: I,
  ): InitiateMobileVerificationResponse {
    const message = createBaseInitiateMobileVerificationResponse();
    message.otpChallenge = (object.otpChallenge !== undefined && object.otpChallenge !== null)
      ? OTPChallenge.fromPartial(object.otpChallenge)
      : undefined;
    return message;
  },
};

function createBaseRespondToMobileVerificationChallenge(): RespondToMobileVerificationChallenge {
  return { challengeId: "", answer: "" };
}

export const RespondToMobileVerificationChallenge: MessageFns<RespondToMobileVerificationChallenge> = {
  encode(message: RespondToMobileVerificationChallenge, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.challengeId !== "") {
      writer.uint32(10).string(message.challengeId);
    }
    if (message.answer !== "") {
      writer.uint32(18).string(message.answer);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RespondToMobileVerificationChallenge {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRespondToMobileVerificationChallenge();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.challengeId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.answer = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RespondToMobileVerificationChallenge {
    return {
      challengeId: isSet(object.challengeId) ? globalThis.String(object.challengeId) : "",
      answer: isSet(object.answer) ? globalThis.String(object.answer) : "",
    };
  },

  toJSON(message: RespondToMobileVerificationChallenge): unknown {
    const obj: any = {};
    if (message.challengeId !== "") {
      obj.challengeId = message.challengeId;
    }
    if (message.answer !== "") {
      obj.answer = message.answer;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RespondToMobileVerificationChallenge>, I>>(
    base?: I,
  ): RespondToMobileVerificationChallenge {
    return RespondToMobileVerificationChallenge.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RespondToMobileVerificationChallenge>, I>>(
    object: I,
  ): RespondToMobileVerificationChallenge {
    const message = createBaseRespondToMobileVerificationChallenge();
    message.challengeId = object.challengeId ?? "";
    message.answer = object.answer ?? "";
    return message;
  },
};

function createBaseRespondToMobileVerificationChallengeResponse(): RespondToMobileVerificationChallengeResponse {
  return { expired: false, message: "" };
}

export const RespondToMobileVerificationChallengeResponse: MessageFns<RespondToMobileVerificationChallengeResponse> = {
  encode(
    message: RespondToMobileVerificationChallengeResponse,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.expired !== false) {
      writer.uint32(8).bool(message.expired);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RespondToMobileVerificationChallengeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRespondToMobileVerificationChallengeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.expired = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RespondToMobileVerificationChallengeResponse {
    return {
      expired: isSet(object.expired) ? globalThis.Boolean(object.expired) : false,
      message: isSet(object.message) ? globalThis.String(object.message) : "",
    };
  },

  toJSON(message: RespondToMobileVerificationChallengeResponse): unknown {
    const obj: any = {};
    if (message.expired !== false) {
      obj.expired = message.expired;
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RespondToMobileVerificationChallengeResponse>, I>>(
    base?: I,
  ): RespondToMobileVerificationChallengeResponse {
    return RespondToMobileVerificationChallengeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RespondToMobileVerificationChallengeResponse>, I>>(
    object: I,
  ): RespondToMobileVerificationChallengeResponse {
    const message = createBaseRespondToMobileVerificationChallengeResponse();
    message.expired = object.expired ?? false;
    message.message = object.message ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
