// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Questionnaire.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum QuestionType {
  QUESTION_TYPE_UNKNOWN = 0,
  SINGLE_CHOICE = 1,
  MULTIPLE_CHOICE = 2,
  UNRECOGNIZED = -1,
}

export function questionTypeFromJSON(object: any): QuestionType {
  switch (object) {
    case 0:
    case "QUESTION_TYPE_UNKNOWN":
      return QuestionType.QUESTION_TYPE_UNKNOWN;
    case 1:
    case "SINGLE_CHOICE":
      return QuestionType.SINGLE_CHOICE;
    case 2:
    case "MULTIPLE_CHOICE":
      return QuestionType.MULTIPLE_CHOICE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return QuestionType.UNRECOGNIZED;
  }
}

export function questionTypeToJSON(object: QuestionType): string {
  switch (object) {
    case QuestionType.QUESTION_TYPE_UNKNOWN:
      return "QUESTION_TYPE_UNKNOWN";
    case QuestionType.SINGLE_CHOICE:
      return "SINGLE_CHOICE";
    case QuestionType.MULTIPLE_CHOICE:
      return "MULTIPLE_CHOICE";
    case QuestionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface QuestionListResponse {
  questionData: QuestionResponse[];
}

export interface QuestionResponse {
  id: string;
  question: string;
  description: string;
  buttonText: string;
  isSkippable: boolean;
  questionType: QuestionType;
  answerData: QuestionAnswerResponse[];
}

export interface QuestionAnswerResponse {
  id: string;
  answer: string;
}

export interface QuestionsAnswerSubmitRequest {
  data: AnswerData[];
  isSkipped: boolean;
}

export interface AnswerData {
  questionId: string;
  answerData: string[];
}

export interface QuestionsAnswerSubmitResponse {
}

function createBaseQuestionListResponse(): QuestionListResponse {
  return { questionData: [] };
}

export const QuestionListResponse: MessageFns<QuestionListResponse> = {
  encode(message: QuestionListResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.questionData) {
      QuestionResponse.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): QuestionListResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQuestionListResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.questionData.push(QuestionResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QuestionListResponse {
    return {
      questionData: globalThis.Array.isArray(object?.questionData)
        ? object.questionData.map((e: any) => QuestionResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: QuestionListResponse): unknown {
    const obj: any = {};
    if (message.questionData?.length) {
      obj.questionData = message.questionData.map((e) => QuestionResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QuestionListResponse>, I>>(base?: I): QuestionListResponse {
    return QuestionListResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QuestionListResponse>, I>>(object: I): QuestionListResponse {
    const message = createBaseQuestionListResponse();
    message.questionData = object.questionData?.map((e) => QuestionResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseQuestionResponse(): QuestionResponse {
  return { id: "", question: "", description: "", buttonText: "", isSkippable: false, questionType: 0, answerData: [] };
}

export const QuestionResponse: MessageFns<QuestionResponse> = {
  encode(message: QuestionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.question !== "") {
      writer.uint32(18).string(message.question);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.buttonText !== "") {
      writer.uint32(34).string(message.buttonText);
    }
    if (message.isSkippable !== false) {
      writer.uint32(40).bool(message.isSkippable);
    }
    if (message.questionType !== 0) {
      writer.uint32(48).int32(message.questionType);
    }
    for (const v of message.answerData) {
      QuestionAnswerResponse.encode(v!, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): QuestionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQuestionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.question = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.buttonText = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isSkippable = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.questionType = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.answerData.push(QuestionAnswerResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QuestionResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      question: isSet(object.question) ? globalThis.String(object.question) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      buttonText: isSet(object.buttonText) ? globalThis.String(object.buttonText) : "",
      isSkippable: isSet(object.isSkippable) ? globalThis.Boolean(object.isSkippable) : false,
      questionType: isSet(object.questionType) ? questionTypeFromJSON(object.questionType) : 0,
      answerData: globalThis.Array.isArray(object?.answerData)
        ? object.answerData.map((e: any) => QuestionAnswerResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: QuestionResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.question !== "") {
      obj.question = message.question;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.buttonText !== "") {
      obj.buttonText = message.buttonText;
    }
    if (message.isSkippable !== false) {
      obj.isSkippable = message.isSkippable;
    }
    if (message.questionType !== 0) {
      obj.questionType = questionTypeToJSON(message.questionType);
    }
    if (message.answerData?.length) {
      obj.answerData = message.answerData.map((e) => QuestionAnswerResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QuestionResponse>, I>>(base?: I): QuestionResponse {
    return QuestionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QuestionResponse>, I>>(object: I): QuestionResponse {
    const message = createBaseQuestionResponse();
    message.id = object.id ?? "";
    message.question = object.question ?? "";
    message.description = object.description ?? "";
    message.buttonText = object.buttonText ?? "";
    message.isSkippable = object.isSkippable ?? false;
    message.questionType = object.questionType ?? 0;
    message.answerData = object.answerData?.map((e) => QuestionAnswerResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseQuestionAnswerResponse(): QuestionAnswerResponse {
  return { id: "", answer: "" };
}

export const QuestionAnswerResponse: MessageFns<QuestionAnswerResponse> = {
  encode(message: QuestionAnswerResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.answer !== "") {
      writer.uint32(18).string(message.answer);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): QuestionAnswerResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQuestionAnswerResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.answer = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QuestionAnswerResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      answer: isSet(object.answer) ? globalThis.String(object.answer) : "",
    };
  },

  toJSON(message: QuestionAnswerResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.answer !== "") {
      obj.answer = message.answer;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QuestionAnswerResponse>, I>>(base?: I): QuestionAnswerResponse {
    return QuestionAnswerResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QuestionAnswerResponse>, I>>(object: I): QuestionAnswerResponse {
    const message = createBaseQuestionAnswerResponse();
    message.id = object.id ?? "";
    message.answer = object.answer ?? "";
    return message;
  },
};

function createBaseQuestionsAnswerSubmitRequest(): QuestionsAnswerSubmitRequest {
  return { data: [], isSkipped: false };
}

export const QuestionsAnswerSubmitRequest: MessageFns<QuestionsAnswerSubmitRequest> = {
  encode(message: QuestionsAnswerSubmitRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      AnswerData.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.isSkipped !== false) {
      writer.uint32(16).bool(message.isSkipped);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): QuestionsAnswerSubmitRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQuestionsAnswerSubmitRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(AnswerData.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isSkipped = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QuestionsAnswerSubmitRequest {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => AnswerData.fromJSON(e)) : [],
      isSkipped: isSet(object.isSkipped) ? globalThis.Boolean(object.isSkipped) : false,
    };
  },

  toJSON(message: QuestionsAnswerSubmitRequest): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => AnswerData.toJSON(e));
    }
    if (message.isSkipped !== false) {
      obj.isSkipped = message.isSkipped;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QuestionsAnswerSubmitRequest>, I>>(base?: I): QuestionsAnswerSubmitRequest {
    return QuestionsAnswerSubmitRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QuestionsAnswerSubmitRequest>, I>>(object: I): QuestionsAnswerSubmitRequest {
    const message = createBaseQuestionsAnswerSubmitRequest();
    message.data = object.data?.map((e) => AnswerData.fromPartial(e)) || [];
    message.isSkipped = object.isSkipped ?? false;
    return message;
  },
};

function createBaseAnswerData(): AnswerData {
  return { questionId: "", answerData: [] };
}

export const AnswerData: MessageFns<AnswerData> = {
  encode(message: AnswerData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.questionId !== "") {
      writer.uint32(10).string(message.questionId);
    }
    for (const v of message.answerData) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AnswerData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAnswerData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.questionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.answerData.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AnswerData {
    return {
      questionId: isSet(object.questionId) ? globalThis.String(object.questionId) : "",
      answerData: globalThis.Array.isArray(object?.answerData)
        ? object.answerData.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: AnswerData): unknown {
    const obj: any = {};
    if (message.questionId !== "") {
      obj.questionId = message.questionId;
    }
    if (message.answerData?.length) {
      obj.answerData = message.answerData;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AnswerData>, I>>(base?: I): AnswerData {
    return AnswerData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AnswerData>, I>>(object: I): AnswerData {
    const message = createBaseAnswerData();
    message.questionId = object.questionId ?? "";
    message.answerData = object.answerData?.map((e) => e) || [];
    return message;
  },
};

function createBaseQuestionsAnswerSubmitResponse(): QuestionsAnswerSubmitResponse {
  return {};
}

export const QuestionsAnswerSubmitResponse: MessageFns<QuestionsAnswerSubmitResponse> = {
  encode(_: QuestionsAnswerSubmitResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): QuestionsAnswerSubmitResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQuestionsAnswerSubmitResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): QuestionsAnswerSubmitResponse {
    return {};
  },

  toJSON(_: QuestionsAnswerSubmitResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<QuestionsAnswerSubmitResponse>, I>>(base?: I): QuestionsAnswerSubmitResponse {
    return QuestionsAnswerSubmitResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QuestionsAnswerSubmitResponse>, I>>(_: I): QuestionsAnswerSubmitResponse {
    const message = createBaseQuestionsAnswerSubmitResponse();
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
