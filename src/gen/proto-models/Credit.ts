// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Credit.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum CreditScoreStatus {
  UNKNOWN_CREDIT_SCORE_STATUS = 0,
  NOT_INITIATED_CREDIT_SCORE_STATUS = 1,
  ACTIVE_CREDIT_SCORE_STATUS = 2,
  CONSENT_REQUIRED_CREDIT_SCORE_STATUS = 3,
  NOT_FOUND_CREDIT_SCORE_STATUS = 4,
  UNRECOGNIZED = -1,
}

export function creditScoreStatusFromJSON(object: any): CreditScoreStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_CREDIT_SCORE_STATUS":
      return CreditScoreStatus.UNKNOWN_CREDIT_SCORE_STATUS;
    case 1:
    case "NOT_INITIATED_CREDIT_SCORE_STATUS":
      return CreditScoreStatus.NOT_INITIATED_CREDIT_SCORE_STATUS;
    case 2:
    case "ACTIVE_CREDIT_SCORE_STATUS":
      return CreditScoreStatus.ACTIVE_CREDIT_SCORE_STATUS;
    case 3:
    case "CONSENT_REQUIRED_CREDIT_SCORE_STATUS":
      return CreditScoreStatus.CONSENT_REQUIRED_CREDIT_SCORE_STATUS;
    case 4:
    case "NOT_FOUND_CREDIT_SCORE_STATUS":
      return CreditScoreStatus.NOT_FOUND_CREDIT_SCORE_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CreditScoreStatus.UNRECOGNIZED;
  }
}

export function creditScoreStatusToJSON(object: CreditScoreStatus): string {
  switch (object) {
    case CreditScoreStatus.UNKNOWN_CREDIT_SCORE_STATUS:
      return "UNKNOWN_CREDIT_SCORE_STATUS";
    case CreditScoreStatus.NOT_INITIATED_CREDIT_SCORE_STATUS:
      return "NOT_INITIATED_CREDIT_SCORE_STATUS";
    case CreditScoreStatus.ACTIVE_CREDIT_SCORE_STATUS:
      return "ACTIVE_CREDIT_SCORE_STATUS";
    case CreditScoreStatus.CONSENT_REQUIRED_CREDIT_SCORE_STATUS:
      return "CONSENT_REQUIRED_CREDIT_SCORE_STATUS";
    case CreditScoreStatus.NOT_FOUND_CREDIT_SCORE_STATUS:
      return "NOT_FOUND_CREDIT_SCORE_STATUS";
    case CreditScoreStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface CreditReportSummary {
  creditScore: number;
  minCreditScore: number;
  maxCreditScore: number;
  lastUpdatedDate: string;
  summaryHeading: string;
  summaryDescription: string;
  summaryLogoUrl: string;
  creditScorePercentage: number;
  heading: string;
  experianLogoUrl: string;
  refreshButton: boolean;
  refreshButtonCta: string;
}

export interface SectionItem {
  itemLogo: string;
  itemTitle: string;
  itemDescription: string;
  itemValue: number;
  itemValueStr: string;
  itemValueDescription: string;
}

export interface CreditReportSection {
  sectionTitle: string;
  sectionItem: SectionItem[];
  bottomSheetHeader: string;
  bottomSheetHeaderColor: string;
  bottomSheetDescription: string;
  bottomSheetCta: string;
  size: number;
}

export interface CreditScoreStatsDetailSection {
  sectionTitle?: string | undefined;
  sectionItem: SectionItem[];
}

export interface CreditScoreStatsDetails {
  creditScoreStatsDetailSections: CreditScoreStatsDetailSection[];
  leftHeading: string;
  rightHeading: string;
  leftHeadingValue: string;
  rightHeadingValue: string;
  question: string;
  answer: string;
  ctaText: string;
}

export interface CreditScoreStatItem {
  statTitle: string;
  statImpact: string;
  statValue: string;
  statRating: string;
  statRatingColor: string;
  creditScoreStatsDetails?: CreditScoreStatsDetails | undefined;
}

export interface CreditScoreStats {
  heading: string;
  body: string;
  stats: CreditScoreStatItem[];
  iconUrl: string;
}

export interface CreditScoreStatsV2 {
  heading: string;
  body: string;
  stats: CreditScoreStatItem[];
  iconUrl: string;
}

export interface CreditReport {
  creditReportSummary: CreditReportSummary | undefined;
  sections: CreditReportSection[];
  creditScoreStats?: CreditScoreStats | undefined;
  experianUrl: string;
  uniqueTrId: string;
  experianRefId: string;
  experianCta: string;
  creditScoreStatsV2?: CreditScoreStatsV2 | undefined;
}

export interface CreditScoreDashboardResponse {
  creditScoreStatus: CreditScoreStatus;
  sectionTitle: string;
  sectionBody: string;
  creditScore?: number | undefined;
  creditScoreDescription?: string | undefined;
  creditScoreColor?: string | undefined;
  ctaText?: string | undefined;
  lastUpdatedDate?: string | undefined;
  creditReport?: CreditReport | undefined;
}

function createBaseCreditReportSummary(): CreditReportSummary {
  return {
    creditScore: 0,
    minCreditScore: 0,
    maxCreditScore: 0,
    lastUpdatedDate: "",
    summaryHeading: "",
    summaryDescription: "",
    summaryLogoUrl: "",
    creditScorePercentage: 0,
    heading: "",
    experianLogoUrl: "",
    refreshButton: false,
    refreshButtonCta: "",
  };
}

export const CreditReportSummary: MessageFns<CreditReportSummary> = {
  encode(message: CreditReportSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.creditScore !== 0) {
      writer.uint32(8).int32(message.creditScore);
    }
    if (message.minCreditScore !== 0) {
      writer.uint32(16).int32(message.minCreditScore);
    }
    if (message.maxCreditScore !== 0) {
      writer.uint32(24).int32(message.maxCreditScore);
    }
    if (message.lastUpdatedDate !== "") {
      writer.uint32(34).string(message.lastUpdatedDate);
    }
    if (message.summaryHeading !== "") {
      writer.uint32(42).string(message.summaryHeading);
    }
    if (message.summaryDescription !== "") {
      writer.uint32(50).string(message.summaryDescription);
    }
    if (message.summaryLogoUrl !== "") {
      writer.uint32(58).string(message.summaryLogoUrl);
    }
    if (message.creditScorePercentage !== 0) {
      writer.uint32(65).double(message.creditScorePercentage);
    }
    if (message.heading !== "") {
      writer.uint32(74).string(message.heading);
    }
    if (message.experianLogoUrl !== "") {
      writer.uint32(82).string(message.experianLogoUrl);
    }
    if (message.refreshButton !== false) {
      writer.uint32(88).bool(message.refreshButton);
    }
    if (message.refreshButtonCta !== "") {
      writer.uint32(98).string(message.refreshButtonCta);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditReportSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditReportSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.creditScore = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.minCreditScore = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.maxCreditScore = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lastUpdatedDate = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.summaryHeading = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.summaryDescription = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.summaryLogoUrl = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.creditScorePercentage = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.heading = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.experianLogoUrl = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.refreshButton = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.refreshButtonCta = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditReportSummary {
    return {
      creditScore: isSet(object.creditScore) ? globalThis.Number(object.creditScore) : 0,
      minCreditScore: isSet(object.minCreditScore) ? globalThis.Number(object.minCreditScore) : 0,
      maxCreditScore: isSet(object.maxCreditScore) ? globalThis.Number(object.maxCreditScore) : 0,
      lastUpdatedDate: isSet(object.lastUpdatedDate) ? globalThis.String(object.lastUpdatedDate) : "",
      summaryHeading: isSet(object.summaryHeading) ? globalThis.String(object.summaryHeading) : "",
      summaryDescription: isSet(object.summaryDescription) ? globalThis.String(object.summaryDescription) : "",
      summaryLogoUrl: isSet(object.summaryLogoUrl) ? globalThis.String(object.summaryLogoUrl) : "",
      creditScorePercentage: isSet(object.creditScorePercentage) ? globalThis.Number(object.creditScorePercentage) : 0,
      heading: isSet(object.heading) ? globalThis.String(object.heading) : "",
      experianLogoUrl: isSet(object.experianLogoUrl) ? globalThis.String(object.experianLogoUrl) : "",
      refreshButton: isSet(object.refreshButton) ? globalThis.Boolean(object.refreshButton) : false,
      refreshButtonCta: isSet(object.refreshButtonCta) ? globalThis.String(object.refreshButtonCta) : "",
    };
  },

  toJSON(message: CreditReportSummary): unknown {
    const obj: any = {};
    if (message.creditScore !== 0) {
      obj.creditScore = Math.round(message.creditScore);
    }
    if (message.minCreditScore !== 0) {
      obj.minCreditScore = Math.round(message.minCreditScore);
    }
    if (message.maxCreditScore !== 0) {
      obj.maxCreditScore = Math.round(message.maxCreditScore);
    }
    if (message.lastUpdatedDate !== "") {
      obj.lastUpdatedDate = message.lastUpdatedDate;
    }
    if (message.summaryHeading !== "") {
      obj.summaryHeading = message.summaryHeading;
    }
    if (message.summaryDescription !== "") {
      obj.summaryDescription = message.summaryDescription;
    }
    if (message.summaryLogoUrl !== "") {
      obj.summaryLogoUrl = message.summaryLogoUrl;
    }
    if (message.creditScorePercentage !== 0) {
      obj.creditScorePercentage = message.creditScorePercentage;
    }
    if (message.heading !== "") {
      obj.heading = message.heading;
    }
    if (message.experianLogoUrl !== "") {
      obj.experianLogoUrl = message.experianLogoUrl;
    }
    if (message.refreshButton !== false) {
      obj.refreshButton = message.refreshButton;
    }
    if (message.refreshButtonCta !== "") {
      obj.refreshButtonCta = message.refreshButtonCta;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditReportSummary>, I>>(base?: I): CreditReportSummary {
    return CreditReportSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditReportSummary>, I>>(object: I): CreditReportSummary {
    const message = createBaseCreditReportSummary();
    message.creditScore = object.creditScore ?? 0;
    message.minCreditScore = object.minCreditScore ?? 0;
    message.maxCreditScore = object.maxCreditScore ?? 0;
    message.lastUpdatedDate = object.lastUpdatedDate ?? "";
    message.summaryHeading = object.summaryHeading ?? "";
    message.summaryDescription = object.summaryDescription ?? "";
    message.summaryLogoUrl = object.summaryLogoUrl ?? "";
    message.creditScorePercentage = object.creditScorePercentage ?? 0;
    message.heading = object.heading ?? "";
    message.experianLogoUrl = object.experianLogoUrl ?? "";
    message.refreshButton = object.refreshButton ?? false;
    message.refreshButtonCta = object.refreshButtonCta ?? "";
    return message;
  },
};

function createBaseSectionItem(): SectionItem {
  return { itemLogo: "", itemTitle: "", itemDescription: "", itemValue: 0, itemValueStr: "", itemValueDescription: "" };
}

export const SectionItem: MessageFns<SectionItem> = {
  encode(message: SectionItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.itemLogo !== "") {
      writer.uint32(10).string(message.itemLogo);
    }
    if (message.itemTitle !== "") {
      writer.uint32(18).string(message.itemTitle);
    }
    if (message.itemDescription !== "") {
      writer.uint32(26).string(message.itemDescription);
    }
    if (message.itemValue !== 0) {
      writer.uint32(33).double(message.itemValue);
    }
    if (message.itemValueStr !== "") {
      writer.uint32(42).string(message.itemValueStr);
    }
    if (message.itemValueDescription !== "") {
      writer.uint32(50).string(message.itemValueDescription);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SectionItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSectionItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.itemLogo = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.itemTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.itemDescription = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.itemValue = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.itemValueStr = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.itemValueDescription = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SectionItem {
    return {
      itemLogo: isSet(object.itemLogo) ? globalThis.String(object.itemLogo) : "",
      itemTitle: isSet(object.itemTitle) ? globalThis.String(object.itemTitle) : "",
      itemDescription: isSet(object.itemDescription) ? globalThis.String(object.itemDescription) : "",
      itemValue: isSet(object.itemValue) ? globalThis.Number(object.itemValue) : 0,
      itemValueStr: isSet(object.itemValueStr) ? globalThis.String(object.itemValueStr) : "",
      itemValueDescription: isSet(object.itemValueDescription) ? globalThis.String(object.itemValueDescription) : "",
    };
  },

  toJSON(message: SectionItem): unknown {
    const obj: any = {};
    if (message.itemLogo !== "") {
      obj.itemLogo = message.itemLogo;
    }
    if (message.itemTitle !== "") {
      obj.itemTitle = message.itemTitle;
    }
    if (message.itemDescription !== "") {
      obj.itemDescription = message.itemDescription;
    }
    if (message.itemValue !== 0) {
      obj.itemValue = message.itemValue;
    }
    if (message.itemValueStr !== "") {
      obj.itemValueStr = message.itemValueStr;
    }
    if (message.itemValueDescription !== "") {
      obj.itemValueDescription = message.itemValueDescription;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SectionItem>, I>>(base?: I): SectionItem {
    return SectionItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SectionItem>, I>>(object: I): SectionItem {
    const message = createBaseSectionItem();
    message.itemLogo = object.itemLogo ?? "";
    message.itemTitle = object.itemTitle ?? "";
    message.itemDescription = object.itemDescription ?? "";
    message.itemValue = object.itemValue ?? 0;
    message.itemValueStr = object.itemValueStr ?? "";
    message.itemValueDescription = object.itemValueDescription ?? "";
    return message;
  },
};

function createBaseCreditReportSection(): CreditReportSection {
  return {
    sectionTitle: "",
    sectionItem: [],
    bottomSheetHeader: "",
    bottomSheetHeaderColor: "",
    bottomSheetDescription: "",
    bottomSheetCta: "",
    size: 0,
  };
}

export const CreditReportSection: MessageFns<CreditReportSection> = {
  encode(message: CreditReportSection, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sectionTitle !== "") {
      writer.uint32(10).string(message.sectionTitle);
    }
    for (const v of message.sectionItem) {
      SectionItem.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.bottomSheetHeader !== "") {
      writer.uint32(26).string(message.bottomSheetHeader);
    }
    if (message.bottomSheetHeaderColor !== "") {
      writer.uint32(34).string(message.bottomSheetHeaderColor);
    }
    if (message.bottomSheetDescription !== "") {
      writer.uint32(42).string(message.bottomSheetDescription);
    }
    if (message.bottomSheetCta !== "") {
      writer.uint32(50).string(message.bottomSheetCta);
    }
    if (message.size !== 0) {
      writer.uint32(56).int32(message.size);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditReportSection {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditReportSection();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.sectionTitle = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sectionItem.push(SectionItem.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bottomSheetHeader = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bottomSheetHeaderColor = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bottomSheetDescription = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.bottomSheetCta = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.size = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditReportSection {
    return {
      sectionTitle: isSet(object.sectionTitle) ? globalThis.String(object.sectionTitle) : "",
      sectionItem: globalThis.Array.isArray(object?.sectionItem)
        ? object.sectionItem.map((e: any) => SectionItem.fromJSON(e))
        : [],
      bottomSheetHeader: isSet(object.bottomSheetHeader) ? globalThis.String(object.bottomSheetHeader) : "",
      bottomSheetHeaderColor: isSet(object.bottomSheetHeaderColor)
        ? globalThis.String(object.bottomSheetHeaderColor)
        : "",
      bottomSheetDescription: isSet(object.bottomSheetDescription)
        ? globalThis.String(object.bottomSheetDescription)
        : "",
      bottomSheetCta: isSet(object.bottomSheetCta) ? globalThis.String(object.bottomSheetCta) : "",
      size: isSet(object.size) ? globalThis.Number(object.size) : 0,
    };
  },

  toJSON(message: CreditReportSection): unknown {
    const obj: any = {};
    if (message.sectionTitle !== "") {
      obj.sectionTitle = message.sectionTitle;
    }
    if (message.sectionItem?.length) {
      obj.sectionItem = message.sectionItem.map((e) => SectionItem.toJSON(e));
    }
    if (message.bottomSheetHeader !== "") {
      obj.bottomSheetHeader = message.bottomSheetHeader;
    }
    if (message.bottomSheetHeaderColor !== "") {
      obj.bottomSheetHeaderColor = message.bottomSheetHeaderColor;
    }
    if (message.bottomSheetDescription !== "") {
      obj.bottomSheetDescription = message.bottomSheetDescription;
    }
    if (message.bottomSheetCta !== "") {
      obj.bottomSheetCta = message.bottomSheetCta;
    }
    if (message.size !== 0) {
      obj.size = Math.round(message.size);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditReportSection>, I>>(base?: I): CreditReportSection {
    return CreditReportSection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditReportSection>, I>>(object: I): CreditReportSection {
    const message = createBaseCreditReportSection();
    message.sectionTitle = object.sectionTitle ?? "";
    message.sectionItem = object.sectionItem?.map((e) => SectionItem.fromPartial(e)) || [];
    message.bottomSheetHeader = object.bottomSheetHeader ?? "";
    message.bottomSheetHeaderColor = object.bottomSheetHeaderColor ?? "";
    message.bottomSheetDescription = object.bottomSheetDescription ?? "";
    message.bottomSheetCta = object.bottomSheetCta ?? "";
    message.size = object.size ?? 0;
    return message;
  },
};

function createBaseCreditScoreStatsDetailSection(): CreditScoreStatsDetailSection {
  return { sectionTitle: undefined, sectionItem: [] };
}

export const CreditScoreStatsDetailSection: MessageFns<CreditScoreStatsDetailSection> = {
  encode(message: CreditScoreStatsDetailSection, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sectionTitle !== undefined) {
      writer.uint32(10).string(message.sectionTitle);
    }
    for (const v of message.sectionItem) {
      SectionItem.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditScoreStatsDetailSection {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditScoreStatsDetailSection();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.sectionTitle = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sectionItem.push(SectionItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditScoreStatsDetailSection {
    return {
      sectionTitle: isSet(object.sectionTitle) ? globalThis.String(object.sectionTitle) : undefined,
      sectionItem: globalThis.Array.isArray(object?.sectionItem)
        ? object.sectionItem.map((e: any) => SectionItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CreditScoreStatsDetailSection): unknown {
    const obj: any = {};
    if (message.sectionTitle !== undefined) {
      obj.sectionTitle = message.sectionTitle;
    }
    if (message.sectionItem?.length) {
      obj.sectionItem = message.sectionItem.map((e) => SectionItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditScoreStatsDetailSection>, I>>(base?: I): CreditScoreStatsDetailSection {
    return CreditScoreStatsDetailSection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditScoreStatsDetailSection>, I>>(
    object: I,
  ): CreditScoreStatsDetailSection {
    const message = createBaseCreditScoreStatsDetailSection();
    message.sectionTitle = object.sectionTitle ?? undefined;
    message.sectionItem = object.sectionItem?.map((e) => SectionItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCreditScoreStatsDetails(): CreditScoreStatsDetails {
  return {
    creditScoreStatsDetailSections: [],
    leftHeading: "",
    rightHeading: "",
    leftHeadingValue: "",
    rightHeadingValue: "",
    question: "",
    answer: "",
    ctaText: "",
  };
}

export const CreditScoreStatsDetails: MessageFns<CreditScoreStatsDetails> = {
  encode(message: CreditScoreStatsDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.creditScoreStatsDetailSections) {
      CreditScoreStatsDetailSection.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.leftHeading !== "") {
      writer.uint32(18).string(message.leftHeading);
    }
    if (message.rightHeading !== "") {
      writer.uint32(26).string(message.rightHeading);
    }
    if (message.leftHeadingValue !== "") {
      writer.uint32(34).string(message.leftHeadingValue);
    }
    if (message.rightHeadingValue !== "") {
      writer.uint32(42).string(message.rightHeadingValue);
    }
    if (message.question !== "") {
      writer.uint32(50).string(message.question);
    }
    if (message.answer !== "") {
      writer.uint32(58).string(message.answer);
    }
    if (message.ctaText !== "") {
      writer.uint32(66).string(message.ctaText);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditScoreStatsDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditScoreStatsDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.creditScoreStatsDetailSections.push(CreditScoreStatsDetailSection.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.leftHeading = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rightHeading = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.leftHeadingValue = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rightHeadingValue = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.question = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.answer = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.ctaText = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditScoreStatsDetails {
    return {
      creditScoreStatsDetailSections: globalThis.Array.isArray(object?.creditScoreStatsDetailSections)
        ? object.creditScoreStatsDetailSections.map((e: any) => CreditScoreStatsDetailSection.fromJSON(e))
        : [],
      leftHeading: isSet(object.leftHeading) ? globalThis.String(object.leftHeading) : "",
      rightHeading: isSet(object.rightHeading) ? globalThis.String(object.rightHeading) : "",
      leftHeadingValue: isSet(object.leftHeadingValue) ? globalThis.String(object.leftHeadingValue) : "",
      rightHeadingValue: isSet(object.rightHeadingValue) ? globalThis.String(object.rightHeadingValue) : "",
      question: isSet(object.question) ? globalThis.String(object.question) : "",
      answer: isSet(object.answer) ? globalThis.String(object.answer) : "",
      ctaText: isSet(object.ctaText) ? globalThis.String(object.ctaText) : "",
    };
  },

  toJSON(message: CreditScoreStatsDetails): unknown {
    const obj: any = {};
    if (message.creditScoreStatsDetailSections?.length) {
      obj.creditScoreStatsDetailSections = message.creditScoreStatsDetailSections.map((e) =>
        CreditScoreStatsDetailSection.toJSON(e)
      );
    }
    if (message.leftHeading !== "") {
      obj.leftHeading = message.leftHeading;
    }
    if (message.rightHeading !== "") {
      obj.rightHeading = message.rightHeading;
    }
    if (message.leftHeadingValue !== "") {
      obj.leftHeadingValue = message.leftHeadingValue;
    }
    if (message.rightHeadingValue !== "") {
      obj.rightHeadingValue = message.rightHeadingValue;
    }
    if (message.question !== "") {
      obj.question = message.question;
    }
    if (message.answer !== "") {
      obj.answer = message.answer;
    }
    if (message.ctaText !== "") {
      obj.ctaText = message.ctaText;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditScoreStatsDetails>, I>>(base?: I): CreditScoreStatsDetails {
    return CreditScoreStatsDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditScoreStatsDetails>, I>>(object: I): CreditScoreStatsDetails {
    const message = createBaseCreditScoreStatsDetails();
    message.creditScoreStatsDetailSections =
      object.creditScoreStatsDetailSections?.map((e) => CreditScoreStatsDetailSection.fromPartial(e)) || [];
    message.leftHeading = object.leftHeading ?? "";
    message.rightHeading = object.rightHeading ?? "";
    message.leftHeadingValue = object.leftHeadingValue ?? "";
    message.rightHeadingValue = object.rightHeadingValue ?? "";
    message.question = object.question ?? "";
    message.answer = object.answer ?? "";
    message.ctaText = object.ctaText ?? "";
    return message;
  },
};

function createBaseCreditScoreStatItem(): CreditScoreStatItem {
  return {
    statTitle: "",
    statImpact: "",
    statValue: "",
    statRating: "",
    statRatingColor: "",
    creditScoreStatsDetails: undefined,
  };
}

export const CreditScoreStatItem: MessageFns<CreditScoreStatItem> = {
  encode(message: CreditScoreStatItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.statTitle !== "") {
      writer.uint32(10).string(message.statTitle);
    }
    if (message.statImpact !== "") {
      writer.uint32(18).string(message.statImpact);
    }
    if (message.statValue !== "") {
      writer.uint32(26).string(message.statValue);
    }
    if (message.statRating !== "") {
      writer.uint32(34).string(message.statRating);
    }
    if (message.statRatingColor !== "") {
      writer.uint32(42).string(message.statRatingColor);
    }
    if (message.creditScoreStatsDetails !== undefined) {
      CreditScoreStatsDetails.encode(message.creditScoreStatsDetails, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditScoreStatItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditScoreStatItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.statTitle = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.statImpact = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.statValue = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.statRating = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.statRatingColor = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.creditScoreStatsDetails = CreditScoreStatsDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditScoreStatItem {
    return {
      statTitle: isSet(object.statTitle) ? globalThis.String(object.statTitle) : "",
      statImpact: isSet(object.statImpact) ? globalThis.String(object.statImpact) : "",
      statValue: isSet(object.statValue) ? globalThis.String(object.statValue) : "",
      statRating: isSet(object.statRating) ? globalThis.String(object.statRating) : "",
      statRatingColor: isSet(object.statRatingColor) ? globalThis.String(object.statRatingColor) : "",
      creditScoreStatsDetails: isSet(object.creditScoreStatsDetails)
        ? CreditScoreStatsDetails.fromJSON(object.creditScoreStatsDetails)
        : undefined,
    };
  },

  toJSON(message: CreditScoreStatItem): unknown {
    const obj: any = {};
    if (message.statTitle !== "") {
      obj.statTitle = message.statTitle;
    }
    if (message.statImpact !== "") {
      obj.statImpact = message.statImpact;
    }
    if (message.statValue !== "") {
      obj.statValue = message.statValue;
    }
    if (message.statRating !== "") {
      obj.statRating = message.statRating;
    }
    if (message.statRatingColor !== "") {
      obj.statRatingColor = message.statRatingColor;
    }
    if (message.creditScoreStatsDetails !== undefined) {
      obj.creditScoreStatsDetails = CreditScoreStatsDetails.toJSON(message.creditScoreStatsDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditScoreStatItem>, I>>(base?: I): CreditScoreStatItem {
    return CreditScoreStatItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditScoreStatItem>, I>>(object: I): CreditScoreStatItem {
    const message = createBaseCreditScoreStatItem();
    message.statTitle = object.statTitle ?? "";
    message.statImpact = object.statImpact ?? "";
    message.statValue = object.statValue ?? "";
    message.statRating = object.statRating ?? "";
    message.statRatingColor = object.statRatingColor ?? "";
    message.creditScoreStatsDetails =
      (object.creditScoreStatsDetails !== undefined && object.creditScoreStatsDetails !== null)
        ? CreditScoreStatsDetails.fromPartial(object.creditScoreStatsDetails)
        : undefined;
    return message;
  },
};

function createBaseCreditScoreStats(): CreditScoreStats {
  return { heading: "", body: "", stats: [], iconUrl: "" };
}

export const CreditScoreStats: MessageFns<CreditScoreStats> = {
  encode(message: CreditScoreStats, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.heading !== "") {
      writer.uint32(10).string(message.heading);
    }
    if (message.body !== "") {
      writer.uint32(18).string(message.body);
    }
    for (const v of message.stats) {
      CreditScoreStatItem.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.iconUrl !== "") {
      writer.uint32(34).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditScoreStats {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditScoreStats();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.heading = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.body = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.stats.push(CreditScoreStatItem.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditScoreStats {
    return {
      heading: isSet(object.heading) ? globalThis.String(object.heading) : "",
      body: isSet(object.body) ? globalThis.String(object.body) : "",
      stats: globalThis.Array.isArray(object?.stats)
        ? object.stats.map((e: any) => CreditScoreStatItem.fromJSON(e))
        : [],
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: CreditScoreStats): unknown {
    const obj: any = {};
    if (message.heading !== "") {
      obj.heading = message.heading;
    }
    if (message.body !== "") {
      obj.body = message.body;
    }
    if (message.stats?.length) {
      obj.stats = message.stats.map((e) => CreditScoreStatItem.toJSON(e));
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditScoreStats>, I>>(base?: I): CreditScoreStats {
    return CreditScoreStats.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditScoreStats>, I>>(object: I): CreditScoreStats {
    const message = createBaseCreditScoreStats();
    message.heading = object.heading ?? "";
    message.body = object.body ?? "";
    message.stats = object.stats?.map((e) => CreditScoreStatItem.fromPartial(e)) || [];
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBaseCreditScoreStatsV2(): CreditScoreStatsV2 {
  return { heading: "", body: "", stats: [], iconUrl: "" };
}

export const CreditScoreStatsV2: MessageFns<CreditScoreStatsV2> = {
  encode(message: CreditScoreStatsV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.heading !== "") {
      writer.uint32(10).string(message.heading);
    }
    if (message.body !== "") {
      writer.uint32(18).string(message.body);
    }
    for (const v of message.stats) {
      CreditScoreStatItem.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.iconUrl !== "") {
      writer.uint32(34).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditScoreStatsV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditScoreStatsV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.heading = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.body = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.stats.push(CreditScoreStatItem.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditScoreStatsV2 {
    return {
      heading: isSet(object.heading) ? globalThis.String(object.heading) : "",
      body: isSet(object.body) ? globalThis.String(object.body) : "",
      stats: globalThis.Array.isArray(object?.stats)
        ? object.stats.map((e: any) => CreditScoreStatItem.fromJSON(e))
        : [],
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: CreditScoreStatsV2): unknown {
    const obj: any = {};
    if (message.heading !== "") {
      obj.heading = message.heading;
    }
    if (message.body !== "") {
      obj.body = message.body;
    }
    if (message.stats?.length) {
      obj.stats = message.stats.map((e) => CreditScoreStatItem.toJSON(e));
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditScoreStatsV2>, I>>(base?: I): CreditScoreStatsV2 {
    return CreditScoreStatsV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditScoreStatsV2>, I>>(object: I): CreditScoreStatsV2 {
    const message = createBaseCreditScoreStatsV2();
    message.heading = object.heading ?? "";
    message.body = object.body ?? "";
    message.stats = object.stats?.map((e) => CreditScoreStatItem.fromPartial(e)) || [];
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBaseCreditReport(): CreditReport {
  return {
    creditReportSummary: undefined,
    sections: [],
    creditScoreStats: undefined,
    experianUrl: "",
    uniqueTrId: "",
    experianRefId: "",
    experianCta: "",
    creditScoreStatsV2: undefined,
  };
}

export const CreditReport: MessageFns<CreditReport> = {
  encode(message: CreditReport, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.creditReportSummary !== undefined) {
      CreditReportSummary.encode(message.creditReportSummary, writer.uint32(10).fork()).join();
    }
    for (const v of message.sections) {
      CreditReportSection.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.creditScoreStats !== undefined) {
      CreditScoreStats.encode(message.creditScoreStats, writer.uint32(26).fork()).join();
    }
    if (message.experianUrl !== "") {
      writer.uint32(34).string(message.experianUrl);
    }
    if (message.uniqueTrId !== "") {
      writer.uint32(42).string(message.uniqueTrId);
    }
    if (message.experianRefId !== "") {
      writer.uint32(50).string(message.experianRefId);
    }
    if (message.experianCta !== "") {
      writer.uint32(58).string(message.experianCta);
    }
    if (message.creditScoreStatsV2 !== undefined) {
      CreditScoreStatsV2.encode(message.creditScoreStatsV2, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditReport {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditReport();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.creditReportSummary = CreditReportSummary.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sections.push(CreditReportSection.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.creditScoreStats = CreditScoreStats.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.experianUrl = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.uniqueTrId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.experianRefId = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.experianCta = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.creditScoreStatsV2 = CreditScoreStatsV2.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditReport {
    return {
      creditReportSummary: isSet(object.creditReportSummary)
        ? CreditReportSummary.fromJSON(object.creditReportSummary)
        : undefined,
      sections: globalThis.Array.isArray(object?.sections)
        ? object.sections.map((e: any) => CreditReportSection.fromJSON(e))
        : [],
      creditScoreStats: isSet(object.creditScoreStats) ? CreditScoreStats.fromJSON(object.creditScoreStats) : undefined,
      experianUrl: isSet(object.experianUrl) ? globalThis.String(object.experianUrl) : "",
      uniqueTrId: isSet(object.uniqueTrId) ? globalThis.String(object.uniqueTrId) : "",
      experianRefId: isSet(object.experianRefId) ? globalThis.String(object.experianRefId) : "",
      experianCta: isSet(object.experianCta) ? globalThis.String(object.experianCta) : "",
      creditScoreStatsV2: isSet(object.creditScoreStatsV2)
        ? CreditScoreStatsV2.fromJSON(object.creditScoreStatsV2)
        : undefined,
    };
  },

  toJSON(message: CreditReport): unknown {
    const obj: any = {};
    if (message.creditReportSummary !== undefined) {
      obj.creditReportSummary = CreditReportSummary.toJSON(message.creditReportSummary);
    }
    if (message.sections?.length) {
      obj.sections = message.sections.map((e) => CreditReportSection.toJSON(e));
    }
    if (message.creditScoreStats !== undefined) {
      obj.creditScoreStats = CreditScoreStats.toJSON(message.creditScoreStats);
    }
    if (message.experianUrl !== "") {
      obj.experianUrl = message.experianUrl;
    }
    if (message.uniqueTrId !== "") {
      obj.uniqueTrId = message.uniqueTrId;
    }
    if (message.experianRefId !== "") {
      obj.experianRefId = message.experianRefId;
    }
    if (message.experianCta !== "") {
      obj.experianCta = message.experianCta;
    }
    if (message.creditScoreStatsV2 !== undefined) {
      obj.creditScoreStatsV2 = CreditScoreStatsV2.toJSON(message.creditScoreStatsV2);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditReport>, I>>(base?: I): CreditReport {
    return CreditReport.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditReport>, I>>(object: I): CreditReport {
    const message = createBaseCreditReport();
    message.creditReportSummary = (object.creditReportSummary !== undefined && object.creditReportSummary !== null)
      ? CreditReportSummary.fromPartial(object.creditReportSummary)
      : undefined;
    message.sections = object.sections?.map((e) => CreditReportSection.fromPartial(e)) || [];
    message.creditScoreStats = (object.creditScoreStats !== undefined && object.creditScoreStats !== null)
      ? CreditScoreStats.fromPartial(object.creditScoreStats)
      : undefined;
    message.experianUrl = object.experianUrl ?? "";
    message.uniqueTrId = object.uniqueTrId ?? "";
    message.experianRefId = object.experianRefId ?? "";
    message.experianCta = object.experianCta ?? "";
    message.creditScoreStatsV2 = (object.creditScoreStatsV2 !== undefined && object.creditScoreStatsV2 !== null)
      ? CreditScoreStatsV2.fromPartial(object.creditScoreStatsV2)
      : undefined;
    return message;
  },
};

function createBaseCreditScoreDashboardResponse(): CreditScoreDashboardResponse {
  return {
    creditScoreStatus: 0,
    sectionTitle: "",
    sectionBody: "",
    creditScore: undefined,
    creditScoreDescription: undefined,
    creditScoreColor: undefined,
    ctaText: undefined,
    lastUpdatedDate: undefined,
    creditReport: undefined,
  };
}

export const CreditScoreDashboardResponse: MessageFns<CreditScoreDashboardResponse> = {
  encode(message: CreditScoreDashboardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.creditScoreStatus !== 0) {
      writer.uint32(8).int32(message.creditScoreStatus);
    }
    if (message.sectionTitle !== "") {
      writer.uint32(18).string(message.sectionTitle);
    }
    if (message.sectionBody !== "") {
      writer.uint32(26).string(message.sectionBody);
    }
    if (message.creditScore !== undefined) {
      writer.uint32(32).int32(message.creditScore);
    }
    if (message.creditScoreDescription !== undefined) {
      writer.uint32(42).string(message.creditScoreDescription);
    }
    if (message.creditScoreColor !== undefined) {
      writer.uint32(50).string(message.creditScoreColor);
    }
    if (message.ctaText !== undefined) {
      writer.uint32(58).string(message.ctaText);
    }
    if (message.lastUpdatedDate !== undefined) {
      writer.uint32(66).string(message.lastUpdatedDate);
    }
    if (message.creditReport !== undefined) {
      CreditReport.encode(message.creditReport, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditScoreDashboardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditScoreDashboardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.creditScoreStatus = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.sectionTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.sectionBody = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.creditScore = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.creditScoreDescription = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.creditScoreColor = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.ctaText = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.lastUpdatedDate = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.creditReport = CreditReport.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditScoreDashboardResponse {
    return {
      creditScoreStatus: isSet(object.creditScoreStatus) ? creditScoreStatusFromJSON(object.creditScoreStatus) : 0,
      sectionTitle: isSet(object.sectionTitle) ? globalThis.String(object.sectionTitle) : "",
      sectionBody: isSet(object.sectionBody) ? globalThis.String(object.sectionBody) : "",
      creditScore: isSet(object.creditScore) ? globalThis.Number(object.creditScore) : undefined,
      creditScoreDescription: isSet(object.creditScoreDescription)
        ? globalThis.String(object.creditScoreDescription)
        : undefined,
      creditScoreColor: isSet(object.creditScoreColor) ? globalThis.String(object.creditScoreColor) : undefined,
      ctaText: isSet(object.ctaText) ? globalThis.String(object.ctaText) : undefined,
      lastUpdatedDate: isSet(object.lastUpdatedDate) ? globalThis.String(object.lastUpdatedDate) : undefined,
      creditReport: isSet(object.creditReport) ? CreditReport.fromJSON(object.creditReport) : undefined,
    };
  },

  toJSON(message: CreditScoreDashboardResponse): unknown {
    const obj: any = {};
    if (message.creditScoreStatus !== 0) {
      obj.creditScoreStatus = creditScoreStatusToJSON(message.creditScoreStatus);
    }
    if (message.sectionTitle !== "") {
      obj.sectionTitle = message.sectionTitle;
    }
    if (message.sectionBody !== "") {
      obj.sectionBody = message.sectionBody;
    }
    if (message.creditScore !== undefined) {
      obj.creditScore = Math.round(message.creditScore);
    }
    if (message.creditScoreDescription !== undefined) {
      obj.creditScoreDescription = message.creditScoreDescription;
    }
    if (message.creditScoreColor !== undefined) {
      obj.creditScoreColor = message.creditScoreColor;
    }
    if (message.ctaText !== undefined) {
      obj.ctaText = message.ctaText;
    }
    if (message.lastUpdatedDate !== undefined) {
      obj.lastUpdatedDate = message.lastUpdatedDate;
    }
    if (message.creditReport !== undefined) {
      obj.creditReport = CreditReport.toJSON(message.creditReport);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditScoreDashboardResponse>, I>>(base?: I): CreditScoreDashboardResponse {
    return CreditScoreDashboardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditScoreDashboardResponse>, I>>(object: I): CreditScoreDashboardResponse {
    const message = createBaseCreditScoreDashboardResponse();
    message.creditScoreStatus = object.creditScoreStatus ?? 0;
    message.sectionTitle = object.sectionTitle ?? "";
    message.sectionBody = object.sectionBody ?? "";
    message.creditScore = object.creditScore ?? undefined;
    message.creditScoreDescription = object.creditScoreDescription ?? undefined;
    message.creditScoreColor = object.creditScoreColor ?? undefined;
    message.ctaText = object.ctaText ?? undefined;
    message.lastUpdatedDate = object.lastUpdatedDate ?? undefined;
    message.creditReport = (object.creditReport !== undefined && object.creditReport !== null)
      ? CreditReport.fromPartial(object.creditReport)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
