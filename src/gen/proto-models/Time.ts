// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Time.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export interface CurrentTimeResponse {
  currentTimezone: string;
  currentTimeIso: string;
  currentTimeEpochMilli: number;
}

function createBaseCurrentTimeResponse(): CurrentTimeResponse {
  return { currentTimezone: "", currentTimeIso: "", currentTimeEpochMilli: 0 };
}

export const CurrentTimeResponse: MessageFns<CurrentTimeResponse> = {
  encode(message: CurrentTimeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.currentTimezone !== "") {
      writer.uint32(10).string(message.currentTimezone);
    }
    if (message.currentTimeIso !== "") {
      writer.uint32(18).string(message.currentTimeIso);
    }
    if (message.currentTimeEpochMilli !== 0) {
      writer.uint32(24).int64(message.currentTimeEpochMilli);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CurrentTimeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCurrentTimeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.currentTimezone = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.currentTimeIso = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.currentTimeEpochMilli = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CurrentTimeResponse {
    return {
      currentTimezone: isSet(object.currentTimezone) ? globalThis.String(object.currentTimezone) : "",
      currentTimeIso: isSet(object.currentTimeIso) ? globalThis.String(object.currentTimeIso) : "",
      currentTimeEpochMilli: isSet(object.currentTimeEpochMilli) ? globalThis.Number(object.currentTimeEpochMilli) : 0,
    };
  },

  toJSON(message: CurrentTimeResponse): unknown {
    const obj: any = {};
    if (message.currentTimezone !== "") {
      obj.currentTimezone = message.currentTimezone;
    }
    if (message.currentTimeIso !== "") {
      obj.currentTimeIso = message.currentTimeIso;
    }
    if (message.currentTimeEpochMilli !== 0) {
      obj.currentTimeEpochMilli = Math.round(message.currentTimeEpochMilli);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CurrentTimeResponse>, I>>(base?: I): CurrentTimeResponse {
    return CurrentTimeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CurrentTimeResponse>, I>>(object: I): CurrentTimeResponse {
    const message = createBaseCurrentTimeResponse();
    message.currentTimezone = object.currentTimezone ?? "";
    message.currentTimeIso = object.currentTimeIso ?? "";
    message.currentTimeEpochMilli = object.currentTimeEpochMilli ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
