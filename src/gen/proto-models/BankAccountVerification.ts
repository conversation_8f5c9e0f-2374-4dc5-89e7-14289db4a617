// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: BankAccountVerification.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum BankVerificationType {
  UNKNOWN_TYPE = 0,
  PENNY_DROP = 1,
  REVERSE_PENNY_DROP = 2,
  CANCEL_CHEQUE = 3,
  UNRECOGNIZED = -1,
}

export function bankVerificationTypeFromJSON(object: any): BankVerificationType {
  switch (object) {
    case 0:
    case "UNKNOWN_TYPE":
      return BankVerificationType.UNKNOWN_TYPE;
    case 1:
    case "PENNY_DROP":
      return BankVerificationType.PENNY_DROP;
    case 2:
    case "REVERSE_PENNY_DROP":
      return BankVerificationType.REVERSE_PENNY_DROP;
    case 3:
    case "CANCEL_CHEQUE":
      return BankVerificationType.CANCEL_CHEQUE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BankVerificationType.UNRECOGNIZED;
  }
}

export function bankVerificationTypeToJSON(object: BankVerificationType): string {
  switch (object) {
    case BankVerificationType.UNKNOWN_TYPE:
      return "UNKNOWN_TYPE";
    case BankVerificationType.PENNY_DROP:
      return "PENNY_DROP";
    case BankVerificationType.REVERSE_PENNY_DROP:
      return "REVERSE_PENNY_DROP";
    case BankVerificationType.CANCEL_CHEQUE:
      return "CANCEL_CHEQUE";
    case BankVerificationType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum BankVerificationProvider {
  UNKNOWN_PROVIDER = 0,
  DIGIO_PD = 1,
  SETU_RPD = 2,
  CHEQUE = 3,
  SIGNZY_OCR_READ = 4,
  UNRECOGNIZED = -1,
}

export function bankVerificationProviderFromJSON(object: any): BankVerificationProvider {
  switch (object) {
    case 0:
    case "UNKNOWN_PROVIDER":
      return BankVerificationProvider.UNKNOWN_PROVIDER;
    case 1:
    case "DIGIO_PD":
      return BankVerificationProvider.DIGIO_PD;
    case 2:
    case "SETU_RPD":
      return BankVerificationProvider.SETU_RPD;
    case 3:
    case "CHEQUE":
      return BankVerificationProvider.CHEQUE;
    case 4:
    case "SIGNZY_OCR_READ":
      return BankVerificationProvider.SIGNZY_OCR_READ;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BankVerificationProvider.UNRECOGNIZED;
  }
}

export function bankVerificationProviderToJSON(object: BankVerificationProvider): string {
  switch (object) {
    case BankVerificationProvider.UNKNOWN_PROVIDER:
      return "UNKNOWN_PROVIDER";
    case BankVerificationProvider.DIGIO_PD:
      return "DIGIO_PD";
    case BankVerificationProvider.SETU_RPD:
      return "SETU_RPD";
    case BankVerificationProvider.CHEQUE:
      return "CHEQUE";
    case BankVerificationProvider.SIGNZY_OCR_READ:
      return "SIGNZY_OCR_READ";
    case BankVerificationProvider.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface InitiateBankVerificationRequest {
  type: BankVerificationType;
  bankAccount?: BankAccount | undefined;
  cheque?: Cheque | undefined;
}

export interface BankAccount {
  beneficiaryAccountNo: string;
  ifscId: string;
  beneficiaryName: string;
}

export interface InitiateBankVerificationResponse {
  initiatePdResponse?: InitiatePdResponse | undefined;
  initiateRpdResponse?: InitiateRpdResponse | undefined;
  initiateChequeResponse?: InitiateChequeResponse | undefined;
}

export interface InitiatePdResponse {
  verified: boolean;
}

export interface InitiateRpdResponse {
  id: string;
  shortUrl: string;
  status: string;
  traceId: string;
  upiBillId: string;
  upiLink: string;
  validUpto: string;
}

export interface RpdStatusRequest {
  requestId: string;
}

export interface RpdStatusResponse {
  id: string;
  shortUrl: string;
  status: string;
  traceId: string;
  upiBillId: string;
  upiLink: string;
  validUpto: string;
  data: string;
  additionalData: AdditionalData | undefined;
  verified: boolean;
}

export interface AdditionalData {
  refId: string;
}

export interface BankListResponse {
  data: BankList[];
}

export interface BankList {
  bankName: string;
  ifsc: string;
  logo: string;
  isPopular: boolean;
  id: string;
}

export interface IfscListResponse {
  data: IfscList[];
}

export interface IfscList {
  bank: string;
  branch: string;
  ifsc: string;
  id: string;
}

export interface BankStatusResponse {
  isVerified: boolean;
  rpdStatus: string;
  pdStatus: string;
  chequeStatus: string;
  metadata: string;
  fuzzyMatchResult: boolean;
  accountNumber: string;
  accountHolderName: string;
  bankName: string;
  branchName: string;
  ifscCode: string;
  logo: string;
}

export interface Cheque {
  documentId: string;
}

export interface InitiateChequeResponse {
}

function createBaseInitiateBankVerificationRequest(): InitiateBankVerificationRequest {
  return { type: 0, bankAccount: undefined, cheque: undefined };
}

export const InitiateBankVerificationRequest: MessageFns<InitiateBankVerificationRequest> = {
  encode(message: InitiateBankVerificationRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== 0) {
      writer.uint32(8).int32(message.type);
    }
    if (message.bankAccount !== undefined) {
      BankAccount.encode(message.bankAccount, writer.uint32(18).fork()).join();
    }
    if (message.cheque !== undefined) {
      Cheque.encode(message.cheque, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateBankVerificationRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateBankVerificationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankAccount = BankAccount.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.cheque = Cheque.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateBankVerificationRequest {
    return {
      type: isSet(object.type) ? bankVerificationTypeFromJSON(object.type) : 0,
      bankAccount: isSet(object.bankAccount) ? BankAccount.fromJSON(object.bankAccount) : undefined,
      cheque: isSet(object.cheque) ? Cheque.fromJSON(object.cheque) : undefined,
    };
  },

  toJSON(message: InitiateBankVerificationRequest): unknown {
    const obj: any = {};
    if (message.type !== 0) {
      obj.type = bankVerificationTypeToJSON(message.type);
    }
    if (message.bankAccount !== undefined) {
      obj.bankAccount = BankAccount.toJSON(message.bankAccount);
    }
    if (message.cheque !== undefined) {
      obj.cheque = Cheque.toJSON(message.cheque);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateBankVerificationRequest>, I>>(base?: I): InitiateBankVerificationRequest {
    return InitiateBankVerificationRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateBankVerificationRequest>, I>>(
    object: I,
  ): InitiateBankVerificationRequest {
    const message = createBaseInitiateBankVerificationRequest();
    message.type = object.type ?? 0;
    message.bankAccount = (object.bankAccount !== undefined && object.bankAccount !== null)
      ? BankAccount.fromPartial(object.bankAccount)
      : undefined;
    message.cheque = (object.cheque !== undefined && object.cheque !== null)
      ? Cheque.fromPartial(object.cheque)
      : undefined;
    return message;
  },
};

function createBaseBankAccount(): BankAccount {
  return { beneficiaryAccountNo: "", ifscId: "", beneficiaryName: "" };
}

export const BankAccount: MessageFns<BankAccount> = {
  encode(message: BankAccount, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.beneficiaryAccountNo !== "") {
      writer.uint32(10).string(message.beneficiaryAccountNo);
    }
    if (message.ifscId !== "") {
      writer.uint32(18).string(message.ifscId);
    }
    if (message.beneficiaryName !== "") {
      writer.uint32(26).string(message.beneficiaryName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankAccount {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankAccount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.beneficiaryAccountNo = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ifscId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.beneficiaryName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankAccount {
    return {
      beneficiaryAccountNo: isSet(object.beneficiaryAccountNo) ? globalThis.String(object.beneficiaryAccountNo) : "",
      ifscId: isSet(object.ifscId) ? globalThis.String(object.ifscId) : "",
      beneficiaryName: isSet(object.beneficiaryName) ? globalThis.String(object.beneficiaryName) : "",
    };
  },

  toJSON(message: BankAccount): unknown {
    const obj: any = {};
    if (message.beneficiaryAccountNo !== "") {
      obj.beneficiaryAccountNo = message.beneficiaryAccountNo;
    }
    if (message.ifscId !== "") {
      obj.ifscId = message.ifscId;
    }
    if (message.beneficiaryName !== "") {
      obj.beneficiaryName = message.beneficiaryName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankAccount>, I>>(base?: I): BankAccount {
    return BankAccount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankAccount>, I>>(object: I): BankAccount {
    const message = createBaseBankAccount();
    message.beneficiaryAccountNo = object.beneficiaryAccountNo ?? "";
    message.ifscId = object.ifscId ?? "";
    message.beneficiaryName = object.beneficiaryName ?? "";
    return message;
  },
};

function createBaseInitiateBankVerificationResponse(): InitiateBankVerificationResponse {
  return { initiatePdResponse: undefined, initiateRpdResponse: undefined, initiateChequeResponse: undefined };
}

export const InitiateBankVerificationResponse: MessageFns<InitiateBankVerificationResponse> = {
  encode(message: InitiateBankVerificationResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.initiatePdResponse !== undefined) {
      InitiatePdResponse.encode(message.initiatePdResponse, writer.uint32(18).fork()).join();
    }
    if (message.initiateRpdResponse !== undefined) {
      InitiateRpdResponse.encode(message.initiateRpdResponse, writer.uint32(26).fork()).join();
    }
    if (message.initiateChequeResponse !== undefined) {
      InitiateChequeResponse.encode(message.initiateChequeResponse, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateBankVerificationResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateBankVerificationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.initiatePdResponse = InitiatePdResponse.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.initiateRpdResponse = InitiateRpdResponse.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.initiateChequeResponse = InitiateChequeResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateBankVerificationResponse {
    return {
      initiatePdResponse: isSet(object.initiatePdResponse)
        ? InitiatePdResponse.fromJSON(object.initiatePdResponse)
        : undefined,
      initiateRpdResponse: isSet(object.initiateRpdResponse)
        ? InitiateRpdResponse.fromJSON(object.initiateRpdResponse)
        : undefined,
      initiateChequeResponse: isSet(object.initiateChequeResponse)
        ? InitiateChequeResponse.fromJSON(object.initiateChequeResponse)
        : undefined,
    };
  },

  toJSON(message: InitiateBankVerificationResponse): unknown {
    const obj: any = {};
    if (message.initiatePdResponse !== undefined) {
      obj.initiatePdResponse = InitiatePdResponse.toJSON(message.initiatePdResponse);
    }
    if (message.initiateRpdResponse !== undefined) {
      obj.initiateRpdResponse = InitiateRpdResponse.toJSON(message.initiateRpdResponse);
    }
    if (message.initiateChequeResponse !== undefined) {
      obj.initiateChequeResponse = InitiateChequeResponse.toJSON(message.initiateChequeResponse);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateBankVerificationResponse>, I>>(
    base?: I,
  ): InitiateBankVerificationResponse {
    return InitiateBankVerificationResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateBankVerificationResponse>, I>>(
    object: I,
  ): InitiateBankVerificationResponse {
    const message = createBaseInitiateBankVerificationResponse();
    message.initiatePdResponse = (object.initiatePdResponse !== undefined && object.initiatePdResponse !== null)
      ? InitiatePdResponse.fromPartial(object.initiatePdResponse)
      : undefined;
    message.initiateRpdResponse = (object.initiateRpdResponse !== undefined && object.initiateRpdResponse !== null)
      ? InitiateRpdResponse.fromPartial(object.initiateRpdResponse)
      : undefined;
    message.initiateChequeResponse =
      (object.initiateChequeResponse !== undefined && object.initiateChequeResponse !== null)
        ? InitiateChequeResponse.fromPartial(object.initiateChequeResponse)
        : undefined;
    return message;
  },
};

function createBaseInitiatePdResponse(): InitiatePdResponse {
  return { verified: false };
}

export const InitiatePdResponse: MessageFns<InitiatePdResponse> = {
  encode(message: InitiatePdResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.verified !== false) {
      writer.uint32(8).bool(message.verified);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiatePdResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiatePdResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.verified = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiatePdResponse {
    return { verified: isSet(object.verified) ? globalThis.Boolean(object.verified) : false };
  },

  toJSON(message: InitiatePdResponse): unknown {
    const obj: any = {};
    if (message.verified !== false) {
      obj.verified = message.verified;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiatePdResponse>, I>>(base?: I): InitiatePdResponse {
    return InitiatePdResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiatePdResponse>, I>>(object: I): InitiatePdResponse {
    const message = createBaseInitiatePdResponse();
    message.verified = object.verified ?? false;
    return message;
  },
};

function createBaseInitiateRpdResponse(): InitiateRpdResponse {
  return { id: "", shortUrl: "", status: "", traceId: "", upiBillId: "", upiLink: "", validUpto: "" };
}

export const InitiateRpdResponse: MessageFns<InitiateRpdResponse> = {
  encode(message: InitiateRpdResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.shortUrl !== "") {
      writer.uint32(18).string(message.shortUrl);
    }
    if (message.status !== "") {
      writer.uint32(26).string(message.status);
    }
    if (message.traceId !== "") {
      writer.uint32(34).string(message.traceId);
    }
    if (message.upiBillId !== "") {
      writer.uint32(42).string(message.upiBillId);
    }
    if (message.upiLink !== "") {
      writer.uint32(50).string(message.upiLink);
    }
    if (message.validUpto !== "") {
      writer.uint32(58).string(message.validUpto);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateRpdResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateRpdResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.shortUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.status = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.traceId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.upiBillId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.upiLink = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.validUpto = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateRpdResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      shortUrl: isSet(object.shortUrl) ? globalThis.String(object.shortUrl) : "",
      status: isSet(object.status) ? globalThis.String(object.status) : "",
      traceId: isSet(object.traceId) ? globalThis.String(object.traceId) : "",
      upiBillId: isSet(object.upiBillId) ? globalThis.String(object.upiBillId) : "",
      upiLink: isSet(object.upiLink) ? globalThis.String(object.upiLink) : "",
      validUpto: isSet(object.validUpto) ? globalThis.String(object.validUpto) : "",
    };
  },

  toJSON(message: InitiateRpdResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.shortUrl !== "") {
      obj.shortUrl = message.shortUrl;
    }
    if (message.status !== "") {
      obj.status = message.status;
    }
    if (message.traceId !== "") {
      obj.traceId = message.traceId;
    }
    if (message.upiBillId !== "") {
      obj.upiBillId = message.upiBillId;
    }
    if (message.upiLink !== "") {
      obj.upiLink = message.upiLink;
    }
    if (message.validUpto !== "") {
      obj.validUpto = message.validUpto;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateRpdResponse>, I>>(base?: I): InitiateRpdResponse {
    return InitiateRpdResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateRpdResponse>, I>>(object: I): InitiateRpdResponse {
    const message = createBaseInitiateRpdResponse();
    message.id = object.id ?? "";
    message.shortUrl = object.shortUrl ?? "";
    message.status = object.status ?? "";
    message.traceId = object.traceId ?? "";
    message.upiBillId = object.upiBillId ?? "";
    message.upiLink = object.upiLink ?? "";
    message.validUpto = object.validUpto ?? "";
    return message;
  },
};

function createBaseRpdStatusRequest(): RpdStatusRequest {
  return { requestId: "" };
}

export const RpdStatusRequest: MessageFns<RpdStatusRequest> = {
  encode(message: RpdStatusRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.requestId !== "") {
      writer.uint32(10).string(message.requestId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RpdStatusRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRpdStatusRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RpdStatusRequest {
    return { requestId: isSet(object.requestId) ? globalThis.String(object.requestId) : "" };
  },

  toJSON(message: RpdStatusRequest): unknown {
    const obj: any = {};
    if (message.requestId !== "") {
      obj.requestId = message.requestId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RpdStatusRequest>, I>>(base?: I): RpdStatusRequest {
    return RpdStatusRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RpdStatusRequest>, I>>(object: I): RpdStatusRequest {
    const message = createBaseRpdStatusRequest();
    message.requestId = object.requestId ?? "";
    return message;
  },
};

function createBaseRpdStatusResponse(): RpdStatusResponse {
  return {
    id: "",
    shortUrl: "",
    status: "",
    traceId: "",
    upiBillId: "",
    upiLink: "",
    validUpto: "",
    data: "",
    additionalData: undefined,
    verified: false,
  };
}

export const RpdStatusResponse: MessageFns<RpdStatusResponse> = {
  encode(message: RpdStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.shortUrl !== "") {
      writer.uint32(18).string(message.shortUrl);
    }
    if (message.status !== "") {
      writer.uint32(26).string(message.status);
    }
    if (message.traceId !== "") {
      writer.uint32(34).string(message.traceId);
    }
    if (message.upiBillId !== "") {
      writer.uint32(42).string(message.upiBillId);
    }
    if (message.upiLink !== "") {
      writer.uint32(50).string(message.upiLink);
    }
    if (message.validUpto !== "") {
      writer.uint32(58).string(message.validUpto);
    }
    if (message.data !== "") {
      writer.uint32(66).string(message.data);
    }
    if (message.additionalData !== undefined) {
      AdditionalData.encode(message.additionalData, writer.uint32(74).fork()).join();
    }
    if (message.verified !== false) {
      writer.uint32(80).bool(message.verified);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RpdStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRpdStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.shortUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.status = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.traceId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.upiBillId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.upiLink = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.validUpto = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.data = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.additionalData = AdditionalData.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.verified = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RpdStatusResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      shortUrl: isSet(object.shortUrl) ? globalThis.String(object.shortUrl) : "",
      status: isSet(object.status) ? globalThis.String(object.status) : "",
      traceId: isSet(object.traceId) ? globalThis.String(object.traceId) : "",
      upiBillId: isSet(object.upiBillId) ? globalThis.String(object.upiBillId) : "",
      upiLink: isSet(object.upiLink) ? globalThis.String(object.upiLink) : "",
      validUpto: isSet(object.validUpto) ? globalThis.String(object.validUpto) : "",
      data: isSet(object.data) ? globalThis.String(object.data) : "",
      additionalData: isSet(object.additionalData) ? AdditionalData.fromJSON(object.additionalData) : undefined,
      verified: isSet(object.verified) ? globalThis.Boolean(object.verified) : false,
    };
  },

  toJSON(message: RpdStatusResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.shortUrl !== "") {
      obj.shortUrl = message.shortUrl;
    }
    if (message.status !== "") {
      obj.status = message.status;
    }
    if (message.traceId !== "") {
      obj.traceId = message.traceId;
    }
    if (message.upiBillId !== "") {
      obj.upiBillId = message.upiBillId;
    }
    if (message.upiLink !== "") {
      obj.upiLink = message.upiLink;
    }
    if (message.validUpto !== "") {
      obj.validUpto = message.validUpto;
    }
    if (message.data !== "") {
      obj.data = message.data;
    }
    if (message.additionalData !== undefined) {
      obj.additionalData = AdditionalData.toJSON(message.additionalData);
    }
    if (message.verified !== false) {
      obj.verified = message.verified;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RpdStatusResponse>, I>>(base?: I): RpdStatusResponse {
    return RpdStatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RpdStatusResponse>, I>>(object: I): RpdStatusResponse {
    const message = createBaseRpdStatusResponse();
    message.id = object.id ?? "";
    message.shortUrl = object.shortUrl ?? "";
    message.status = object.status ?? "";
    message.traceId = object.traceId ?? "";
    message.upiBillId = object.upiBillId ?? "";
    message.upiLink = object.upiLink ?? "";
    message.validUpto = object.validUpto ?? "";
    message.data = object.data ?? "";
    message.additionalData = (object.additionalData !== undefined && object.additionalData !== null)
      ? AdditionalData.fromPartial(object.additionalData)
      : undefined;
    message.verified = object.verified ?? false;
    return message;
  },
};

function createBaseAdditionalData(): AdditionalData {
  return { refId: "" };
}

export const AdditionalData: MessageFns<AdditionalData> = {
  encode(message: AdditionalData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.refId !== "") {
      writer.uint32(10).string(message.refId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AdditionalData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAdditionalData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.refId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AdditionalData {
    return { refId: isSet(object.refId) ? globalThis.String(object.refId) : "" };
  },

  toJSON(message: AdditionalData): unknown {
    const obj: any = {};
    if (message.refId !== "") {
      obj.refId = message.refId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AdditionalData>, I>>(base?: I): AdditionalData {
    return AdditionalData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AdditionalData>, I>>(object: I): AdditionalData {
    const message = createBaseAdditionalData();
    message.refId = object.refId ?? "";
    return message;
  },
};

function createBaseBankListResponse(): BankListResponse {
  return { data: [] };
}

export const BankListResponse: MessageFns<BankListResponse> = {
  encode(message: BankListResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      BankList.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.data.push(BankList.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListResponse {
    return { data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => BankList.fromJSON(e)) : [] };
  },

  toJSON(message: BankListResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => BankList.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListResponse>, I>>(base?: I): BankListResponse {
    return BankListResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListResponse>, I>>(object: I): BankListResponse {
    const message = createBaseBankListResponse();
    message.data = object.data?.map((e) => BankList.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBankList(): BankList {
  return { bankName: "", ifsc: "", logo: "", isPopular: false, id: "" };
}

export const BankList: MessageFns<BankList> = {
  encode(message: BankList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankName !== "") {
      writer.uint32(10).string(message.bankName);
    }
    if (message.ifsc !== "") {
      writer.uint32(18).string(message.ifsc);
    }
    if (message.logo !== "") {
      writer.uint32(26).string(message.logo);
    }
    if (message.isPopular !== false) {
      writer.uint32(32).bool(message.isPopular);
    }
    if (message.id !== "") {
      writer.uint32(42).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.ifsc = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.logo = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isPopular = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankList {
    return {
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      ifsc: isSet(object.ifsc) ? globalThis.String(object.ifsc) : "",
      logo: isSet(object.logo) ? globalThis.String(object.logo) : "",
      isPopular: isSet(object.isPopular) ? globalThis.Boolean(object.isPopular) : false,
      id: isSet(object.id) ? globalThis.String(object.id) : "",
    };
  },

  toJSON(message: BankList): unknown {
    const obj: any = {};
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.ifsc !== "") {
      obj.ifsc = message.ifsc;
    }
    if (message.logo !== "") {
      obj.logo = message.logo;
    }
    if (message.isPopular !== false) {
      obj.isPopular = message.isPopular;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankList>, I>>(base?: I): BankList {
    return BankList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankList>, I>>(object: I): BankList {
    const message = createBaseBankList();
    message.bankName = object.bankName ?? "";
    message.ifsc = object.ifsc ?? "";
    message.logo = object.logo ?? "";
    message.isPopular = object.isPopular ?? false;
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseIfscListResponse(): IfscListResponse {
  return { data: [] };
}

export const IfscListResponse: MessageFns<IfscListResponse> = {
  encode(message: IfscListResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      IfscList.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IfscListResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIfscListResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.data.push(IfscList.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IfscListResponse {
    return { data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => IfscList.fromJSON(e)) : [] };
  },

  toJSON(message: IfscListResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => IfscList.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IfscListResponse>, I>>(base?: I): IfscListResponse {
    return IfscListResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IfscListResponse>, I>>(object: I): IfscListResponse {
    const message = createBaseIfscListResponse();
    message.data = object.data?.map((e) => IfscList.fromPartial(e)) || [];
    return message;
  },
};

function createBaseIfscList(): IfscList {
  return { bank: "", branch: "", ifsc: "", id: "" };
}

export const IfscList: MessageFns<IfscList> = {
  encode(message: IfscList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bank !== "") {
      writer.uint32(10).string(message.bank);
    }
    if (message.branch !== "") {
      writer.uint32(18).string(message.branch);
    }
    if (message.ifsc !== "") {
      writer.uint32(26).string(message.ifsc);
    }
    if (message.id !== "") {
      writer.uint32(34).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IfscList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIfscList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bank = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.branch = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.ifsc = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IfscList {
    return {
      bank: isSet(object.bank) ? globalThis.String(object.bank) : "",
      branch: isSet(object.branch) ? globalThis.String(object.branch) : "",
      ifsc: isSet(object.ifsc) ? globalThis.String(object.ifsc) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
    };
  },

  toJSON(message: IfscList): unknown {
    const obj: any = {};
    if (message.bank !== "") {
      obj.bank = message.bank;
    }
    if (message.branch !== "") {
      obj.branch = message.branch;
    }
    if (message.ifsc !== "") {
      obj.ifsc = message.ifsc;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IfscList>, I>>(base?: I): IfscList {
    return IfscList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IfscList>, I>>(object: I): IfscList {
    const message = createBaseIfscList();
    message.bank = object.bank ?? "";
    message.branch = object.branch ?? "";
    message.ifsc = object.ifsc ?? "";
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseBankStatusResponse(): BankStatusResponse {
  return {
    isVerified: false,
    rpdStatus: "",
    pdStatus: "",
    chequeStatus: "",
    metadata: "",
    fuzzyMatchResult: false,
    accountNumber: "",
    accountHolderName: "",
    bankName: "",
    branchName: "",
    ifscCode: "",
    logo: "",
  };
}

export const BankStatusResponse: MessageFns<BankStatusResponse> = {
  encode(message: BankStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isVerified !== false) {
      writer.uint32(8).bool(message.isVerified);
    }
    if (message.rpdStatus !== "") {
      writer.uint32(18).string(message.rpdStatus);
    }
    if (message.pdStatus !== "") {
      writer.uint32(26).string(message.pdStatus);
    }
    if (message.chequeStatus !== "") {
      writer.uint32(34).string(message.chequeStatus);
    }
    if (message.metadata !== "") {
      writer.uint32(42).string(message.metadata);
    }
    if (message.fuzzyMatchResult !== false) {
      writer.uint32(48).bool(message.fuzzyMatchResult);
    }
    if (message.accountNumber !== "") {
      writer.uint32(58).string(message.accountNumber);
    }
    if (message.accountHolderName !== "") {
      writer.uint32(66).string(message.accountHolderName);
    }
    if (message.bankName !== "") {
      writer.uint32(74).string(message.bankName);
    }
    if (message.branchName !== "") {
      writer.uint32(82).string(message.branchName);
    }
    if (message.ifscCode !== "") {
      writer.uint32(90).string(message.ifscCode);
    }
    if (message.logo !== "") {
      writer.uint32(98).string(message.logo);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isVerified = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rpdStatus = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pdStatus = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.chequeStatus = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.metadata = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.fuzzyMatchResult = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.accountNumber = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.accountHolderName = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.branchName = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.ifscCode = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.logo = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankStatusResponse {
    return {
      isVerified: isSet(object.isVerified) ? globalThis.Boolean(object.isVerified) : false,
      rpdStatus: isSet(object.rpdStatus) ? globalThis.String(object.rpdStatus) : "",
      pdStatus: isSet(object.pdStatus) ? globalThis.String(object.pdStatus) : "",
      chequeStatus: isSet(object.chequeStatus) ? globalThis.String(object.chequeStatus) : "",
      metadata: isSet(object.metadata) ? globalThis.String(object.metadata) : "",
      fuzzyMatchResult: isSet(object.fuzzyMatchResult) ? globalThis.Boolean(object.fuzzyMatchResult) : false,
      accountNumber: isSet(object.accountNumber) ? globalThis.String(object.accountNumber) : "",
      accountHolderName: isSet(object.accountHolderName) ? globalThis.String(object.accountHolderName) : "",
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      branchName: isSet(object.branchName) ? globalThis.String(object.branchName) : "",
      ifscCode: isSet(object.ifscCode) ? globalThis.String(object.ifscCode) : "",
      logo: isSet(object.logo) ? globalThis.String(object.logo) : "",
    };
  },

  toJSON(message: BankStatusResponse): unknown {
    const obj: any = {};
    if (message.isVerified !== false) {
      obj.isVerified = message.isVerified;
    }
    if (message.rpdStatus !== "") {
      obj.rpdStatus = message.rpdStatus;
    }
    if (message.pdStatus !== "") {
      obj.pdStatus = message.pdStatus;
    }
    if (message.chequeStatus !== "") {
      obj.chequeStatus = message.chequeStatus;
    }
    if (message.metadata !== "") {
      obj.metadata = message.metadata;
    }
    if (message.fuzzyMatchResult !== false) {
      obj.fuzzyMatchResult = message.fuzzyMatchResult;
    }
    if (message.accountNumber !== "") {
      obj.accountNumber = message.accountNumber;
    }
    if (message.accountHolderName !== "") {
      obj.accountHolderName = message.accountHolderName;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.branchName !== "") {
      obj.branchName = message.branchName;
    }
    if (message.ifscCode !== "") {
      obj.ifscCode = message.ifscCode;
    }
    if (message.logo !== "") {
      obj.logo = message.logo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankStatusResponse>, I>>(base?: I): BankStatusResponse {
    return BankStatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankStatusResponse>, I>>(object: I): BankStatusResponse {
    const message = createBaseBankStatusResponse();
    message.isVerified = object.isVerified ?? false;
    message.rpdStatus = object.rpdStatus ?? "";
    message.pdStatus = object.pdStatus ?? "";
    message.chequeStatus = object.chequeStatus ?? "";
    message.metadata = object.metadata ?? "";
    message.fuzzyMatchResult = object.fuzzyMatchResult ?? false;
    message.accountNumber = object.accountNumber ?? "";
    message.accountHolderName = object.accountHolderName ?? "";
    message.bankName = object.bankName ?? "";
    message.branchName = object.branchName ?? "";
    message.ifscCode = object.ifscCode ?? "";
    message.logo = object.logo ?? "";
    return message;
  },
};

function createBaseCheque(): Cheque {
  return { documentId: "" };
}

export const Cheque: MessageFns<Cheque> = {
  encode(message: Cheque, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.documentId !== "") {
      writer.uint32(10).string(message.documentId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Cheque {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCheque();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.documentId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Cheque {
    return { documentId: isSet(object.documentId) ? globalThis.String(object.documentId) : "" };
  },

  toJSON(message: Cheque): unknown {
    const obj: any = {};
    if (message.documentId !== "") {
      obj.documentId = message.documentId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Cheque>, I>>(base?: I): Cheque {
    return Cheque.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Cheque>, I>>(object: I): Cheque {
    const message = createBaseCheque();
    message.documentId = object.documentId ?? "";
    return message;
  },
};

function createBaseInitiateChequeResponse(): InitiateChequeResponse {
  return {};
}

export const InitiateChequeResponse: MessageFns<InitiateChequeResponse> = {
  encode(_: InitiateChequeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateChequeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateChequeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): InitiateChequeResponse {
    return {};
  },

  toJSON(_: InitiateChequeResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateChequeResponse>, I>>(base?: I): InitiateChequeResponse {
    return InitiateChequeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateChequeResponse>, I>>(_: I): InitiateChequeResponse {
    const message = createBaseInitiateChequeResponse();
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
