// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Contact.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export interface SaveContactListRequest {
  contact: Contact[];
}

export interface Contact {
  name: string;
  phoneNumber: string;
  isOnboarded?: boolean | undefined;
}

export interface SaveContactListResponse {
}

export interface GetContactListResponse {
  contact: Contact[];
}

function createBaseSaveContactListRequest(): SaveContactListRequest {
  return { contact: [] };
}

export const SaveContactListRequest: MessageFns<SaveContactListRequest> = {
  encode(message: SaveContactListRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.contact) {
      Contact.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SaveContactListRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSaveContactListRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.contact.push(Contact.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SaveContactListRequest {
    return {
      contact: globalThis.Array.isArray(object?.contact) ? object.contact.map((e: any) => Contact.fromJSON(e)) : [],
    };
  },

  toJSON(message: SaveContactListRequest): unknown {
    const obj: any = {};
    if (message.contact?.length) {
      obj.contact = message.contact.map((e) => Contact.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SaveContactListRequest>, I>>(base?: I): SaveContactListRequest {
    return SaveContactListRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveContactListRequest>, I>>(object: I): SaveContactListRequest {
    const message = createBaseSaveContactListRequest();
    message.contact = object.contact?.map((e) => Contact.fromPartial(e)) || [];
    return message;
  },
};

function createBaseContact(): Contact {
  return { name: "", phoneNumber: "", isOnboarded: undefined };
}

export const Contact: MessageFns<Contact> = {
  encode(message: Contact, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.phoneNumber !== "") {
      writer.uint32(18).string(message.phoneNumber);
    }
    if (message.isOnboarded !== undefined) {
      writer.uint32(24).bool(message.isOnboarded);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Contact {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseContact();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.phoneNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isOnboarded = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Contact {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      phoneNumber: isSet(object.phoneNumber) ? globalThis.String(object.phoneNumber) : "",
      isOnboarded: isSet(object.isOnboarded) ? globalThis.Boolean(object.isOnboarded) : undefined,
    };
  },

  toJSON(message: Contact): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.phoneNumber !== "") {
      obj.phoneNumber = message.phoneNumber;
    }
    if (message.isOnboarded !== undefined) {
      obj.isOnboarded = message.isOnboarded;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Contact>, I>>(base?: I): Contact {
    return Contact.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Contact>, I>>(object: I): Contact {
    const message = createBaseContact();
    message.name = object.name ?? "";
    message.phoneNumber = object.phoneNumber ?? "";
    message.isOnboarded = object.isOnboarded ?? undefined;
    return message;
  },
};

function createBaseSaveContactListResponse(): SaveContactListResponse {
  return {};
}

export const SaveContactListResponse: MessageFns<SaveContactListResponse> = {
  encode(_: SaveContactListResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SaveContactListResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSaveContactListResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): SaveContactListResponse {
    return {};
  },

  toJSON(_: SaveContactListResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SaveContactListResponse>, I>>(base?: I): SaveContactListResponse {
    return SaveContactListResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveContactListResponse>, I>>(_: I): SaveContactListResponse {
    const message = createBaseSaveContactListResponse();
    return message;
  },
};

function createBaseGetContactListResponse(): GetContactListResponse {
  return { contact: [] };
}

export const GetContactListResponse: MessageFns<GetContactListResponse> = {
  encode(message: GetContactListResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.contact) {
      Contact.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetContactListResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetContactListResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.contact.push(Contact.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetContactListResponse {
    return {
      contact: globalThis.Array.isArray(object?.contact) ? object.contact.map((e: any) => Contact.fromJSON(e)) : [],
    };
  },

  toJSON(message: GetContactListResponse): unknown {
    const obj: any = {};
    if (message.contact?.length) {
      obj.contact = message.contact.map((e) => Contact.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetContactListResponse>, I>>(base?: I): GetContactListResponse {
    return GetContactListResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetContactListResponse>, I>>(object: I): GetContactListResponse {
    const message = createBaseGetContactListResponse();
    message.contact = object.contact?.map((e) => Contact.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
