// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Common.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum MessageType {
  UNKNOWN_MESSAGE_TYPE = 0,
  PROMOTIONAL_MESSAGE = 1,
  TRANSACTIONAL_MESSAGE = 2,
  OTP_MESSAGE = 3,
  UNRECOGNIZED = -1,
}

export function messageTypeFromJSON(object: any): MessageType {
  switch (object) {
    case 0:
    case "UNKNOWN_MESSAGE_TYPE":
      return MessageType.UNKNOWN_MESSAGE_TYPE;
    case 1:
    case "PROMOTIONAL_MESSAGE":
      return MessageType.PROMOTIONAL_MESSAGE;
    case 2:
    case "TRANSACTIONAL_MESSAGE":
      return MessageType.TRANSACTIONAL_MESSAGE;
    case 3:
    case "OTP_MESSAGE":
      return MessageType.OTP_MESSAGE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MessageType.UNRECOGNIZED;
  }
}

export function messageTypeToJSON(object: MessageType): string {
  switch (object) {
    case MessageType.UNKNOWN_MESSAGE_TYPE:
      return "UNKNOWN_MESSAGE_TYPE";
    case MessageType.PROMOTIONAL_MESSAGE:
      return "PROMOTIONAL_MESSAGE";
    case MessageType.TRANSACTIONAL_MESSAGE:
      return "TRANSACTIONAL_MESSAGE";
    case MessageType.OTP_MESSAGE:
      return "OTP_MESSAGE";
    case MessageType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ContentType {
  UNKNOWN_CONTENT_TYPE = 0,
  TEXT = 1,
  UNICODE = 2,
  UNRECOGNIZED = -1,
}

export function contentTypeFromJSON(object: any): ContentType {
  switch (object) {
    case 0:
    case "UNKNOWN_CONTENT_TYPE":
      return ContentType.UNKNOWN_CONTENT_TYPE;
    case 1:
    case "TEXT":
      return ContentType.TEXT;
    case 2:
    case "UNICODE":
      return ContentType.UNICODE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ContentType.UNRECOGNIZED;
  }
}

export function contentTypeToJSON(object: ContentType): string {
  switch (object) {
    case ContentType.UNKNOWN_CONTENT_TYPE:
      return "UNKNOWN_CONTENT_TYPE";
    case ContentType.TEXT:
      return "TEXT";
    case ContentType.UNICODE:
      return "UNICODE";
    case ContentType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum OptinStatus {
  UNKNOWN_OPTIN_STATUS = 0,
  OPTED_IN = 1,
  OPTED_OUT = 2,
  UNRECOGNIZED = -1,
}

export function optinStatusFromJSON(object: any): OptinStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_OPTIN_STATUS":
      return OptinStatus.UNKNOWN_OPTIN_STATUS;
    case 1:
    case "OPTED_IN":
      return OptinStatus.OPTED_IN;
    case 2:
    case "OPTED_OUT":
      return OptinStatus.OPTED_OUT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return OptinStatus.UNRECOGNIZED;
  }
}

export function optinStatusToJSON(object: OptinStatus): string {
  switch (object) {
    case OptinStatus.UNKNOWN_OPTIN_STATUS:
      return "UNKNOWN_OPTIN_STATUS";
    case OptinStatus.OPTED_IN:
      return "OPTED_IN";
    case OptinStatus.OPTED_OUT:
      return "OPTED_OUT";
    case OptinStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum WhatsappProviderType {
  UNKNOWN_WHATSAPP_PROVIDER_TYPE = 0,
  GUPSHUP = 1,
  UNRECOGNIZED = -1,
}

export function whatsappProviderTypeFromJSON(object: any): WhatsappProviderType {
  switch (object) {
    case 0:
    case "UNKNOWN_WHATSAPP_PROVIDER_TYPE":
      return WhatsappProviderType.UNKNOWN_WHATSAPP_PROVIDER_TYPE;
    case 1:
    case "GUPSHUP":
      return WhatsappProviderType.GUPSHUP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WhatsappProviderType.UNRECOGNIZED;
  }
}

export function whatsappProviderTypeToJSON(object: WhatsappProviderType): string {
  switch (object) {
    case WhatsappProviderType.UNKNOWN_WHATSAPP_PROVIDER_TYPE:
      return "UNKNOWN_WHATSAPP_PROVIDER_TYPE";
    case WhatsappProviderType.GUPSHUP:
      return "GUPSHUP";
    case WhatsappProviderType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AppConfigType {
  APP_CONFIG_TYPE_UNKNOWN = 0,
  BANK_VERIFICATION_RPD = 1,
  BANK_VERIFICATION_PD = 2,
  BASIC_DETAILS_QUESTIONS = 3,
  PAN_CVL_CHECK = 4,
  RPD_SUPPORTED_UPI_APPS = 5,
  CREDIT_REPORT = 6,
  MOBILE_RESEND = 7,
  MOBILE_ATTEMPT = 8,
  MOBILE_OTP_LENGTH = 9,
  EMAIL_OTP_LENGTH = 10,
  EMAIL_RESEND = 11,
  EMAIL_ATTEMPT = 12,
  ANDROID_APP_VERSION = 13,
  IOS_APP_VERSION = 14,
  WEB_APP_VERSION = 15,
  MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16,
  EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17,
  MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18,
  EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19,
  EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20,
  EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21,
  MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22,
  MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23,
  MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24,
  EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25,
  MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26,
  TOKEN_VALIDITY_IN_HOURS = 27,
  REFRESH_TOKEN_VALIDITY_IN_HOURS = 28,
  ENCRYPTION_PUBLIC_KEY = 29,
  ZOHO_CONFIG = 30,
  IN_APP_RATING_CONFIG = 31,
  GT_GRACE_PERIOD_IN_DAYS = 32,
  RSA_ENCRYPTION_PUBLIC_KEY = 33,
  UNRECOGNIZED = -1,
}

export function appConfigTypeFromJSON(object: any): AppConfigType {
  switch (object) {
    case 0:
    case "APP_CONFIG_TYPE_UNKNOWN":
      return AppConfigType.APP_CONFIG_TYPE_UNKNOWN;
    case 1:
    case "BANK_VERIFICATION_RPD":
      return AppConfigType.BANK_VERIFICATION_RPD;
    case 2:
    case "BANK_VERIFICATION_PD":
      return AppConfigType.BANK_VERIFICATION_PD;
    case 3:
    case "BASIC_DETAILS_QUESTIONS":
      return AppConfigType.BASIC_DETAILS_QUESTIONS;
    case 4:
    case "PAN_CVL_CHECK":
      return AppConfigType.PAN_CVL_CHECK;
    case 5:
    case "RPD_SUPPORTED_UPI_APPS":
      return AppConfigType.RPD_SUPPORTED_UPI_APPS;
    case 6:
    case "CREDIT_REPORT":
      return AppConfigType.CREDIT_REPORT;
    case 7:
    case "MOBILE_RESEND":
      return AppConfigType.MOBILE_RESEND;
    case 8:
    case "MOBILE_ATTEMPT":
      return AppConfigType.MOBILE_ATTEMPT;
    case 9:
    case "MOBILE_OTP_LENGTH":
      return AppConfigType.MOBILE_OTP_LENGTH;
    case 10:
    case "EMAIL_OTP_LENGTH":
      return AppConfigType.EMAIL_OTP_LENGTH;
    case 11:
    case "EMAIL_RESEND":
      return AppConfigType.EMAIL_RESEND;
    case 12:
    case "EMAIL_ATTEMPT":
      return AppConfigType.EMAIL_ATTEMPT;
    case 13:
    case "ANDROID_APP_VERSION":
      return AppConfigType.ANDROID_APP_VERSION;
    case 14:
    case "IOS_APP_VERSION":
      return AppConfigType.IOS_APP_VERSION;
    case 15:
    case "WEB_APP_VERSION":
      return AppConfigType.WEB_APP_VERSION;
    case 16:
    case "MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS":
      return AppConfigType.MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS;
    case 17:
    case "EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS":
      return AppConfigType.EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS;
    case 18:
    case "MOBILE_OTP_EXPIRY_TIME_IN_MINUTES":
      return AppConfigType.MOBILE_OTP_EXPIRY_TIME_IN_MINUTES;
    case 19:
    case "EMAIL_OTP_EXPIRY_TIME_IN_MINUTES":
      return AppConfigType.EMAIL_OTP_EXPIRY_TIME_IN_MINUTES;
    case 20:
    case "EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES":
      return AppConfigType.EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES;
    case 21:
    case "EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS":
      return AppConfigType.EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS;
    case 22:
    case "MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS":
      return AppConfigType.MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS;
    case 23:
    case "MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS":
      return AppConfigType.MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS;
    case 24:
    case "MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES":
      return AppConfigType.MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES;
    case 25:
    case "EMAIL_OTP_RESEND_TIMER_IN_SECONDS":
      return AppConfigType.EMAIL_OTP_RESEND_TIMER_IN_SECONDS;
    case 26:
    case "MOBILE_OTP_RESEND_TIMER_IN_SECONDS":
      return AppConfigType.MOBILE_OTP_RESEND_TIMER_IN_SECONDS;
    case 27:
    case "TOKEN_VALIDITY_IN_HOURS":
      return AppConfigType.TOKEN_VALIDITY_IN_HOURS;
    case 28:
    case "REFRESH_TOKEN_VALIDITY_IN_HOURS":
      return AppConfigType.REFRESH_TOKEN_VALIDITY_IN_HOURS;
    case 29:
    case "ENCRYPTION_PUBLIC_KEY":
      return AppConfigType.ENCRYPTION_PUBLIC_KEY;
    case 30:
    case "ZOHO_CONFIG":
      return AppConfigType.ZOHO_CONFIG;
    case 31:
    case "IN_APP_RATING_CONFIG":
      return AppConfigType.IN_APP_RATING_CONFIG;
    case 32:
    case "GT_GRACE_PERIOD_IN_DAYS":
      return AppConfigType.GT_GRACE_PERIOD_IN_DAYS;
    case 33:
    case "RSA_ENCRYPTION_PUBLIC_KEY":
      return AppConfigType.RSA_ENCRYPTION_PUBLIC_KEY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppConfigType.UNRECOGNIZED;
  }
}

export function appConfigTypeToJSON(object: AppConfigType): string {
  switch (object) {
    case AppConfigType.APP_CONFIG_TYPE_UNKNOWN:
      return "APP_CONFIG_TYPE_UNKNOWN";
    case AppConfigType.BANK_VERIFICATION_RPD:
      return "BANK_VERIFICATION_RPD";
    case AppConfigType.BANK_VERIFICATION_PD:
      return "BANK_VERIFICATION_PD";
    case AppConfigType.BASIC_DETAILS_QUESTIONS:
      return "BASIC_DETAILS_QUESTIONS";
    case AppConfigType.PAN_CVL_CHECK:
      return "PAN_CVL_CHECK";
    case AppConfigType.RPD_SUPPORTED_UPI_APPS:
      return "RPD_SUPPORTED_UPI_APPS";
    case AppConfigType.CREDIT_REPORT:
      return "CREDIT_REPORT";
    case AppConfigType.MOBILE_RESEND:
      return "MOBILE_RESEND";
    case AppConfigType.MOBILE_ATTEMPT:
      return "MOBILE_ATTEMPT";
    case AppConfigType.MOBILE_OTP_LENGTH:
      return "MOBILE_OTP_LENGTH";
    case AppConfigType.EMAIL_OTP_LENGTH:
      return "EMAIL_OTP_LENGTH";
    case AppConfigType.EMAIL_RESEND:
      return "EMAIL_RESEND";
    case AppConfigType.EMAIL_ATTEMPT:
      return "EMAIL_ATTEMPT";
    case AppConfigType.ANDROID_APP_VERSION:
      return "ANDROID_APP_VERSION";
    case AppConfigType.IOS_APP_VERSION:
      return "IOS_APP_VERSION";
    case AppConfigType.WEB_APP_VERSION:
      return "WEB_APP_VERSION";
    case AppConfigType.MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS:
      return "MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS";
    case AppConfigType.EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS:
      return "EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS";
    case AppConfigType.MOBILE_OTP_EXPIRY_TIME_IN_MINUTES:
      return "MOBILE_OTP_EXPIRY_TIME_IN_MINUTES";
    case AppConfigType.EMAIL_OTP_EXPIRY_TIME_IN_MINUTES:
      return "EMAIL_OTP_EXPIRY_TIME_IN_MINUTES";
    case AppConfigType.EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES:
      return "EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES";
    case AppConfigType.EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS:
      return "EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS";
    case AppConfigType.MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS:
      return "MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS";
    case AppConfigType.MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS:
      return "MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS";
    case AppConfigType.MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES:
      return "MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES";
    case AppConfigType.EMAIL_OTP_RESEND_TIMER_IN_SECONDS:
      return "EMAIL_OTP_RESEND_TIMER_IN_SECONDS";
    case AppConfigType.MOBILE_OTP_RESEND_TIMER_IN_SECONDS:
      return "MOBILE_OTP_RESEND_TIMER_IN_SECONDS";
    case AppConfigType.TOKEN_VALIDITY_IN_HOURS:
      return "TOKEN_VALIDITY_IN_HOURS";
    case AppConfigType.REFRESH_TOKEN_VALIDITY_IN_HOURS:
      return "REFRESH_TOKEN_VALIDITY_IN_HOURS";
    case AppConfigType.ENCRYPTION_PUBLIC_KEY:
      return "ENCRYPTION_PUBLIC_KEY";
    case AppConfigType.ZOHO_CONFIG:
      return "ZOHO_CONFIG";
    case AppConfigType.IN_APP_RATING_CONFIG:
      return "IN_APP_RATING_CONFIG";
    case AppConfigType.GT_GRACE_PERIOD_IN_DAYS:
      return "GT_GRACE_PERIOD_IN_DAYS";
    case AppConfigType.RSA_ENCRYPTION_PUBLIC_KEY:
      return "RSA_ENCRYPTION_PUBLIC_KEY";
    case AppConfigType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AppConfigValueType {
  APP_CONFIG_VALUE_TYPE_UNKNOWN = 0,
  TEXT_TYPE = 1,
  BOOLEAN_TYPE = 2,
  STRING_TYPE = 3,
  JSON_TYPE = 4,
  INTEGER_TYPE = 5,
  UNRECOGNIZED = -1,
}

export function appConfigValueTypeFromJSON(object: any): AppConfigValueType {
  switch (object) {
    case 0:
    case "APP_CONFIG_VALUE_TYPE_UNKNOWN":
      return AppConfigValueType.APP_CONFIG_VALUE_TYPE_UNKNOWN;
    case 1:
    case "TEXT_TYPE":
      return AppConfigValueType.TEXT_TYPE;
    case 2:
    case "BOOLEAN_TYPE":
      return AppConfigValueType.BOOLEAN_TYPE;
    case 3:
    case "STRING_TYPE":
      return AppConfigValueType.STRING_TYPE;
    case 4:
    case "JSON_TYPE":
      return AppConfigValueType.JSON_TYPE;
    case 5:
    case "INTEGER_TYPE":
      return AppConfigValueType.INTEGER_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppConfigValueType.UNRECOGNIZED;
  }
}

export function appConfigValueTypeToJSON(object: AppConfigValueType): string {
  switch (object) {
    case AppConfigValueType.APP_CONFIG_VALUE_TYPE_UNKNOWN:
      return "APP_CONFIG_VALUE_TYPE_UNKNOWN";
    case AppConfigValueType.TEXT_TYPE:
      return "TEXT_TYPE";
    case AppConfigValueType.BOOLEAN_TYPE:
      return "BOOLEAN_TYPE";
    case AppConfigValueType.STRING_TYPE:
      return "STRING_TYPE";
    case AppConfigValueType.JSON_TYPE:
      return "JSON_TYPE";
    case AppConfigValueType.INTEGER_TYPE:
      return "INTEGER_TYPE";
    case AppConfigValueType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum PushNotificationType {
  UNKNOWN_PUSH_NOTIFICATION_TYPE = 0,
  TD_BOOKED_COMPLETE = 1,
  PAYMENT_SUCCESS = 2,
  TD_RESUME = 3,
  PAYMENT_FAILURE = 4,
  VKYC_SUCCESS = 5,
  VKYC_REMINDER = 6,
  VKYC_RETRY = 7,
  TD_WITHDRAWAL_COMPLETE = 8,
  TD_REJECTED = 9,
  TD_MATURITY = 10,
  TD_BOOKED_ACK = 11,
  PROGRESS_SAVED = 12,
  INVEST_INSTANT_FDS = 13,
  TICKET_ADDED = 14,
  NTB_AUTH_ERROR = 15,
  ETB_AUTH_ERROR = 16,
  BINDING_SUCCESS = 17,
  VKYC_FAILURE = 18,
  TD_MATURITY_INSTRUCTION_UPDATE_DUE = 19,
  TD_RENEWAL_COMPLETE = 20,
  UNRECOGNIZED = -1,
}

export function pushNotificationTypeFromJSON(object: any): PushNotificationType {
  switch (object) {
    case 0:
    case "UNKNOWN_PUSH_NOTIFICATION_TYPE":
      return PushNotificationType.UNKNOWN_PUSH_NOTIFICATION_TYPE;
    case 1:
    case "TD_BOOKED_COMPLETE":
      return PushNotificationType.TD_BOOKED_COMPLETE;
    case 2:
    case "PAYMENT_SUCCESS":
      return PushNotificationType.PAYMENT_SUCCESS;
    case 3:
    case "TD_RESUME":
      return PushNotificationType.TD_RESUME;
    case 4:
    case "PAYMENT_FAILURE":
      return PushNotificationType.PAYMENT_FAILURE;
    case 5:
    case "VKYC_SUCCESS":
      return PushNotificationType.VKYC_SUCCESS;
    case 6:
    case "VKYC_REMINDER":
      return PushNotificationType.VKYC_REMINDER;
    case 7:
    case "VKYC_RETRY":
      return PushNotificationType.VKYC_RETRY;
    case 8:
    case "TD_WITHDRAWAL_COMPLETE":
      return PushNotificationType.TD_WITHDRAWAL_COMPLETE;
    case 9:
    case "TD_REJECTED":
      return PushNotificationType.TD_REJECTED;
    case 10:
    case "TD_MATURITY":
      return PushNotificationType.TD_MATURITY;
    case 11:
    case "TD_BOOKED_ACK":
      return PushNotificationType.TD_BOOKED_ACK;
    case 12:
    case "PROGRESS_SAVED":
      return PushNotificationType.PROGRESS_SAVED;
    case 13:
    case "INVEST_INSTANT_FDS":
      return PushNotificationType.INVEST_INSTANT_FDS;
    case 14:
    case "TICKET_ADDED":
      return PushNotificationType.TICKET_ADDED;
    case 15:
    case "NTB_AUTH_ERROR":
      return PushNotificationType.NTB_AUTH_ERROR;
    case 16:
    case "ETB_AUTH_ERROR":
      return PushNotificationType.ETB_AUTH_ERROR;
    case 17:
    case "BINDING_SUCCESS":
      return PushNotificationType.BINDING_SUCCESS;
    case 18:
    case "VKYC_FAILURE":
      return PushNotificationType.VKYC_FAILURE;
    case 19:
    case "TD_MATURITY_INSTRUCTION_UPDATE_DUE":
      return PushNotificationType.TD_MATURITY_INSTRUCTION_UPDATE_DUE;
    case 20:
    case "TD_RENEWAL_COMPLETE":
      return PushNotificationType.TD_RENEWAL_COMPLETE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PushNotificationType.UNRECOGNIZED;
  }
}

export function pushNotificationTypeToJSON(object: PushNotificationType): string {
  switch (object) {
    case PushNotificationType.UNKNOWN_PUSH_NOTIFICATION_TYPE:
      return "UNKNOWN_PUSH_NOTIFICATION_TYPE";
    case PushNotificationType.TD_BOOKED_COMPLETE:
      return "TD_BOOKED_COMPLETE";
    case PushNotificationType.PAYMENT_SUCCESS:
      return "PAYMENT_SUCCESS";
    case PushNotificationType.TD_RESUME:
      return "TD_RESUME";
    case PushNotificationType.PAYMENT_FAILURE:
      return "PAYMENT_FAILURE";
    case PushNotificationType.VKYC_SUCCESS:
      return "VKYC_SUCCESS";
    case PushNotificationType.VKYC_REMINDER:
      return "VKYC_REMINDER";
    case PushNotificationType.VKYC_RETRY:
      return "VKYC_RETRY";
    case PushNotificationType.TD_WITHDRAWAL_COMPLETE:
      return "TD_WITHDRAWAL_COMPLETE";
    case PushNotificationType.TD_REJECTED:
      return "TD_REJECTED";
    case PushNotificationType.TD_MATURITY:
      return "TD_MATURITY";
    case PushNotificationType.TD_BOOKED_ACK:
      return "TD_BOOKED_ACK";
    case PushNotificationType.PROGRESS_SAVED:
      return "PROGRESS_SAVED";
    case PushNotificationType.INVEST_INSTANT_FDS:
      return "INVEST_INSTANT_FDS";
    case PushNotificationType.TICKET_ADDED:
      return "TICKET_ADDED";
    case PushNotificationType.NTB_AUTH_ERROR:
      return "NTB_AUTH_ERROR";
    case PushNotificationType.ETB_AUTH_ERROR:
      return "ETB_AUTH_ERROR";
    case PushNotificationType.BINDING_SUCCESS:
      return "BINDING_SUCCESS";
    case PushNotificationType.VKYC_FAILURE:
      return "VKYC_FAILURE";
    case PushNotificationType.TD_MATURITY_INSTRUCTION_UPDATE_DUE:
      return "TD_MATURITY_INSTRUCTION_UPDATE_DUE";
    case PushNotificationType.TD_RENEWAL_COMPLETE:
      return "TD_RENEWAL_COMPLETE";
    case PushNotificationType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ErrorResponse {
  errorCode: string;
  errorMessage: string;
}

export interface ConfigDataResponse {
  data: ConfigData[];
}

export interface ConfigData {
  configName: AppConfigType;
  configValue: string;
  configType: AppConfigValueType;
  minVersion: number;
  maxVersion: number;
  description: string;
}

export interface PaginationFilter {
  page: number;
  size?: number | undefined;
}

export interface DataKey {
  key: string;
  contextVariables: DataKey_Variable[];
}

export enum DataKey_VariableType {
  UNKNOWN = 0,
  STRING = 1,
  CURRENCY = 2,
  DATE = 3,
  DATE_TIME = 4,
  USER_FIRST_NAME = 5,
  SHORT_CURRENCY = 6,
  PERCENT = 7,
  PERCENT_2F = 8,
  NUMBER = 9,
  UNRECOGNIZED = -1,
}

export function dataKey_VariableTypeFromJSON(object: any): DataKey_VariableType {
  switch (object) {
    case 0:
    case "UNKNOWN":
      return DataKey_VariableType.UNKNOWN;
    case 1:
    case "STRING":
      return DataKey_VariableType.STRING;
    case 2:
    case "CURRENCY":
      return DataKey_VariableType.CURRENCY;
    case 3:
    case "DATE":
      return DataKey_VariableType.DATE;
    case 4:
    case "DATE_TIME":
      return DataKey_VariableType.DATE_TIME;
    case 5:
    case "USER_FIRST_NAME":
      return DataKey_VariableType.USER_FIRST_NAME;
    case 6:
    case "SHORT_CURRENCY":
      return DataKey_VariableType.SHORT_CURRENCY;
    case 7:
    case "PERCENT":
      return DataKey_VariableType.PERCENT;
    case 8:
    case "PERCENT_2F":
      return DataKey_VariableType.PERCENT_2F;
    case 9:
    case "NUMBER":
      return DataKey_VariableType.NUMBER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DataKey_VariableType.UNRECOGNIZED;
  }
}

export function dataKey_VariableTypeToJSON(object: DataKey_VariableType): string {
  switch (object) {
    case DataKey_VariableType.UNKNOWN:
      return "UNKNOWN";
    case DataKey_VariableType.STRING:
      return "STRING";
    case DataKey_VariableType.CURRENCY:
      return "CURRENCY";
    case DataKey_VariableType.DATE:
      return "DATE";
    case DataKey_VariableType.DATE_TIME:
      return "DATE_TIME";
    case DataKey_VariableType.USER_FIRST_NAME:
      return "USER_FIRST_NAME";
    case DataKey_VariableType.SHORT_CURRENCY:
      return "SHORT_CURRENCY";
    case DataKey_VariableType.PERCENT:
      return "PERCENT";
    case DataKey_VariableType.PERCENT_2F:
      return "PERCENT_2F";
    case DataKey_VariableType.NUMBER:
      return "NUMBER";
    case DataKey_VariableType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface DataKey_Variable {
  name: string;
  type: DataKey_VariableType;
  value: string;
}

export interface PaginationResponse {
  hasNextPage: boolean;
}

export interface PaginationRequest {
  page: number;
  size: number;
}

function createBaseErrorResponse(): ErrorResponse {
  return { errorCode: "", errorMessage: "" };
}

export const ErrorResponse: MessageFns<ErrorResponse> = {
  encode(message: ErrorResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.errorCode !== "") {
      writer.uint32(10).string(message.errorCode);
    }
    if (message.errorMessage !== "") {
      writer.uint32(18).string(message.errorMessage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ErrorResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseErrorResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.errorCode = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.errorMessage = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ErrorResponse {
    return {
      errorCode: isSet(object.errorCode) ? globalThis.String(object.errorCode) : "",
      errorMessage: isSet(object.errorMessage) ? globalThis.String(object.errorMessage) : "",
    };
  },

  toJSON(message: ErrorResponse): unknown {
    const obj: any = {};
    if (message.errorCode !== "") {
      obj.errorCode = message.errorCode;
    }
    if (message.errorMessage !== "") {
      obj.errorMessage = message.errorMessage;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ErrorResponse>, I>>(base?: I): ErrorResponse {
    return ErrorResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ErrorResponse>, I>>(object: I): ErrorResponse {
    const message = createBaseErrorResponse();
    message.errorCode = object.errorCode ?? "";
    message.errorMessage = object.errorMessage ?? "";
    return message;
  },
};

function createBaseConfigDataResponse(): ConfigDataResponse {
  return { data: [] };
}

export const ConfigDataResponse: MessageFns<ConfigDataResponse> = {
  encode(message: ConfigDataResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      ConfigData.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConfigDataResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfigDataResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(ConfigData.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfigDataResponse {
    return { data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => ConfigData.fromJSON(e)) : [] };
  },

  toJSON(message: ConfigDataResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => ConfigData.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfigDataResponse>, I>>(base?: I): ConfigDataResponse {
    return ConfigDataResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigDataResponse>, I>>(object: I): ConfigDataResponse {
    const message = createBaseConfigDataResponse();
    message.data = object.data?.map((e) => ConfigData.fromPartial(e)) || [];
    return message;
  },
};

function createBaseConfigData(): ConfigData {
  return { configName: 0, configValue: "", configType: 0, minVersion: 0, maxVersion: 0, description: "" };
}

export const ConfigData: MessageFns<ConfigData> = {
  encode(message: ConfigData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.configName !== 0) {
      writer.uint32(8).int32(message.configName);
    }
    if (message.configValue !== "") {
      writer.uint32(18).string(message.configValue);
    }
    if (message.configType !== 0) {
      writer.uint32(24).int32(message.configType);
    }
    if (message.minVersion !== 0) {
      writer.uint32(32).int32(message.minVersion);
    }
    if (message.maxVersion !== 0) {
      writer.uint32(40).int32(message.maxVersion);
    }
    if (message.description !== "") {
      writer.uint32(50).string(message.description);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConfigData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfigData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.configName = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.configValue = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.configType = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.minVersion = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.maxVersion = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.description = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfigData {
    return {
      configName: isSet(object.configName) ? appConfigTypeFromJSON(object.configName) : 0,
      configValue: isSet(object.configValue) ? globalThis.String(object.configValue) : "",
      configType: isSet(object.configType) ? appConfigValueTypeFromJSON(object.configType) : 0,
      minVersion: isSet(object.minVersion) ? globalThis.Number(object.minVersion) : 0,
      maxVersion: isSet(object.maxVersion) ? globalThis.Number(object.maxVersion) : 0,
      description: isSet(object.description) ? globalThis.String(object.description) : "",
    };
  },

  toJSON(message: ConfigData): unknown {
    const obj: any = {};
    if (message.configName !== 0) {
      obj.configName = appConfigTypeToJSON(message.configName);
    }
    if (message.configValue !== "") {
      obj.configValue = message.configValue;
    }
    if (message.configType !== 0) {
      obj.configType = appConfigValueTypeToJSON(message.configType);
    }
    if (message.minVersion !== 0) {
      obj.minVersion = Math.round(message.minVersion);
    }
    if (message.maxVersion !== 0) {
      obj.maxVersion = Math.round(message.maxVersion);
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfigData>, I>>(base?: I): ConfigData {
    return ConfigData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigData>, I>>(object: I): ConfigData {
    const message = createBaseConfigData();
    message.configName = object.configName ?? 0;
    message.configValue = object.configValue ?? "";
    message.configType = object.configType ?? 0;
    message.minVersion = object.minVersion ?? 0;
    message.maxVersion = object.maxVersion ?? 0;
    message.description = object.description ?? "";
    return message;
  },
};

function createBasePaginationFilter(): PaginationFilter {
  return { page: 0, size: undefined };
}

export const PaginationFilter: MessageFns<PaginationFilter> = {
  encode(message: PaginationFilter, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.page !== 0) {
      writer.uint32(8).int32(message.page);
    }
    if (message.size !== undefined) {
      writer.uint32(16).int32(message.size);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PaginationFilter {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePaginationFilter();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.page = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.size = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PaginationFilter {
    return {
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      size: isSet(object.size) ? globalThis.Number(object.size) : undefined,
    };
  },

  toJSON(message: PaginationFilter): unknown {
    const obj: any = {};
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.size !== undefined) {
      obj.size = Math.round(message.size);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PaginationFilter>, I>>(base?: I): PaginationFilter {
    return PaginationFilter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaginationFilter>, I>>(object: I): PaginationFilter {
    const message = createBasePaginationFilter();
    message.page = object.page ?? 0;
    message.size = object.size ?? undefined;
    return message;
  },
};

function createBaseDataKey(): DataKey {
  return { key: "", contextVariables: [] };
}

export const DataKey: MessageFns<DataKey> = {
  encode(message: DataKey, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    for (const v of message.contextVariables) {
      DataKey_Variable.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataKey {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataKey();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.contextVariables.push(DataKey_Variable.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataKey {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      contextVariables: globalThis.Array.isArray(object?.contextVariables)
        ? object.contextVariables.map((e: any) => DataKey_Variable.fromJSON(e))
        : [],
    };
  },

  toJSON(message: DataKey): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.contextVariables?.length) {
      obj.contextVariables = message.contextVariables.map((e) => DataKey_Variable.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataKey>, I>>(base?: I): DataKey {
    return DataKey.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataKey>, I>>(object: I): DataKey {
    const message = createBaseDataKey();
    message.key = object.key ?? "";
    message.contextVariables = object.contextVariables?.map((e) => DataKey_Variable.fromPartial(e)) || [];
    return message;
  },
};

function createBaseDataKey_Variable(): DataKey_Variable {
  return { name: "", type: 0, value: "" };
}

export const DataKey_Variable: MessageFns<DataKey_Variable> = {
  encode(message: DataKey_Variable, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.value !== "") {
      writer.uint32(26).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DataKey_Variable {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDataKey_Variable();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DataKey_Variable {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      type: isSet(object.type) ? dataKey_VariableTypeFromJSON(object.type) : 0,
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: DataKey_Variable): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.type !== 0) {
      obj.type = dataKey_VariableTypeToJSON(message.type);
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DataKey_Variable>, I>>(base?: I): DataKey_Variable {
    return DataKey_Variable.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DataKey_Variable>, I>>(object: I): DataKey_Variable {
    const message = createBaseDataKey_Variable();
    message.name = object.name ?? "";
    message.type = object.type ?? 0;
    message.value = object.value ?? "";
    return message;
  },
};

function createBasePaginationResponse(): PaginationResponse {
  return { hasNextPage: false };
}

export const PaginationResponse: MessageFns<PaginationResponse> = {
  encode(message: PaginationResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hasNextPage !== false) {
      writer.uint32(8).bool(message.hasNextPage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PaginationResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePaginationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.hasNextPage = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PaginationResponse {
    return { hasNextPage: isSet(object.hasNextPage) ? globalThis.Boolean(object.hasNextPage) : false };
  },

  toJSON(message: PaginationResponse): unknown {
    const obj: any = {};
    if (message.hasNextPage !== false) {
      obj.hasNextPage = message.hasNextPage;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PaginationResponse>, I>>(base?: I): PaginationResponse {
    return PaginationResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaginationResponse>, I>>(object: I): PaginationResponse {
    const message = createBasePaginationResponse();
    message.hasNextPage = object.hasNextPage ?? false;
    return message;
  },
};

function createBasePaginationRequest(): PaginationRequest {
  return { page: 0, size: 0 };
}

export const PaginationRequest: MessageFns<PaginationRequest> = {
  encode(message: PaginationRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.page !== 0) {
      writer.uint32(8).int32(message.page);
    }
    if (message.size !== 0) {
      writer.uint32(16).int32(message.size);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PaginationRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePaginationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.page = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.size = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PaginationRequest {
    return {
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      size: isSet(object.size) ? globalThis.Number(object.size) : 0,
    };
  },

  toJSON(message: PaginationRequest): unknown {
    const obj: any = {};
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.size !== 0) {
      obj.size = Math.round(message.size);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PaginationRequest>, I>>(base?: I): PaginationRequest {
    return PaginationRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaginationRequest>, I>>(object: I): PaginationRequest {
    const message = createBasePaginationRequest();
    message.page = object.page ?? 0;
    message.size = object.size ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
