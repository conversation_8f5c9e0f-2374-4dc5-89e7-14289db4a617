// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: SearchPage.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { BankResponseWithFdData } from "./Collection";

export const protobufPackage = "com.stablemoney.api.identity";

export interface SearchPageItem {
  bankResponseWithFdData: BankResponseWithFdData | undefined;
  displayName: string;
  operatingBankId: string;
}

export interface SearchPageResponse {
  searchPageItem: SearchPageItem[];
  searchHits: number;
}

function createBaseSearchPageItem(): SearchPageItem {
  return { bankResponseWithFdData: undefined, displayName: "", operatingBankId: "" };
}

export const SearchPageItem: MessageFns<SearchPageItem> = {
  encode(message: SearchPageItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankResponseWithFdData !== undefined) {
      BankResponseWithFdData.encode(message.bankResponseWithFdData, writer.uint32(10).fork()).join();
    }
    if (message.displayName !== "") {
      writer.uint32(18).string(message.displayName);
    }
    if (message.operatingBankId !== "") {
      writer.uint32(26).string(message.operatingBankId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchPageItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchPageItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankResponseWithFdData = BankResponseWithFdData.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.displayName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.operatingBankId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchPageItem {
    return {
      bankResponseWithFdData: isSet(object.bankResponseWithFdData)
        ? BankResponseWithFdData.fromJSON(object.bankResponseWithFdData)
        : undefined,
      displayName: isSet(object.displayName) ? globalThis.String(object.displayName) : "",
      operatingBankId: isSet(object.operatingBankId) ? globalThis.String(object.operatingBankId) : "",
    };
  },

  toJSON(message: SearchPageItem): unknown {
    const obj: any = {};
    if (message.bankResponseWithFdData !== undefined) {
      obj.bankResponseWithFdData = BankResponseWithFdData.toJSON(message.bankResponseWithFdData);
    }
    if (message.displayName !== "") {
      obj.displayName = message.displayName;
    }
    if (message.operatingBankId !== "") {
      obj.operatingBankId = message.operatingBankId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchPageItem>, I>>(base?: I): SearchPageItem {
    return SearchPageItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchPageItem>, I>>(object: I): SearchPageItem {
    const message = createBaseSearchPageItem();
    message.bankResponseWithFdData =
      (object.bankResponseWithFdData !== undefined && object.bankResponseWithFdData !== null)
        ? BankResponseWithFdData.fromPartial(object.bankResponseWithFdData)
        : undefined;
    message.displayName = object.displayName ?? "";
    message.operatingBankId = object.operatingBankId ?? "";
    return message;
  },
};

function createBaseSearchPageResponse(): SearchPageResponse {
  return { searchPageItem: [], searchHits: 0 };
}

export const SearchPageResponse: MessageFns<SearchPageResponse> = {
  encode(message: SearchPageResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.searchPageItem) {
      SearchPageItem.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.searchHits !== 0) {
      writer.uint32(16).int32(message.searchHits);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchPageResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchPageResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.searchPageItem.push(SearchPageItem.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.searchHits = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchPageResponse {
    return {
      searchPageItem: globalThis.Array.isArray(object?.searchPageItem)
        ? object.searchPageItem.map((e: any) => SearchPageItem.fromJSON(e))
        : [],
      searchHits: isSet(object.searchHits) ? globalThis.Number(object.searchHits) : 0,
    };
  },

  toJSON(message: SearchPageResponse): unknown {
    const obj: any = {};
    if (message.searchPageItem?.length) {
      obj.searchPageItem = message.searchPageItem.map((e) => SearchPageItem.toJSON(e));
    }
    if (message.searchHits !== 0) {
      obj.searchHits = Math.round(message.searchHits);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchPageResponse>, I>>(base?: I): SearchPageResponse {
    return SearchPageResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchPageResponse>, I>>(object: I): SearchPageResponse {
    const message = createBaseSearchPageResponse();
    message.searchPageItem = object.searchPageItem?.map((e) => SearchPageItem.fromPartial(e)) || [];
    message.searchHits = object.searchHits ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
