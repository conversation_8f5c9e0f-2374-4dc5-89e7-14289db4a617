// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Appsflyer.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export interface AppsFlyerUpdateTtlRequest {
  url: string;
}

export interface AppsFlyerUpdateTtlResponse {
}

function createBaseAppsFlyerUpdateTtlRequest(): AppsFlyerUpdateTtlRequest {
  return { url: "" };
}

export const AppsFlyerUpdateTtlRequest: MessageFns<AppsFlyerUpdateTtlRequest> = {
  encode(message: AppsFlyerUpdateTtlRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.url !== "") {
      writer.uint32(10).string(message.url);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AppsFlyerUpdateTtlRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppsFlyerUpdateTtlRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.url = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppsFlyerUpdateTtlRequest {
    return { url: isSet(object.url) ? globalThis.String(object.url) : "" };
  },

  toJSON(message: AppsFlyerUpdateTtlRequest): unknown {
    const obj: any = {};
    if (message.url !== "") {
      obj.url = message.url;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppsFlyerUpdateTtlRequest>, I>>(base?: I): AppsFlyerUpdateTtlRequest {
    return AppsFlyerUpdateTtlRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppsFlyerUpdateTtlRequest>, I>>(object: I): AppsFlyerUpdateTtlRequest {
    const message = createBaseAppsFlyerUpdateTtlRequest();
    message.url = object.url ?? "";
    return message;
  },
};

function createBaseAppsFlyerUpdateTtlResponse(): AppsFlyerUpdateTtlResponse {
  return {};
}

export const AppsFlyerUpdateTtlResponse: MessageFns<AppsFlyerUpdateTtlResponse> = {
  encode(_: AppsFlyerUpdateTtlResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AppsFlyerUpdateTtlResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppsFlyerUpdateTtlResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): AppsFlyerUpdateTtlResponse {
    return {};
  },

  toJSON(_: AppsFlyerUpdateTtlResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<AppsFlyerUpdateTtlResponse>, I>>(base?: I): AppsFlyerUpdateTtlResponse {
    return AppsFlyerUpdateTtlResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppsFlyerUpdateTtlResponse>, I>>(_: I): AppsFlyerUpdateTtlResponse {
    const message = createBaseAppsFlyerUpdateTtlResponse();
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
