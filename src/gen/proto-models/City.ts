// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: City.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.location.city";

export interface CityResponse {
  id: string;
  name: string;
  state: string;
  country: string;
  isPopular: boolean;
  aliasList: string[];
}

/** @deprecated */
export interface GetCitiesResponse {
  cities: CityResponse[];
  popularCities: CityResponse[];
}

export interface GetPopularCitiesResponse {
  popularCities: CityResponse[];
}

export interface SearchCityResponse {
  cities: CityResponse[];
}

function createBaseCityResponse(): CityResponse {
  return { id: "", name: "", state: "", country: "", isPopular: false, aliasList: [] };
}

export const CityResponse: MessageFns<CityResponse> = {
  encode(message: CityResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.state !== "") {
      writer.uint32(26).string(message.state);
    }
    if (message.country !== "") {
      writer.uint32(34).string(message.country);
    }
    if (message.isPopular !== false) {
      writer.uint32(40).bool(message.isPopular);
    }
    for (const v of message.aliasList) {
      writer.uint32(50).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.state = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.country = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isPopular = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.aliasList.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CityResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      state: isSet(object.state) ? globalThis.String(object.state) : "",
      country: isSet(object.country) ? globalThis.String(object.country) : "",
      isPopular: isSet(object.isPopular) ? globalThis.Boolean(object.isPopular) : false,
      aliasList: globalThis.Array.isArray(object?.aliasList)
        ? object.aliasList.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: CityResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.state !== "") {
      obj.state = message.state;
    }
    if (message.country !== "") {
      obj.country = message.country;
    }
    if (message.isPopular !== false) {
      obj.isPopular = message.isPopular;
    }
    if (message.aliasList?.length) {
      obj.aliasList = message.aliasList;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CityResponse>, I>>(base?: I): CityResponse {
    return CityResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityResponse>, I>>(object: I): CityResponse {
    const message = createBaseCityResponse();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.state = object.state ?? "";
    message.country = object.country ?? "";
    message.isPopular = object.isPopular ?? false;
    message.aliasList = object.aliasList?.map((e) => e) || [];
    return message;
  },
};

function createBaseGetCitiesResponse(): GetCitiesResponse {
  return { cities: [], popularCities: [] };
}

export const GetCitiesResponse: MessageFns<GetCitiesResponse> = {
  encode(message: GetCitiesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cities) {
      CityResponse.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.popularCities) {
      CityResponse.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCitiesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCitiesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cities.push(CityResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.popularCities.push(CityResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCitiesResponse {
    return {
      cities: globalThis.Array.isArray(object?.cities) ? object.cities.map((e: any) => CityResponse.fromJSON(e)) : [],
      popularCities: globalThis.Array.isArray(object?.popularCities)
        ? object.popularCities.map((e: any) => CityResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetCitiesResponse): unknown {
    const obj: any = {};
    if (message.cities?.length) {
      obj.cities = message.cities.map((e) => CityResponse.toJSON(e));
    }
    if (message.popularCities?.length) {
      obj.popularCities = message.popularCities.map((e) => CityResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetCitiesResponse>, I>>(base?: I): GetCitiesResponse {
    return GetCitiesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCitiesResponse>, I>>(object: I): GetCitiesResponse {
    const message = createBaseGetCitiesResponse();
    message.cities = object.cities?.map((e) => CityResponse.fromPartial(e)) || [];
    message.popularCities = object.popularCities?.map((e) => CityResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetPopularCitiesResponse(): GetPopularCitiesResponse {
  return { popularCities: [] };
}

export const GetPopularCitiesResponse: MessageFns<GetPopularCitiesResponse> = {
  encode(message: GetPopularCitiesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.popularCities) {
      CityResponse.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetPopularCitiesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetPopularCitiesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.popularCities.push(CityResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetPopularCitiesResponse {
    return {
      popularCities: globalThis.Array.isArray(object?.popularCities)
        ? object.popularCities.map((e: any) => CityResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetPopularCitiesResponse): unknown {
    const obj: any = {};
    if (message.popularCities?.length) {
      obj.popularCities = message.popularCities.map((e) => CityResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetPopularCitiesResponse>, I>>(base?: I): GetPopularCitiesResponse {
    return GetPopularCitiesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPopularCitiesResponse>, I>>(object: I): GetPopularCitiesResponse {
    const message = createBaseGetPopularCitiesResponse();
    message.popularCities = object.popularCities?.map((e) => CityResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSearchCityResponse(): SearchCityResponse {
  return { cities: [] };
}

export const SearchCityResponse: MessageFns<SearchCityResponse> = {
  encode(message: SearchCityResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cities) {
      CityResponse.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchCityResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchCityResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.cities.push(CityResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchCityResponse {
    return {
      cities: globalThis.Array.isArray(object?.cities) ? object.cities.map((e: any) => CityResponse.fromJSON(e)) : [],
    };
  },

  toJSON(message: SearchCityResponse): unknown {
    const obj: any = {};
    if (message.cities?.length) {
      obj.cities = message.cities.map((e) => CityResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchCityResponse>, I>>(base?: I): SearchCityResponse {
    return SearchCityResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchCityResponse>, I>>(object: I): SearchCityResponse {
    const message = createBaseSearchCityResponse();
    message.cities = object.cities?.map((e) => CityResponse.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
