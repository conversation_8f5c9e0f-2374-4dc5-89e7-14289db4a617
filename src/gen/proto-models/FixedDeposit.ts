// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: FixedDeposit.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  BankType,
  bankTypeFromJSON,
  bankTypeToJSON,
  CompoundingFrequencyType,
  compoundingFrequencyTypeFromJSON,
  compoundingFrequencyTypeToJSON,
  RedirectDeeplink,
} from "./BusinessCommon";
import { DataKey } from "./Common";

export const protobufPackage = "com.stablemoney.api.business";

export enum FdProvider {
  FD_PROVIDER_UNKNOWN = 0,
  UPSWING = 1,
  FINCARE = 2,
  UNITY = 3,
  UJJIVAN = 4,
  INDUSIND = 5,
  IDFC = 6,
  BANDHAN = 7,
  UNRECOGNIZED = -1,
}

export function fdProviderFromJSON(object: any): FdProvider {
  switch (object) {
    case 0:
    case "FD_PROVIDER_UNKNOWN":
      return FdProvider.FD_PROVIDER_UNKNOWN;
    case 1:
    case "UPSWING":
      return FdProvider.UPSWING;
    case 2:
    case "FINCARE":
      return FdProvider.FINCARE;
    case 3:
    case "UNITY":
      return FdProvider.UNITY;
    case 4:
    case "UJJIVAN":
      return FdProvider.UJJIVAN;
    case 5:
    case "INDUSIND":
      return FdProvider.INDUSIND;
    case 6:
    case "IDFC":
      return FdProvider.IDFC;
    case 7:
    case "BANDHAN":
      return FdProvider.BANDHAN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FdProvider.UNRECOGNIZED;
  }
}

export function fdProviderToJSON(object: FdProvider): string {
  switch (object) {
    case FdProvider.FD_PROVIDER_UNKNOWN:
      return "FD_PROVIDER_UNKNOWN";
    case FdProvider.UPSWING:
      return "UPSWING";
    case FdProvider.FINCARE:
      return "FINCARE";
    case FdProvider.UNITY:
      return "UNITY";
    case FdProvider.UJJIVAN:
      return "UJJIVAN";
    case FdProvider.INDUSIND:
      return "INDUSIND";
    case FdProvider.IDFC:
      return "IDFC";
    case FdProvider.BANDHAN:
      return "BANDHAN";
    case FdProvider.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum InvestmentStatus {
  /** INVESTMENT_STATUS_UNKNOWN - Valid statuses in user investment */
  INVESTMENT_STATUS_UNKNOWN = 0,
  FD_INITIATE = 1,
  FD_ACCOUNT_CREATED = 2,
  PRE_PAYMENT_VKYC_MAKER_SUCCESS = 3,
  FD_REVIEW = 4,
  PAYMENT_FAILURE = 5,
  PAYMENT_SUCCESS = 6,
  VKYC_PENDING = 7,
  VKYC_FAILURE = 8,
  VKYC_MAKER_SUCCESS = 9,
  /** VKYC_ATTEMPTED - means VKYC was attempted */
  VKYC_ATTEMPTED = 44,
  VKYC_SUCCESS = 10,
  BOOKING_SUCCESS = 11,
  WITHDRAWN = 12,
  MATURED = 13,
  FD_APPLY = 14,
  REFUND = 49,
  DISCARDED = 50,
  /** FD_ACCEPT_TERMS - Not persisted in user investment */
  FD_ACCEPT_TERMS = 15,
  FD_CALCULATE = 16,
  FD_VERIFY = 17,
  FD_PAYMENT_INITIATED = 18,
  FD_PAYMENT_FAILED = 19,
  FD_PAYMENT_SUCCESS = 20,
  FD_PERSONAL_FORM = 21,
  FD_PERSONAL_VERIFY = 22,
  FD_VKYC = 23,
  FD_VKYC_INTIATED = 24,
  FD_VKYC_RESULT_PAGE = 25,
  FD_PENDING_AUTHENTICATE = 26,
  FD_WELCOME_BACK = 27,
  FD_ETB_PAYMENT_INITIATED = 28,
  FD_ETB_CALCULATE = 29,
  FD_ETB_PERSONAL_FORM = 30,
  FD_ETB_PERSONAL_VERIFY = 31,
  FD_ETB_FD_CHECK = 32,
  FD_ETB_PAYMENT_FAILED = 33,
  FD_ETB_PAYMENT_SUCCESS = 34,
  FD_ETB_PAYMENT_PENDING = 35,
  FD_ETB_PENDING_AUTHENTICATE = 36,
  FD_ETB_WELCOME_BACK = 37,
  FD_EXISTING_FLOW_MESSAGE = 38,
  FD_APPLY_ETB = 39,
  FD_AMOUNT_BEFORE_PAYMENT_INFO = 40,
  FD_ACC_CREATED_INFO = 41,
  ETB_FD_ACCOUNT_CREATED = 42,
  FD_PAYMENT_SUCCESS_INFO = 43,
  UJJIVAN_MAIN_PAGE = 45,
  VKYC_LINK_COPIED = 46,
  NTB_USER_EXISTS = 47,
  ETB_BY_MISTAKE_USER = 48,
  UTM_INGESTED = 51,
  NTB_AUTH_ERROR = 52,
  ETB_AUTH_ERROR = 53,
  ETB_NO_PENDING_JOURNEY = 54,
  SM_AADHAAR_FAILED = 55,
  NTB_BY_MISTAKE_USER = 56,
  UNRECOGNIZED = -1,
}

export function investmentStatusFromJSON(object: any): InvestmentStatus {
  switch (object) {
    case 0:
    case "INVESTMENT_STATUS_UNKNOWN":
      return InvestmentStatus.INVESTMENT_STATUS_UNKNOWN;
    case 1:
    case "FD_INITIATE":
      return InvestmentStatus.FD_INITIATE;
    case 2:
    case "FD_ACCOUNT_CREATED":
      return InvestmentStatus.FD_ACCOUNT_CREATED;
    case 3:
    case "PRE_PAYMENT_VKYC_MAKER_SUCCESS":
      return InvestmentStatus.PRE_PAYMENT_VKYC_MAKER_SUCCESS;
    case 4:
    case "FD_REVIEW":
      return InvestmentStatus.FD_REVIEW;
    case 5:
    case "PAYMENT_FAILURE":
      return InvestmentStatus.PAYMENT_FAILURE;
    case 6:
    case "PAYMENT_SUCCESS":
      return InvestmentStatus.PAYMENT_SUCCESS;
    case 7:
    case "VKYC_PENDING":
      return InvestmentStatus.VKYC_PENDING;
    case 8:
    case "VKYC_FAILURE":
      return InvestmentStatus.VKYC_FAILURE;
    case 9:
    case "VKYC_MAKER_SUCCESS":
      return InvestmentStatus.VKYC_MAKER_SUCCESS;
    case 44:
    case "VKYC_ATTEMPTED":
      return InvestmentStatus.VKYC_ATTEMPTED;
    case 10:
    case "VKYC_SUCCESS":
      return InvestmentStatus.VKYC_SUCCESS;
    case 11:
    case "BOOKING_SUCCESS":
      return InvestmentStatus.BOOKING_SUCCESS;
    case 12:
    case "WITHDRAWN":
      return InvestmentStatus.WITHDRAWN;
    case 13:
    case "MATURED":
      return InvestmentStatus.MATURED;
    case 14:
    case "FD_APPLY":
      return InvestmentStatus.FD_APPLY;
    case 49:
    case "REFUND":
      return InvestmentStatus.REFUND;
    case 50:
    case "DISCARDED":
      return InvestmentStatus.DISCARDED;
    case 15:
    case "FD_ACCEPT_TERMS":
      return InvestmentStatus.FD_ACCEPT_TERMS;
    case 16:
    case "FD_CALCULATE":
      return InvestmentStatus.FD_CALCULATE;
    case 17:
    case "FD_VERIFY":
      return InvestmentStatus.FD_VERIFY;
    case 18:
    case "FD_PAYMENT_INITIATED":
      return InvestmentStatus.FD_PAYMENT_INITIATED;
    case 19:
    case "FD_PAYMENT_FAILED":
      return InvestmentStatus.FD_PAYMENT_FAILED;
    case 20:
    case "FD_PAYMENT_SUCCESS":
      return InvestmentStatus.FD_PAYMENT_SUCCESS;
    case 21:
    case "FD_PERSONAL_FORM":
      return InvestmentStatus.FD_PERSONAL_FORM;
    case 22:
    case "FD_PERSONAL_VERIFY":
      return InvestmentStatus.FD_PERSONAL_VERIFY;
    case 23:
    case "FD_VKYC":
      return InvestmentStatus.FD_VKYC;
    case 24:
    case "FD_VKYC_INTIATED":
      return InvestmentStatus.FD_VKYC_INTIATED;
    case 25:
    case "FD_VKYC_RESULT_PAGE":
      return InvestmentStatus.FD_VKYC_RESULT_PAGE;
    case 26:
    case "FD_PENDING_AUTHENTICATE":
      return InvestmentStatus.FD_PENDING_AUTHENTICATE;
    case 27:
    case "FD_WELCOME_BACK":
      return InvestmentStatus.FD_WELCOME_BACK;
    case 28:
    case "FD_ETB_PAYMENT_INITIATED":
      return InvestmentStatus.FD_ETB_PAYMENT_INITIATED;
    case 29:
    case "FD_ETB_CALCULATE":
      return InvestmentStatus.FD_ETB_CALCULATE;
    case 30:
    case "FD_ETB_PERSONAL_FORM":
      return InvestmentStatus.FD_ETB_PERSONAL_FORM;
    case 31:
    case "FD_ETB_PERSONAL_VERIFY":
      return InvestmentStatus.FD_ETB_PERSONAL_VERIFY;
    case 32:
    case "FD_ETB_FD_CHECK":
      return InvestmentStatus.FD_ETB_FD_CHECK;
    case 33:
    case "FD_ETB_PAYMENT_FAILED":
      return InvestmentStatus.FD_ETB_PAYMENT_FAILED;
    case 34:
    case "FD_ETB_PAYMENT_SUCCESS":
      return InvestmentStatus.FD_ETB_PAYMENT_SUCCESS;
    case 35:
    case "FD_ETB_PAYMENT_PENDING":
      return InvestmentStatus.FD_ETB_PAYMENT_PENDING;
    case 36:
    case "FD_ETB_PENDING_AUTHENTICATE":
      return InvestmentStatus.FD_ETB_PENDING_AUTHENTICATE;
    case 37:
    case "FD_ETB_WELCOME_BACK":
      return InvestmentStatus.FD_ETB_WELCOME_BACK;
    case 38:
    case "FD_EXISTING_FLOW_MESSAGE":
      return InvestmentStatus.FD_EXISTING_FLOW_MESSAGE;
    case 39:
    case "FD_APPLY_ETB":
      return InvestmentStatus.FD_APPLY_ETB;
    case 40:
    case "FD_AMOUNT_BEFORE_PAYMENT_INFO":
      return InvestmentStatus.FD_AMOUNT_BEFORE_PAYMENT_INFO;
    case 41:
    case "FD_ACC_CREATED_INFO":
      return InvestmentStatus.FD_ACC_CREATED_INFO;
    case 42:
    case "ETB_FD_ACCOUNT_CREATED":
      return InvestmentStatus.ETB_FD_ACCOUNT_CREATED;
    case 43:
    case "FD_PAYMENT_SUCCESS_INFO":
      return InvestmentStatus.FD_PAYMENT_SUCCESS_INFO;
    case 45:
    case "UJJIVAN_MAIN_PAGE":
      return InvestmentStatus.UJJIVAN_MAIN_PAGE;
    case 46:
    case "VKYC_LINK_COPIED":
      return InvestmentStatus.VKYC_LINK_COPIED;
    case 47:
    case "NTB_USER_EXISTS":
      return InvestmentStatus.NTB_USER_EXISTS;
    case 48:
    case "ETB_BY_MISTAKE_USER":
      return InvestmentStatus.ETB_BY_MISTAKE_USER;
    case 51:
    case "UTM_INGESTED":
      return InvestmentStatus.UTM_INGESTED;
    case 52:
    case "NTB_AUTH_ERROR":
      return InvestmentStatus.NTB_AUTH_ERROR;
    case 53:
    case "ETB_AUTH_ERROR":
      return InvestmentStatus.ETB_AUTH_ERROR;
    case 54:
    case "ETB_NO_PENDING_JOURNEY":
      return InvestmentStatus.ETB_NO_PENDING_JOURNEY;
    case 55:
    case "SM_AADHAAR_FAILED":
      return InvestmentStatus.SM_AADHAAR_FAILED;
    case 56:
    case "NTB_BY_MISTAKE_USER":
      return InvestmentStatus.NTB_BY_MISTAKE_USER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return InvestmentStatus.UNRECOGNIZED;
  }
}

export function investmentStatusToJSON(object: InvestmentStatus): string {
  switch (object) {
    case InvestmentStatus.INVESTMENT_STATUS_UNKNOWN:
      return "INVESTMENT_STATUS_UNKNOWN";
    case InvestmentStatus.FD_INITIATE:
      return "FD_INITIATE";
    case InvestmentStatus.FD_ACCOUNT_CREATED:
      return "FD_ACCOUNT_CREATED";
    case InvestmentStatus.PRE_PAYMENT_VKYC_MAKER_SUCCESS:
      return "PRE_PAYMENT_VKYC_MAKER_SUCCESS";
    case InvestmentStatus.FD_REVIEW:
      return "FD_REVIEW";
    case InvestmentStatus.PAYMENT_FAILURE:
      return "PAYMENT_FAILURE";
    case InvestmentStatus.PAYMENT_SUCCESS:
      return "PAYMENT_SUCCESS";
    case InvestmentStatus.VKYC_PENDING:
      return "VKYC_PENDING";
    case InvestmentStatus.VKYC_FAILURE:
      return "VKYC_FAILURE";
    case InvestmentStatus.VKYC_MAKER_SUCCESS:
      return "VKYC_MAKER_SUCCESS";
    case InvestmentStatus.VKYC_ATTEMPTED:
      return "VKYC_ATTEMPTED";
    case InvestmentStatus.VKYC_SUCCESS:
      return "VKYC_SUCCESS";
    case InvestmentStatus.BOOKING_SUCCESS:
      return "BOOKING_SUCCESS";
    case InvestmentStatus.WITHDRAWN:
      return "WITHDRAWN";
    case InvestmentStatus.MATURED:
      return "MATURED";
    case InvestmentStatus.FD_APPLY:
      return "FD_APPLY";
    case InvestmentStatus.REFUND:
      return "REFUND";
    case InvestmentStatus.DISCARDED:
      return "DISCARDED";
    case InvestmentStatus.FD_ACCEPT_TERMS:
      return "FD_ACCEPT_TERMS";
    case InvestmentStatus.FD_CALCULATE:
      return "FD_CALCULATE";
    case InvestmentStatus.FD_VERIFY:
      return "FD_VERIFY";
    case InvestmentStatus.FD_PAYMENT_INITIATED:
      return "FD_PAYMENT_INITIATED";
    case InvestmentStatus.FD_PAYMENT_FAILED:
      return "FD_PAYMENT_FAILED";
    case InvestmentStatus.FD_PAYMENT_SUCCESS:
      return "FD_PAYMENT_SUCCESS";
    case InvestmentStatus.FD_PERSONAL_FORM:
      return "FD_PERSONAL_FORM";
    case InvestmentStatus.FD_PERSONAL_VERIFY:
      return "FD_PERSONAL_VERIFY";
    case InvestmentStatus.FD_VKYC:
      return "FD_VKYC";
    case InvestmentStatus.FD_VKYC_INTIATED:
      return "FD_VKYC_INTIATED";
    case InvestmentStatus.FD_VKYC_RESULT_PAGE:
      return "FD_VKYC_RESULT_PAGE";
    case InvestmentStatus.FD_PENDING_AUTHENTICATE:
      return "FD_PENDING_AUTHENTICATE";
    case InvestmentStatus.FD_WELCOME_BACK:
      return "FD_WELCOME_BACK";
    case InvestmentStatus.FD_ETB_PAYMENT_INITIATED:
      return "FD_ETB_PAYMENT_INITIATED";
    case InvestmentStatus.FD_ETB_CALCULATE:
      return "FD_ETB_CALCULATE";
    case InvestmentStatus.FD_ETB_PERSONAL_FORM:
      return "FD_ETB_PERSONAL_FORM";
    case InvestmentStatus.FD_ETB_PERSONAL_VERIFY:
      return "FD_ETB_PERSONAL_VERIFY";
    case InvestmentStatus.FD_ETB_FD_CHECK:
      return "FD_ETB_FD_CHECK";
    case InvestmentStatus.FD_ETB_PAYMENT_FAILED:
      return "FD_ETB_PAYMENT_FAILED";
    case InvestmentStatus.FD_ETB_PAYMENT_SUCCESS:
      return "FD_ETB_PAYMENT_SUCCESS";
    case InvestmentStatus.FD_ETB_PAYMENT_PENDING:
      return "FD_ETB_PAYMENT_PENDING";
    case InvestmentStatus.FD_ETB_PENDING_AUTHENTICATE:
      return "FD_ETB_PENDING_AUTHENTICATE";
    case InvestmentStatus.FD_ETB_WELCOME_BACK:
      return "FD_ETB_WELCOME_BACK";
    case InvestmentStatus.FD_EXISTING_FLOW_MESSAGE:
      return "FD_EXISTING_FLOW_MESSAGE";
    case InvestmentStatus.FD_APPLY_ETB:
      return "FD_APPLY_ETB";
    case InvestmentStatus.FD_AMOUNT_BEFORE_PAYMENT_INFO:
      return "FD_AMOUNT_BEFORE_PAYMENT_INFO";
    case InvestmentStatus.FD_ACC_CREATED_INFO:
      return "FD_ACC_CREATED_INFO";
    case InvestmentStatus.ETB_FD_ACCOUNT_CREATED:
      return "ETB_FD_ACCOUNT_CREATED";
    case InvestmentStatus.FD_PAYMENT_SUCCESS_INFO:
      return "FD_PAYMENT_SUCCESS_INFO";
    case InvestmentStatus.UJJIVAN_MAIN_PAGE:
      return "UJJIVAN_MAIN_PAGE";
    case InvestmentStatus.VKYC_LINK_COPIED:
      return "VKYC_LINK_COPIED";
    case InvestmentStatus.NTB_USER_EXISTS:
      return "NTB_USER_EXISTS";
    case InvestmentStatus.ETB_BY_MISTAKE_USER:
      return "ETB_BY_MISTAKE_USER";
    case InvestmentStatus.UTM_INGESTED:
      return "UTM_INGESTED";
    case InvestmentStatus.NTB_AUTH_ERROR:
      return "NTB_AUTH_ERROR";
    case InvestmentStatus.ETB_AUTH_ERROR:
      return "ETB_AUTH_ERROR";
    case InvestmentStatus.ETB_NO_PENDING_JOURNEY:
      return "ETB_NO_PENDING_JOURNEY";
    case InvestmentStatus.SM_AADHAAR_FAILED:
      return "SM_AADHAAR_FAILED";
    case InvestmentStatus.NTB_BY_MISTAKE_USER:
      return "NTB_BY_MISTAKE_USER";
    case InvestmentStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum FdEvent {
  FD_EVENT_UNKNOWN = 0,
  HALF_KYC_EVENT = 1,
  FULL_KYC_EVENT = 2,
  FD_INITIATE_EVENT = 3,
  PAYMENT_SUCCESS_EVENT = 4,
  PERSONAL_DETAILS_EVENT = 5,
  UNRECOGNIZED = -1,
}

export function fdEventFromJSON(object: any): FdEvent {
  switch (object) {
    case 0:
    case "FD_EVENT_UNKNOWN":
      return FdEvent.FD_EVENT_UNKNOWN;
    case 1:
    case "HALF_KYC_EVENT":
      return FdEvent.HALF_KYC_EVENT;
    case 2:
    case "FULL_KYC_EVENT":
      return FdEvent.FULL_KYC_EVENT;
    case 3:
    case "FD_INITIATE_EVENT":
      return FdEvent.FD_INITIATE_EVENT;
    case 4:
    case "PAYMENT_SUCCESS_EVENT":
      return FdEvent.PAYMENT_SUCCESS_EVENT;
    case 5:
    case "PERSONAL_DETAILS_EVENT":
      return FdEvent.PERSONAL_DETAILS_EVENT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FdEvent.UNRECOGNIZED;
  }
}

export function fdEventToJSON(object: FdEvent): string {
  switch (object) {
    case FdEvent.FD_EVENT_UNKNOWN:
      return "FD_EVENT_UNKNOWN";
    case FdEvent.HALF_KYC_EVENT:
      return "HALF_KYC_EVENT";
    case FdEvent.FULL_KYC_EVENT:
      return "FULL_KYC_EVENT";
    case FdEvent.FD_INITIATE_EVENT:
      return "FD_INITIATE_EVENT";
    case FdEvent.PAYMENT_SUCCESS_EVENT:
      return "PAYMENT_SUCCESS_EVENT";
    case FdEvent.PERSONAL_DETAILS_EVENT:
      return "PERSONAL_DETAILS_EVENT";
    case FdEvent.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface InitiateFdRequest {
  provider: FdProvider;
  bankId: string;
  fdData: FdData | undefined;
}

export interface InitiateFdResponse {
  upswingJourney?: InitiateFdResponse_UpswingJourney | undefined;
  webRedirectionJourney?: InitiateFdResponse_WebRedirectionJourney | undefined;
  stableMoneyJourney?: InitiateFdResponse_StableMoneyJourney | undefined;
}

export interface InitiateFdResponse_UpswingJourney {
  ici: string;
  token: string;
}

export interface InitiateFdResponse_WebRedirectionJourney {
  journeyId: string;
  redirectionUrl: string;
  fdPrefillData?: FdPrefillData | undefined;
}

export interface InitiateFdResponse_StableMoneyJourney {
}

export interface FincareInvestmentSummary {
  totalInvestedAmount: TotalInvestedAmount | undefined;
  totalInterestEarned: TotalInterestEarned | undefined;
  bankName: string;
  bankLogo: string;
  fincareMaximumAmountCheck?: boolean | undefined;
  fincareVkycCompleted?: boolean | undefined;
  activeTermDepositCount?: number | undefined;
}

export interface UnityInvestmentSummary {
  totalInvestedAmount: TotalInvestedAmount | undefined;
  totalInterestEarned: TotalInterestEarned | undefined;
  bankName: string;
  bankLogo: string;
  bankId: string;
  activeTermDepositCount?: number | undefined;
}

export interface UjjivanInvestmentSummary {
  totalInvestedAmount: TotalInvestedAmount | undefined;
  totalInterestEarned: TotalInterestEarned | undefined;
  bankName: string;
  bankLogo: string;
  bankId: string;
  activeTermDepositCount?: number | undefined;
}

export interface IndusindInvestmentSummary {
  totalInvestedAmount: TotalInvestedAmount | undefined;
  totalInterestEarned: TotalInterestEarned | undefined;
  bankName: string;
  bankLogo: string;
  bankId: string;
  activeTermDepositCount?: number | undefined;
}

export interface NetWorthSummaryResponse {
  totalInvestedAmount: TotalInvestedAmount | undefined;
  totalInterestEarned: TotalInterestEarned | undefined;
  currentAmount: CurrentAmount | undefined;
  activeTermDepositCount: number;
  isInvested: boolean;
  isPending: boolean;
  pendingEventTime: string;
  pendingEventType: string;
  isVkycPending: boolean;
  isUpswingInitiated: boolean;
  fincareInvestmentSummary?: FincareInvestmentSummary | undefined;
  unityInvestmentSummary?: UnityInvestmentSummary | undefined;
  emergencyFundSummary?: EmergencyFundSummary | undefined;
  ujjivanInvestmentSummary?: UjjivanInvestmentSummary | undefined;
  indusindInvestmentSummary?: IndusindInvestmentSummary | undefined;
  inProgressTotal: number;
}

export interface EmergencyFundSummary {
  isEmergencyFundCreated: boolean;
  targetAmount: number;
  currentAmount: number;
  progressPercentage: number;
  progressDescription: string;
  isActive: boolean;
}

export interface TotalInvestedAmount {
  amount: number;
  currency: string;
}

export interface TotalInterestEarned {
  amount: number;
  currency: string;
}

export interface CurrentAmount {
  amount: number;
  currency: string;
}

export interface FincarePayload {
  sessionId: string;
  utmMedium: string;
  utmSource: string;
  utmCampaign: string;
  referralLink: string;
  fdRate?: number | undefined;
  fdMaturityDate?: string | undefined;
  fdAmount?: number | undefined;
  kycStatus: string;
  accountCreatedStatus: string;
  isSeniorCitizen?: boolean | undefined;
  tenureInDays?: number | undefined;
  interestPayoutType?: string | undefined;
  maturityInstruction?: string | undefined;
  fdActive?: boolean | undefined;
  fdClosingDate?: string | undefined;
  fdBookingDate?: string | undefined;
  prodType?: string | undefined;
}

export interface TenurePrefillInfo {
  tenureHtmlTag?: string | undefined;
  tenureYear?: number | undefined;
  tenureMonth?: number | undefined;
  tenureDay?: number | undefined;
}

export interface NomineePrefillInfo {
  nomineeName?: string | undefined;
  nomineeRelationship?: string | undefined;
  nomineeDateOfBirth?: string | undefined;
}

export interface BankAccountPrefillInfo {
  accountHolderName?: string | undefined;
  bankAccountNumber?: string | undefined;
  ifscCode?: string | undefined;
}

export interface FdPrefillData {
  investmentAmount: number;
  maturityInstruction: string;
  interestPayoutType: string;
  tenurePrefillInfo: TenurePrefillInfo | undefined;
  nomineePrefillInfo: NomineePrefillInfo | undefined;
  bankAccountPrefillInfo: BankAccountPrefillInfo | undefined;
  isTaxSaving: boolean;
  panNumber?: string | undefined;
  dob?: string | undefined;
}

export interface FdEventResponse {
  bookingUrl: string;
  bookingId: string;
  fdPrefillData: FdPrefillData | undefined;
  fdId: string;
  isBlock?: boolean | undefined;
  isInput?: boolean | undefined;
}

export interface EtbResponse {
  isEtbKnown: boolean;
}

export interface SetEtbRequest {
  bankId: string;
  hasAccount: boolean;
  hasFd: boolean;
}

export interface GetEtbRequest {
  bankId: string;
}

export interface PanStatusResponse {
  isPanNeeded: boolean;
}

export interface EventMetadata {
}

export interface FdData {
  fdId: string;
  investmentAmount: number;
  interestPayoutType: string;
  maturityInstruction: string;
  panNumber?: string | undefined;
  dob?: string | undefined;
  panConsent?: boolean | undefined;
}

export interface FdEventRequest {
  bankId: string;
  eventType: string;
  fdData?: FdData | undefined;
  eventMetadata?: EventMetadata | undefined;
  bookingId?: string | undefined;
  pageHtml?: string | undefined;
  pageUrl?: string | undefined;
  timestamp?: string | undefined;
}

export interface FdCalculationRequest {
  fdId: string;
  investmentAmount: number;
  interestPayoutType: string;
}

export interface FdCalculationResponse {
  maturityAmount: number;
  maturityDescription: string;
}

export interface FdCalculationResponseV2 {
  maturityAmount: number;
  interestGained: number;
  maturityDescription: string;
}

export interface NearMaturityInvestments {
  investmentItems: InvestmentItem[];
}

export interface NearMaturityInvestments_FixedDeposit {
  bankId: string;
  bankName: string;
  bankLogoUrl: string;
  bankType: BankType;
  tenure: string;
  interestRate: number;
  redirectDeepLink: RedirectDeeplink | undefined;
  description: string[];
}

export interface LastMaturedInvestment {
  investmentItem: InvestmentItem | undefined;
  pointers: DataKey[];
  suggestedFds: NearMaturityInvestments_FixedDeposit[];
}

export interface NearMaturityInvestmentDetail {
  investmentItem: InvestmentItem | undefined;
  recommendation: NearMaturityInvestmentDetail_Recommendation | undefined;
  suggestedFds: NearMaturityInvestments_FixedDeposit[];
  term: NearMaturityInvestmentDetail_Term;
}

export enum NearMaturityInvestmentDetail_Term {
  SHORT = 0,
  MEDIUM = 1,
  LONG = 2,
  UNRECOGNIZED = -1,
}

export function nearMaturityInvestmentDetail_TermFromJSON(object: any): NearMaturityInvestmentDetail_Term {
  switch (object) {
    case 0:
    case "SHORT":
      return NearMaturityInvestmentDetail_Term.SHORT;
    case 1:
    case "MEDIUM":
      return NearMaturityInvestmentDetail_Term.MEDIUM;
    case 2:
    case "LONG":
      return NearMaturityInvestmentDetail_Term.LONG;
    case -1:
    case "UNRECOGNIZED":
    default:
      return NearMaturityInvestmentDetail_Term.UNRECOGNIZED;
  }
}

export function nearMaturityInvestmentDetail_TermToJSON(object: NearMaturityInvestmentDetail_Term): string {
  switch (object) {
    case NearMaturityInvestmentDetail_Term.SHORT:
      return "SHORT";
    case NearMaturityInvestmentDetail_Term.MEDIUM:
      return "MEDIUM";
    case NearMaturityInvestmentDetail_Term.LONG:
      return "LONG";
    case NearMaturityInvestmentDetail_Term.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface NearMaturityInvestmentDetail_Recommendation {
  type: NearMaturityInvestmentDetail_Recommendation_Type;
  amount: number;
  interestRate: number;
  tenure: string;
}

export enum NearMaturityInvestmentDetail_Recommendation_Type {
  UNKNOWN = 0,
  NONE = 1,
  REINVEST = 2,
  WITHDRAW = 3,
  UNRECOGNIZED = -1,
}

export function nearMaturityInvestmentDetail_Recommendation_TypeFromJSON(
  object: any,
): NearMaturityInvestmentDetail_Recommendation_Type {
  switch (object) {
    case 0:
    case "UNKNOWN":
      return NearMaturityInvestmentDetail_Recommendation_Type.UNKNOWN;
    case 1:
    case "NONE":
      return NearMaturityInvestmentDetail_Recommendation_Type.NONE;
    case 2:
    case "REINVEST":
      return NearMaturityInvestmentDetail_Recommendation_Type.REINVEST;
    case 3:
    case "WITHDRAW":
      return NearMaturityInvestmentDetail_Recommendation_Type.WITHDRAW;
    case -1:
    case "UNRECOGNIZED":
    default:
      return NearMaturityInvestmentDetail_Recommendation_Type.UNRECOGNIZED;
  }
}

export function nearMaturityInvestmentDetail_Recommendation_TypeToJSON(
  object: NearMaturityInvestmentDetail_Recommendation_Type,
): string {
  switch (object) {
    case NearMaturityInvestmentDetail_Recommendation_Type.UNKNOWN:
      return "UNKNOWN";
    case NearMaturityInvestmentDetail_Recommendation_Type.NONE:
      return "NONE";
    case NearMaturityInvestmentDetail_Recommendation_Type.REINVEST:
      return "REINVEST";
    case NearMaturityInvestmentDetail_Recommendation_Type.WITHDRAW:
      return "WITHDRAW";
    case NearMaturityInvestmentDetail_Recommendation_Type.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface InvestmentItem {
  investmentAmount: number;
  fdMaturityDate: string;
  interestPayoutType: string;
  currentGains: number;
  bankName: string;
  bankLogoUrl: string;
  tenure: string;
  fdBookingDate: string;
  interestRate: number;
  maturityAmount: number;
  bookingId: string;
  investmentStatus: string;
  investedAt: string;
  maturityInstruction: string;
  redirectDeepLink: RedirectDeeplink | undefined;
}

export interface InvestmentResponse {
  bankId: string;
  pendingInvestedItems: InvestmentItem[];
  bookedInvestedItems: InvestmentItem[];
  totalInvestedAmount: TotalInvestedAmount | undefined;
  totalInterestEarned: TotalInterestEarned | undefined;
}

export interface FincareSheetRow {
  hmac: string;
  fincarePayload: FincarePayload | undefined;
}

export interface FincareSheetRequest {
  fincareSheet: FincareSheetRow[];
}

export interface UjjivanPayload {
  applicationId: string;
  applicationStatus: string;
  creationDate: string;
  lastUpdateDate: string;
  amount?: number | undefined;
  userId?: string | undefined;
  investmentId?: string | undefined;
}

export interface UjjivanSheetRow {
  hmac: string;
  payload: UjjivanPayload | undefined;
}

export interface UjjivanSheetRequest {
  sheet: UjjivanSheetRow[];
}

export interface SheetResponse {
  successes: string[];
  failures: string[];
  skipped: string[];
}

export interface UserFixedDepositSummaryRequest {
  userId: string;
}

export interface UserFixedDepositSummaryResponse {
  totalInvestedAmount: number;
  totalInterestEarned: number;
  currentAmount: number;
  emergencyFundSummary?: EmergencyFundSummary | undefined;
  activeTermDepositCount: number;
  inProgressTotal: number;
}

export interface WithdrawalCalculatorRequest {
  fdId: string;
}

export interface WithdrawalCalculatorResponse {
  investedAmount: number;
  withdrawalAmount: number;
  interestEarned: number;
  penaltyInterest: number;
  fdAccountNumber: string;
  applicableInterestRate: number;
  maturityAmount: number;
  maturityInterest: number;
  fdActiveDays: string;
  bookingDate: string;
  fdBookedTenure: string;
  bankLogo: string;
  withdrawalCreditBankDetails?: WithdrawalCreditBankDetails | undefined;
  redirectDeepLink?: RedirectDeeplink | undefined;
  withdrawalMetadata: { [key: string]: string };
}

export interface WithdrawalCalculatorResponse_WithdrawalMetadataEntry {
  key: string;
  value: string;
}

export interface WithdrawalCreditBankDetails {
  bankName: string;
  accountNumber: string;
  paymentMode: string;
  creditExpectedDate: string;
}

export interface WithdrawalRequest {
  fdId: string;
}

export interface WithdrawalResponse {
  message: string;
}

export interface CompareFdResponse {
  bankId: string;
  bankName: string;
  bankLogo: string;
  shortTerm: CompareFdResponse_RateAndTenure | undefined;
  midTerm: CompareFdResponse_RateAndTenure | undefined;
  longTerm: CompareFdResponse_RateAndTenure | undefined;
  payout: string[];
  bankAccountRequired: boolean;
  insured: boolean;
  withdrawal: string;
}

export interface CompareFdResponse_RateAndTenure {
  rate: number;
  tenureInDays: number;
  compoundingFrequency: CompoundingFrequencyType;
}

function createBaseInitiateFdRequest(): InitiateFdRequest {
  return { provider: 0, bankId: "", fdData: undefined };
}

export const InitiateFdRequest: MessageFns<InitiateFdRequest> = {
  encode(message: InitiateFdRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.provider !== 0) {
      writer.uint32(8).int32(message.provider);
    }
    if (message.bankId !== "") {
      writer.uint32(18).string(message.bankId);
    }
    if (message.fdData !== undefined) {
      FdData.encode(message.fdData, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateFdRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateFdRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.provider = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fdData = FdData.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateFdRequest {
    return {
      provider: isSet(object.provider) ? fdProviderFromJSON(object.provider) : 0,
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      fdData: isSet(object.fdData) ? FdData.fromJSON(object.fdData) : undefined,
    };
  },

  toJSON(message: InitiateFdRequest): unknown {
    const obj: any = {};
    if (message.provider !== 0) {
      obj.provider = fdProviderToJSON(message.provider);
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.fdData !== undefined) {
      obj.fdData = FdData.toJSON(message.fdData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateFdRequest>, I>>(base?: I): InitiateFdRequest {
    return InitiateFdRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateFdRequest>, I>>(object: I): InitiateFdRequest {
    const message = createBaseInitiateFdRequest();
    message.provider = object.provider ?? 0;
    message.bankId = object.bankId ?? "";
    message.fdData = (object.fdData !== undefined && object.fdData !== null)
      ? FdData.fromPartial(object.fdData)
      : undefined;
    return message;
  },
};

function createBaseInitiateFdResponse(): InitiateFdResponse {
  return { upswingJourney: undefined, webRedirectionJourney: undefined, stableMoneyJourney: undefined };
}

export const InitiateFdResponse: MessageFns<InitiateFdResponse> = {
  encode(message: InitiateFdResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.upswingJourney !== undefined) {
      InitiateFdResponse_UpswingJourney.encode(message.upswingJourney, writer.uint32(10).fork()).join();
    }
    if (message.webRedirectionJourney !== undefined) {
      InitiateFdResponse_WebRedirectionJourney.encode(message.webRedirectionJourney, writer.uint32(18).fork()).join();
    }
    if (message.stableMoneyJourney !== undefined) {
      InitiateFdResponse_StableMoneyJourney.encode(message.stableMoneyJourney, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateFdResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateFdResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.upswingJourney = InitiateFdResponse_UpswingJourney.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.webRedirectionJourney = InitiateFdResponse_WebRedirectionJourney.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.stableMoneyJourney = InitiateFdResponse_StableMoneyJourney.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateFdResponse {
    return {
      upswingJourney: isSet(object.upswingJourney)
        ? InitiateFdResponse_UpswingJourney.fromJSON(object.upswingJourney)
        : undefined,
      webRedirectionJourney: isSet(object.webRedirectionJourney)
        ? InitiateFdResponse_WebRedirectionJourney.fromJSON(object.webRedirectionJourney)
        : undefined,
      stableMoneyJourney: isSet(object.stableMoneyJourney)
        ? InitiateFdResponse_StableMoneyJourney.fromJSON(object.stableMoneyJourney)
        : undefined,
    };
  },

  toJSON(message: InitiateFdResponse): unknown {
    const obj: any = {};
    if (message.upswingJourney !== undefined) {
      obj.upswingJourney = InitiateFdResponse_UpswingJourney.toJSON(message.upswingJourney);
    }
    if (message.webRedirectionJourney !== undefined) {
      obj.webRedirectionJourney = InitiateFdResponse_WebRedirectionJourney.toJSON(message.webRedirectionJourney);
    }
    if (message.stableMoneyJourney !== undefined) {
      obj.stableMoneyJourney = InitiateFdResponse_StableMoneyJourney.toJSON(message.stableMoneyJourney);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateFdResponse>, I>>(base?: I): InitiateFdResponse {
    return InitiateFdResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateFdResponse>, I>>(object: I): InitiateFdResponse {
    const message = createBaseInitiateFdResponse();
    message.upswingJourney = (object.upswingJourney !== undefined && object.upswingJourney !== null)
      ? InitiateFdResponse_UpswingJourney.fromPartial(object.upswingJourney)
      : undefined;
    message.webRedirectionJourney =
      (object.webRedirectionJourney !== undefined && object.webRedirectionJourney !== null)
        ? InitiateFdResponse_WebRedirectionJourney.fromPartial(object.webRedirectionJourney)
        : undefined;
    message.stableMoneyJourney = (object.stableMoneyJourney !== undefined && object.stableMoneyJourney !== null)
      ? InitiateFdResponse_StableMoneyJourney.fromPartial(object.stableMoneyJourney)
      : undefined;
    return message;
  },
};

function createBaseInitiateFdResponse_UpswingJourney(): InitiateFdResponse_UpswingJourney {
  return { ici: "", token: "" };
}

export const InitiateFdResponse_UpswingJourney: MessageFns<InitiateFdResponse_UpswingJourney> = {
  encode(message: InitiateFdResponse_UpswingJourney, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.ici !== "") {
      writer.uint32(10).string(message.ici);
    }
    if (message.token !== "") {
      writer.uint32(18).string(message.token);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateFdResponse_UpswingJourney {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateFdResponse_UpswingJourney();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.ici = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.token = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateFdResponse_UpswingJourney {
    return {
      ici: isSet(object.ici) ? globalThis.String(object.ici) : "",
      token: isSet(object.token) ? globalThis.String(object.token) : "",
    };
  },

  toJSON(message: InitiateFdResponse_UpswingJourney): unknown {
    const obj: any = {};
    if (message.ici !== "") {
      obj.ici = message.ici;
    }
    if (message.token !== "") {
      obj.token = message.token;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateFdResponse_UpswingJourney>, I>>(
    base?: I,
  ): InitiateFdResponse_UpswingJourney {
    return InitiateFdResponse_UpswingJourney.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateFdResponse_UpswingJourney>, I>>(
    object: I,
  ): InitiateFdResponse_UpswingJourney {
    const message = createBaseInitiateFdResponse_UpswingJourney();
    message.ici = object.ici ?? "";
    message.token = object.token ?? "";
    return message;
  },
};

function createBaseInitiateFdResponse_WebRedirectionJourney(): InitiateFdResponse_WebRedirectionJourney {
  return { journeyId: "", redirectionUrl: "", fdPrefillData: undefined };
}

export const InitiateFdResponse_WebRedirectionJourney: MessageFns<InitiateFdResponse_WebRedirectionJourney> = {
  encode(message: InitiateFdResponse_WebRedirectionJourney, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.journeyId !== "") {
      writer.uint32(10).string(message.journeyId);
    }
    if (message.redirectionUrl !== "") {
      writer.uint32(18).string(message.redirectionUrl);
    }
    if (message.fdPrefillData !== undefined) {
      FdPrefillData.encode(message.fdPrefillData, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateFdResponse_WebRedirectionJourney {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateFdResponse_WebRedirectionJourney();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.journeyId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.redirectionUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fdPrefillData = FdPrefillData.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateFdResponse_WebRedirectionJourney {
    return {
      journeyId: isSet(object.journeyId) ? globalThis.String(object.journeyId) : "",
      redirectionUrl: isSet(object.redirectionUrl) ? globalThis.String(object.redirectionUrl) : "",
      fdPrefillData: isSet(object.fdPrefillData) ? FdPrefillData.fromJSON(object.fdPrefillData) : undefined,
    };
  },

  toJSON(message: InitiateFdResponse_WebRedirectionJourney): unknown {
    const obj: any = {};
    if (message.journeyId !== "") {
      obj.journeyId = message.journeyId;
    }
    if (message.redirectionUrl !== "") {
      obj.redirectionUrl = message.redirectionUrl;
    }
    if (message.fdPrefillData !== undefined) {
      obj.fdPrefillData = FdPrefillData.toJSON(message.fdPrefillData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateFdResponse_WebRedirectionJourney>, I>>(
    base?: I,
  ): InitiateFdResponse_WebRedirectionJourney {
    return InitiateFdResponse_WebRedirectionJourney.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateFdResponse_WebRedirectionJourney>, I>>(
    object: I,
  ): InitiateFdResponse_WebRedirectionJourney {
    const message = createBaseInitiateFdResponse_WebRedirectionJourney();
    message.journeyId = object.journeyId ?? "";
    message.redirectionUrl = object.redirectionUrl ?? "";
    message.fdPrefillData = (object.fdPrefillData !== undefined && object.fdPrefillData !== null)
      ? FdPrefillData.fromPartial(object.fdPrefillData)
      : undefined;
    return message;
  },
};

function createBaseInitiateFdResponse_StableMoneyJourney(): InitiateFdResponse_StableMoneyJourney {
  return {};
}

export const InitiateFdResponse_StableMoneyJourney: MessageFns<InitiateFdResponse_StableMoneyJourney> = {
  encode(_: InitiateFdResponse_StableMoneyJourney, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateFdResponse_StableMoneyJourney {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateFdResponse_StableMoneyJourney();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): InitiateFdResponse_StableMoneyJourney {
    return {};
  },

  toJSON(_: InitiateFdResponse_StableMoneyJourney): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateFdResponse_StableMoneyJourney>, I>>(
    base?: I,
  ): InitiateFdResponse_StableMoneyJourney {
    return InitiateFdResponse_StableMoneyJourney.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateFdResponse_StableMoneyJourney>, I>>(
    _: I,
  ): InitiateFdResponse_StableMoneyJourney {
    const message = createBaseInitiateFdResponse_StableMoneyJourney();
    return message;
  },
};

function createBaseFincareInvestmentSummary(): FincareInvestmentSummary {
  return {
    totalInvestedAmount: undefined,
    totalInterestEarned: undefined,
    bankName: "",
    bankLogo: "",
    fincareMaximumAmountCheck: undefined,
    fincareVkycCompleted: undefined,
    activeTermDepositCount: undefined,
  };
}

export const FincareInvestmentSummary: MessageFns<FincareInvestmentSummary> = {
  encode(message: FincareInvestmentSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalInvestedAmount !== undefined) {
      TotalInvestedAmount.encode(message.totalInvestedAmount, writer.uint32(10).fork()).join();
    }
    if (message.totalInterestEarned !== undefined) {
      TotalInterestEarned.encode(message.totalInterestEarned, writer.uint32(18).fork()).join();
    }
    if (message.bankName !== "") {
      writer.uint32(26).string(message.bankName);
    }
    if (message.bankLogo !== "") {
      writer.uint32(34).string(message.bankLogo);
    }
    if (message.fincareMaximumAmountCheck !== undefined) {
      writer.uint32(40).bool(message.fincareMaximumAmountCheck);
    }
    if (message.fincareVkycCompleted !== undefined) {
      writer.uint32(48).bool(message.fincareVkycCompleted);
    }
    if (message.activeTermDepositCount !== undefined) {
      writer.uint32(56).int32(message.activeTermDepositCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FincareInvestmentSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFincareInvestmentSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.totalInvestedAmount = TotalInvestedAmount.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.totalInterestEarned = TotalInterestEarned.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bankLogo = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.fincareMaximumAmountCheck = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.fincareVkycCompleted = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.activeTermDepositCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FincareInvestmentSummary {
    return {
      totalInvestedAmount: isSet(object.totalInvestedAmount)
        ? TotalInvestedAmount.fromJSON(object.totalInvestedAmount)
        : undefined,
      totalInterestEarned: isSet(object.totalInterestEarned)
        ? TotalInterestEarned.fromJSON(object.totalInterestEarned)
        : undefined,
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogo: isSet(object.bankLogo) ? globalThis.String(object.bankLogo) : "",
      fincareMaximumAmountCheck: isSet(object.fincareMaximumAmountCheck)
        ? globalThis.Boolean(object.fincareMaximumAmountCheck)
        : undefined,
      fincareVkycCompleted: isSet(object.fincareVkycCompleted)
        ? globalThis.Boolean(object.fincareVkycCompleted)
        : undefined,
      activeTermDepositCount: isSet(object.activeTermDepositCount)
        ? globalThis.Number(object.activeTermDepositCount)
        : undefined,
    };
  },

  toJSON(message: FincareInvestmentSummary): unknown {
    const obj: any = {};
    if (message.totalInvestedAmount !== undefined) {
      obj.totalInvestedAmount = TotalInvestedAmount.toJSON(message.totalInvestedAmount);
    }
    if (message.totalInterestEarned !== undefined) {
      obj.totalInterestEarned = TotalInterestEarned.toJSON(message.totalInterestEarned);
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogo !== "") {
      obj.bankLogo = message.bankLogo;
    }
    if (message.fincareMaximumAmountCheck !== undefined) {
      obj.fincareMaximumAmountCheck = message.fincareMaximumAmountCheck;
    }
    if (message.fincareVkycCompleted !== undefined) {
      obj.fincareVkycCompleted = message.fincareVkycCompleted;
    }
    if (message.activeTermDepositCount !== undefined) {
      obj.activeTermDepositCount = Math.round(message.activeTermDepositCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FincareInvestmentSummary>, I>>(base?: I): FincareInvestmentSummary {
    return FincareInvestmentSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FincareInvestmentSummary>, I>>(object: I): FincareInvestmentSummary {
    const message = createBaseFincareInvestmentSummary();
    message.totalInvestedAmount = (object.totalInvestedAmount !== undefined && object.totalInvestedAmount !== null)
      ? TotalInvestedAmount.fromPartial(object.totalInvestedAmount)
      : undefined;
    message.totalInterestEarned = (object.totalInterestEarned !== undefined && object.totalInterestEarned !== null)
      ? TotalInterestEarned.fromPartial(object.totalInterestEarned)
      : undefined;
    message.bankName = object.bankName ?? "";
    message.bankLogo = object.bankLogo ?? "";
    message.fincareMaximumAmountCheck = object.fincareMaximumAmountCheck ?? undefined;
    message.fincareVkycCompleted = object.fincareVkycCompleted ?? undefined;
    message.activeTermDepositCount = object.activeTermDepositCount ?? undefined;
    return message;
  },
};

function createBaseUnityInvestmentSummary(): UnityInvestmentSummary {
  return {
    totalInvestedAmount: undefined,
    totalInterestEarned: undefined,
    bankName: "",
    bankLogo: "",
    bankId: "",
    activeTermDepositCount: undefined,
  };
}

export const UnityInvestmentSummary: MessageFns<UnityInvestmentSummary> = {
  encode(message: UnityInvestmentSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalInvestedAmount !== undefined) {
      TotalInvestedAmount.encode(message.totalInvestedAmount, writer.uint32(10).fork()).join();
    }
    if (message.totalInterestEarned !== undefined) {
      TotalInterestEarned.encode(message.totalInterestEarned, writer.uint32(18).fork()).join();
    }
    if (message.bankName !== "") {
      writer.uint32(26).string(message.bankName);
    }
    if (message.bankLogo !== "") {
      writer.uint32(34).string(message.bankLogo);
    }
    if (message.bankId !== "") {
      writer.uint32(42).string(message.bankId);
    }
    if (message.activeTermDepositCount !== undefined) {
      writer.uint32(48).int32(message.activeTermDepositCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UnityInvestmentSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUnityInvestmentSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.totalInvestedAmount = TotalInvestedAmount.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.totalInterestEarned = TotalInterestEarned.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bankLogo = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.activeTermDepositCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UnityInvestmentSummary {
    return {
      totalInvestedAmount: isSet(object.totalInvestedAmount)
        ? TotalInvestedAmount.fromJSON(object.totalInvestedAmount)
        : undefined,
      totalInterestEarned: isSet(object.totalInterestEarned)
        ? TotalInterestEarned.fromJSON(object.totalInterestEarned)
        : undefined,
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogo: isSet(object.bankLogo) ? globalThis.String(object.bankLogo) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      activeTermDepositCount: isSet(object.activeTermDepositCount)
        ? globalThis.Number(object.activeTermDepositCount)
        : undefined,
    };
  },

  toJSON(message: UnityInvestmentSummary): unknown {
    const obj: any = {};
    if (message.totalInvestedAmount !== undefined) {
      obj.totalInvestedAmount = TotalInvestedAmount.toJSON(message.totalInvestedAmount);
    }
    if (message.totalInterestEarned !== undefined) {
      obj.totalInterestEarned = TotalInterestEarned.toJSON(message.totalInterestEarned);
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogo !== "") {
      obj.bankLogo = message.bankLogo;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.activeTermDepositCount !== undefined) {
      obj.activeTermDepositCount = Math.round(message.activeTermDepositCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UnityInvestmentSummary>, I>>(base?: I): UnityInvestmentSummary {
    return UnityInvestmentSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnityInvestmentSummary>, I>>(object: I): UnityInvestmentSummary {
    const message = createBaseUnityInvestmentSummary();
    message.totalInvestedAmount = (object.totalInvestedAmount !== undefined && object.totalInvestedAmount !== null)
      ? TotalInvestedAmount.fromPartial(object.totalInvestedAmount)
      : undefined;
    message.totalInterestEarned = (object.totalInterestEarned !== undefined && object.totalInterestEarned !== null)
      ? TotalInterestEarned.fromPartial(object.totalInterestEarned)
      : undefined;
    message.bankName = object.bankName ?? "";
    message.bankLogo = object.bankLogo ?? "";
    message.bankId = object.bankId ?? "";
    message.activeTermDepositCount = object.activeTermDepositCount ?? undefined;
    return message;
  },
};

function createBaseUjjivanInvestmentSummary(): UjjivanInvestmentSummary {
  return {
    totalInvestedAmount: undefined,
    totalInterestEarned: undefined,
    bankName: "",
    bankLogo: "",
    bankId: "",
    activeTermDepositCount: undefined,
  };
}

export const UjjivanInvestmentSummary: MessageFns<UjjivanInvestmentSummary> = {
  encode(message: UjjivanInvestmentSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalInvestedAmount !== undefined) {
      TotalInvestedAmount.encode(message.totalInvestedAmount, writer.uint32(10).fork()).join();
    }
    if (message.totalInterestEarned !== undefined) {
      TotalInterestEarned.encode(message.totalInterestEarned, writer.uint32(18).fork()).join();
    }
    if (message.bankName !== "") {
      writer.uint32(26).string(message.bankName);
    }
    if (message.bankLogo !== "") {
      writer.uint32(34).string(message.bankLogo);
    }
    if (message.bankId !== "") {
      writer.uint32(42).string(message.bankId);
    }
    if (message.activeTermDepositCount !== undefined) {
      writer.uint32(48).int32(message.activeTermDepositCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UjjivanInvestmentSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUjjivanInvestmentSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.totalInvestedAmount = TotalInvestedAmount.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.totalInterestEarned = TotalInterestEarned.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bankLogo = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.activeTermDepositCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UjjivanInvestmentSummary {
    return {
      totalInvestedAmount: isSet(object.totalInvestedAmount)
        ? TotalInvestedAmount.fromJSON(object.totalInvestedAmount)
        : undefined,
      totalInterestEarned: isSet(object.totalInterestEarned)
        ? TotalInterestEarned.fromJSON(object.totalInterestEarned)
        : undefined,
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogo: isSet(object.bankLogo) ? globalThis.String(object.bankLogo) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      activeTermDepositCount: isSet(object.activeTermDepositCount)
        ? globalThis.Number(object.activeTermDepositCount)
        : undefined,
    };
  },

  toJSON(message: UjjivanInvestmentSummary): unknown {
    const obj: any = {};
    if (message.totalInvestedAmount !== undefined) {
      obj.totalInvestedAmount = TotalInvestedAmount.toJSON(message.totalInvestedAmount);
    }
    if (message.totalInterestEarned !== undefined) {
      obj.totalInterestEarned = TotalInterestEarned.toJSON(message.totalInterestEarned);
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogo !== "") {
      obj.bankLogo = message.bankLogo;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.activeTermDepositCount !== undefined) {
      obj.activeTermDepositCount = Math.round(message.activeTermDepositCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UjjivanInvestmentSummary>, I>>(base?: I): UjjivanInvestmentSummary {
    return UjjivanInvestmentSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UjjivanInvestmentSummary>, I>>(object: I): UjjivanInvestmentSummary {
    const message = createBaseUjjivanInvestmentSummary();
    message.totalInvestedAmount = (object.totalInvestedAmount !== undefined && object.totalInvestedAmount !== null)
      ? TotalInvestedAmount.fromPartial(object.totalInvestedAmount)
      : undefined;
    message.totalInterestEarned = (object.totalInterestEarned !== undefined && object.totalInterestEarned !== null)
      ? TotalInterestEarned.fromPartial(object.totalInterestEarned)
      : undefined;
    message.bankName = object.bankName ?? "";
    message.bankLogo = object.bankLogo ?? "";
    message.bankId = object.bankId ?? "";
    message.activeTermDepositCount = object.activeTermDepositCount ?? undefined;
    return message;
  },
};

function createBaseIndusindInvestmentSummary(): IndusindInvestmentSummary {
  return {
    totalInvestedAmount: undefined,
    totalInterestEarned: undefined,
    bankName: "",
    bankLogo: "",
    bankId: "",
    activeTermDepositCount: undefined,
  };
}

export const IndusindInvestmentSummary: MessageFns<IndusindInvestmentSummary> = {
  encode(message: IndusindInvestmentSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalInvestedAmount !== undefined) {
      TotalInvestedAmount.encode(message.totalInvestedAmount, writer.uint32(10).fork()).join();
    }
    if (message.totalInterestEarned !== undefined) {
      TotalInterestEarned.encode(message.totalInterestEarned, writer.uint32(18).fork()).join();
    }
    if (message.bankName !== "") {
      writer.uint32(26).string(message.bankName);
    }
    if (message.bankLogo !== "") {
      writer.uint32(34).string(message.bankLogo);
    }
    if (message.bankId !== "") {
      writer.uint32(42).string(message.bankId);
    }
    if (message.activeTermDepositCount !== undefined) {
      writer.uint32(48).int32(message.activeTermDepositCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IndusindInvestmentSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIndusindInvestmentSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.totalInvestedAmount = TotalInvestedAmount.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.totalInterestEarned = TotalInterestEarned.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bankLogo = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.activeTermDepositCount = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IndusindInvestmentSummary {
    return {
      totalInvestedAmount: isSet(object.totalInvestedAmount)
        ? TotalInvestedAmount.fromJSON(object.totalInvestedAmount)
        : undefined,
      totalInterestEarned: isSet(object.totalInterestEarned)
        ? TotalInterestEarned.fromJSON(object.totalInterestEarned)
        : undefined,
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogo: isSet(object.bankLogo) ? globalThis.String(object.bankLogo) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      activeTermDepositCount: isSet(object.activeTermDepositCount)
        ? globalThis.Number(object.activeTermDepositCount)
        : undefined,
    };
  },

  toJSON(message: IndusindInvestmentSummary): unknown {
    const obj: any = {};
    if (message.totalInvestedAmount !== undefined) {
      obj.totalInvestedAmount = TotalInvestedAmount.toJSON(message.totalInvestedAmount);
    }
    if (message.totalInterestEarned !== undefined) {
      obj.totalInterestEarned = TotalInterestEarned.toJSON(message.totalInterestEarned);
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogo !== "") {
      obj.bankLogo = message.bankLogo;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.activeTermDepositCount !== undefined) {
      obj.activeTermDepositCount = Math.round(message.activeTermDepositCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IndusindInvestmentSummary>, I>>(base?: I): IndusindInvestmentSummary {
    return IndusindInvestmentSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IndusindInvestmentSummary>, I>>(object: I): IndusindInvestmentSummary {
    const message = createBaseIndusindInvestmentSummary();
    message.totalInvestedAmount = (object.totalInvestedAmount !== undefined && object.totalInvestedAmount !== null)
      ? TotalInvestedAmount.fromPartial(object.totalInvestedAmount)
      : undefined;
    message.totalInterestEarned = (object.totalInterestEarned !== undefined && object.totalInterestEarned !== null)
      ? TotalInterestEarned.fromPartial(object.totalInterestEarned)
      : undefined;
    message.bankName = object.bankName ?? "";
    message.bankLogo = object.bankLogo ?? "";
    message.bankId = object.bankId ?? "";
    message.activeTermDepositCount = object.activeTermDepositCount ?? undefined;
    return message;
  },
};

function createBaseNetWorthSummaryResponse(): NetWorthSummaryResponse {
  return {
    totalInvestedAmount: undefined,
    totalInterestEarned: undefined,
    currentAmount: undefined,
    activeTermDepositCount: 0,
    isInvested: false,
    isPending: false,
    pendingEventTime: "",
    pendingEventType: "",
    isVkycPending: false,
    isUpswingInitiated: false,
    fincareInvestmentSummary: undefined,
    unityInvestmentSummary: undefined,
    emergencyFundSummary: undefined,
    ujjivanInvestmentSummary: undefined,
    indusindInvestmentSummary: undefined,
    inProgressTotal: 0,
  };
}

export const NetWorthSummaryResponse: MessageFns<NetWorthSummaryResponse> = {
  encode(message: NetWorthSummaryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalInvestedAmount !== undefined) {
      TotalInvestedAmount.encode(message.totalInvestedAmount, writer.uint32(10).fork()).join();
    }
    if (message.totalInterestEarned !== undefined) {
      TotalInterestEarned.encode(message.totalInterestEarned, writer.uint32(18).fork()).join();
    }
    if (message.currentAmount !== undefined) {
      CurrentAmount.encode(message.currentAmount, writer.uint32(26).fork()).join();
    }
    if (message.activeTermDepositCount !== 0) {
      writer.uint32(32).int32(message.activeTermDepositCount);
    }
    if (message.isInvested !== false) {
      writer.uint32(40).bool(message.isInvested);
    }
    if (message.isPending !== false) {
      writer.uint32(48).bool(message.isPending);
    }
    if (message.pendingEventTime !== "") {
      writer.uint32(58).string(message.pendingEventTime);
    }
    if (message.pendingEventType !== "") {
      writer.uint32(66).string(message.pendingEventType);
    }
    if (message.isVkycPending !== false) {
      writer.uint32(72).bool(message.isVkycPending);
    }
    if (message.isUpswingInitiated !== false) {
      writer.uint32(80).bool(message.isUpswingInitiated);
    }
    if (message.fincareInvestmentSummary !== undefined) {
      FincareInvestmentSummary.encode(message.fincareInvestmentSummary, writer.uint32(90).fork()).join();
    }
    if (message.unityInvestmentSummary !== undefined) {
      UnityInvestmentSummary.encode(message.unityInvestmentSummary, writer.uint32(98).fork()).join();
    }
    if (message.emergencyFundSummary !== undefined) {
      EmergencyFundSummary.encode(message.emergencyFundSummary, writer.uint32(106).fork()).join();
    }
    if (message.ujjivanInvestmentSummary !== undefined) {
      UjjivanInvestmentSummary.encode(message.ujjivanInvestmentSummary, writer.uint32(114).fork()).join();
    }
    if (message.indusindInvestmentSummary !== undefined) {
      IndusindInvestmentSummary.encode(message.indusindInvestmentSummary, writer.uint32(122).fork()).join();
    }
    if (message.inProgressTotal !== 0) {
      writer.uint32(129).double(message.inProgressTotal);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NetWorthSummaryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNetWorthSummaryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.totalInvestedAmount = TotalInvestedAmount.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.totalInterestEarned = TotalInterestEarned.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.currentAmount = CurrentAmount.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.activeTermDepositCount = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isInvested = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isPending = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.pendingEventTime = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.pendingEventType = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.isVkycPending = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.isUpswingInitiated = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.fincareInvestmentSummary = FincareInvestmentSummary.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.unityInvestmentSummary = UnityInvestmentSummary.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.emergencyFundSummary = EmergencyFundSummary.decode(reader, reader.uint32());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.ujjivanInvestmentSummary = UjjivanInvestmentSummary.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.indusindInvestmentSummary = IndusindInvestmentSummary.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 129) {
            break;
          }

          message.inProgressTotal = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NetWorthSummaryResponse {
    return {
      totalInvestedAmount: isSet(object.totalInvestedAmount)
        ? TotalInvestedAmount.fromJSON(object.totalInvestedAmount)
        : undefined,
      totalInterestEarned: isSet(object.totalInterestEarned)
        ? TotalInterestEarned.fromJSON(object.totalInterestEarned)
        : undefined,
      currentAmount: isSet(object.currentAmount) ? CurrentAmount.fromJSON(object.currentAmount) : undefined,
      activeTermDepositCount: isSet(object.activeTermDepositCount)
        ? globalThis.Number(object.activeTermDepositCount)
        : 0,
      isInvested: isSet(object.isInvested) ? globalThis.Boolean(object.isInvested) : false,
      isPending: isSet(object.isPending) ? globalThis.Boolean(object.isPending) : false,
      pendingEventTime: isSet(object.pendingEventTime) ? globalThis.String(object.pendingEventTime) : "",
      pendingEventType: isSet(object.pendingEventType) ? globalThis.String(object.pendingEventType) : "",
      isVkycPending: isSet(object.isVkycPending) ? globalThis.Boolean(object.isVkycPending) : false,
      isUpswingInitiated: isSet(object.isUpswingInitiated) ? globalThis.Boolean(object.isUpswingInitiated) : false,
      fincareInvestmentSummary: isSet(object.fincareInvestmentSummary)
        ? FincareInvestmentSummary.fromJSON(object.fincareInvestmentSummary)
        : undefined,
      unityInvestmentSummary: isSet(object.unityInvestmentSummary)
        ? UnityInvestmentSummary.fromJSON(object.unityInvestmentSummary)
        : undefined,
      emergencyFundSummary: isSet(object.emergencyFundSummary)
        ? EmergencyFundSummary.fromJSON(object.emergencyFundSummary)
        : undefined,
      ujjivanInvestmentSummary: isSet(object.ujjivanInvestmentSummary)
        ? UjjivanInvestmentSummary.fromJSON(object.ujjivanInvestmentSummary)
        : undefined,
      indusindInvestmentSummary: isSet(object.indusindInvestmentSummary)
        ? IndusindInvestmentSummary.fromJSON(object.indusindInvestmentSummary)
        : undefined,
      inProgressTotal: isSet(object.inProgressTotal) ? globalThis.Number(object.inProgressTotal) : 0,
    };
  },

  toJSON(message: NetWorthSummaryResponse): unknown {
    const obj: any = {};
    if (message.totalInvestedAmount !== undefined) {
      obj.totalInvestedAmount = TotalInvestedAmount.toJSON(message.totalInvestedAmount);
    }
    if (message.totalInterestEarned !== undefined) {
      obj.totalInterestEarned = TotalInterestEarned.toJSON(message.totalInterestEarned);
    }
    if (message.currentAmount !== undefined) {
      obj.currentAmount = CurrentAmount.toJSON(message.currentAmount);
    }
    if (message.activeTermDepositCount !== 0) {
      obj.activeTermDepositCount = Math.round(message.activeTermDepositCount);
    }
    if (message.isInvested !== false) {
      obj.isInvested = message.isInvested;
    }
    if (message.isPending !== false) {
      obj.isPending = message.isPending;
    }
    if (message.pendingEventTime !== "") {
      obj.pendingEventTime = message.pendingEventTime;
    }
    if (message.pendingEventType !== "") {
      obj.pendingEventType = message.pendingEventType;
    }
    if (message.isVkycPending !== false) {
      obj.isVkycPending = message.isVkycPending;
    }
    if (message.isUpswingInitiated !== false) {
      obj.isUpswingInitiated = message.isUpswingInitiated;
    }
    if (message.fincareInvestmentSummary !== undefined) {
      obj.fincareInvestmentSummary = FincareInvestmentSummary.toJSON(message.fincareInvestmentSummary);
    }
    if (message.unityInvestmentSummary !== undefined) {
      obj.unityInvestmentSummary = UnityInvestmentSummary.toJSON(message.unityInvestmentSummary);
    }
    if (message.emergencyFundSummary !== undefined) {
      obj.emergencyFundSummary = EmergencyFundSummary.toJSON(message.emergencyFundSummary);
    }
    if (message.ujjivanInvestmentSummary !== undefined) {
      obj.ujjivanInvestmentSummary = UjjivanInvestmentSummary.toJSON(message.ujjivanInvestmentSummary);
    }
    if (message.indusindInvestmentSummary !== undefined) {
      obj.indusindInvestmentSummary = IndusindInvestmentSummary.toJSON(message.indusindInvestmentSummary);
    }
    if (message.inProgressTotal !== 0) {
      obj.inProgressTotal = message.inProgressTotal;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NetWorthSummaryResponse>, I>>(base?: I): NetWorthSummaryResponse {
    return NetWorthSummaryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NetWorthSummaryResponse>, I>>(object: I): NetWorthSummaryResponse {
    const message = createBaseNetWorthSummaryResponse();
    message.totalInvestedAmount = (object.totalInvestedAmount !== undefined && object.totalInvestedAmount !== null)
      ? TotalInvestedAmount.fromPartial(object.totalInvestedAmount)
      : undefined;
    message.totalInterestEarned = (object.totalInterestEarned !== undefined && object.totalInterestEarned !== null)
      ? TotalInterestEarned.fromPartial(object.totalInterestEarned)
      : undefined;
    message.currentAmount = (object.currentAmount !== undefined && object.currentAmount !== null)
      ? CurrentAmount.fromPartial(object.currentAmount)
      : undefined;
    message.activeTermDepositCount = object.activeTermDepositCount ?? 0;
    message.isInvested = object.isInvested ?? false;
    message.isPending = object.isPending ?? false;
    message.pendingEventTime = object.pendingEventTime ?? "";
    message.pendingEventType = object.pendingEventType ?? "";
    message.isVkycPending = object.isVkycPending ?? false;
    message.isUpswingInitiated = object.isUpswingInitiated ?? false;
    message.fincareInvestmentSummary =
      (object.fincareInvestmentSummary !== undefined && object.fincareInvestmentSummary !== null)
        ? FincareInvestmentSummary.fromPartial(object.fincareInvestmentSummary)
        : undefined;
    message.unityInvestmentSummary =
      (object.unityInvestmentSummary !== undefined && object.unityInvestmentSummary !== null)
        ? UnityInvestmentSummary.fromPartial(object.unityInvestmentSummary)
        : undefined;
    message.emergencyFundSummary = (object.emergencyFundSummary !== undefined && object.emergencyFundSummary !== null)
      ? EmergencyFundSummary.fromPartial(object.emergencyFundSummary)
      : undefined;
    message.ujjivanInvestmentSummary =
      (object.ujjivanInvestmentSummary !== undefined && object.ujjivanInvestmentSummary !== null)
        ? UjjivanInvestmentSummary.fromPartial(object.ujjivanInvestmentSummary)
        : undefined;
    message.indusindInvestmentSummary =
      (object.indusindInvestmentSummary !== undefined && object.indusindInvestmentSummary !== null)
        ? IndusindInvestmentSummary.fromPartial(object.indusindInvestmentSummary)
        : undefined;
    message.inProgressTotal = object.inProgressTotal ?? 0;
    return message;
  },
};

function createBaseEmergencyFundSummary(): EmergencyFundSummary {
  return {
    isEmergencyFundCreated: false,
    targetAmount: 0,
    currentAmount: 0,
    progressPercentage: 0,
    progressDescription: "",
    isActive: false,
  };
}

export const EmergencyFundSummary: MessageFns<EmergencyFundSummary> = {
  encode(message: EmergencyFundSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isEmergencyFundCreated !== false) {
      writer.uint32(8).bool(message.isEmergencyFundCreated);
    }
    if (message.targetAmount !== 0) {
      writer.uint32(17).double(message.targetAmount);
    }
    if (message.currentAmount !== 0) {
      writer.uint32(25).double(message.currentAmount);
    }
    if (message.progressPercentage !== 0) {
      writer.uint32(33).double(message.progressPercentage);
    }
    if (message.progressDescription !== "") {
      writer.uint32(42).string(message.progressDescription);
    }
    if (message.isActive !== false) {
      writer.uint32(48).bool(message.isActive);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmergencyFundSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmergencyFundSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isEmergencyFundCreated = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.targetAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.currentAmount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.progressPercentage = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.progressDescription = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmergencyFundSummary {
    return {
      isEmergencyFundCreated: isSet(object.isEmergencyFundCreated)
        ? globalThis.Boolean(object.isEmergencyFundCreated)
        : false,
      targetAmount: isSet(object.targetAmount) ? globalThis.Number(object.targetAmount) : 0,
      currentAmount: isSet(object.currentAmount) ? globalThis.Number(object.currentAmount) : 0,
      progressPercentage: isSet(object.progressPercentage) ? globalThis.Number(object.progressPercentage) : 0,
      progressDescription: isSet(object.progressDescription) ? globalThis.String(object.progressDescription) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
    };
  },

  toJSON(message: EmergencyFundSummary): unknown {
    const obj: any = {};
    if (message.isEmergencyFundCreated !== false) {
      obj.isEmergencyFundCreated = message.isEmergencyFundCreated;
    }
    if (message.targetAmount !== 0) {
      obj.targetAmount = message.targetAmount;
    }
    if (message.currentAmount !== 0) {
      obj.currentAmount = message.currentAmount;
    }
    if (message.progressPercentage !== 0) {
      obj.progressPercentage = message.progressPercentage;
    }
    if (message.progressDescription !== "") {
      obj.progressDescription = message.progressDescription;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmergencyFundSummary>, I>>(base?: I): EmergencyFundSummary {
    return EmergencyFundSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmergencyFundSummary>, I>>(object: I): EmergencyFundSummary {
    const message = createBaseEmergencyFundSummary();
    message.isEmergencyFundCreated = object.isEmergencyFundCreated ?? false;
    message.targetAmount = object.targetAmount ?? 0;
    message.currentAmount = object.currentAmount ?? 0;
    message.progressPercentage = object.progressPercentage ?? 0;
    message.progressDescription = object.progressDescription ?? "";
    message.isActive = object.isActive ?? false;
    return message;
  },
};

function createBaseTotalInvestedAmount(): TotalInvestedAmount {
  return { amount: 0, currency: "" };
}

export const TotalInvestedAmount: MessageFns<TotalInvestedAmount> = {
  encode(message: TotalInvestedAmount, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.amount !== 0) {
      writer.uint32(9).double(message.amount);
    }
    if (message.currency !== "") {
      writer.uint32(18).string(message.currency);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TotalInvestedAmount {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTotalInvestedAmount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.currency = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TotalInvestedAmount {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      currency: isSet(object.currency) ? globalThis.String(object.currency) : "",
    };
  },

  toJSON(message: TotalInvestedAmount): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.currency !== "") {
      obj.currency = message.currency;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TotalInvestedAmount>, I>>(base?: I): TotalInvestedAmount {
    return TotalInvestedAmount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TotalInvestedAmount>, I>>(object: I): TotalInvestedAmount {
    const message = createBaseTotalInvestedAmount();
    message.amount = object.amount ?? 0;
    message.currency = object.currency ?? "";
    return message;
  },
};

function createBaseTotalInterestEarned(): TotalInterestEarned {
  return { amount: 0, currency: "" };
}

export const TotalInterestEarned: MessageFns<TotalInterestEarned> = {
  encode(message: TotalInterestEarned, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.amount !== 0) {
      writer.uint32(9).double(message.amount);
    }
    if (message.currency !== "") {
      writer.uint32(18).string(message.currency);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TotalInterestEarned {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTotalInterestEarned();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.currency = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TotalInterestEarned {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      currency: isSet(object.currency) ? globalThis.String(object.currency) : "",
    };
  },

  toJSON(message: TotalInterestEarned): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.currency !== "") {
      obj.currency = message.currency;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TotalInterestEarned>, I>>(base?: I): TotalInterestEarned {
    return TotalInterestEarned.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TotalInterestEarned>, I>>(object: I): TotalInterestEarned {
    const message = createBaseTotalInterestEarned();
    message.amount = object.amount ?? 0;
    message.currency = object.currency ?? "";
    return message;
  },
};

function createBaseCurrentAmount(): CurrentAmount {
  return { amount: 0, currency: "" };
}

export const CurrentAmount: MessageFns<CurrentAmount> = {
  encode(message: CurrentAmount, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.amount !== 0) {
      writer.uint32(9).double(message.amount);
    }
    if (message.currency !== "") {
      writer.uint32(18).string(message.currency);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CurrentAmount {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCurrentAmount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.currency = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CurrentAmount {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      currency: isSet(object.currency) ? globalThis.String(object.currency) : "",
    };
  },

  toJSON(message: CurrentAmount): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.currency !== "") {
      obj.currency = message.currency;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CurrentAmount>, I>>(base?: I): CurrentAmount {
    return CurrentAmount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CurrentAmount>, I>>(object: I): CurrentAmount {
    const message = createBaseCurrentAmount();
    message.amount = object.amount ?? 0;
    message.currency = object.currency ?? "";
    return message;
  },
};

function createBaseFincarePayload(): FincarePayload {
  return {
    sessionId: "",
    utmMedium: "",
    utmSource: "",
    utmCampaign: "",
    referralLink: "",
    fdRate: undefined,
    fdMaturityDate: undefined,
    fdAmount: undefined,
    kycStatus: "",
    accountCreatedStatus: "",
    isSeniorCitizen: undefined,
    tenureInDays: undefined,
    interestPayoutType: undefined,
    maturityInstruction: undefined,
    fdActive: undefined,
    fdClosingDate: undefined,
    fdBookingDate: undefined,
    prodType: undefined,
  };
}

export const FincarePayload: MessageFns<FincarePayload> = {
  encode(message: FincarePayload, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.sessionId !== "") {
      writer.uint32(10).string(message.sessionId);
    }
    if (message.utmMedium !== "") {
      writer.uint32(18).string(message.utmMedium);
    }
    if (message.utmSource !== "") {
      writer.uint32(26).string(message.utmSource);
    }
    if (message.utmCampaign !== "") {
      writer.uint32(34).string(message.utmCampaign);
    }
    if (message.referralLink !== "") {
      writer.uint32(42).string(message.referralLink);
    }
    if (message.fdRate !== undefined) {
      writer.uint32(49).double(message.fdRate);
    }
    if (message.fdMaturityDate !== undefined) {
      writer.uint32(58).string(message.fdMaturityDate);
    }
    if (message.fdAmount !== undefined) {
      writer.uint32(65).double(message.fdAmount);
    }
    if (message.kycStatus !== "") {
      writer.uint32(74).string(message.kycStatus);
    }
    if (message.accountCreatedStatus !== "") {
      writer.uint32(82).string(message.accountCreatedStatus);
    }
    if (message.isSeniorCitizen !== undefined) {
      writer.uint32(88).bool(message.isSeniorCitizen);
    }
    if (message.tenureInDays !== undefined) {
      writer.uint32(96).int32(message.tenureInDays);
    }
    if (message.interestPayoutType !== undefined) {
      writer.uint32(106).string(message.interestPayoutType);
    }
    if (message.maturityInstruction !== undefined) {
      writer.uint32(114).string(message.maturityInstruction);
    }
    if (message.fdActive !== undefined) {
      writer.uint32(120).bool(message.fdActive);
    }
    if (message.fdClosingDate !== undefined) {
      writer.uint32(130).string(message.fdClosingDate);
    }
    if (message.fdBookingDate !== undefined) {
      writer.uint32(138).string(message.fdBookingDate);
    }
    if (message.prodType !== undefined) {
      writer.uint32(146).string(message.prodType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FincarePayload {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFincarePayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.sessionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.utmMedium = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.utmSource = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.utmCampaign = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.referralLink = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.fdRate = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.fdMaturityDate = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.fdAmount = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.kycStatus = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.accountCreatedStatus = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.isSeniorCitizen = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.tenureInDays = reader.int32();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.interestPayoutType = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.maturityInstruction = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.fdActive = reader.bool();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.fdClosingDate = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.fdBookingDate = reader.string();
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.prodType = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FincarePayload {
    return {
      sessionId: isSet(object.sessionId) ? globalThis.String(object.sessionId) : "",
      utmMedium: isSet(object.utmMedium) ? globalThis.String(object.utmMedium) : "",
      utmSource: isSet(object.utmSource) ? globalThis.String(object.utmSource) : "",
      utmCampaign: isSet(object.utmCampaign) ? globalThis.String(object.utmCampaign) : "",
      referralLink: isSet(object.referralLink) ? globalThis.String(object.referralLink) : "",
      fdRate: isSet(object.fdRate) ? globalThis.Number(object.fdRate) : undefined,
      fdMaturityDate: isSet(object.fdMaturityDate) ? globalThis.String(object.fdMaturityDate) : undefined,
      fdAmount: isSet(object.fdAmount) ? globalThis.Number(object.fdAmount) : undefined,
      kycStatus: isSet(object.kycStatus) ? globalThis.String(object.kycStatus) : "",
      accountCreatedStatus: isSet(object.accountCreatedStatus) ? globalThis.String(object.accountCreatedStatus) : "",
      isSeniorCitizen: isSet(object.isSeniorCitizen) ? globalThis.Boolean(object.isSeniorCitizen) : undefined,
      tenureInDays: isSet(object.tenureInDays) ? globalThis.Number(object.tenureInDays) : undefined,
      interestPayoutType: isSet(object.interestPayoutType) ? globalThis.String(object.interestPayoutType) : undefined,
      maturityInstruction: isSet(object.maturityInstruction)
        ? globalThis.String(object.maturityInstruction)
        : undefined,
      fdActive: isSet(object.fdActive) ? globalThis.Boolean(object.fdActive) : undefined,
      fdClosingDate: isSet(object.fdClosingDate) ? globalThis.String(object.fdClosingDate) : undefined,
      fdBookingDate: isSet(object.fdBookingDate) ? globalThis.String(object.fdBookingDate) : undefined,
      prodType: isSet(object.prodType) ? globalThis.String(object.prodType) : undefined,
    };
  },

  toJSON(message: FincarePayload): unknown {
    const obj: any = {};
    if (message.sessionId !== "") {
      obj.sessionId = message.sessionId;
    }
    if (message.utmMedium !== "") {
      obj.utmMedium = message.utmMedium;
    }
    if (message.utmSource !== "") {
      obj.utmSource = message.utmSource;
    }
    if (message.utmCampaign !== "") {
      obj.utmCampaign = message.utmCampaign;
    }
    if (message.referralLink !== "") {
      obj.referralLink = message.referralLink;
    }
    if (message.fdRate !== undefined) {
      obj.fdRate = message.fdRate;
    }
    if (message.fdMaturityDate !== undefined) {
      obj.fdMaturityDate = message.fdMaturityDate;
    }
    if (message.fdAmount !== undefined) {
      obj.fdAmount = message.fdAmount;
    }
    if (message.kycStatus !== "") {
      obj.kycStatus = message.kycStatus;
    }
    if (message.accountCreatedStatus !== "") {
      obj.accountCreatedStatus = message.accountCreatedStatus;
    }
    if (message.isSeniorCitizen !== undefined) {
      obj.isSeniorCitizen = message.isSeniorCitizen;
    }
    if (message.tenureInDays !== undefined) {
      obj.tenureInDays = Math.round(message.tenureInDays);
    }
    if (message.interestPayoutType !== undefined) {
      obj.interestPayoutType = message.interestPayoutType;
    }
    if (message.maturityInstruction !== undefined) {
      obj.maturityInstruction = message.maturityInstruction;
    }
    if (message.fdActive !== undefined) {
      obj.fdActive = message.fdActive;
    }
    if (message.fdClosingDate !== undefined) {
      obj.fdClosingDate = message.fdClosingDate;
    }
    if (message.fdBookingDate !== undefined) {
      obj.fdBookingDate = message.fdBookingDate;
    }
    if (message.prodType !== undefined) {
      obj.prodType = message.prodType;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FincarePayload>, I>>(base?: I): FincarePayload {
    return FincarePayload.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FincarePayload>, I>>(object: I): FincarePayload {
    const message = createBaseFincarePayload();
    message.sessionId = object.sessionId ?? "";
    message.utmMedium = object.utmMedium ?? "";
    message.utmSource = object.utmSource ?? "";
    message.utmCampaign = object.utmCampaign ?? "";
    message.referralLink = object.referralLink ?? "";
    message.fdRate = object.fdRate ?? undefined;
    message.fdMaturityDate = object.fdMaturityDate ?? undefined;
    message.fdAmount = object.fdAmount ?? undefined;
    message.kycStatus = object.kycStatus ?? "";
    message.accountCreatedStatus = object.accountCreatedStatus ?? "";
    message.isSeniorCitizen = object.isSeniorCitizen ?? undefined;
    message.tenureInDays = object.tenureInDays ?? undefined;
    message.interestPayoutType = object.interestPayoutType ?? undefined;
    message.maturityInstruction = object.maturityInstruction ?? undefined;
    message.fdActive = object.fdActive ?? undefined;
    message.fdClosingDate = object.fdClosingDate ?? undefined;
    message.fdBookingDate = object.fdBookingDate ?? undefined;
    message.prodType = object.prodType ?? undefined;
    return message;
  },
};

function createBaseTenurePrefillInfo(): TenurePrefillInfo {
  return { tenureHtmlTag: undefined, tenureYear: undefined, tenureMonth: undefined, tenureDay: undefined };
}

export const TenurePrefillInfo: MessageFns<TenurePrefillInfo> = {
  encode(message: TenurePrefillInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenureHtmlTag !== undefined) {
      writer.uint32(10).string(message.tenureHtmlTag);
    }
    if (message.tenureYear !== undefined) {
      writer.uint32(16).int32(message.tenureYear);
    }
    if (message.tenureMonth !== undefined) {
      writer.uint32(24).int32(message.tenureMonth);
    }
    if (message.tenureDay !== undefined) {
      writer.uint32(32).int32(message.tenureDay);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TenurePrefillInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenurePrefillInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenureHtmlTag = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.tenureYear = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.tenureMonth = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.tenureDay = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TenurePrefillInfo {
    return {
      tenureHtmlTag: isSet(object.tenureHtmlTag) ? globalThis.String(object.tenureHtmlTag) : undefined,
      tenureYear: isSet(object.tenureYear) ? globalThis.Number(object.tenureYear) : undefined,
      tenureMonth: isSet(object.tenureMonth) ? globalThis.Number(object.tenureMonth) : undefined,
      tenureDay: isSet(object.tenureDay) ? globalThis.Number(object.tenureDay) : undefined,
    };
  },

  toJSON(message: TenurePrefillInfo): unknown {
    const obj: any = {};
    if (message.tenureHtmlTag !== undefined) {
      obj.tenureHtmlTag = message.tenureHtmlTag;
    }
    if (message.tenureYear !== undefined) {
      obj.tenureYear = Math.round(message.tenureYear);
    }
    if (message.tenureMonth !== undefined) {
      obj.tenureMonth = Math.round(message.tenureMonth);
    }
    if (message.tenureDay !== undefined) {
      obj.tenureDay = Math.round(message.tenureDay);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TenurePrefillInfo>, I>>(base?: I): TenurePrefillInfo {
    return TenurePrefillInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TenurePrefillInfo>, I>>(object: I): TenurePrefillInfo {
    const message = createBaseTenurePrefillInfo();
    message.tenureHtmlTag = object.tenureHtmlTag ?? undefined;
    message.tenureYear = object.tenureYear ?? undefined;
    message.tenureMonth = object.tenureMonth ?? undefined;
    message.tenureDay = object.tenureDay ?? undefined;
    return message;
  },
};

function createBaseNomineePrefillInfo(): NomineePrefillInfo {
  return { nomineeName: undefined, nomineeRelationship: undefined, nomineeDateOfBirth: undefined };
}

export const NomineePrefillInfo: MessageFns<NomineePrefillInfo> = {
  encode(message: NomineePrefillInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nomineeName !== undefined) {
      writer.uint32(10).string(message.nomineeName);
    }
    if (message.nomineeRelationship !== undefined) {
      writer.uint32(18).string(message.nomineeRelationship);
    }
    if (message.nomineeDateOfBirth !== undefined) {
      writer.uint32(26).string(message.nomineeDateOfBirth);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NomineePrefillInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNomineePrefillInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.nomineeName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nomineeRelationship = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.nomineeDateOfBirth = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NomineePrefillInfo {
    return {
      nomineeName: isSet(object.nomineeName) ? globalThis.String(object.nomineeName) : undefined,
      nomineeRelationship: isSet(object.nomineeRelationship)
        ? globalThis.String(object.nomineeRelationship)
        : undefined,
      nomineeDateOfBirth: isSet(object.nomineeDateOfBirth) ? globalThis.String(object.nomineeDateOfBirth) : undefined,
    };
  },

  toJSON(message: NomineePrefillInfo): unknown {
    const obj: any = {};
    if (message.nomineeName !== undefined) {
      obj.nomineeName = message.nomineeName;
    }
    if (message.nomineeRelationship !== undefined) {
      obj.nomineeRelationship = message.nomineeRelationship;
    }
    if (message.nomineeDateOfBirth !== undefined) {
      obj.nomineeDateOfBirth = message.nomineeDateOfBirth;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NomineePrefillInfo>, I>>(base?: I): NomineePrefillInfo {
    return NomineePrefillInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NomineePrefillInfo>, I>>(object: I): NomineePrefillInfo {
    const message = createBaseNomineePrefillInfo();
    message.nomineeName = object.nomineeName ?? undefined;
    message.nomineeRelationship = object.nomineeRelationship ?? undefined;
    message.nomineeDateOfBirth = object.nomineeDateOfBirth ?? undefined;
    return message;
  },
};

function createBaseBankAccountPrefillInfo(): BankAccountPrefillInfo {
  return { accountHolderName: undefined, bankAccountNumber: undefined, ifscCode: undefined };
}

export const BankAccountPrefillInfo: MessageFns<BankAccountPrefillInfo> = {
  encode(message: BankAccountPrefillInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountHolderName !== undefined) {
      writer.uint32(10).string(message.accountHolderName);
    }
    if (message.bankAccountNumber !== undefined) {
      writer.uint32(18).string(message.bankAccountNumber);
    }
    if (message.ifscCode !== undefined) {
      writer.uint32(26).string(message.ifscCode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankAccountPrefillInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankAccountPrefillInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountHolderName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankAccountNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.ifscCode = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankAccountPrefillInfo {
    return {
      accountHolderName: isSet(object.accountHolderName) ? globalThis.String(object.accountHolderName) : undefined,
      bankAccountNumber: isSet(object.bankAccountNumber) ? globalThis.String(object.bankAccountNumber) : undefined,
      ifscCode: isSet(object.ifscCode) ? globalThis.String(object.ifscCode) : undefined,
    };
  },

  toJSON(message: BankAccountPrefillInfo): unknown {
    const obj: any = {};
    if (message.accountHolderName !== undefined) {
      obj.accountHolderName = message.accountHolderName;
    }
    if (message.bankAccountNumber !== undefined) {
      obj.bankAccountNumber = message.bankAccountNumber;
    }
    if (message.ifscCode !== undefined) {
      obj.ifscCode = message.ifscCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankAccountPrefillInfo>, I>>(base?: I): BankAccountPrefillInfo {
    return BankAccountPrefillInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankAccountPrefillInfo>, I>>(object: I): BankAccountPrefillInfo {
    const message = createBaseBankAccountPrefillInfo();
    message.accountHolderName = object.accountHolderName ?? undefined;
    message.bankAccountNumber = object.bankAccountNumber ?? undefined;
    message.ifscCode = object.ifscCode ?? undefined;
    return message;
  },
};

function createBaseFdPrefillData(): FdPrefillData {
  return {
    investmentAmount: 0,
    maturityInstruction: "",
    interestPayoutType: "",
    tenurePrefillInfo: undefined,
    nomineePrefillInfo: undefined,
    bankAccountPrefillInfo: undefined,
    isTaxSaving: false,
    panNumber: undefined,
    dob: undefined,
  };
}

export const FdPrefillData: MessageFns<FdPrefillData> = {
  encode(message: FdPrefillData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.investmentAmount !== 0) {
      writer.uint32(9).double(message.investmentAmount);
    }
    if (message.maturityInstruction !== "") {
      writer.uint32(18).string(message.maturityInstruction);
    }
    if (message.interestPayoutType !== "") {
      writer.uint32(26).string(message.interestPayoutType);
    }
    if (message.tenurePrefillInfo !== undefined) {
      TenurePrefillInfo.encode(message.tenurePrefillInfo, writer.uint32(34).fork()).join();
    }
    if (message.nomineePrefillInfo !== undefined) {
      NomineePrefillInfo.encode(message.nomineePrefillInfo, writer.uint32(42).fork()).join();
    }
    if (message.bankAccountPrefillInfo !== undefined) {
      BankAccountPrefillInfo.encode(message.bankAccountPrefillInfo, writer.uint32(50).fork()).join();
    }
    if (message.isTaxSaving !== false) {
      writer.uint32(56).bool(message.isTaxSaving);
    }
    if (message.panNumber !== undefined) {
      writer.uint32(66).string(message.panNumber);
    }
    if (message.dob !== undefined) {
      writer.uint32(74).string(message.dob);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FdPrefillData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFdPrefillData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.maturityInstruction = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.interestPayoutType = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.tenurePrefillInfo = TenurePrefillInfo.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.nomineePrefillInfo = NomineePrefillInfo.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.bankAccountPrefillInfo = BankAccountPrefillInfo.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.isTaxSaving = reader.bool();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.panNumber = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.dob = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FdPrefillData {
    return {
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
      maturityInstruction: isSet(object.maturityInstruction) ? globalThis.String(object.maturityInstruction) : "",
      interestPayoutType: isSet(object.interestPayoutType) ? globalThis.String(object.interestPayoutType) : "",
      tenurePrefillInfo: isSet(object.tenurePrefillInfo)
        ? TenurePrefillInfo.fromJSON(object.tenurePrefillInfo)
        : undefined,
      nomineePrefillInfo: isSet(object.nomineePrefillInfo)
        ? NomineePrefillInfo.fromJSON(object.nomineePrefillInfo)
        : undefined,
      bankAccountPrefillInfo: isSet(object.bankAccountPrefillInfo)
        ? BankAccountPrefillInfo.fromJSON(object.bankAccountPrefillInfo)
        : undefined,
      isTaxSaving: isSet(object.isTaxSaving) ? globalThis.Boolean(object.isTaxSaving) : false,
      panNumber: isSet(object.panNumber) ? globalThis.String(object.panNumber) : undefined,
      dob: isSet(object.dob) ? globalThis.String(object.dob) : undefined,
    };
  },

  toJSON(message: FdPrefillData): unknown {
    const obj: any = {};
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    if (message.maturityInstruction !== "") {
      obj.maturityInstruction = message.maturityInstruction;
    }
    if (message.interestPayoutType !== "") {
      obj.interestPayoutType = message.interestPayoutType;
    }
    if (message.tenurePrefillInfo !== undefined) {
      obj.tenurePrefillInfo = TenurePrefillInfo.toJSON(message.tenurePrefillInfo);
    }
    if (message.nomineePrefillInfo !== undefined) {
      obj.nomineePrefillInfo = NomineePrefillInfo.toJSON(message.nomineePrefillInfo);
    }
    if (message.bankAccountPrefillInfo !== undefined) {
      obj.bankAccountPrefillInfo = BankAccountPrefillInfo.toJSON(message.bankAccountPrefillInfo);
    }
    if (message.isTaxSaving !== false) {
      obj.isTaxSaving = message.isTaxSaving;
    }
    if (message.panNumber !== undefined) {
      obj.panNumber = message.panNumber;
    }
    if (message.dob !== undefined) {
      obj.dob = message.dob;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FdPrefillData>, I>>(base?: I): FdPrefillData {
    return FdPrefillData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FdPrefillData>, I>>(object: I): FdPrefillData {
    const message = createBaseFdPrefillData();
    message.investmentAmount = object.investmentAmount ?? 0;
    message.maturityInstruction = object.maturityInstruction ?? "";
    message.interestPayoutType = object.interestPayoutType ?? "";
    message.tenurePrefillInfo = (object.tenurePrefillInfo !== undefined && object.tenurePrefillInfo !== null)
      ? TenurePrefillInfo.fromPartial(object.tenurePrefillInfo)
      : undefined;
    message.nomineePrefillInfo = (object.nomineePrefillInfo !== undefined && object.nomineePrefillInfo !== null)
      ? NomineePrefillInfo.fromPartial(object.nomineePrefillInfo)
      : undefined;
    message.bankAccountPrefillInfo =
      (object.bankAccountPrefillInfo !== undefined && object.bankAccountPrefillInfo !== null)
        ? BankAccountPrefillInfo.fromPartial(object.bankAccountPrefillInfo)
        : undefined;
    message.isTaxSaving = object.isTaxSaving ?? false;
    message.panNumber = object.panNumber ?? undefined;
    message.dob = object.dob ?? undefined;
    return message;
  },
};

function createBaseFdEventResponse(): FdEventResponse {
  return { bookingUrl: "", bookingId: "", fdPrefillData: undefined, fdId: "", isBlock: undefined, isInput: undefined };
}

export const FdEventResponse: MessageFns<FdEventResponse> = {
  encode(message: FdEventResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bookingUrl !== "") {
      writer.uint32(10).string(message.bookingUrl);
    }
    if (message.bookingId !== "") {
      writer.uint32(18).string(message.bookingId);
    }
    if (message.fdPrefillData !== undefined) {
      FdPrefillData.encode(message.fdPrefillData, writer.uint32(26).fork()).join();
    }
    if (message.fdId !== "") {
      writer.uint32(34).string(message.fdId);
    }
    if (message.isBlock !== undefined) {
      writer.uint32(40).bool(message.isBlock);
    }
    if (message.isInput !== undefined) {
      writer.uint32(48).bool(message.isInput);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FdEventResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFdEventResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bookingUrl = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bookingId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fdPrefillData = FdPrefillData.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.fdId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isBlock = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isInput = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FdEventResponse {
    return {
      bookingUrl: isSet(object.bookingUrl) ? globalThis.String(object.bookingUrl) : "",
      bookingId: isSet(object.bookingId) ? globalThis.String(object.bookingId) : "",
      fdPrefillData: isSet(object.fdPrefillData) ? FdPrefillData.fromJSON(object.fdPrefillData) : undefined,
      fdId: isSet(object.fdId) ? globalThis.String(object.fdId) : "",
      isBlock: isSet(object.isBlock) ? globalThis.Boolean(object.isBlock) : undefined,
      isInput: isSet(object.isInput) ? globalThis.Boolean(object.isInput) : undefined,
    };
  },

  toJSON(message: FdEventResponse): unknown {
    const obj: any = {};
    if (message.bookingUrl !== "") {
      obj.bookingUrl = message.bookingUrl;
    }
    if (message.bookingId !== "") {
      obj.bookingId = message.bookingId;
    }
    if (message.fdPrefillData !== undefined) {
      obj.fdPrefillData = FdPrefillData.toJSON(message.fdPrefillData);
    }
    if (message.fdId !== "") {
      obj.fdId = message.fdId;
    }
    if (message.isBlock !== undefined) {
      obj.isBlock = message.isBlock;
    }
    if (message.isInput !== undefined) {
      obj.isInput = message.isInput;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FdEventResponse>, I>>(base?: I): FdEventResponse {
    return FdEventResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FdEventResponse>, I>>(object: I): FdEventResponse {
    const message = createBaseFdEventResponse();
    message.bookingUrl = object.bookingUrl ?? "";
    message.bookingId = object.bookingId ?? "";
    message.fdPrefillData = (object.fdPrefillData !== undefined && object.fdPrefillData !== null)
      ? FdPrefillData.fromPartial(object.fdPrefillData)
      : undefined;
    message.fdId = object.fdId ?? "";
    message.isBlock = object.isBlock ?? undefined;
    message.isInput = object.isInput ?? undefined;
    return message;
  },
};

function createBaseEtbResponse(): EtbResponse {
  return { isEtbKnown: false };
}

export const EtbResponse: MessageFns<EtbResponse> = {
  encode(message: EtbResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isEtbKnown !== false) {
      writer.uint32(8).bool(message.isEtbKnown);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EtbResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEtbResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isEtbKnown = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EtbResponse {
    return { isEtbKnown: isSet(object.isEtbKnown) ? globalThis.Boolean(object.isEtbKnown) : false };
  },

  toJSON(message: EtbResponse): unknown {
    const obj: any = {};
    if (message.isEtbKnown !== false) {
      obj.isEtbKnown = message.isEtbKnown;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EtbResponse>, I>>(base?: I): EtbResponse {
    return EtbResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EtbResponse>, I>>(object: I): EtbResponse {
    const message = createBaseEtbResponse();
    message.isEtbKnown = object.isEtbKnown ?? false;
    return message;
  },
};

function createBaseSetEtbRequest(): SetEtbRequest {
  return { bankId: "", hasAccount: false, hasFd: false };
}

export const SetEtbRequest: MessageFns<SetEtbRequest> = {
  encode(message: SetEtbRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    if (message.hasAccount !== false) {
      writer.uint32(16).bool(message.hasAccount);
    }
    if (message.hasFd !== false) {
      writer.uint32(24).bool(message.hasFd);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SetEtbRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetEtbRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasAccount = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.hasFd = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SetEtbRequest {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      hasAccount: isSet(object.hasAccount) ? globalThis.Boolean(object.hasAccount) : false,
      hasFd: isSet(object.hasFd) ? globalThis.Boolean(object.hasFd) : false,
    };
  },

  toJSON(message: SetEtbRequest): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.hasAccount !== false) {
      obj.hasAccount = message.hasAccount;
    }
    if (message.hasFd !== false) {
      obj.hasFd = message.hasFd;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SetEtbRequest>, I>>(base?: I): SetEtbRequest {
    return SetEtbRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetEtbRequest>, I>>(object: I): SetEtbRequest {
    const message = createBaseSetEtbRequest();
    message.bankId = object.bankId ?? "";
    message.hasAccount = object.hasAccount ?? false;
    message.hasFd = object.hasFd ?? false;
    return message;
  },
};

function createBaseGetEtbRequest(): GetEtbRequest {
  return { bankId: "" };
}

export const GetEtbRequest: MessageFns<GetEtbRequest> = {
  encode(message: GetEtbRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetEtbRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEtbRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetEtbRequest {
    return { bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "" };
  },

  toJSON(message: GetEtbRequest): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetEtbRequest>, I>>(base?: I): GetEtbRequest {
    return GetEtbRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetEtbRequest>, I>>(object: I): GetEtbRequest {
    const message = createBaseGetEtbRequest();
    message.bankId = object.bankId ?? "";
    return message;
  },
};

function createBasePanStatusResponse(): PanStatusResponse {
  return { isPanNeeded: false };
}

export const PanStatusResponse: MessageFns<PanStatusResponse> = {
  encode(message: PanStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isPanNeeded !== false) {
      writer.uint32(8).bool(message.isPanNeeded);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PanStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePanStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isPanNeeded = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PanStatusResponse {
    return { isPanNeeded: isSet(object.isPanNeeded) ? globalThis.Boolean(object.isPanNeeded) : false };
  },

  toJSON(message: PanStatusResponse): unknown {
    const obj: any = {};
    if (message.isPanNeeded !== false) {
      obj.isPanNeeded = message.isPanNeeded;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PanStatusResponse>, I>>(base?: I): PanStatusResponse {
    return PanStatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PanStatusResponse>, I>>(object: I): PanStatusResponse {
    const message = createBasePanStatusResponse();
    message.isPanNeeded = object.isPanNeeded ?? false;
    return message;
  },
};

function createBaseEventMetadata(): EventMetadata {
  return {};
}

export const EventMetadata: MessageFns<EventMetadata> = {
  encode(_: EventMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EventMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEventMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): EventMetadata {
    return {};
  },

  toJSON(_: EventMetadata): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<EventMetadata>, I>>(base?: I): EventMetadata {
    return EventMetadata.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EventMetadata>, I>>(_: I): EventMetadata {
    const message = createBaseEventMetadata();
    return message;
  },
};

function createBaseFdData(): FdData {
  return {
    fdId: "",
    investmentAmount: 0,
    interestPayoutType: "",
    maturityInstruction: "",
    panNumber: undefined,
    dob: undefined,
    panConsent: undefined,
  };
}

export const FdData: MessageFns<FdData> = {
  encode(message: FdData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdId !== "") {
      writer.uint32(10).string(message.fdId);
    }
    if (message.investmentAmount !== 0) {
      writer.uint32(17).double(message.investmentAmount);
    }
    if (message.interestPayoutType !== "") {
      writer.uint32(26).string(message.interestPayoutType);
    }
    if (message.maturityInstruction !== "") {
      writer.uint32(34).string(message.maturityInstruction);
    }
    if (message.panNumber !== undefined) {
      writer.uint32(42).string(message.panNumber);
    }
    if (message.dob !== undefined) {
      writer.uint32(50).string(message.dob);
    }
    if (message.panConsent !== undefined) {
      writer.uint32(56).bool(message.panConsent);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FdData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFdData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fdId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.interestPayoutType = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.maturityInstruction = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.panNumber = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.dob = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.panConsent = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FdData {
    return {
      fdId: isSet(object.fdId) ? globalThis.String(object.fdId) : "",
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
      interestPayoutType: isSet(object.interestPayoutType) ? globalThis.String(object.interestPayoutType) : "",
      maturityInstruction: isSet(object.maturityInstruction) ? globalThis.String(object.maturityInstruction) : "",
      panNumber: isSet(object.panNumber) ? globalThis.String(object.panNumber) : undefined,
      dob: isSet(object.dob) ? globalThis.String(object.dob) : undefined,
      panConsent: isSet(object.panConsent) ? globalThis.Boolean(object.panConsent) : undefined,
    };
  },

  toJSON(message: FdData): unknown {
    const obj: any = {};
    if (message.fdId !== "") {
      obj.fdId = message.fdId;
    }
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    if (message.interestPayoutType !== "") {
      obj.interestPayoutType = message.interestPayoutType;
    }
    if (message.maturityInstruction !== "") {
      obj.maturityInstruction = message.maturityInstruction;
    }
    if (message.panNumber !== undefined) {
      obj.panNumber = message.panNumber;
    }
    if (message.dob !== undefined) {
      obj.dob = message.dob;
    }
    if (message.panConsent !== undefined) {
      obj.panConsent = message.panConsent;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FdData>, I>>(base?: I): FdData {
    return FdData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FdData>, I>>(object: I): FdData {
    const message = createBaseFdData();
    message.fdId = object.fdId ?? "";
    message.investmentAmount = object.investmentAmount ?? 0;
    message.interestPayoutType = object.interestPayoutType ?? "";
    message.maturityInstruction = object.maturityInstruction ?? "";
    message.panNumber = object.panNumber ?? undefined;
    message.dob = object.dob ?? undefined;
    message.panConsent = object.panConsent ?? undefined;
    return message;
  },
};

function createBaseFdEventRequest(): FdEventRequest {
  return {
    bankId: "",
    eventType: "",
    fdData: undefined,
    eventMetadata: undefined,
    bookingId: undefined,
    pageHtml: undefined,
    pageUrl: undefined,
    timestamp: undefined,
  };
}

export const FdEventRequest: MessageFns<FdEventRequest> = {
  encode(message: FdEventRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    if (message.eventType !== "") {
      writer.uint32(18).string(message.eventType);
    }
    if (message.fdData !== undefined) {
      FdData.encode(message.fdData, writer.uint32(26).fork()).join();
    }
    if (message.eventMetadata !== undefined) {
      EventMetadata.encode(message.eventMetadata, writer.uint32(34).fork()).join();
    }
    if (message.bookingId !== undefined) {
      writer.uint32(42).string(message.bookingId);
    }
    if (message.pageHtml !== undefined) {
      writer.uint32(50).string(message.pageHtml);
    }
    if (message.pageUrl !== undefined) {
      writer.uint32(58).string(message.pageUrl);
    }
    if (message.timestamp !== undefined) {
      writer.uint32(66).string(message.timestamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FdEventRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFdEventRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.eventType = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fdData = FdData.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.eventMetadata = EventMetadata.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bookingId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.pageHtml = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.pageUrl = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.timestamp = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FdEventRequest {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      eventType: isSet(object.eventType) ? globalThis.String(object.eventType) : "",
      fdData: isSet(object.fdData) ? FdData.fromJSON(object.fdData) : undefined,
      eventMetadata: isSet(object.eventMetadata) ? EventMetadata.fromJSON(object.eventMetadata) : undefined,
      bookingId: isSet(object.bookingId) ? globalThis.String(object.bookingId) : undefined,
      pageHtml: isSet(object.pageHtml) ? globalThis.String(object.pageHtml) : undefined,
      pageUrl: isSet(object.pageUrl) ? globalThis.String(object.pageUrl) : undefined,
      timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : undefined,
    };
  },

  toJSON(message: FdEventRequest): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.eventType !== "") {
      obj.eventType = message.eventType;
    }
    if (message.fdData !== undefined) {
      obj.fdData = FdData.toJSON(message.fdData);
    }
    if (message.eventMetadata !== undefined) {
      obj.eventMetadata = EventMetadata.toJSON(message.eventMetadata);
    }
    if (message.bookingId !== undefined) {
      obj.bookingId = message.bookingId;
    }
    if (message.pageHtml !== undefined) {
      obj.pageHtml = message.pageHtml;
    }
    if (message.pageUrl !== undefined) {
      obj.pageUrl = message.pageUrl;
    }
    if (message.timestamp !== undefined) {
      obj.timestamp = message.timestamp;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FdEventRequest>, I>>(base?: I): FdEventRequest {
    return FdEventRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FdEventRequest>, I>>(object: I): FdEventRequest {
    const message = createBaseFdEventRequest();
    message.bankId = object.bankId ?? "";
    message.eventType = object.eventType ?? "";
    message.fdData = (object.fdData !== undefined && object.fdData !== null)
      ? FdData.fromPartial(object.fdData)
      : undefined;
    message.eventMetadata = (object.eventMetadata !== undefined && object.eventMetadata !== null)
      ? EventMetadata.fromPartial(object.eventMetadata)
      : undefined;
    message.bookingId = object.bookingId ?? undefined;
    message.pageHtml = object.pageHtml ?? undefined;
    message.pageUrl = object.pageUrl ?? undefined;
    message.timestamp = object.timestamp ?? undefined;
    return message;
  },
};

function createBaseFdCalculationRequest(): FdCalculationRequest {
  return { fdId: "", investmentAmount: 0, interestPayoutType: "" };
}

export const FdCalculationRequest: MessageFns<FdCalculationRequest> = {
  encode(message: FdCalculationRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdId !== "") {
      writer.uint32(10).string(message.fdId);
    }
    if (message.investmentAmount !== 0) {
      writer.uint32(17).double(message.investmentAmount);
    }
    if (message.interestPayoutType !== "") {
      writer.uint32(26).string(message.interestPayoutType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FdCalculationRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFdCalculationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fdId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.interestPayoutType = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FdCalculationRequest {
    return {
      fdId: isSet(object.fdId) ? globalThis.String(object.fdId) : "",
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
      interestPayoutType: isSet(object.interestPayoutType) ? globalThis.String(object.interestPayoutType) : "",
    };
  },

  toJSON(message: FdCalculationRequest): unknown {
    const obj: any = {};
    if (message.fdId !== "") {
      obj.fdId = message.fdId;
    }
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    if (message.interestPayoutType !== "") {
      obj.interestPayoutType = message.interestPayoutType;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FdCalculationRequest>, I>>(base?: I): FdCalculationRequest {
    return FdCalculationRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FdCalculationRequest>, I>>(object: I): FdCalculationRequest {
    const message = createBaseFdCalculationRequest();
    message.fdId = object.fdId ?? "";
    message.investmentAmount = object.investmentAmount ?? 0;
    message.interestPayoutType = object.interestPayoutType ?? "";
    return message;
  },
};

function createBaseFdCalculationResponse(): FdCalculationResponse {
  return { maturityAmount: 0, maturityDescription: "" };
}

export const FdCalculationResponse: MessageFns<FdCalculationResponse> = {
  encode(message: FdCalculationResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.maturityAmount !== 0) {
      writer.uint32(9).double(message.maturityAmount);
    }
    if (message.maturityDescription !== "") {
      writer.uint32(18).string(message.maturityDescription);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FdCalculationResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFdCalculationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.maturityAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.maturityDescription = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FdCalculationResponse {
    return {
      maturityAmount: isSet(object.maturityAmount) ? globalThis.Number(object.maturityAmount) : 0,
      maturityDescription: isSet(object.maturityDescription) ? globalThis.String(object.maturityDescription) : "",
    };
  },

  toJSON(message: FdCalculationResponse): unknown {
    const obj: any = {};
    if (message.maturityAmount !== 0) {
      obj.maturityAmount = message.maturityAmount;
    }
    if (message.maturityDescription !== "") {
      obj.maturityDescription = message.maturityDescription;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FdCalculationResponse>, I>>(base?: I): FdCalculationResponse {
    return FdCalculationResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FdCalculationResponse>, I>>(object: I): FdCalculationResponse {
    const message = createBaseFdCalculationResponse();
    message.maturityAmount = object.maturityAmount ?? 0;
    message.maturityDescription = object.maturityDescription ?? "";
    return message;
  },
};

function createBaseFdCalculationResponseV2(): FdCalculationResponseV2 {
  return { maturityAmount: 0, interestGained: 0, maturityDescription: "" };
}

export const FdCalculationResponseV2: MessageFns<FdCalculationResponseV2> = {
  encode(message: FdCalculationResponseV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.maturityAmount !== 0) {
      writer.uint32(9).double(message.maturityAmount);
    }
    if (message.interestGained !== 0) {
      writer.uint32(17).double(message.interestGained);
    }
    if (message.maturityDescription !== "") {
      writer.uint32(26).string(message.maturityDescription);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FdCalculationResponseV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFdCalculationResponseV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.maturityAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.interestGained = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.maturityDescription = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FdCalculationResponseV2 {
    return {
      maturityAmount: isSet(object.maturityAmount) ? globalThis.Number(object.maturityAmount) : 0,
      interestGained: isSet(object.interestGained) ? globalThis.Number(object.interestGained) : 0,
      maturityDescription: isSet(object.maturityDescription) ? globalThis.String(object.maturityDescription) : "",
    };
  },

  toJSON(message: FdCalculationResponseV2): unknown {
    const obj: any = {};
    if (message.maturityAmount !== 0) {
      obj.maturityAmount = message.maturityAmount;
    }
    if (message.interestGained !== 0) {
      obj.interestGained = message.interestGained;
    }
    if (message.maturityDescription !== "") {
      obj.maturityDescription = message.maturityDescription;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FdCalculationResponseV2>, I>>(base?: I): FdCalculationResponseV2 {
    return FdCalculationResponseV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FdCalculationResponseV2>, I>>(object: I): FdCalculationResponseV2 {
    const message = createBaseFdCalculationResponseV2();
    message.maturityAmount = object.maturityAmount ?? 0;
    message.interestGained = object.interestGained ?? 0;
    message.maturityDescription = object.maturityDescription ?? "";
    return message;
  },
};

function createBaseNearMaturityInvestments(): NearMaturityInvestments {
  return { investmentItems: [] };
}

export const NearMaturityInvestments: MessageFns<NearMaturityInvestments> = {
  encode(message: NearMaturityInvestments, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.investmentItems) {
      InvestmentItem.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NearMaturityInvestments {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearMaturityInvestments();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.investmentItems.push(InvestmentItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NearMaturityInvestments {
    return {
      investmentItems: globalThis.Array.isArray(object?.investmentItems)
        ? object.investmentItems.map((e: any) => InvestmentItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: NearMaturityInvestments): unknown {
    const obj: any = {};
    if (message.investmentItems?.length) {
      obj.investmentItems = message.investmentItems.map((e) => InvestmentItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NearMaturityInvestments>, I>>(base?: I): NearMaturityInvestments {
    return NearMaturityInvestments.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NearMaturityInvestments>, I>>(object: I): NearMaturityInvestments {
    const message = createBaseNearMaturityInvestments();
    message.investmentItems = object.investmentItems?.map((e) => InvestmentItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseNearMaturityInvestments_FixedDeposit(): NearMaturityInvestments_FixedDeposit {
  return {
    bankId: "",
    bankName: "",
    bankLogoUrl: "",
    bankType: 0,
    tenure: "",
    interestRate: 0,
    redirectDeepLink: undefined,
    description: [],
  };
}

export const NearMaturityInvestments_FixedDeposit: MessageFns<NearMaturityInvestments_FixedDeposit> = {
  encode(message: NearMaturityInvestments_FixedDeposit, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    if (message.bankName !== "") {
      writer.uint32(18).string(message.bankName);
    }
    if (message.bankLogoUrl !== "") {
      writer.uint32(26).string(message.bankLogoUrl);
    }
    if (message.bankType !== 0) {
      writer.uint32(32).int32(message.bankType);
    }
    if (message.tenure !== "") {
      writer.uint32(42).string(message.tenure);
    }
    if (message.interestRate !== 0) {
      writer.uint32(49).double(message.interestRate);
    }
    if (message.redirectDeepLink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeepLink, writer.uint32(58).fork()).join();
    }
    for (const v of message.description) {
      writer.uint32(66).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NearMaturityInvestments_FixedDeposit {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearMaturityInvestments_FixedDeposit();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankLogoUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.bankType = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.interestRate = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.redirectDeepLink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.description.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NearMaturityInvestments_FixedDeposit {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogoUrl: isSet(object.bankLogoUrl) ? globalThis.String(object.bankLogoUrl) : "",
      bankType: isSet(object.bankType) ? bankTypeFromJSON(object.bankType) : 0,
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      interestRate: isSet(object.interestRate) ? globalThis.Number(object.interestRate) : 0,
      redirectDeepLink: isSet(object.redirectDeepLink) ? RedirectDeeplink.fromJSON(object.redirectDeepLink) : undefined,
      description: globalThis.Array.isArray(object?.description)
        ? object.description.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: NearMaturityInvestments_FixedDeposit): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogoUrl !== "") {
      obj.bankLogoUrl = message.bankLogoUrl;
    }
    if (message.bankType !== 0) {
      obj.bankType = bankTypeToJSON(message.bankType);
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.interestRate !== 0) {
      obj.interestRate = message.interestRate;
    }
    if (message.redirectDeepLink !== undefined) {
      obj.redirectDeepLink = RedirectDeeplink.toJSON(message.redirectDeepLink);
    }
    if (message.description?.length) {
      obj.description = message.description;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NearMaturityInvestments_FixedDeposit>, I>>(
    base?: I,
  ): NearMaturityInvestments_FixedDeposit {
    return NearMaturityInvestments_FixedDeposit.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NearMaturityInvestments_FixedDeposit>, I>>(
    object: I,
  ): NearMaturityInvestments_FixedDeposit {
    const message = createBaseNearMaturityInvestments_FixedDeposit();
    message.bankId = object.bankId ?? "";
    message.bankName = object.bankName ?? "";
    message.bankLogoUrl = object.bankLogoUrl ?? "";
    message.bankType = object.bankType ?? 0;
    message.tenure = object.tenure ?? "";
    message.interestRate = object.interestRate ?? 0;
    message.redirectDeepLink = (object.redirectDeepLink !== undefined && object.redirectDeepLink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeepLink)
      : undefined;
    message.description = object.description?.map((e) => e) || [];
    return message;
  },
};

function createBaseLastMaturedInvestment(): LastMaturedInvestment {
  return { investmentItem: undefined, pointers: [], suggestedFds: [] };
}

export const LastMaturedInvestment: MessageFns<LastMaturedInvestment> = {
  encode(message: LastMaturedInvestment, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.investmentItem !== undefined) {
      InvestmentItem.encode(message.investmentItem, writer.uint32(10).fork()).join();
    }
    for (const v of message.pointers) {
      DataKey.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.suggestedFds) {
      NearMaturityInvestments_FixedDeposit.encode(v!, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): LastMaturedInvestment {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLastMaturedInvestment();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.investmentItem = InvestmentItem.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.pointers.push(DataKey.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.suggestedFds.push(NearMaturityInvestments_FixedDeposit.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LastMaturedInvestment {
    return {
      investmentItem: isSet(object.investmentItem) ? InvestmentItem.fromJSON(object.investmentItem) : undefined,
      pointers: globalThis.Array.isArray(object?.pointers) ? object.pointers.map((e: any) => DataKey.fromJSON(e)) : [],
      suggestedFds: globalThis.Array.isArray(object?.suggestedFds)
        ? object.suggestedFds.map((e: any) => NearMaturityInvestments_FixedDeposit.fromJSON(e))
        : [],
    };
  },

  toJSON(message: LastMaturedInvestment): unknown {
    const obj: any = {};
    if (message.investmentItem !== undefined) {
      obj.investmentItem = InvestmentItem.toJSON(message.investmentItem);
    }
    if (message.pointers?.length) {
      obj.pointers = message.pointers.map((e) => DataKey.toJSON(e));
    }
    if (message.suggestedFds?.length) {
      obj.suggestedFds = message.suggestedFds.map((e) => NearMaturityInvestments_FixedDeposit.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LastMaturedInvestment>, I>>(base?: I): LastMaturedInvestment {
    return LastMaturedInvestment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LastMaturedInvestment>, I>>(object: I): LastMaturedInvestment {
    const message = createBaseLastMaturedInvestment();
    message.investmentItem = (object.investmentItem !== undefined && object.investmentItem !== null)
      ? InvestmentItem.fromPartial(object.investmentItem)
      : undefined;
    message.pointers = object.pointers?.map((e) => DataKey.fromPartial(e)) || [];
    message.suggestedFds = object.suggestedFds?.map((e) => NearMaturityInvestments_FixedDeposit.fromPartial(e)) || [];
    return message;
  },
};

function createBaseNearMaturityInvestmentDetail(): NearMaturityInvestmentDetail {
  return { investmentItem: undefined, recommendation: undefined, suggestedFds: [], term: 0 };
}

export const NearMaturityInvestmentDetail: MessageFns<NearMaturityInvestmentDetail> = {
  encode(message: NearMaturityInvestmentDetail, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.investmentItem !== undefined) {
      InvestmentItem.encode(message.investmentItem, writer.uint32(10).fork()).join();
    }
    if (message.recommendation !== undefined) {
      NearMaturityInvestmentDetail_Recommendation.encode(message.recommendation, writer.uint32(18).fork()).join();
    }
    for (const v of message.suggestedFds) {
      NearMaturityInvestments_FixedDeposit.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.term !== 0) {
      writer.uint32(32).int32(message.term);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NearMaturityInvestmentDetail {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearMaturityInvestmentDetail();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.investmentItem = InvestmentItem.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recommendation = NearMaturityInvestmentDetail_Recommendation.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.suggestedFds.push(NearMaturityInvestments_FixedDeposit.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.term = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NearMaturityInvestmentDetail {
    return {
      investmentItem: isSet(object.investmentItem) ? InvestmentItem.fromJSON(object.investmentItem) : undefined,
      recommendation: isSet(object.recommendation)
        ? NearMaturityInvestmentDetail_Recommendation.fromJSON(object.recommendation)
        : undefined,
      suggestedFds: globalThis.Array.isArray(object?.suggestedFds)
        ? object.suggestedFds.map((e: any) => NearMaturityInvestments_FixedDeposit.fromJSON(e))
        : [],
      term: isSet(object.term) ? nearMaturityInvestmentDetail_TermFromJSON(object.term) : 0,
    };
  },

  toJSON(message: NearMaturityInvestmentDetail): unknown {
    const obj: any = {};
    if (message.investmentItem !== undefined) {
      obj.investmentItem = InvestmentItem.toJSON(message.investmentItem);
    }
    if (message.recommendation !== undefined) {
      obj.recommendation = NearMaturityInvestmentDetail_Recommendation.toJSON(message.recommendation);
    }
    if (message.suggestedFds?.length) {
      obj.suggestedFds = message.suggestedFds.map((e) => NearMaturityInvestments_FixedDeposit.toJSON(e));
    }
    if (message.term !== 0) {
      obj.term = nearMaturityInvestmentDetail_TermToJSON(message.term);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NearMaturityInvestmentDetail>, I>>(base?: I): NearMaturityInvestmentDetail {
    return NearMaturityInvestmentDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NearMaturityInvestmentDetail>, I>>(object: I): NearMaturityInvestmentDetail {
    const message = createBaseNearMaturityInvestmentDetail();
    message.investmentItem = (object.investmentItem !== undefined && object.investmentItem !== null)
      ? InvestmentItem.fromPartial(object.investmentItem)
      : undefined;
    message.recommendation = (object.recommendation !== undefined && object.recommendation !== null)
      ? NearMaturityInvestmentDetail_Recommendation.fromPartial(object.recommendation)
      : undefined;
    message.suggestedFds = object.suggestedFds?.map((e) => NearMaturityInvestments_FixedDeposit.fromPartial(e)) || [];
    message.term = object.term ?? 0;
    return message;
  },
};

function createBaseNearMaturityInvestmentDetail_Recommendation(): NearMaturityInvestmentDetail_Recommendation {
  return { type: 0, amount: 0, interestRate: 0, tenure: "" };
}

export const NearMaturityInvestmentDetail_Recommendation: MessageFns<NearMaturityInvestmentDetail_Recommendation> = {
  encode(
    message: NearMaturityInvestmentDetail_Recommendation,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.type !== 0) {
      writer.uint32(8).int32(message.type);
    }
    if (message.amount !== 0) {
      writer.uint32(17).double(message.amount);
    }
    if (message.interestRate !== 0) {
      writer.uint32(25).double(message.interestRate);
    }
    if (message.tenure !== "") {
      writer.uint32(34).string(message.tenure);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NearMaturityInvestmentDetail_Recommendation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearMaturityInvestmentDetail_Recommendation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.interestRate = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NearMaturityInvestmentDetail_Recommendation {
    return {
      type: isSet(object.type) ? nearMaturityInvestmentDetail_Recommendation_TypeFromJSON(object.type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      interestRate: isSet(object.interestRate) ? globalThis.Number(object.interestRate) : 0,
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
    };
  },

  toJSON(message: NearMaturityInvestmentDetail_Recommendation): unknown {
    const obj: any = {};
    if (message.type !== 0) {
      obj.type = nearMaturityInvestmentDetail_Recommendation_TypeToJSON(message.type);
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.interestRate !== 0) {
      obj.interestRate = message.interestRate;
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NearMaturityInvestmentDetail_Recommendation>, I>>(
    base?: I,
  ): NearMaturityInvestmentDetail_Recommendation {
    return NearMaturityInvestmentDetail_Recommendation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NearMaturityInvestmentDetail_Recommendation>, I>>(
    object: I,
  ): NearMaturityInvestmentDetail_Recommendation {
    const message = createBaseNearMaturityInvestmentDetail_Recommendation();
    message.type = object.type ?? 0;
    message.amount = object.amount ?? 0;
    message.interestRate = object.interestRate ?? 0;
    message.tenure = object.tenure ?? "";
    return message;
  },
};

function createBaseInvestmentItem(): InvestmentItem {
  return {
    investmentAmount: 0,
    fdMaturityDate: "",
    interestPayoutType: "",
    currentGains: 0,
    bankName: "",
    bankLogoUrl: "",
    tenure: "",
    fdBookingDate: "",
    interestRate: 0,
    maturityAmount: 0,
    bookingId: "",
    investmentStatus: "",
    investedAt: "",
    maturityInstruction: "",
    redirectDeepLink: undefined,
  };
}

export const InvestmentItem: MessageFns<InvestmentItem> = {
  encode(message: InvestmentItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.investmentAmount !== 0) {
      writer.uint32(9).double(message.investmentAmount);
    }
    if (message.fdMaturityDate !== "") {
      writer.uint32(18).string(message.fdMaturityDate);
    }
    if (message.interestPayoutType !== "") {
      writer.uint32(26).string(message.interestPayoutType);
    }
    if (message.currentGains !== 0) {
      writer.uint32(33).double(message.currentGains);
    }
    if (message.bankName !== "") {
      writer.uint32(42).string(message.bankName);
    }
    if (message.bankLogoUrl !== "") {
      writer.uint32(50).string(message.bankLogoUrl);
    }
    if (message.tenure !== "") {
      writer.uint32(58).string(message.tenure);
    }
    if (message.fdBookingDate !== "") {
      writer.uint32(66).string(message.fdBookingDate);
    }
    if (message.interestRate !== 0) {
      writer.uint32(73).double(message.interestRate);
    }
    if (message.maturityAmount !== 0) {
      writer.uint32(81).double(message.maturityAmount);
    }
    if (message.bookingId !== "") {
      writer.uint32(90).string(message.bookingId);
    }
    if (message.investmentStatus !== "") {
      writer.uint32(98).string(message.investmentStatus);
    }
    if (message.investedAt !== "") {
      writer.uint32(106).string(message.investedAt);
    }
    if (message.maturityInstruction !== "") {
      writer.uint32(122).string(message.maturityInstruction);
    }
    if (message.redirectDeepLink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeepLink, writer.uint32(130).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fdMaturityDate = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.interestPayoutType = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.currentGains = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.bankLogoUrl = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.fdBookingDate = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 73) {
            break;
          }

          message.interestRate = reader.double();
          continue;
        }
        case 10: {
          if (tag !== 81) {
            break;
          }

          message.maturityAmount = reader.double();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.bookingId = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.investmentStatus = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.investedAt = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.maturityInstruction = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.redirectDeepLink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentItem {
    return {
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
      fdMaturityDate: isSet(object.fdMaturityDate) ? globalThis.String(object.fdMaturityDate) : "",
      interestPayoutType: isSet(object.interestPayoutType) ? globalThis.String(object.interestPayoutType) : "",
      currentGains: isSet(object.currentGains) ? globalThis.Number(object.currentGains) : 0,
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogoUrl: isSet(object.bankLogoUrl) ? globalThis.String(object.bankLogoUrl) : "",
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      fdBookingDate: isSet(object.fdBookingDate) ? globalThis.String(object.fdBookingDate) : "",
      interestRate: isSet(object.interestRate) ? globalThis.Number(object.interestRate) : 0,
      maturityAmount: isSet(object.maturityAmount) ? globalThis.Number(object.maturityAmount) : 0,
      bookingId: isSet(object.bookingId) ? globalThis.String(object.bookingId) : "",
      investmentStatus: isSet(object.investmentStatus) ? globalThis.String(object.investmentStatus) : "",
      investedAt: isSet(object.investedAt) ? globalThis.String(object.investedAt) : "",
      maturityInstruction: isSet(object.maturityInstruction) ? globalThis.String(object.maturityInstruction) : "",
      redirectDeepLink: isSet(object.redirectDeepLink) ? RedirectDeeplink.fromJSON(object.redirectDeepLink) : undefined,
    };
  },

  toJSON(message: InvestmentItem): unknown {
    const obj: any = {};
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    if (message.fdMaturityDate !== "") {
      obj.fdMaturityDate = message.fdMaturityDate;
    }
    if (message.interestPayoutType !== "") {
      obj.interestPayoutType = message.interestPayoutType;
    }
    if (message.currentGains !== 0) {
      obj.currentGains = message.currentGains;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogoUrl !== "") {
      obj.bankLogoUrl = message.bankLogoUrl;
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.fdBookingDate !== "") {
      obj.fdBookingDate = message.fdBookingDate;
    }
    if (message.interestRate !== 0) {
      obj.interestRate = message.interestRate;
    }
    if (message.maturityAmount !== 0) {
      obj.maturityAmount = message.maturityAmount;
    }
    if (message.bookingId !== "") {
      obj.bookingId = message.bookingId;
    }
    if (message.investmentStatus !== "") {
      obj.investmentStatus = message.investmentStatus;
    }
    if (message.investedAt !== "") {
      obj.investedAt = message.investedAt;
    }
    if (message.maturityInstruction !== "") {
      obj.maturityInstruction = message.maturityInstruction;
    }
    if (message.redirectDeepLink !== undefined) {
      obj.redirectDeepLink = RedirectDeeplink.toJSON(message.redirectDeepLink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentItem>, I>>(base?: I): InvestmentItem {
    return InvestmentItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentItem>, I>>(object: I): InvestmentItem {
    const message = createBaseInvestmentItem();
    message.investmentAmount = object.investmentAmount ?? 0;
    message.fdMaturityDate = object.fdMaturityDate ?? "";
    message.interestPayoutType = object.interestPayoutType ?? "";
    message.currentGains = object.currentGains ?? 0;
    message.bankName = object.bankName ?? "";
    message.bankLogoUrl = object.bankLogoUrl ?? "";
    message.tenure = object.tenure ?? "";
    message.fdBookingDate = object.fdBookingDate ?? "";
    message.interestRate = object.interestRate ?? 0;
    message.maturityAmount = object.maturityAmount ?? 0;
    message.bookingId = object.bookingId ?? "";
    message.investmentStatus = object.investmentStatus ?? "";
    message.investedAt = object.investedAt ?? "";
    message.maturityInstruction = object.maturityInstruction ?? "";
    message.redirectDeepLink = (object.redirectDeepLink !== undefined && object.redirectDeepLink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeepLink)
      : undefined;
    return message;
  },
};

function createBaseInvestmentResponse(): InvestmentResponse {
  return {
    bankId: "",
    pendingInvestedItems: [],
    bookedInvestedItems: [],
    totalInvestedAmount: undefined,
    totalInterestEarned: undefined,
  };
}

export const InvestmentResponse: MessageFns<InvestmentResponse> = {
  encode(message: InvestmentResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    for (const v of message.pendingInvestedItems) {
      InvestmentItem.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.bookedInvestedItems) {
      InvestmentItem.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.totalInvestedAmount !== undefined) {
      TotalInvestedAmount.encode(message.totalInvestedAmount, writer.uint32(34).fork()).join();
    }
    if (message.totalInterestEarned !== undefined) {
      TotalInterestEarned.encode(message.totalInterestEarned, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pendingInvestedItems.push(InvestmentItem.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bookedInvestedItems.push(InvestmentItem.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.totalInvestedAmount = TotalInvestedAmount.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.totalInterestEarned = TotalInterestEarned.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentResponse {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      pendingInvestedItems: globalThis.Array.isArray(object?.pendingInvestedItems)
        ? object.pendingInvestedItems.map((e: any) => InvestmentItem.fromJSON(e))
        : [],
      bookedInvestedItems: globalThis.Array.isArray(object?.bookedInvestedItems)
        ? object.bookedInvestedItems.map((e: any) => InvestmentItem.fromJSON(e))
        : [],
      totalInvestedAmount: isSet(object.totalInvestedAmount)
        ? TotalInvestedAmount.fromJSON(object.totalInvestedAmount)
        : undefined,
      totalInterestEarned: isSet(object.totalInterestEarned)
        ? TotalInterestEarned.fromJSON(object.totalInterestEarned)
        : undefined,
    };
  },

  toJSON(message: InvestmentResponse): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.pendingInvestedItems?.length) {
      obj.pendingInvestedItems = message.pendingInvestedItems.map((e) => InvestmentItem.toJSON(e));
    }
    if (message.bookedInvestedItems?.length) {
      obj.bookedInvestedItems = message.bookedInvestedItems.map((e) => InvestmentItem.toJSON(e));
    }
    if (message.totalInvestedAmount !== undefined) {
      obj.totalInvestedAmount = TotalInvestedAmount.toJSON(message.totalInvestedAmount);
    }
    if (message.totalInterestEarned !== undefined) {
      obj.totalInterestEarned = TotalInterestEarned.toJSON(message.totalInterestEarned);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentResponse>, I>>(base?: I): InvestmentResponse {
    return InvestmentResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentResponse>, I>>(object: I): InvestmentResponse {
    const message = createBaseInvestmentResponse();
    message.bankId = object.bankId ?? "";
    message.pendingInvestedItems = object.pendingInvestedItems?.map((e) => InvestmentItem.fromPartial(e)) || [];
    message.bookedInvestedItems = object.bookedInvestedItems?.map((e) => InvestmentItem.fromPartial(e)) || [];
    message.totalInvestedAmount = (object.totalInvestedAmount !== undefined && object.totalInvestedAmount !== null)
      ? TotalInvestedAmount.fromPartial(object.totalInvestedAmount)
      : undefined;
    message.totalInterestEarned = (object.totalInterestEarned !== undefined && object.totalInterestEarned !== null)
      ? TotalInterestEarned.fromPartial(object.totalInterestEarned)
      : undefined;
    return message;
  },
};

function createBaseFincareSheetRow(): FincareSheetRow {
  return { hmac: "", fincarePayload: undefined };
}

export const FincareSheetRow: MessageFns<FincareSheetRow> = {
  encode(message: FincareSheetRow, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hmac !== "") {
      writer.uint32(10).string(message.hmac);
    }
    if (message.fincarePayload !== undefined) {
      FincarePayload.encode(message.fincarePayload, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FincareSheetRow {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFincareSheetRow();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.hmac = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fincarePayload = FincarePayload.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FincareSheetRow {
    return {
      hmac: isSet(object.hmac) ? globalThis.String(object.hmac) : "",
      fincarePayload: isSet(object.fincarePayload) ? FincarePayload.fromJSON(object.fincarePayload) : undefined,
    };
  },

  toJSON(message: FincareSheetRow): unknown {
    const obj: any = {};
    if (message.hmac !== "") {
      obj.hmac = message.hmac;
    }
    if (message.fincarePayload !== undefined) {
      obj.fincarePayload = FincarePayload.toJSON(message.fincarePayload);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FincareSheetRow>, I>>(base?: I): FincareSheetRow {
    return FincareSheetRow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FincareSheetRow>, I>>(object: I): FincareSheetRow {
    const message = createBaseFincareSheetRow();
    message.hmac = object.hmac ?? "";
    message.fincarePayload = (object.fincarePayload !== undefined && object.fincarePayload !== null)
      ? FincarePayload.fromPartial(object.fincarePayload)
      : undefined;
    return message;
  },
};

function createBaseFincareSheetRequest(): FincareSheetRequest {
  return { fincareSheet: [] };
}

export const FincareSheetRequest: MessageFns<FincareSheetRequest> = {
  encode(message: FincareSheetRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.fincareSheet) {
      FincareSheetRow.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FincareSheetRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFincareSheetRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fincareSheet.push(FincareSheetRow.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FincareSheetRequest {
    return {
      fincareSheet: globalThis.Array.isArray(object?.fincareSheet)
        ? object.fincareSheet.map((e: any) => FincareSheetRow.fromJSON(e))
        : [],
    };
  },

  toJSON(message: FincareSheetRequest): unknown {
    const obj: any = {};
    if (message.fincareSheet?.length) {
      obj.fincareSheet = message.fincareSheet.map((e) => FincareSheetRow.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FincareSheetRequest>, I>>(base?: I): FincareSheetRequest {
    return FincareSheetRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FincareSheetRequest>, I>>(object: I): FincareSheetRequest {
    const message = createBaseFincareSheetRequest();
    message.fincareSheet = object.fincareSheet?.map((e) => FincareSheetRow.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUjjivanPayload(): UjjivanPayload {
  return {
    applicationId: "",
    applicationStatus: "",
    creationDate: "",
    lastUpdateDate: "",
    amount: undefined,
    userId: undefined,
    investmentId: undefined,
  };
}

export const UjjivanPayload: MessageFns<UjjivanPayload> = {
  encode(message: UjjivanPayload, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.applicationId !== "") {
      writer.uint32(10).string(message.applicationId);
    }
    if (message.applicationStatus !== "") {
      writer.uint32(18).string(message.applicationStatus);
    }
    if (message.creationDate !== "") {
      writer.uint32(26).string(message.creationDate);
    }
    if (message.lastUpdateDate !== "") {
      writer.uint32(34).string(message.lastUpdateDate);
    }
    if (message.amount !== undefined) {
      writer.uint32(41).double(message.amount);
    }
    if (message.userId !== undefined) {
      writer.uint32(50).string(message.userId);
    }
    if (message.investmentId !== undefined) {
      writer.uint32(58).string(message.investmentId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UjjivanPayload {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUjjivanPayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.applicationId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.applicationStatus = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.creationDate = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lastUpdateDate = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.investmentId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UjjivanPayload {
    return {
      applicationId: isSet(object.applicationId) ? globalThis.String(object.applicationId) : "",
      applicationStatus: isSet(object.applicationStatus) ? globalThis.String(object.applicationStatus) : "",
      creationDate: isSet(object.creationDate) ? globalThis.String(object.creationDate) : "",
      lastUpdateDate: isSet(object.lastUpdateDate) ? globalThis.String(object.lastUpdateDate) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : undefined,
      userId: isSet(object.userId) ? globalThis.String(object.userId) : undefined,
      investmentId: isSet(object.investmentId) ? globalThis.String(object.investmentId) : undefined,
    };
  },

  toJSON(message: UjjivanPayload): unknown {
    const obj: any = {};
    if (message.applicationId !== "") {
      obj.applicationId = message.applicationId;
    }
    if (message.applicationStatus !== "") {
      obj.applicationStatus = message.applicationStatus;
    }
    if (message.creationDate !== "") {
      obj.creationDate = message.creationDate;
    }
    if (message.lastUpdateDate !== "") {
      obj.lastUpdateDate = message.lastUpdateDate;
    }
    if (message.amount !== undefined) {
      obj.amount = message.amount;
    }
    if (message.userId !== undefined) {
      obj.userId = message.userId;
    }
    if (message.investmentId !== undefined) {
      obj.investmentId = message.investmentId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UjjivanPayload>, I>>(base?: I): UjjivanPayload {
    return UjjivanPayload.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UjjivanPayload>, I>>(object: I): UjjivanPayload {
    const message = createBaseUjjivanPayload();
    message.applicationId = object.applicationId ?? "";
    message.applicationStatus = object.applicationStatus ?? "";
    message.creationDate = object.creationDate ?? "";
    message.lastUpdateDate = object.lastUpdateDate ?? "";
    message.amount = object.amount ?? undefined;
    message.userId = object.userId ?? undefined;
    message.investmentId = object.investmentId ?? undefined;
    return message;
  },
};

function createBaseUjjivanSheetRow(): UjjivanSheetRow {
  return { hmac: "", payload: undefined };
}

export const UjjivanSheetRow: MessageFns<UjjivanSheetRow> = {
  encode(message: UjjivanSheetRow, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hmac !== "") {
      writer.uint32(10).string(message.hmac);
    }
    if (message.payload !== undefined) {
      UjjivanPayload.encode(message.payload, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UjjivanSheetRow {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUjjivanSheetRow();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.hmac = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.payload = UjjivanPayload.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UjjivanSheetRow {
    return {
      hmac: isSet(object.hmac) ? globalThis.String(object.hmac) : "",
      payload: isSet(object.payload) ? UjjivanPayload.fromJSON(object.payload) : undefined,
    };
  },

  toJSON(message: UjjivanSheetRow): unknown {
    const obj: any = {};
    if (message.hmac !== "") {
      obj.hmac = message.hmac;
    }
    if (message.payload !== undefined) {
      obj.payload = UjjivanPayload.toJSON(message.payload);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UjjivanSheetRow>, I>>(base?: I): UjjivanSheetRow {
    return UjjivanSheetRow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UjjivanSheetRow>, I>>(object: I): UjjivanSheetRow {
    const message = createBaseUjjivanSheetRow();
    message.hmac = object.hmac ?? "";
    message.payload = (object.payload !== undefined && object.payload !== null)
      ? UjjivanPayload.fromPartial(object.payload)
      : undefined;
    return message;
  },
};

function createBaseUjjivanSheetRequest(): UjjivanSheetRequest {
  return { sheet: [] };
}

export const UjjivanSheetRequest: MessageFns<UjjivanSheetRequest> = {
  encode(message: UjjivanSheetRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.sheet) {
      UjjivanSheetRow.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UjjivanSheetRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUjjivanSheetRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.sheet.push(UjjivanSheetRow.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UjjivanSheetRequest {
    return {
      sheet: globalThis.Array.isArray(object?.sheet) ? object.sheet.map((e: any) => UjjivanSheetRow.fromJSON(e)) : [],
    };
  },

  toJSON(message: UjjivanSheetRequest): unknown {
    const obj: any = {};
    if (message.sheet?.length) {
      obj.sheet = message.sheet.map((e) => UjjivanSheetRow.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UjjivanSheetRequest>, I>>(base?: I): UjjivanSheetRequest {
    return UjjivanSheetRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UjjivanSheetRequest>, I>>(object: I): UjjivanSheetRequest {
    const message = createBaseUjjivanSheetRequest();
    message.sheet = object.sheet?.map((e) => UjjivanSheetRow.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSheetResponse(): SheetResponse {
  return { successes: [], failures: [], skipped: [] };
}

export const SheetResponse: MessageFns<SheetResponse> = {
  encode(message: SheetResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.successes) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.failures) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.skipped) {
      writer.uint32(26).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SheetResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSheetResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.successes.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.failures.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.skipped.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SheetResponse {
    return {
      successes: globalThis.Array.isArray(object?.successes)
        ? object.successes.map((e: any) => globalThis.String(e))
        : [],
      failures: globalThis.Array.isArray(object?.failures) ? object.failures.map((e: any) => globalThis.String(e)) : [],
      skipped: globalThis.Array.isArray(object?.skipped) ? object.skipped.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: SheetResponse): unknown {
    const obj: any = {};
    if (message.successes?.length) {
      obj.successes = message.successes;
    }
    if (message.failures?.length) {
      obj.failures = message.failures;
    }
    if (message.skipped?.length) {
      obj.skipped = message.skipped;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SheetResponse>, I>>(base?: I): SheetResponse {
    return SheetResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SheetResponse>, I>>(object: I): SheetResponse {
    const message = createBaseSheetResponse();
    message.successes = object.successes?.map((e) => e) || [];
    message.failures = object.failures?.map((e) => e) || [];
    message.skipped = object.skipped?.map((e) => e) || [];
    return message;
  },
};

function createBaseUserFixedDepositSummaryRequest(): UserFixedDepositSummaryRequest {
  return { userId: "" };
}

export const UserFixedDepositSummaryRequest: MessageFns<UserFixedDepositSummaryRequest> = {
  encode(message: UserFixedDepositSummaryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserFixedDepositSummaryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserFixedDepositSummaryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserFixedDepositSummaryRequest {
    return { userId: isSet(object.userId) ? globalThis.String(object.userId) : "" };
  },

  toJSON(message: UserFixedDepositSummaryRequest): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserFixedDepositSummaryRequest>, I>>(base?: I): UserFixedDepositSummaryRequest {
    return UserFixedDepositSummaryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserFixedDepositSummaryRequest>, I>>(
    object: I,
  ): UserFixedDepositSummaryRequest {
    const message = createBaseUserFixedDepositSummaryRequest();
    message.userId = object.userId ?? "";
    return message;
  },
};

function createBaseUserFixedDepositSummaryResponse(): UserFixedDepositSummaryResponse {
  return {
    totalInvestedAmount: 0,
    totalInterestEarned: 0,
    currentAmount: 0,
    emergencyFundSummary: undefined,
    activeTermDepositCount: 0,
    inProgressTotal: 0,
  };
}

export const UserFixedDepositSummaryResponse: MessageFns<UserFixedDepositSummaryResponse> = {
  encode(message: UserFixedDepositSummaryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalInvestedAmount !== 0) {
      writer.uint32(9).double(message.totalInvestedAmount);
    }
    if (message.totalInterestEarned !== 0) {
      writer.uint32(17).double(message.totalInterestEarned);
    }
    if (message.currentAmount !== 0) {
      writer.uint32(25).double(message.currentAmount);
    }
    if (message.emergencyFundSummary !== undefined) {
      EmergencyFundSummary.encode(message.emergencyFundSummary, writer.uint32(34).fork()).join();
    }
    if (message.activeTermDepositCount !== 0) {
      writer.uint32(40).int32(message.activeTermDepositCount);
    }
    if (message.inProgressTotal !== 0) {
      writer.uint32(49).double(message.inProgressTotal);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserFixedDepositSummaryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserFixedDepositSummaryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.totalInvestedAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.totalInterestEarned = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.currentAmount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.emergencyFundSummary = EmergencyFundSummary.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.activeTermDepositCount = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.inProgressTotal = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserFixedDepositSummaryResponse {
    return {
      totalInvestedAmount: isSet(object.totalInvestedAmount) ? globalThis.Number(object.totalInvestedAmount) : 0,
      totalInterestEarned: isSet(object.totalInterestEarned) ? globalThis.Number(object.totalInterestEarned) : 0,
      currentAmount: isSet(object.currentAmount) ? globalThis.Number(object.currentAmount) : 0,
      emergencyFundSummary: isSet(object.emergencyFundSummary)
        ? EmergencyFundSummary.fromJSON(object.emergencyFundSummary)
        : undefined,
      activeTermDepositCount: isSet(object.activeTermDepositCount)
        ? globalThis.Number(object.activeTermDepositCount)
        : 0,
      inProgressTotal: isSet(object.inProgressTotal) ? globalThis.Number(object.inProgressTotal) : 0,
    };
  },

  toJSON(message: UserFixedDepositSummaryResponse): unknown {
    const obj: any = {};
    if (message.totalInvestedAmount !== 0) {
      obj.totalInvestedAmount = message.totalInvestedAmount;
    }
    if (message.totalInterestEarned !== 0) {
      obj.totalInterestEarned = message.totalInterestEarned;
    }
    if (message.currentAmount !== 0) {
      obj.currentAmount = message.currentAmount;
    }
    if (message.emergencyFundSummary !== undefined) {
      obj.emergencyFundSummary = EmergencyFundSummary.toJSON(message.emergencyFundSummary);
    }
    if (message.activeTermDepositCount !== 0) {
      obj.activeTermDepositCount = Math.round(message.activeTermDepositCount);
    }
    if (message.inProgressTotal !== 0) {
      obj.inProgressTotal = message.inProgressTotal;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserFixedDepositSummaryResponse>, I>>(base?: I): UserFixedDepositSummaryResponse {
    return UserFixedDepositSummaryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserFixedDepositSummaryResponse>, I>>(
    object: I,
  ): UserFixedDepositSummaryResponse {
    const message = createBaseUserFixedDepositSummaryResponse();
    message.totalInvestedAmount = object.totalInvestedAmount ?? 0;
    message.totalInterestEarned = object.totalInterestEarned ?? 0;
    message.currentAmount = object.currentAmount ?? 0;
    message.emergencyFundSummary = (object.emergencyFundSummary !== undefined && object.emergencyFundSummary !== null)
      ? EmergencyFundSummary.fromPartial(object.emergencyFundSummary)
      : undefined;
    message.activeTermDepositCount = object.activeTermDepositCount ?? 0;
    message.inProgressTotal = object.inProgressTotal ?? 0;
    return message;
  },
};

function createBaseWithdrawalCalculatorRequest(): WithdrawalCalculatorRequest {
  return { fdId: "" };
}

export const WithdrawalCalculatorRequest: MessageFns<WithdrawalCalculatorRequest> = {
  encode(message: WithdrawalCalculatorRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdId !== "") {
      writer.uint32(10).string(message.fdId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalCalculatorRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalCalculatorRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fdId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalCalculatorRequest {
    return { fdId: isSet(object.fdId) ? globalThis.String(object.fdId) : "" };
  },

  toJSON(message: WithdrawalCalculatorRequest): unknown {
    const obj: any = {};
    if (message.fdId !== "") {
      obj.fdId = message.fdId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalCalculatorRequest>, I>>(base?: I): WithdrawalCalculatorRequest {
    return WithdrawalCalculatorRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalCalculatorRequest>, I>>(object: I): WithdrawalCalculatorRequest {
    const message = createBaseWithdrawalCalculatorRequest();
    message.fdId = object.fdId ?? "";
    return message;
  },
};

function createBaseWithdrawalCalculatorResponse(): WithdrawalCalculatorResponse {
  return {
    investedAmount: 0,
    withdrawalAmount: 0,
    interestEarned: 0,
    penaltyInterest: 0,
    fdAccountNumber: "",
    applicableInterestRate: 0,
    maturityAmount: 0,
    maturityInterest: 0,
    fdActiveDays: "",
    bookingDate: "",
    fdBookedTenure: "",
    bankLogo: "",
    withdrawalCreditBankDetails: undefined,
    redirectDeepLink: undefined,
    withdrawalMetadata: {},
  };
}

export const WithdrawalCalculatorResponse: MessageFns<WithdrawalCalculatorResponse> = {
  encode(message: WithdrawalCalculatorResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.investedAmount !== 0) {
      writer.uint32(9).double(message.investedAmount);
    }
    if (message.withdrawalAmount !== 0) {
      writer.uint32(17).double(message.withdrawalAmount);
    }
    if (message.interestEarned !== 0) {
      writer.uint32(25).double(message.interestEarned);
    }
    if (message.penaltyInterest !== 0) {
      writer.uint32(33).double(message.penaltyInterest);
    }
    if (message.fdAccountNumber !== "") {
      writer.uint32(42).string(message.fdAccountNumber);
    }
    if (message.applicableInterestRate !== 0) {
      writer.uint32(49).double(message.applicableInterestRate);
    }
    if (message.maturityAmount !== 0) {
      writer.uint32(57).double(message.maturityAmount);
    }
    if (message.maturityInterest !== 0) {
      writer.uint32(65).double(message.maturityInterest);
    }
    if (message.fdActiveDays !== "") {
      writer.uint32(74).string(message.fdActiveDays);
    }
    if (message.bookingDate !== "") {
      writer.uint32(82).string(message.bookingDate);
    }
    if (message.fdBookedTenure !== "") {
      writer.uint32(90).string(message.fdBookedTenure);
    }
    if (message.bankLogo !== "") {
      writer.uint32(98).string(message.bankLogo);
    }
    if (message.withdrawalCreditBankDetails !== undefined) {
      WithdrawalCreditBankDetails.encode(message.withdrawalCreditBankDetails, writer.uint32(106).fork()).join();
    }
    if (message.redirectDeepLink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeepLink, writer.uint32(114).fork()).join();
    }
    Object.entries(message.withdrawalMetadata).forEach(([key, value]) => {
      WithdrawalCalculatorResponse_WithdrawalMetadataEntry.encode({ key: key as any, value }, writer.uint32(122).fork())
        .join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalCalculatorResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalCalculatorResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.investedAmount = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.withdrawalAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.interestEarned = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.penaltyInterest = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.fdAccountNumber = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.applicableInterestRate = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.maturityAmount = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.maturityInterest = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.fdActiveDays = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.bookingDate = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.fdBookedTenure = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.bankLogo = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.withdrawalCreditBankDetails = WithdrawalCreditBankDetails.decode(reader, reader.uint32());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.redirectDeepLink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          const entry15 = WithdrawalCalculatorResponse_WithdrawalMetadataEntry.decode(reader, reader.uint32());
          if (entry15.value !== undefined) {
            message.withdrawalMetadata[entry15.key] = entry15.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalCalculatorResponse {
    return {
      investedAmount: isSet(object.investedAmount) ? globalThis.Number(object.investedAmount) : 0,
      withdrawalAmount: isSet(object.withdrawalAmount) ? globalThis.Number(object.withdrawalAmount) : 0,
      interestEarned: isSet(object.interestEarned) ? globalThis.Number(object.interestEarned) : 0,
      penaltyInterest: isSet(object.penaltyInterest) ? globalThis.Number(object.penaltyInterest) : 0,
      fdAccountNumber: isSet(object.fdAccountNumber) ? globalThis.String(object.fdAccountNumber) : "",
      applicableInterestRate: isSet(object.applicableInterestRate)
        ? globalThis.Number(object.applicableInterestRate)
        : 0,
      maturityAmount: isSet(object.maturityAmount) ? globalThis.Number(object.maturityAmount) : 0,
      maturityInterest: isSet(object.maturityInterest) ? globalThis.Number(object.maturityInterest) : 0,
      fdActiveDays: isSet(object.fdActiveDays) ? globalThis.String(object.fdActiveDays) : "",
      bookingDate: isSet(object.bookingDate) ? globalThis.String(object.bookingDate) : "",
      fdBookedTenure: isSet(object.fdBookedTenure) ? globalThis.String(object.fdBookedTenure) : "",
      bankLogo: isSet(object.bankLogo) ? globalThis.String(object.bankLogo) : "",
      withdrawalCreditBankDetails: isSet(object.withdrawalCreditBankDetails)
        ? WithdrawalCreditBankDetails.fromJSON(object.withdrawalCreditBankDetails)
        : undefined,
      redirectDeepLink: isSet(object.redirectDeepLink) ? RedirectDeeplink.fromJSON(object.redirectDeepLink) : undefined,
      withdrawalMetadata: isObject(object.withdrawalMetadata)
        ? Object.entries(object.withdrawalMetadata).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: WithdrawalCalculatorResponse): unknown {
    const obj: any = {};
    if (message.investedAmount !== 0) {
      obj.investedAmount = message.investedAmount;
    }
    if (message.withdrawalAmount !== 0) {
      obj.withdrawalAmount = message.withdrawalAmount;
    }
    if (message.interestEarned !== 0) {
      obj.interestEarned = message.interestEarned;
    }
    if (message.penaltyInterest !== 0) {
      obj.penaltyInterest = message.penaltyInterest;
    }
    if (message.fdAccountNumber !== "") {
      obj.fdAccountNumber = message.fdAccountNumber;
    }
    if (message.applicableInterestRate !== 0) {
      obj.applicableInterestRate = message.applicableInterestRate;
    }
    if (message.maturityAmount !== 0) {
      obj.maturityAmount = message.maturityAmount;
    }
    if (message.maturityInterest !== 0) {
      obj.maturityInterest = message.maturityInterest;
    }
    if (message.fdActiveDays !== "") {
      obj.fdActiveDays = message.fdActiveDays;
    }
    if (message.bookingDate !== "") {
      obj.bookingDate = message.bookingDate;
    }
    if (message.fdBookedTenure !== "") {
      obj.fdBookedTenure = message.fdBookedTenure;
    }
    if (message.bankLogo !== "") {
      obj.bankLogo = message.bankLogo;
    }
    if (message.withdrawalCreditBankDetails !== undefined) {
      obj.withdrawalCreditBankDetails = WithdrawalCreditBankDetails.toJSON(message.withdrawalCreditBankDetails);
    }
    if (message.redirectDeepLink !== undefined) {
      obj.redirectDeepLink = RedirectDeeplink.toJSON(message.redirectDeepLink);
    }
    if (message.withdrawalMetadata) {
      const entries = Object.entries(message.withdrawalMetadata);
      if (entries.length > 0) {
        obj.withdrawalMetadata = {};
        entries.forEach(([k, v]) => {
          obj.withdrawalMetadata[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalCalculatorResponse>, I>>(base?: I): WithdrawalCalculatorResponse {
    return WithdrawalCalculatorResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalCalculatorResponse>, I>>(object: I): WithdrawalCalculatorResponse {
    const message = createBaseWithdrawalCalculatorResponse();
    message.investedAmount = object.investedAmount ?? 0;
    message.withdrawalAmount = object.withdrawalAmount ?? 0;
    message.interestEarned = object.interestEarned ?? 0;
    message.penaltyInterest = object.penaltyInterest ?? 0;
    message.fdAccountNumber = object.fdAccountNumber ?? "";
    message.applicableInterestRate = object.applicableInterestRate ?? 0;
    message.maturityAmount = object.maturityAmount ?? 0;
    message.maturityInterest = object.maturityInterest ?? 0;
    message.fdActiveDays = object.fdActiveDays ?? "";
    message.bookingDate = object.bookingDate ?? "";
    message.fdBookedTenure = object.fdBookedTenure ?? "";
    message.bankLogo = object.bankLogo ?? "";
    message.withdrawalCreditBankDetails =
      (object.withdrawalCreditBankDetails !== undefined && object.withdrawalCreditBankDetails !== null)
        ? WithdrawalCreditBankDetails.fromPartial(object.withdrawalCreditBankDetails)
        : undefined;
    message.redirectDeepLink = (object.redirectDeepLink !== undefined && object.redirectDeepLink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeepLink)
      : undefined;
    message.withdrawalMetadata = Object.entries(object.withdrawalMetadata ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseWithdrawalCalculatorResponse_WithdrawalMetadataEntry(): WithdrawalCalculatorResponse_WithdrawalMetadataEntry {
  return { key: "", value: "" };
}

export const WithdrawalCalculatorResponse_WithdrawalMetadataEntry: MessageFns<
  WithdrawalCalculatorResponse_WithdrawalMetadataEntry
> = {
  encode(
    message: WithdrawalCalculatorResponse_WithdrawalMetadataEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalCalculatorResponse_WithdrawalMetadataEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalCalculatorResponse_WithdrawalMetadataEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalCalculatorResponse_WithdrawalMetadataEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: WithdrawalCalculatorResponse_WithdrawalMetadataEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalCalculatorResponse_WithdrawalMetadataEntry>, I>>(
    base?: I,
  ): WithdrawalCalculatorResponse_WithdrawalMetadataEntry {
    return WithdrawalCalculatorResponse_WithdrawalMetadataEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalCalculatorResponse_WithdrawalMetadataEntry>, I>>(
    object: I,
  ): WithdrawalCalculatorResponse_WithdrawalMetadataEntry {
    const message = createBaseWithdrawalCalculatorResponse_WithdrawalMetadataEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseWithdrawalCreditBankDetails(): WithdrawalCreditBankDetails {
  return { bankName: "", accountNumber: "", paymentMode: "", creditExpectedDate: "" };
}

export const WithdrawalCreditBankDetails: MessageFns<WithdrawalCreditBankDetails> = {
  encode(message: WithdrawalCreditBankDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankName !== "") {
      writer.uint32(10).string(message.bankName);
    }
    if (message.accountNumber !== "") {
      writer.uint32(18).string(message.accountNumber);
    }
    if (message.paymentMode !== "") {
      writer.uint32(26).string(message.paymentMode);
    }
    if (message.creditExpectedDate !== "") {
      writer.uint32(34).string(message.creditExpectedDate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalCreditBankDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalCreditBankDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.paymentMode = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.creditExpectedDate = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalCreditBankDetails {
    return {
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      accountNumber: isSet(object.accountNumber) ? globalThis.String(object.accountNumber) : "",
      paymentMode: isSet(object.paymentMode) ? globalThis.String(object.paymentMode) : "",
      creditExpectedDate: isSet(object.creditExpectedDate) ? globalThis.String(object.creditExpectedDate) : "",
    };
  },

  toJSON(message: WithdrawalCreditBankDetails): unknown {
    const obj: any = {};
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.accountNumber !== "") {
      obj.accountNumber = message.accountNumber;
    }
    if (message.paymentMode !== "") {
      obj.paymentMode = message.paymentMode;
    }
    if (message.creditExpectedDate !== "") {
      obj.creditExpectedDate = message.creditExpectedDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalCreditBankDetails>, I>>(base?: I): WithdrawalCreditBankDetails {
    return WithdrawalCreditBankDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalCreditBankDetails>, I>>(object: I): WithdrawalCreditBankDetails {
    const message = createBaseWithdrawalCreditBankDetails();
    message.bankName = object.bankName ?? "";
    message.accountNumber = object.accountNumber ?? "";
    message.paymentMode = object.paymentMode ?? "";
    message.creditExpectedDate = object.creditExpectedDate ?? "";
    return message;
  },
};

function createBaseWithdrawalRequest(): WithdrawalRequest {
  return { fdId: "" };
}

export const WithdrawalRequest: MessageFns<WithdrawalRequest> = {
  encode(message: WithdrawalRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdId !== "") {
      writer.uint32(10).string(message.fdId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fdId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalRequest {
    return { fdId: isSet(object.fdId) ? globalThis.String(object.fdId) : "" };
  },

  toJSON(message: WithdrawalRequest): unknown {
    const obj: any = {};
    if (message.fdId !== "") {
      obj.fdId = message.fdId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalRequest>, I>>(base?: I): WithdrawalRequest {
    return WithdrawalRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalRequest>, I>>(object: I): WithdrawalRequest {
    const message = createBaseWithdrawalRequest();
    message.fdId = object.fdId ?? "";
    return message;
  },
};

function createBaseWithdrawalResponse(): WithdrawalResponse {
  return { message: "" };
}

export const WithdrawalResponse: MessageFns<WithdrawalResponse> = {
  encode(message: WithdrawalResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.message !== "") {
      writer.uint32(10).string(message.message);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.message = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalResponse {
    return { message: isSet(object.message) ? globalThis.String(object.message) : "" };
  },

  toJSON(message: WithdrawalResponse): unknown {
    const obj: any = {};
    if (message.message !== "") {
      obj.message = message.message;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalResponse>, I>>(base?: I): WithdrawalResponse {
    return WithdrawalResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalResponse>, I>>(object: I): WithdrawalResponse {
    const message = createBaseWithdrawalResponse();
    message.message = object.message ?? "";
    return message;
  },
};

function createBaseCompareFdResponse(): CompareFdResponse {
  return {
    bankId: "",
    bankName: "",
    bankLogo: "",
    shortTerm: undefined,
    midTerm: undefined,
    longTerm: undefined,
    payout: [],
    bankAccountRequired: false,
    insured: false,
    withdrawal: "",
  };
}

export const CompareFdResponse: MessageFns<CompareFdResponse> = {
  encode(message: CompareFdResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    if (message.bankName !== "") {
      writer.uint32(18).string(message.bankName);
    }
    if (message.bankLogo !== "") {
      writer.uint32(26).string(message.bankLogo);
    }
    if (message.shortTerm !== undefined) {
      CompareFdResponse_RateAndTenure.encode(message.shortTerm, writer.uint32(34).fork()).join();
    }
    if (message.midTerm !== undefined) {
      CompareFdResponse_RateAndTenure.encode(message.midTerm, writer.uint32(42).fork()).join();
    }
    if (message.longTerm !== undefined) {
      CompareFdResponse_RateAndTenure.encode(message.longTerm, writer.uint32(50).fork()).join();
    }
    for (const v of message.payout) {
      writer.uint32(58).string(v!);
    }
    if (message.bankAccountRequired !== false) {
      writer.uint32(64).bool(message.bankAccountRequired);
    }
    if (message.insured !== false) {
      writer.uint32(72).bool(message.insured);
    }
    if (message.withdrawal !== "") {
      writer.uint32(82).string(message.withdrawal);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompareFdResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompareFdResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankLogo = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.shortTerm = CompareFdResponse_RateAndTenure.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.midTerm = CompareFdResponse_RateAndTenure.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.longTerm = CompareFdResponse_RateAndTenure.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.payout.push(reader.string());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.bankAccountRequired = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.insured = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.withdrawal = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CompareFdResponse {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogo: isSet(object.bankLogo) ? globalThis.String(object.bankLogo) : "",
      shortTerm: isSet(object.shortTerm) ? CompareFdResponse_RateAndTenure.fromJSON(object.shortTerm) : undefined,
      midTerm: isSet(object.midTerm) ? CompareFdResponse_RateAndTenure.fromJSON(object.midTerm) : undefined,
      longTerm: isSet(object.longTerm) ? CompareFdResponse_RateAndTenure.fromJSON(object.longTerm) : undefined,
      payout: globalThis.Array.isArray(object?.payout) ? object.payout.map((e: any) => globalThis.String(e)) : [],
      bankAccountRequired: isSet(object.bankAccountRequired) ? globalThis.Boolean(object.bankAccountRequired) : false,
      insured: isSet(object.insured) ? globalThis.Boolean(object.insured) : false,
      withdrawal: isSet(object.withdrawal) ? globalThis.String(object.withdrawal) : "",
    };
  },

  toJSON(message: CompareFdResponse): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogo !== "") {
      obj.bankLogo = message.bankLogo;
    }
    if (message.shortTerm !== undefined) {
      obj.shortTerm = CompareFdResponse_RateAndTenure.toJSON(message.shortTerm);
    }
    if (message.midTerm !== undefined) {
      obj.midTerm = CompareFdResponse_RateAndTenure.toJSON(message.midTerm);
    }
    if (message.longTerm !== undefined) {
      obj.longTerm = CompareFdResponse_RateAndTenure.toJSON(message.longTerm);
    }
    if (message.payout?.length) {
      obj.payout = message.payout;
    }
    if (message.bankAccountRequired !== false) {
      obj.bankAccountRequired = message.bankAccountRequired;
    }
    if (message.insured !== false) {
      obj.insured = message.insured;
    }
    if (message.withdrawal !== "") {
      obj.withdrawal = message.withdrawal;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompareFdResponse>, I>>(base?: I): CompareFdResponse {
    return CompareFdResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompareFdResponse>, I>>(object: I): CompareFdResponse {
    const message = createBaseCompareFdResponse();
    message.bankId = object.bankId ?? "";
    message.bankName = object.bankName ?? "";
    message.bankLogo = object.bankLogo ?? "";
    message.shortTerm = (object.shortTerm !== undefined && object.shortTerm !== null)
      ? CompareFdResponse_RateAndTenure.fromPartial(object.shortTerm)
      : undefined;
    message.midTerm = (object.midTerm !== undefined && object.midTerm !== null)
      ? CompareFdResponse_RateAndTenure.fromPartial(object.midTerm)
      : undefined;
    message.longTerm = (object.longTerm !== undefined && object.longTerm !== null)
      ? CompareFdResponse_RateAndTenure.fromPartial(object.longTerm)
      : undefined;
    message.payout = object.payout?.map((e) => e) || [];
    message.bankAccountRequired = object.bankAccountRequired ?? false;
    message.insured = object.insured ?? false;
    message.withdrawal = object.withdrawal ?? "";
    return message;
  },
};

function createBaseCompareFdResponse_RateAndTenure(): CompareFdResponse_RateAndTenure {
  return { rate: 0, tenureInDays: 0, compoundingFrequency: 0 };
}

export const CompareFdResponse_RateAndTenure: MessageFns<CompareFdResponse_RateAndTenure> = {
  encode(message: CompareFdResponse_RateAndTenure, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rate !== 0) {
      writer.uint32(9).double(message.rate);
    }
    if (message.tenureInDays !== 0) {
      writer.uint32(16).int32(message.tenureInDays);
    }
    if (message.compoundingFrequency !== 0) {
      writer.uint32(24).int32(message.compoundingFrequency);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompareFdResponse_RateAndTenure {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompareFdResponse_RateAndTenure();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.tenureInDays = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.compoundingFrequency = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CompareFdResponse_RateAndTenure {
    return {
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      tenureInDays: isSet(object.tenureInDays) ? globalThis.Number(object.tenureInDays) : 0,
      compoundingFrequency: isSet(object.compoundingFrequency)
        ? compoundingFrequencyTypeFromJSON(object.compoundingFrequency)
        : 0,
    };
  },

  toJSON(message: CompareFdResponse_RateAndTenure): unknown {
    const obj: any = {};
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.tenureInDays !== 0) {
      obj.tenureInDays = Math.round(message.tenureInDays);
    }
    if (message.compoundingFrequency !== 0) {
      obj.compoundingFrequency = compoundingFrequencyTypeToJSON(message.compoundingFrequency);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompareFdResponse_RateAndTenure>, I>>(base?: I): CompareFdResponse_RateAndTenure {
    return CompareFdResponse_RateAndTenure.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompareFdResponse_RateAndTenure>, I>>(
    object: I,
  ): CompareFdResponse_RateAndTenure {
    const message = createBaseCompareFdResponse_RateAndTenure();
    message.rate = object.rate ?? 0;
    message.tenureInDays = object.tenureInDays ?? 0;
    message.compoundingFrequency = object.compoundingFrequency ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
