// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Widgets.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { UiScreenType, uiScreenTypeFromJSON, uiScreenTypeToJSON } from "./BusinessCommon";

export const protobufPackage = "com.stablemoney.api.identity";

export enum UiWidgetType {
  UNKNOWN_UI_WIDGET_TYPE = 0,
  FD_HORIZONTAL_CARDS_WITH_BUTTON = 1,
  INVESTABLE_BANKS_WIDGET = 2,
  SINGLE_BANNER_WIDGET = 3,
  SAFE_AND_SECURE_WIDGET = 4,
  STATIC_HORIZONTAL_CARDS_WIDGET = 5,
  SMALL_FD_CARD_WITH_BUTTON = 6,
  UNRECOGNIZED = -1,
}

export function uiWidgetTypeFromJSON(object: any): UiWidgetType {
  switch (object) {
    case 0:
    case "UNKNOWN_UI_WIDGET_TYPE":
      return UiWidgetType.UNKNOWN_UI_WIDGET_TYPE;
    case 1:
    case "FD_HORIZONTAL_CARDS_WITH_BUTTON":
      return UiWidgetType.FD_HORIZONTAL_CARDS_WITH_BUTTON;
    case 2:
    case "INVESTABLE_BANKS_WIDGET":
      return UiWidgetType.INVESTABLE_BANKS_WIDGET;
    case 3:
    case "SINGLE_BANNER_WIDGET":
      return UiWidgetType.SINGLE_BANNER_WIDGET;
    case 4:
    case "SAFE_AND_SECURE_WIDGET":
      return UiWidgetType.SAFE_AND_SECURE_WIDGET;
    case 5:
    case "STATIC_HORIZONTAL_CARDS_WIDGET":
      return UiWidgetType.STATIC_HORIZONTAL_CARDS_WIDGET;
    case 6:
    case "SMALL_FD_CARD_WITH_BUTTON":
      return UiWidgetType.SMALL_FD_CARD_WITH_BUTTON;
    case -1:
    case "UNRECOGNIZED":
    default:
      return UiWidgetType.UNRECOGNIZED;
  }
}

export function uiWidgetTypeToJSON(object: UiWidgetType): string {
  switch (object) {
    case UiWidgetType.UNKNOWN_UI_WIDGET_TYPE:
      return "UNKNOWN_UI_WIDGET_TYPE";
    case UiWidgetType.FD_HORIZONTAL_CARDS_WITH_BUTTON:
      return "FD_HORIZONTAL_CARDS_WITH_BUTTON";
    case UiWidgetType.INVESTABLE_BANKS_WIDGET:
      return "INVESTABLE_BANKS_WIDGET";
    case UiWidgetType.SINGLE_BANNER_WIDGET:
      return "SINGLE_BANNER_WIDGET";
    case UiWidgetType.SAFE_AND_SECURE_WIDGET:
      return "SAFE_AND_SECURE_WIDGET";
    case UiWidgetType.STATIC_HORIZONTAL_CARDS_WIDGET:
      return "STATIC_HORIZONTAL_CARDS_WIDGET";
    case UiWidgetType.SMALL_FD_CARD_WITH_BUTTON:
      return "SMALL_FD_CARD_WITH_BUTTON";
    case UiWidgetType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum BannerType {
  UNKNOWN_BANNER_TYPE = 0,
  IMAGE_BANNER = 1,
  LOTTIE_BANNER = 2,
  SVG_BANNER = 3,
  WEBP_BANNER = 4,
  UNRECOGNIZED = -1,
}

export function bannerTypeFromJSON(object: any): BannerType {
  switch (object) {
    case 0:
    case "UNKNOWN_BANNER_TYPE":
      return BannerType.UNKNOWN_BANNER_TYPE;
    case 1:
    case "IMAGE_BANNER":
      return BannerType.IMAGE_BANNER;
    case 2:
    case "LOTTIE_BANNER":
      return BannerType.LOTTIE_BANNER;
    case 3:
    case "SVG_BANNER":
      return BannerType.SVG_BANNER;
    case 4:
    case "WEBP_BANNER":
      return BannerType.WEBP_BANNER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BannerType.UNRECOGNIZED;
  }
}

export function bannerTypeToJSON(object: BannerType): string {
  switch (object) {
    case BannerType.UNKNOWN_BANNER_TYPE:
      return "UNKNOWN_BANNER_TYPE";
    case BannerType.IMAGE_BANNER:
      return "IMAGE_BANNER";
    case BannerType.LOTTIE_BANNER:
      return "LOTTIE_BANNER";
    case BannerType.SVG_BANNER:
      return "SVG_BANNER";
    case BannerType.WEBP_BANNER:
      return "WEBP_BANNER";
    case BannerType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum BannerLayoutType {
  UNKNOWN_BANNER_LAYOUT_TYPE = 0,
  CARD_BANNER = 1,
  FLAT_BANNER = 2,
  UNRECOGNIZED = -1,
}

export function bannerLayoutTypeFromJSON(object: any): BannerLayoutType {
  switch (object) {
    case 0:
    case "UNKNOWN_BANNER_LAYOUT_TYPE":
      return BannerLayoutType.UNKNOWN_BANNER_LAYOUT_TYPE;
    case 1:
    case "CARD_BANNER":
      return BannerLayoutType.CARD_BANNER;
    case 2:
    case "FLAT_BANNER":
      return BannerLayoutType.FLAT_BANNER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BannerLayoutType.UNRECOGNIZED;
  }
}

export function bannerLayoutTypeToJSON(object: BannerLayoutType): string {
  switch (object) {
    case BannerLayoutType.UNKNOWN_BANNER_LAYOUT_TYPE:
      return "UNKNOWN_BANNER_LAYOUT_TYPE";
    case BannerLayoutType.CARD_BANNER:
      return "CARD_BANNER";
    case BannerLayoutType.FLAT_BANNER:
      return "FLAT_BANNER";
    case BannerLayoutType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface UiWidgetResponse {
  uiWidget: UiWidget[];
}

export interface UiWidget {
  uiWidgetType: UiWidgetType;
  uiWidgetData: UiWidgetData | undefined;
}

export interface UiWidgetData {
  fdHorizontalCardsWithButton: FDHorizontalCardsWithButton | undefined;
  investableBanksWidget: InvestableBanksWidget | undefined;
  singleBannerWidget: SingleBannerWidget | undefined;
  safeAndSecureWidget: SafeAndSecureWidget | undefined;
  staticHorizontalCardsWidget: StaticHorizontalCardsWidget | undefined;
  smallFdCardWithButton: SmallFDCardWithButton | undefined;
}

export interface FDHorizontalCardsWithButton {
  title: string;
  description: string;
  buttonText: string;
  showRank: boolean;
  numberOfItemsToLoad: number;
  collectionId: string;
}

export interface InvestableBanksWidget {
  title: string;
  description: string;
  buttonText: string;
  numberOfItemsToLoad: number;
  collectionId: string;
  investableBanksWidgetHighlightTags: InvestableBanksWidgetHighlightTags[];
  footerDescription: string;
  footerDescriptionIcon: string;
  bankItemButtonText: string;
}

export interface InvestableBanksWidgetHighlightTags {
  label: string;
  icon: string;
}

export interface SingleBannerWidget {
  bannerType: BannerType;
  bannerLayoutType: BannerLayoutType;
  title: string;
  description: string;
  buttonText: string;
  screenToOpen: UiScreenType;
}

export interface SafeAndSecureWidget {
  screenToOpen: UiScreenType;
}

export interface StaticHorizontalCardsWidget {
  title: string;
  description: string;
  staticHorizontalCardsWidgetItem: StaticHorizontalCardsWidgetItem[];
}

export interface StaticHorizontalCardsWidgetItem {
  title: string;
  description: string;
  iconUrl: string;
  screenToOpen: UiScreenType;
}

export interface SmallFDCardWithButton {
  title: string;
  description: string;
  buttonText: string;
  showRank: boolean;
  collectionId: string;
}

function createBaseUiWidgetResponse(): UiWidgetResponse {
  return { uiWidget: [] };
}

export const UiWidgetResponse: MessageFns<UiWidgetResponse> = {
  encode(message: UiWidgetResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.uiWidget) {
      UiWidget.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UiWidgetResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUiWidgetResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.uiWidget.push(UiWidget.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UiWidgetResponse {
    return {
      uiWidget: globalThis.Array.isArray(object?.uiWidget) ? object.uiWidget.map((e: any) => UiWidget.fromJSON(e)) : [],
    };
  },

  toJSON(message: UiWidgetResponse): unknown {
    const obj: any = {};
    if (message.uiWidget?.length) {
      obj.uiWidget = message.uiWidget.map((e) => UiWidget.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UiWidgetResponse>, I>>(base?: I): UiWidgetResponse {
    return UiWidgetResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UiWidgetResponse>, I>>(object: I): UiWidgetResponse {
    const message = createBaseUiWidgetResponse();
    message.uiWidget = object.uiWidget?.map((e) => UiWidget.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUiWidget(): UiWidget {
  return { uiWidgetType: 0, uiWidgetData: undefined };
}

export const UiWidget: MessageFns<UiWidget> = {
  encode(message: UiWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uiWidgetType !== 0) {
      writer.uint32(8).int32(message.uiWidgetType);
    }
    if (message.uiWidgetData !== undefined) {
      UiWidgetData.encode(message.uiWidgetData, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UiWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUiWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.uiWidgetType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.uiWidgetData = UiWidgetData.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UiWidget {
    return {
      uiWidgetType: isSet(object.uiWidgetType) ? uiWidgetTypeFromJSON(object.uiWidgetType) : 0,
      uiWidgetData: isSet(object.uiWidgetData) ? UiWidgetData.fromJSON(object.uiWidgetData) : undefined,
    };
  },

  toJSON(message: UiWidget): unknown {
    const obj: any = {};
    if (message.uiWidgetType !== 0) {
      obj.uiWidgetType = uiWidgetTypeToJSON(message.uiWidgetType);
    }
    if (message.uiWidgetData !== undefined) {
      obj.uiWidgetData = UiWidgetData.toJSON(message.uiWidgetData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UiWidget>, I>>(base?: I): UiWidget {
    return UiWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UiWidget>, I>>(object: I): UiWidget {
    const message = createBaseUiWidget();
    message.uiWidgetType = object.uiWidgetType ?? 0;
    message.uiWidgetData = (object.uiWidgetData !== undefined && object.uiWidgetData !== null)
      ? UiWidgetData.fromPartial(object.uiWidgetData)
      : undefined;
    return message;
  },
};

function createBaseUiWidgetData(): UiWidgetData {
  return {
    fdHorizontalCardsWithButton: undefined,
    investableBanksWidget: undefined,
    singleBannerWidget: undefined,
    safeAndSecureWidget: undefined,
    staticHorizontalCardsWidget: undefined,
    smallFdCardWithButton: undefined,
  };
}

export const UiWidgetData: MessageFns<UiWidgetData> = {
  encode(message: UiWidgetData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdHorizontalCardsWithButton !== undefined) {
      FDHorizontalCardsWithButton.encode(message.fdHorizontalCardsWithButton, writer.uint32(10).fork()).join();
    }
    if (message.investableBanksWidget !== undefined) {
      InvestableBanksWidget.encode(message.investableBanksWidget, writer.uint32(18).fork()).join();
    }
    if (message.singleBannerWidget !== undefined) {
      SingleBannerWidget.encode(message.singleBannerWidget, writer.uint32(26).fork()).join();
    }
    if (message.safeAndSecureWidget !== undefined) {
      SafeAndSecureWidget.encode(message.safeAndSecureWidget, writer.uint32(34).fork()).join();
    }
    if (message.staticHorizontalCardsWidget !== undefined) {
      StaticHorizontalCardsWidget.encode(message.staticHorizontalCardsWidget, writer.uint32(42).fork()).join();
    }
    if (message.smallFdCardWithButton !== undefined) {
      SmallFDCardWithButton.encode(message.smallFdCardWithButton, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UiWidgetData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUiWidgetData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fdHorizontalCardsWithButton = FDHorizontalCardsWithButton.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.investableBanksWidget = InvestableBanksWidget.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.singleBannerWidget = SingleBannerWidget.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.safeAndSecureWidget = SafeAndSecureWidget.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.staticHorizontalCardsWidget = StaticHorizontalCardsWidget.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.smallFdCardWithButton = SmallFDCardWithButton.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UiWidgetData {
    return {
      fdHorizontalCardsWithButton: isSet(object.fdHorizontalCardsWithButton)
        ? FDHorizontalCardsWithButton.fromJSON(object.fdHorizontalCardsWithButton)
        : undefined,
      investableBanksWidget: isSet(object.investableBanksWidget)
        ? InvestableBanksWidget.fromJSON(object.investableBanksWidget)
        : undefined,
      singleBannerWidget: isSet(object.singleBannerWidget)
        ? SingleBannerWidget.fromJSON(object.singleBannerWidget)
        : undefined,
      safeAndSecureWidget: isSet(object.safeAndSecureWidget)
        ? SafeAndSecureWidget.fromJSON(object.safeAndSecureWidget)
        : undefined,
      staticHorizontalCardsWidget: isSet(object.staticHorizontalCardsWidget)
        ? StaticHorizontalCardsWidget.fromJSON(object.staticHorizontalCardsWidget)
        : undefined,
      smallFdCardWithButton: isSet(object.smallFdCardWithButton)
        ? SmallFDCardWithButton.fromJSON(object.smallFdCardWithButton)
        : undefined,
    };
  },

  toJSON(message: UiWidgetData): unknown {
    const obj: any = {};
    if (message.fdHorizontalCardsWithButton !== undefined) {
      obj.fdHorizontalCardsWithButton = FDHorizontalCardsWithButton.toJSON(message.fdHorizontalCardsWithButton);
    }
    if (message.investableBanksWidget !== undefined) {
      obj.investableBanksWidget = InvestableBanksWidget.toJSON(message.investableBanksWidget);
    }
    if (message.singleBannerWidget !== undefined) {
      obj.singleBannerWidget = SingleBannerWidget.toJSON(message.singleBannerWidget);
    }
    if (message.safeAndSecureWidget !== undefined) {
      obj.safeAndSecureWidget = SafeAndSecureWidget.toJSON(message.safeAndSecureWidget);
    }
    if (message.staticHorizontalCardsWidget !== undefined) {
      obj.staticHorizontalCardsWidget = StaticHorizontalCardsWidget.toJSON(message.staticHorizontalCardsWidget);
    }
    if (message.smallFdCardWithButton !== undefined) {
      obj.smallFdCardWithButton = SmallFDCardWithButton.toJSON(message.smallFdCardWithButton);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UiWidgetData>, I>>(base?: I): UiWidgetData {
    return UiWidgetData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UiWidgetData>, I>>(object: I): UiWidgetData {
    const message = createBaseUiWidgetData();
    message.fdHorizontalCardsWithButton =
      (object.fdHorizontalCardsWithButton !== undefined && object.fdHorizontalCardsWithButton !== null)
        ? FDHorizontalCardsWithButton.fromPartial(object.fdHorizontalCardsWithButton)
        : undefined;
    message.investableBanksWidget =
      (object.investableBanksWidget !== undefined && object.investableBanksWidget !== null)
        ? InvestableBanksWidget.fromPartial(object.investableBanksWidget)
        : undefined;
    message.singleBannerWidget = (object.singleBannerWidget !== undefined && object.singleBannerWidget !== null)
      ? SingleBannerWidget.fromPartial(object.singleBannerWidget)
      : undefined;
    message.safeAndSecureWidget = (object.safeAndSecureWidget !== undefined && object.safeAndSecureWidget !== null)
      ? SafeAndSecureWidget.fromPartial(object.safeAndSecureWidget)
      : undefined;
    message.staticHorizontalCardsWidget =
      (object.staticHorizontalCardsWidget !== undefined && object.staticHorizontalCardsWidget !== null)
        ? StaticHorizontalCardsWidget.fromPartial(object.staticHorizontalCardsWidget)
        : undefined;
    message.smallFdCardWithButton =
      (object.smallFdCardWithButton !== undefined && object.smallFdCardWithButton !== null)
        ? SmallFDCardWithButton.fromPartial(object.smallFdCardWithButton)
        : undefined;
    return message;
  },
};

function createBaseFDHorizontalCardsWithButton(): FDHorizontalCardsWithButton {
  return { title: "", description: "", buttonText: "", showRank: false, numberOfItemsToLoad: 0, collectionId: "" };
}

export const FDHorizontalCardsWithButton: MessageFns<FDHorizontalCardsWithButton> = {
  encode(message: FDHorizontalCardsWithButton, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.buttonText !== "") {
      writer.uint32(26).string(message.buttonText);
    }
    if (message.showRank !== false) {
      writer.uint32(32).bool(message.showRank);
    }
    if (message.numberOfItemsToLoad !== 0) {
      writer.uint32(40).int32(message.numberOfItemsToLoad);
    }
    if (message.collectionId !== "") {
      writer.uint32(50).string(message.collectionId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FDHorizontalCardsWithButton {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFDHorizontalCardsWithButton();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.buttonText = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.showRank = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.numberOfItemsToLoad = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FDHorizontalCardsWithButton {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      buttonText: isSet(object.buttonText) ? globalThis.String(object.buttonText) : "",
      showRank: isSet(object.showRank) ? globalThis.Boolean(object.showRank) : false,
      numberOfItemsToLoad: isSet(object.numberOfItemsToLoad) ? globalThis.Number(object.numberOfItemsToLoad) : 0,
      collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "",
    };
  },

  toJSON(message: FDHorizontalCardsWithButton): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.buttonText !== "") {
      obj.buttonText = message.buttonText;
    }
    if (message.showRank !== false) {
      obj.showRank = message.showRank;
    }
    if (message.numberOfItemsToLoad !== 0) {
      obj.numberOfItemsToLoad = Math.round(message.numberOfItemsToLoad);
    }
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FDHorizontalCardsWithButton>, I>>(base?: I): FDHorizontalCardsWithButton {
    return FDHorizontalCardsWithButton.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FDHorizontalCardsWithButton>, I>>(object: I): FDHorizontalCardsWithButton {
    const message = createBaseFDHorizontalCardsWithButton();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.buttonText = object.buttonText ?? "";
    message.showRank = object.showRank ?? false;
    message.numberOfItemsToLoad = object.numberOfItemsToLoad ?? 0;
    message.collectionId = object.collectionId ?? "";
    return message;
  },
};

function createBaseInvestableBanksWidget(): InvestableBanksWidget {
  return {
    title: "",
    description: "",
    buttonText: "",
    numberOfItemsToLoad: 0,
    collectionId: "",
    investableBanksWidgetHighlightTags: [],
    footerDescription: "",
    footerDescriptionIcon: "",
    bankItemButtonText: "",
  };
}

export const InvestableBanksWidget: MessageFns<InvestableBanksWidget> = {
  encode(message: InvestableBanksWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.buttonText !== "") {
      writer.uint32(26).string(message.buttonText);
    }
    if (message.numberOfItemsToLoad !== 0) {
      writer.uint32(32).int32(message.numberOfItemsToLoad);
    }
    if (message.collectionId !== "") {
      writer.uint32(42).string(message.collectionId);
    }
    for (const v of message.investableBanksWidgetHighlightTags) {
      InvestableBanksWidgetHighlightTags.encode(v!, writer.uint32(50).fork()).join();
    }
    if (message.footerDescription !== "") {
      writer.uint32(58).string(message.footerDescription);
    }
    if (message.footerDescriptionIcon !== "") {
      writer.uint32(66).string(message.footerDescriptionIcon);
    }
    if (message.bankItemButtonText !== "") {
      writer.uint32(74).string(message.bankItemButtonText);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestableBanksWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestableBanksWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.buttonText = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.numberOfItemsToLoad = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.investableBanksWidgetHighlightTags.push(
            InvestableBanksWidgetHighlightTags.decode(reader, reader.uint32()),
          );
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.footerDescription = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.footerDescriptionIcon = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.bankItemButtonText = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestableBanksWidget {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      buttonText: isSet(object.buttonText) ? globalThis.String(object.buttonText) : "",
      numberOfItemsToLoad: isSet(object.numberOfItemsToLoad) ? globalThis.Number(object.numberOfItemsToLoad) : 0,
      collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "",
      investableBanksWidgetHighlightTags: globalThis.Array.isArray(object?.investableBanksWidgetHighlightTags)
        ? object.investableBanksWidgetHighlightTags.map((e: any) => InvestableBanksWidgetHighlightTags.fromJSON(e))
        : [],
      footerDescription: isSet(object.footerDescription) ? globalThis.String(object.footerDescription) : "",
      footerDescriptionIcon: isSet(object.footerDescriptionIcon) ? globalThis.String(object.footerDescriptionIcon) : "",
      bankItemButtonText: isSet(object.bankItemButtonText) ? globalThis.String(object.bankItemButtonText) : "",
    };
  },

  toJSON(message: InvestableBanksWidget): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.buttonText !== "") {
      obj.buttonText = message.buttonText;
    }
    if (message.numberOfItemsToLoad !== 0) {
      obj.numberOfItemsToLoad = Math.round(message.numberOfItemsToLoad);
    }
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    if (message.investableBanksWidgetHighlightTags?.length) {
      obj.investableBanksWidgetHighlightTags = message.investableBanksWidgetHighlightTags.map((e) =>
        InvestableBanksWidgetHighlightTags.toJSON(e)
      );
    }
    if (message.footerDescription !== "") {
      obj.footerDescription = message.footerDescription;
    }
    if (message.footerDescriptionIcon !== "") {
      obj.footerDescriptionIcon = message.footerDescriptionIcon;
    }
    if (message.bankItemButtonText !== "") {
      obj.bankItemButtonText = message.bankItemButtonText;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestableBanksWidget>, I>>(base?: I): InvestableBanksWidget {
    return InvestableBanksWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestableBanksWidget>, I>>(object: I): InvestableBanksWidget {
    const message = createBaseInvestableBanksWidget();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.buttonText = object.buttonText ?? "";
    message.numberOfItemsToLoad = object.numberOfItemsToLoad ?? 0;
    message.collectionId = object.collectionId ?? "";
    message.investableBanksWidgetHighlightTags =
      object.investableBanksWidgetHighlightTags?.map((e) => InvestableBanksWidgetHighlightTags.fromPartial(e)) || [];
    message.footerDescription = object.footerDescription ?? "";
    message.footerDescriptionIcon = object.footerDescriptionIcon ?? "";
    message.bankItemButtonText = object.bankItemButtonText ?? "";
    return message;
  },
};

function createBaseInvestableBanksWidgetHighlightTags(): InvestableBanksWidgetHighlightTags {
  return { label: "", icon: "" };
}

export const InvestableBanksWidgetHighlightTags: MessageFns<InvestableBanksWidgetHighlightTags> = {
  encode(message: InvestableBanksWidgetHighlightTags, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.label !== "") {
      writer.uint32(10).string(message.label);
    }
    if (message.icon !== "") {
      writer.uint32(18).string(message.icon);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestableBanksWidgetHighlightTags {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestableBanksWidgetHighlightTags();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.label = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.icon = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestableBanksWidgetHighlightTags {
    return {
      label: isSet(object.label) ? globalThis.String(object.label) : "",
      icon: isSet(object.icon) ? globalThis.String(object.icon) : "",
    };
  },

  toJSON(message: InvestableBanksWidgetHighlightTags): unknown {
    const obj: any = {};
    if (message.label !== "") {
      obj.label = message.label;
    }
    if (message.icon !== "") {
      obj.icon = message.icon;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestableBanksWidgetHighlightTags>, I>>(
    base?: I,
  ): InvestableBanksWidgetHighlightTags {
    return InvestableBanksWidgetHighlightTags.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestableBanksWidgetHighlightTags>, I>>(
    object: I,
  ): InvestableBanksWidgetHighlightTags {
    const message = createBaseInvestableBanksWidgetHighlightTags();
    message.label = object.label ?? "";
    message.icon = object.icon ?? "";
    return message;
  },
};

function createBaseSingleBannerWidget(): SingleBannerWidget {
  return { bannerType: 0, bannerLayoutType: 0, title: "", description: "", buttonText: "", screenToOpen: 0 };
}

export const SingleBannerWidget: MessageFns<SingleBannerWidget> = {
  encode(message: SingleBannerWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bannerType !== 0) {
      writer.uint32(8).int32(message.bannerType);
    }
    if (message.bannerLayoutType !== 0) {
      writer.uint32(16).int32(message.bannerLayoutType);
    }
    if (message.title !== "") {
      writer.uint32(26).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.buttonText !== "") {
      writer.uint32(42).string(message.buttonText);
    }
    if (message.screenToOpen !== 0) {
      writer.uint32(48).int32(message.screenToOpen);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SingleBannerWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSingleBannerWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.bannerType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.bannerLayoutType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.buttonText = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.screenToOpen = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SingleBannerWidget {
    return {
      bannerType: isSet(object.bannerType) ? bannerTypeFromJSON(object.bannerType) : 0,
      bannerLayoutType: isSet(object.bannerLayoutType) ? bannerLayoutTypeFromJSON(object.bannerLayoutType) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      buttonText: isSet(object.buttonText) ? globalThis.String(object.buttonText) : "",
      screenToOpen: isSet(object.screenToOpen) ? uiScreenTypeFromJSON(object.screenToOpen) : 0,
    };
  },

  toJSON(message: SingleBannerWidget): unknown {
    const obj: any = {};
    if (message.bannerType !== 0) {
      obj.bannerType = bannerTypeToJSON(message.bannerType);
    }
    if (message.bannerLayoutType !== 0) {
      obj.bannerLayoutType = bannerLayoutTypeToJSON(message.bannerLayoutType);
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.buttonText !== "") {
      obj.buttonText = message.buttonText;
    }
    if (message.screenToOpen !== 0) {
      obj.screenToOpen = uiScreenTypeToJSON(message.screenToOpen);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SingleBannerWidget>, I>>(base?: I): SingleBannerWidget {
    return SingleBannerWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SingleBannerWidget>, I>>(object: I): SingleBannerWidget {
    const message = createBaseSingleBannerWidget();
    message.bannerType = object.bannerType ?? 0;
    message.bannerLayoutType = object.bannerLayoutType ?? 0;
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.buttonText = object.buttonText ?? "";
    message.screenToOpen = object.screenToOpen ?? 0;
    return message;
  },
};

function createBaseSafeAndSecureWidget(): SafeAndSecureWidget {
  return { screenToOpen: 0 };
}

export const SafeAndSecureWidget: MessageFns<SafeAndSecureWidget> = {
  encode(message: SafeAndSecureWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.screenToOpen !== 0) {
      writer.uint32(8).int32(message.screenToOpen);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SafeAndSecureWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSafeAndSecureWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.screenToOpen = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SafeAndSecureWidget {
    return { screenToOpen: isSet(object.screenToOpen) ? uiScreenTypeFromJSON(object.screenToOpen) : 0 };
  },

  toJSON(message: SafeAndSecureWidget): unknown {
    const obj: any = {};
    if (message.screenToOpen !== 0) {
      obj.screenToOpen = uiScreenTypeToJSON(message.screenToOpen);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SafeAndSecureWidget>, I>>(base?: I): SafeAndSecureWidget {
    return SafeAndSecureWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SafeAndSecureWidget>, I>>(object: I): SafeAndSecureWidget {
    const message = createBaseSafeAndSecureWidget();
    message.screenToOpen = object.screenToOpen ?? 0;
    return message;
  },
};

function createBaseStaticHorizontalCardsWidget(): StaticHorizontalCardsWidget {
  return { title: "", description: "", staticHorizontalCardsWidgetItem: [] };
}

export const StaticHorizontalCardsWidget: MessageFns<StaticHorizontalCardsWidget> = {
  encode(message: StaticHorizontalCardsWidget, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    for (const v of message.staticHorizontalCardsWidgetItem) {
      StaticHorizontalCardsWidgetItem.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StaticHorizontalCardsWidget {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStaticHorizontalCardsWidget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.staticHorizontalCardsWidgetItem.push(StaticHorizontalCardsWidgetItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StaticHorizontalCardsWidget {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      staticHorizontalCardsWidgetItem: globalThis.Array.isArray(object?.staticHorizontalCardsWidgetItem)
        ? object.staticHorizontalCardsWidgetItem.map((e: any) => StaticHorizontalCardsWidgetItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: StaticHorizontalCardsWidget): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.staticHorizontalCardsWidgetItem?.length) {
      obj.staticHorizontalCardsWidgetItem = message.staticHorizontalCardsWidgetItem.map((e) =>
        StaticHorizontalCardsWidgetItem.toJSON(e)
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StaticHorizontalCardsWidget>, I>>(base?: I): StaticHorizontalCardsWidget {
    return StaticHorizontalCardsWidget.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StaticHorizontalCardsWidget>, I>>(object: I): StaticHorizontalCardsWidget {
    const message = createBaseStaticHorizontalCardsWidget();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.staticHorizontalCardsWidgetItem =
      object.staticHorizontalCardsWidgetItem?.map((e) => StaticHorizontalCardsWidgetItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseStaticHorizontalCardsWidgetItem(): StaticHorizontalCardsWidgetItem {
  return { title: "", description: "", iconUrl: "", screenToOpen: 0 };
}

export const StaticHorizontalCardsWidgetItem: MessageFns<StaticHorizontalCardsWidgetItem> = {
  encode(message: StaticHorizontalCardsWidgetItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.iconUrl !== "") {
      writer.uint32(26).string(message.iconUrl);
    }
    if (message.screenToOpen !== 0) {
      writer.uint32(32).int32(message.screenToOpen);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StaticHorizontalCardsWidgetItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStaticHorizontalCardsWidgetItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.screenToOpen = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StaticHorizontalCardsWidgetItem {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      screenToOpen: isSet(object.screenToOpen) ? uiScreenTypeFromJSON(object.screenToOpen) : 0,
    };
  },

  toJSON(message: StaticHorizontalCardsWidgetItem): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.screenToOpen !== 0) {
      obj.screenToOpen = uiScreenTypeToJSON(message.screenToOpen);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StaticHorizontalCardsWidgetItem>, I>>(base?: I): StaticHorizontalCardsWidgetItem {
    return StaticHorizontalCardsWidgetItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StaticHorizontalCardsWidgetItem>, I>>(
    object: I,
  ): StaticHorizontalCardsWidgetItem {
    const message = createBaseStaticHorizontalCardsWidgetItem();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.screenToOpen = object.screenToOpen ?? 0;
    return message;
  },
};

function createBaseSmallFDCardWithButton(): SmallFDCardWithButton {
  return { title: "", description: "", buttonText: "", showRank: false, collectionId: "" };
}

export const SmallFDCardWithButton: MessageFns<SmallFDCardWithButton> = {
  encode(message: SmallFDCardWithButton, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.buttonText !== "") {
      writer.uint32(26).string(message.buttonText);
    }
    if (message.showRank !== false) {
      writer.uint32(32).bool(message.showRank);
    }
    if (message.collectionId !== "") {
      writer.uint32(42).string(message.collectionId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SmallFDCardWithButton {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSmallFDCardWithButton();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.buttonText = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.showRank = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SmallFDCardWithButton {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      buttonText: isSet(object.buttonText) ? globalThis.String(object.buttonText) : "",
      showRank: isSet(object.showRank) ? globalThis.Boolean(object.showRank) : false,
      collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "",
    };
  },

  toJSON(message: SmallFDCardWithButton): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.buttonText !== "") {
      obj.buttonText = message.buttonText;
    }
    if (message.showRank !== false) {
      obj.showRank = message.showRank;
    }
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SmallFDCardWithButton>, I>>(base?: I): SmallFDCardWithButton {
    return SmallFDCardWithButton.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SmallFDCardWithButton>, I>>(object: I): SmallFDCardWithButton {
    const message = createBaseSmallFDCardWithButton();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.buttonText = object.buttonText ?? "";
    message.showRank = object.showRank ?? false;
    message.collectionId = object.collectionId ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
