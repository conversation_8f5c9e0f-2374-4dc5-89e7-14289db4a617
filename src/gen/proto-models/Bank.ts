// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Bank.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RedirectDeeplink } from "./BusinessCommon";

export const protobufPackage = "com.stablemoney.api.bank";

export interface Bank {
  id: string;
  name: string;
  logoUrl: string;
  redirectDeeplink: RedirectDeeplink | undefined;
}

export interface AgentAvailabilityDetails {
  isAvailable: boolean;
  availabilityTimingsDescription: string;
  isHoliday: boolean;
  holidayDescription: string;
}

export interface Tenure {
  rawTenure: string;
  tenure: string;
  tenureInDays: number;
}

export interface TagConfig {
  name: string;
  iconUrl: string;
  color: string;
  bgColor: string;
  type: string;
}

function createBaseBank(): Bank {
  return { id: "", name: "", logoUrl: "", redirectDeeplink: undefined };
}

export const Bank: MessageFns<Bank> = {
  encode(message: Bank, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.logoUrl !== "") {
      writer.uint32(26).string(message.logoUrl);
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Bank {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBank();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Bank {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
    };
  },

  toJSON(message: Bank): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Bank>, I>>(base?: I): Bank {
    return Bank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Bank>, I>>(object: I): Bank {
    const message = createBaseBank();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.logoUrl = object.logoUrl ?? "";
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    return message;
  },
};

function createBaseAgentAvailabilityDetails(): AgentAvailabilityDetails {
  return { isAvailable: false, availabilityTimingsDescription: "", isHoliday: false, holidayDescription: "" };
}

export const AgentAvailabilityDetails: MessageFns<AgentAvailabilityDetails> = {
  encode(message: AgentAvailabilityDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isAvailable !== false) {
      writer.uint32(8).bool(message.isAvailable);
    }
    if (message.availabilityTimingsDescription !== "") {
      writer.uint32(18).string(message.availabilityTimingsDescription);
    }
    if (message.isHoliday !== false) {
      writer.uint32(24).bool(message.isHoliday);
    }
    if (message.holidayDescription !== "") {
      writer.uint32(34).string(message.holidayDescription);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AgentAvailabilityDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAgentAvailabilityDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isAvailable = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.availabilityTimingsDescription = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isHoliday = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.holidayDescription = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AgentAvailabilityDetails {
    return {
      isAvailable: isSet(object.isAvailable) ? globalThis.Boolean(object.isAvailable) : false,
      availabilityTimingsDescription: isSet(object.availabilityTimingsDescription)
        ? globalThis.String(object.availabilityTimingsDescription)
        : "",
      isHoliday: isSet(object.isHoliday) ? globalThis.Boolean(object.isHoliday) : false,
      holidayDescription: isSet(object.holidayDescription) ? globalThis.String(object.holidayDescription) : "",
    };
  },

  toJSON(message: AgentAvailabilityDetails): unknown {
    const obj: any = {};
    if (message.isAvailable !== false) {
      obj.isAvailable = message.isAvailable;
    }
    if (message.availabilityTimingsDescription !== "") {
      obj.availabilityTimingsDescription = message.availabilityTimingsDescription;
    }
    if (message.isHoliday !== false) {
      obj.isHoliday = message.isHoliday;
    }
    if (message.holidayDescription !== "") {
      obj.holidayDescription = message.holidayDescription;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AgentAvailabilityDetails>, I>>(base?: I): AgentAvailabilityDetails {
    return AgentAvailabilityDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AgentAvailabilityDetails>, I>>(object: I): AgentAvailabilityDetails {
    const message = createBaseAgentAvailabilityDetails();
    message.isAvailable = object.isAvailable ?? false;
    message.availabilityTimingsDescription = object.availabilityTimingsDescription ?? "";
    message.isHoliday = object.isHoliday ?? false;
    message.holidayDescription = object.holidayDescription ?? "";
    return message;
  },
};

function createBaseTenure(): Tenure {
  return { rawTenure: "", tenure: "", tenureInDays: 0 };
}

export const Tenure: MessageFns<Tenure> = {
  encode(message: Tenure, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rawTenure !== "") {
      writer.uint32(10).string(message.rawTenure);
    }
    if (message.tenure !== "") {
      writer.uint32(18).string(message.tenure);
    }
    if (message.tenureInDays !== 0) {
      writer.uint32(24).int32(message.tenureInDays);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tenure {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenure();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rawTenure = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.tenureInDays = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Tenure {
    return {
      rawTenure: isSet(object.rawTenure) ? globalThis.String(object.rawTenure) : "",
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      tenureInDays: isSet(object.tenureInDays) ? globalThis.Number(object.tenureInDays) : 0,
    };
  },

  toJSON(message: Tenure): unknown {
    const obj: any = {};
    if (message.rawTenure !== "") {
      obj.rawTenure = message.rawTenure;
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.tenureInDays !== 0) {
      obj.tenureInDays = Math.round(message.tenureInDays);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Tenure>, I>>(base?: I): Tenure {
    return Tenure.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tenure>, I>>(object: I): Tenure {
    const message = createBaseTenure();
    message.rawTenure = object.rawTenure ?? "";
    message.tenure = object.tenure ?? "";
    message.tenureInDays = object.tenureInDays ?? 0;
    return message;
  },
};

function createBaseTagConfig(): TagConfig {
  return { name: "", iconUrl: "", color: "", bgColor: "", type: "" };
}

export const TagConfig: MessageFns<TagConfig> = {
  encode(message: TagConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.iconUrl !== "") {
      writer.uint32(18).string(message.iconUrl);
    }
    if (message.color !== "") {
      writer.uint32(26).string(message.color);
    }
    if (message.bgColor !== "") {
      writer.uint32(34).string(message.bgColor);
    }
    if (message.type !== "") {
      writer.uint32(42).string(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TagConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTagConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bgColor = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.type = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TagConfig {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      bgColor: isSet(object.bgColor) ? globalThis.String(object.bgColor) : "",
      type: isSet(object.type) ? globalThis.String(object.type) : "",
    };
  },

  toJSON(message: TagConfig): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.bgColor !== "") {
      obj.bgColor = message.bgColor;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TagConfig>, I>>(base?: I): TagConfig {
    return TagConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TagConfig>, I>>(object: I): TagConfig {
    const message = createBaseTagConfig();
    message.name = object.name ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.color = object.color ?? "";
    message.bgColor = object.bgColor ?? "";
    message.type = object.type ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
