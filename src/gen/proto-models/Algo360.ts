// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Algo360.proto

/* eslint-disable */

export const protobufPackage = "com.stablemoney.api.identity";

export enum AlgoStatusType {
  DATA_INGESTED_NEW = 0,
  DATA_INGESTED_INCREMENTAL = 1,
  ALGO360_PROCESS_FINISHED = 2,
  ALGO360_PRIORITY_VARIABLES_PROCESSED = 3,
  ALGO360_GAP_VARIABLES_PROCESSED = 4,
  UNKNOWN_ALGO_STATUS_TYPE = 5,
  UNRECOGNIZED = -1,
}

export function algoStatusTypeFromJSON(object: any): AlgoStatusType {
  switch (object) {
    case 0:
    case "DATA_INGESTED_NEW":
      return AlgoStatusType.DATA_INGESTED_NEW;
    case 1:
    case "DATA_INGESTED_INCREMENTAL":
      return AlgoStatusType.DATA_INGESTED_INCREMENTAL;
    case 2:
    case "ALGO360_PROCESS_FINISHED":
      return AlgoStatusType.ALGO360_PROCESS_FINISHED;
    case 3:
    case "ALGO360_PRIORITY_VARIABLES_PROCESSED":
      return AlgoStatusType.ALGO360_PRIORITY_VARIABLES_PROCESSED;
    case 4:
    case "ALGO360_GAP_VARIABLES_PROCESSED":
      return AlgoStatusType.ALGO360_GAP_VARIABLES_PROCESSED;
    case 5:
    case "UNKNOWN_ALGO_STATUS_TYPE":
      return AlgoStatusType.UNKNOWN_ALGO_STATUS_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AlgoStatusType.UNRECOGNIZED;
  }
}

export function algoStatusTypeToJSON(object: AlgoStatusType): string {
  switch (object) {
    case AlgoStatusType.DATA_INGESTED_NEW:
      return "DATA_INGESTED_NEW";
    case AlgoStatusType.DATA_INGESTED_INCREMENTAL:
      return "DATA_INGESTED_INCREMENTAL";
    case AlgoStatusType.ALGO360_PROCESS_FINISHED:
      return "ALGO360_PROCESS_FINISHED";
    case AlgoStatusType.ALGO360_PRIORITY_VARIABLES_PROCESSED:
      return "ALGO360_PRIORITY_VARIABLES_PROCESSED";
    case AlgoStatusType.ALGO360_GAP_VARIABLES_PROCESSED:
      return "ALGO360_GAP_VARIABLES_PROCESSED";
    case AlgoStatusType.UNKNOWN_ALGO_STATUS_TYPE:
      return "UNKNOWN_ALGO_STATUS_TYPE";
    case AlgoStatusType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}
