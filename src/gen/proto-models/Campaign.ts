// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Campaign.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum CampaignType {
  UNKNOWN_CAMPAIGN_TYPE = 0,
  FUNDING_SWEETS_CAMPAIGN_TYPE = 1,
  EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE = 2,
  DEFAULT_REFERRAL_CAMPAIGN_TYPE = 3,
  T_SHIRT_CAMPAIGN_TYPE = 4,
  UNRECOGNIZED = -1,
}

export function campaignTypeFromJSON(object: any): CampaignType {
  switch (object) {
    case 0:
    case "UNKNOWN_CAMPAIGN_TYPE":
      return CampaignType.UNKNOWN_CAMPAIGN_TYPE;
    case 1:
    case "FUNDING_SWEETS_CAMPAIGN_TYPE":
      return CampaignType.FUNDING_SWEETS_CAMPAIGN_TYPE;
    case 2:
    case "EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE":
      return CampaignType.EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE;
    case 3:
    case "DEFAULT_REFERRAL_CAMPAIGN_TYPE":
      return CampaignType.DEFAULT_REFERRAL_CAMPAIGN_TYPE;
    case 4:
    case "T_SHIRT_CAMPAIGN_TYPE":
      return CampaignType.T_SHIRT_CAMPAIGN_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CampaignType.UNRECOGNIZED;
  }
}

export function campaignTypeToJSON(object: CampaignType): string {
  switch (object) {
    case CampaignType.UNKNOWN_CAMPAIGN_TYPE:
      return "UNKNOWN_CAMPAIGN_TYPE";
    case CampaignType.FUNDING_SWEETS_CAMPAIGN_TYPE:
      return "FUNDING_SWEETS_CAMPAIGN_TYPE";
    case CampaignType.EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE:
      return "EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE";
    case CampaignType.DEFAULT_REFERRAL_CAMPAIGN_TYPE:
      return "DEFAULT_REFERRAL_CAMPAIGN_TYPE";
    case CampaignType.T_SHIRT_CAMPAIGN_TYPE:
      return "T_SHIRT_CAMPAIGN_TYPE";
    case CampaignType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum GiftOrderStatus {
  UNKNOWN_GIFT_ORDER_STATUS = 0,
  ORDER_PLACED_GIFT_ORDER_STATUS = 1,
  FAILED_GIFT_ORDER_STATUS = 2,
  INITIATED_GIFT_ORDER_STATUS = 3,
  UNRECOGNIZED = -1,
}

export function giftOrderStatusFromJSON(object: any): GiftOrderStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_GIFT_ORDER_STATUS":
      return GiftOrderStatus.UNKNOWN_GIFT_ORDER_STATUS;
    case 1:
    case "ORDER_PLACED_GIFT_ORDER_STATUS":
      return GiftOrderStatus.ORDER_PLACED_GIFT_ORDER_STATUS;
    case 2:
    case "FAILED_GIFT_ORDER_STATUS":
      return GiftOrderStatus.FAILED_GIFT_ORDER_STATUS;
    case 3:
    case "INITIATED_GIFT_ORDER_STATUS":
      return GiftOrderStatus.INITIATED_GIFT_ORDER_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GiftOrderStatus.UNRECOGNIZED;
  }
}

export function giftOrderStatusToJSON(object: GiftOrderStatus): string {
  switch (object) {
    case GiftOrderStatus.UNKNOWN_GIFT_ORDER_STATUS:
      return "UNKNOWN_GIFT_ORDER_STATUS";
    case GiftOrderStatus.ORDER_PLACED_GIFT_ORDER_STATUS:
      return "ORDER_PLACED_GIFT_ORDER_STATUS";
    case GiftOrderStatus.FAILED_GIFT_ORDER_STATUS:
      return "FAILED_GIFT_ORDER_STATUS";
    case GiftOrderStatus.INITIATED_GIFT_ORDER_STATUS:
      return "INITIATED_GIFT_ORDER_STATUS";
    case GiftOrderStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface Address {
  addressLine1: string;
  addressLine2?: string | undefined;
  addressLine3?: string | undefined;
  cityId: string;
  pinCode: string;
}

export interface FundingCampaignMetadata {
  orderStatus: GiftOrderStatus;
  canUserRefer: boolean;
  referralsLeftCount: number;
  trackingLinkUrl?: string | undefined;
}

export interface TShirtCampaignMetadata {
  giftOrderStatus: GiftOrderStatus;
  address?: Address | undefined;
  tShirtSize?: string | undefined;
  eligibilityReferralCount: number;
  currentReferralCount: number;
  campaignReferralLink: string;
}

export interface CampaignResponse {
  campaignType: CampaignType;
  isUserEligible: boolean;
  fundingCampaignMetadata?: FundingCampaignMetadata | undefined;
  tShirtCampaignMetadata?: TShirtCampaignMetadata | undefined;
}

export interface TShirtCampaignRequest {
  tShirtSize?: string | undefined;
  address?: Address | undefined;
}

export interface PlaceOrderRequest {
  campaignType: CampaignType;
  tShirtCampaignRequest?: TShirtCampaignRequest | undefined;
}

export interface PlaceOrderResponse {
  orderStatus: GiftOrderStatus;
}

function createBaseAddress(): Address {
  return { addressLine1: "", addressLine2: undefined, addressLine3: undefined, cityId: "", pinCode: "" };
}

export const Address: MessageFns<Address> = {
  encode(message: Address, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.addressLine1 !== "") {
      writer.uint32(10).string(message.addressLine1);
    }
    if (message.addressLine2 !== undefined) {
      writer.uint32(18).string(message.addressLine2);
    }
    if (message.addressLine3 !== undefined) {
      writer.uint32(26).string(message.addressLine3);
    }
    if (message.cityId !== "") {
      writer.uint32(34).string(message.cityId);
    }
    if (message.pinCode !== "") {
      writer.uint32(42).string(message.pinCode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Address {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddress();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.addressLine1 = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.addressLine2 = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.addressLine3 = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.cityId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.pinCode = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Address {
    return {
      addressLine1: isSet(object.addressLine1) ? globalThis.String(object.addressLine1) : "",
      addressLine2: isSet(object.addressLine2) ? globalThis.String(object.addressLine2) : undefined,
      addressLine3: isSet(object.addressLine3) ? globalThis.String(object.addressLine3) : undefined,
      cityId: isSet(object.cityId) ? globalThis.String(object.cityId) : "",
      pinCode: isSet(object.pinCode) ? globalThis.String(object.pinCode) : "",
    };
  },

  toJSON(message: Address): unknown {
    const obj: any = {};
    if (message.addressLine1 !== "") {
      obj.addressLine1 = message.addressLine1;
    }
    if (message.addressLine2 !== undefined) {
      obj.addressLine2 = message.addressLine2;
    }
    if (message.addressLine3 !== undefined) {
      obj.addressLine3 = message.addressLine3;
    }
    if (message.cityId !== "") {
      obj.cityId = message.cityId;
    }
    if (message.pinCode !== "") {
      obj.pinCode = message.pinCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Address>, I>>(base?: I): Address {
    return Address.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Address>, I>>(object: I): Address {
    const message = createBaseAddress();
    message.addressLine1 = object.addressLine1 ?? "";
    message.addressLine2 = object.addressLine2 ?? undefined;
    message.addressLine3 = object.addressLine3 ?? undefined;
    message.cityId = object.cityId ?? "";
    message.pinCode = object.pinCode ?? "";
    return message;
  },
};

function createBaseFundingCampaignMetadata(): FundingCampaignMetadata {
  return { orderStatus: 0, canUserRefer: false, referralsLeftCount: 0, trackingLinkUrl: undefined };
}

export const FundingCampaignMetadata: MessageFns<FundingCampaignMetadata> = {
  encode(message: FundingCampaignMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.orderStatus !== 0) {
      writer.uint32(8).int32(message.orderStatus);
    }
    if (message.canUserRefer !== false) {
      writer.uint32(16).bool(message.canUserRefer);
    }
    if (message.referralsLeftCount !== 0) {
      writer.uint32(24).int32(message.referralsLeftCount);
    }
    if (message.trackingLinkUrl !== undefined) {
      writer.uint32(34).string(message.trackingLinkUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FundingCampaignMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFundingCampaignMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.orderStatus = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.canUserRefer = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.referralsLeftCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.trackingLinkUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FundingCampaignMetadata {
    return {
      orderStatus: isSet(object.orderStatus) ? giftOrderStatusFromJSON(object.orderStatus) : 0,
      canUserRefer: isSet(object.canUserRefer) ? globalThis.Boolean(object.canUserRefer) : false,
      referralsLeftCount: isSet(object.referralsLeftCount) ? globalThis.Number(object.referralsLeftCount) : 0,
      trackingLinkUrl: isSet(object.trackingLinkUrl) ? globalThis.String(object.trackingLinkUrl) : undefined,
    };
  },

  toJSON(message: FundingCampaignMetadata): unknown {
    const obj: any = {};
    if (message.orderStatus !== 0) {
      obj.orderStatus = giftOrderStatusToJSON(message.orderStatus);
    }
    if (message.canUserRefer !== false) {
      obj.canUserRefer = message.canUserRefer;
    }
    if (message.referralsLeftCount !== 0) {
      obj.referralsLeftCount = Math.round(message.referralsLeftCount);
    }
    if (message.trackingLinkUrl !== undefined) {
      obj.trackingLinkUrl = message.trackingLinkUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FundingCampaignMetadata>, I>>(base?: I): FundingCampaignMetadata {
    return FundingCampaignMetadata.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FundingCampaignMetadata>, I>>(object: I): FundingCampaignMetadata {
    const message = createBaseFundingCampaignMetadata();
    message.orderStatus = object.orderStatus ?? 0;
    message.canUserRefer = object.canUserRefer ?? false;
    message.referralsLeftCount = object.referralsLeftCount ?? 0;
    message.trackingLinkUrl = object.trackingLinkUrl ?? undefined;
    return message;
  },
};

function createBaseTShirtCampaignMetadata(): TShirtCampaignMetadata {
  return {
    giftOrderStatus: 0,
    address: undefined,
    tShirtSize: undefined,
    eligibilityReferralCount: 0,
    currentReferralCount: 0,
    campaignReferralLink: "",
  };
}

export const TShirtCampaignMetadata: MessageFns<TShirtCampaignMetadata> = {
  encode(message: TShirtCampaignMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.giftOrderStatus !== 0) {
      writer.uint32(8).int32(message.giftOrderStatus);
    }
    if (message.address !== undefined) {
      Address.encode(message.address, writer.uint32(18).fork()).join();
    }
    if (message.tShirtSize !== undefined) {
      writer.uint32(26).string(message.tShirtSize);
    }
    if (message.eligibilityReferralCount !== 0) {
      writer.uint32(32).int32(message.eligibilityReferralCount);
    }
    if (message.currentReferralCount !== 0) {
      writer.uint32(40).int32(message.currentReferralCount);
    }
    if (message.campaignReferralLink !== "") {
      writer.uint32(50).string(message.campaignReferralLink);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TShirtCampaignMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTShirtCampaignMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.giftOrderStatus = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.address = Address.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tShirtSize = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.eligibilityReferralCount = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.currentReferralCount = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.campaignReferralLink = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TShirtCampaignMetadata {
    return {
      giftOrderStatus: isSet(object.giftOrderStatus) ? giftOrderStatusFromJSON(object.giftOrderStatus) : 0,
      address: isSet(object.address) ? Address.fromJSON(object.address) : undefined,
      tShirtSize: isSet(object.tShirtSize) ? globalThis.String(object.tShirtSize) : undefined,
      eligibilityReferralCount: isSet(object.eligibilityReferralCount)
        ? globalThis.Number(object.eligibilityReferralCount)
        : 0,
      currentReferralCount: isSet(object.currentReferralCount) ? globalThis.Number(object.currentReferralCount) : 0,
      campaignReferralLink: isSet(object.campaignReferralLink) ? globalThis.String(object.campaignReferralLink) : "",
    };
  },

  toJSON(message: TShirtCampaignMetadata): unknown {
    const obj: any = {};
    if (message.giftOrderStatus !== 0) {
      obj.giftOrderStatus = giftOrderStatusToJSON(message.giftOrderStatus);
    }
    if (message.address !== undefined) {
      obj.address = Address.toJSON(message.address);
    }
    if (message.tShirtSize !== undefined) {
      obj.tShirtSize = message.tShirtSize;
    }
    if (message.eligibilityReferralCount !== 0) {
      obj.eligibilityReferralCount = Math.round(message.eligibilityReferralCount);
    }
    if (message.currentReferralCount !== 0) {
      obj.currentReferralCount = Math.round(message.currentReferralCount);
    }
    if (message.campaignReferralLink !== "") {
      obj.campaignReferralLink = message.campaignReferralLink;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TShirtCampaignMetadata>, I>>(base?: I): TShirtCampaignMetadata {
    return TShirtCampaignMetadata.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TShirtCampaignMetadata>, I>>(object: I): TShirtCampaignMetadata {
    const message = createBaseTShirtCampaignMetadata();
    message.giftOrderStatus = object.giftOrderStatus ?? 0;
    message.address = (object.address !== undefined && object.address !== null)
      ? Address.fromPartial(object.address)
      : undefined;
    message.tShirtSize = object.tShirtSize ?? undefined;
    message.eligibilityReferralCount = object.eligibilityReferralCount ?? 0;
    message.currentReferralCount = object.currentReferralCount ?? 0;
    message.campaignReferralLink = object.campaignReferralLink ?? "";
    return message;
  },
};

function createBaseCampaignResponse(): CampaignResponse {
  return {
    campaignType: 0,
    isUserEligible: false,
    fundingCampaignMetadata: undefined,
    tShirtCampaignMetadata: undefined,
  };
}

export const CampaignResponse: MessageFns<CampaignResponse> = {
  encode(message: CampaignResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.campaignType !== 0) {
      writer.uint32(8).int32(message.campaignType);
    }
    if (message.isUserEligible !== false) {
      writer.uint32(16).bool(message.isUserEligible);
    }
    if (message.fundingCampaignMetadata !== undefined) {
      FundingCampaignMetadata.encode(message.fundingCampaignMetadata, writer.uint32(26).fork()).join();
    }
    if (message.tShirtCampaignMetadata !== undefined) {
      TShirtCampaignMetadata.encode(message.tShirtCampaignMetadata, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CampaignResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCampaignResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.campaignType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isUserEligible = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fundingCampaignMetadata = FundingCampaignMetadata.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.tShirtCampaignMetadata = TShirtCampaignMetadata.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CampaignResponse {
    return {
      campaignType: isSet(object.campaignType) ? campaignTypeFromJSON(object.campaignType) : 0,
      isUserEligible: isSet(object.isUserEligible) ? globalThis.Boolean(object.isUserEligible) : false,
      fundingCampaignMetadata: isSet(object.fundingCampaignMetadata)
        ? FundingCampaignMetadata.fromJSON(object.fundingCampaignMetadata)
        : undefined,
      tShirtCampaignMetadata: isSet(object.tShirtCampaignMetadata)
        ? TShirtCampaignMetadata.fromJSON(object.tShirtCampaignMetadata)
        : undefined,
    };
  },

  toJSON(message: CampaignResponse): unknown {
    const obj: any = {};
    if (message.campaignType !== 0) {
      obj.campaignType = campaignTypeToJSON(message.campaignType);
    }
    if (message.isUserEligible !== false) {
      obj.isUserEligible = message.isUserEligible;
    }
    if (message.fundingCampaignMetadata !== undefined) {
      obj.fundingCampaignMetadata = FundingCampaignMetadata.toJSON(message.fundingCampaignMetadata);
    }
    if (message.tShirtCampaignMetadata !== undefined) {
      obj.tShirtCampaignMetadata = TShirtCampaignMetadata.toJSON(message.tShirtCampaignMetadata);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CampaignResponse>, I>>(base?: I): CampaignResponse {
    return CampaignResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CampaignResponse>, I>>(object: I): CampaignResponse {
    const message = createBaseCampaignResponse();
    message.campaignType = object.campaignType ?? 0;
    message.isUserEligible = object.isUserEligible ?? false;
    message.fundingCampaignMetadata =
      (object.fundingCampaignMetadata !== undefined && object.fundingCampaignMetadata !== null)
        ? FundingCampaignMetadata.fromPartial(object.fundingCampaignMetadata)
        : undefined;
    message.tShirtCampaignMetadata =
      (object.tShirtCampaignMetadata !== undefined && object.tShirtCampaignMetadata !== null)
        ? TShirtCampaignMetadata.fromPartial(object.tShirtCampaignMetadata)
        : undefined;
    return message;
  },
};

function createBaseTShirtCampaignRequest(): TShirtCampaignRequest {
  return { tShirtSize: undefined, address: undefined };
}

export const TShirtCampaignRequest: MessageFns<TShirtCampaignRequest> = {
  encode(message: TShirtCampaignRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tShirtSize !== undefined) {
      writer.uint32(10).string(message.tShirtSize);
    }
    if (message.address !== undefined) {
      Address.encode(message.address, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TShirtCampaignRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTShirtCampaignRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tShirtSize = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.address = Address.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TShirtCampaignRequest {
    return {
      tShirtSize: isSet(object.tShirtSize) ? globalThis.String(object.tShirtSize) : undefined,
      address: isSet(object.address) ? Address.fromJSON(object.address) : undefined,
    };
  },

  toJSON(message: TShirtCampaignRequest): unknown {
    const obj: any = {};
    if (message.tShirtSize !== undefined) {
      obj.tShirtSize = message.tShirtSize;
    }
    if (message.address !== undefined) {
      obj.address = Address.toJSON(message.address);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TShirtCampaignRequest>, I>>(base?: I): TShirtCampaignRequest {
    return TShirtCampaignRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TShirtCampaignRequest>, I>>(object: I): TShirtCampaignRequest {
    const message = createBaseTShirtCampaignRequest();
    message.tShirtSize = object.tShirtSize ?? undefined;
    message.address = (object.address !== undefined && object.address !== null)
      ? Address.fromPartial(object.address)
      : undefined;
    return message;
  },
};

function createBasePlaceOrderRequest(): PlaceOrderRequest {
  return { campaignType: 0, tShirtCampaignRequest: undefined };
}

export const PlaceOrderRequest: MessageFns<PlaceOrderRequest> = {
  encode(message: PlaceOrderRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.campaignType !== 0) {
      writer.uint32(8).int32(message.campaignType);
    }
    if (message.tShirtCampaignRequest !== undefined) {
      TShirtCampaignRequest.encode(message.tShirtCampaignRequest, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlaceOrderRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlaceOrderRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.campaignType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tShirtCampaignRequest = TShirtCampaignRequest.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PlaceOrderRequest {
    return {
      campaignType: isSet(object.campaignType) ? campaignTypeFromJSON(object.campaignType) : 0,
      tShirtCampaignRequest: isSet(object.tShirtCampaignRequest)
        ? TShirtCampaignRequest.fromJSON(object.tShirtCampaignRequest)
        : undefined,
    };
  },

  toJSON(message: PlaceOrderRequest): unknown {
    const obj: any = {};
    if (message.campaignType !== 0) {
      obj.campaignType = campaignTypeToJSON(message.campaignType);
    }
    if (message.tShirtCampaignRequest !== undefined) {
      obj.tShirtCampaignRequest = TShirtCampaignRequest.toJSON(message.tShirtCampaignRequest);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PlaceOrderRequest>, I>>(base?: I): PlaceOrderRequest {
    return PlaceOrderRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlaceOrderRequest>, I>>(object: I): PlaceOrderRequest {
    const message = createBasePlaceOrderRequest();
    message.campaignType = object.campaignType ?? 0;
    message.tShirtCampaignRequest =
      (object.tShirtCampaignRequest !== undefined && object.tShirtCampaignRequest !== null)
        ? TShirtCampaignRequest.fromPartial(object.tShirtCampaignRequest)
        : undefined;
    return message;
  },
};

function createBasePlaceOrderResponse(): PlaceOrderResponse {
  return { orderStatus: 0 };
}

export const PlaceOrderResponse: MessageFns<PlaceOrderResponse> = {
  encode(message: PlaceOrderResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.orderStatus !== 0) {
      writer.uint32(8).int32(message.orderStatus);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PlaceOrderResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePlaceOrderResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.orderStatus = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PlaceOrderResponse {
    return { orderStatus: isSet(object.orderStatus) ? giftOrderStatusFromJSON(object.orderStatus) : 0 };
  },

  toJSON(message: PlaceOrderResponse): unknown {
    const obj: any = {};
    if (message.orderStatus !== 0) {
      obj.orderStatus = giftOrderStatusToJSON(message.orderStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PlaceOrderResponse>, I>>(base?: I): PlaceOrderResponse {
    return PlaceOrderResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlaceOrderResponse>, I>>(object: I): PlaceOrderResponse {
    const message = createBasePlaceOrderResponse();
    message.orderStatus = object.orderStatus ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
