// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Profile.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { CampaignType, campaignTypeFromJSON, campaignTypeToJSON } from "./Campaign";
import { UserDevice } from "./Device";
import { OnboardingModule, onboardingModuleFromJSON, onboardingModuleToJSON } from "./Onboarding";

export const protobufPackage = "com.stablemoney.api.identity";

export enum IncomeRange {
  UNKNOWN_INCOME_RANGE = 0,
  LESS_THAN_1L = 1,
  BETWEEN_1L_AND_5L = 2,
  BETWEEN_5L_AND_10L = 3,
  BETWEEN_10L_AND_25L = 4,
  BETWEEN_25L_AND_ABOVE = 5,
  UNRECOGNIZED = -1,
}

export function incomeRangeFromJSON(object: any): IncomeRange {
  switch (object) {
    case 0:
    case "UNKNOWN_INCOME_RANGE":
      return IncomeRange.UNKNOWN_INCOME_RANGE;
    case 1:
    case "LESS_THAN_1L":
      return IncomeRange.LESS_THAN_1L;
    case 2:
    case "BETWEEN_1L_AND_5L":
      return IncomeRange.BETWEEN_1L_AND_5L;
    case 3:
    case "BETWEEN_5L_AND_10L":
      return IncomeRange.BETWEEN_5L_AND_10L;
    case 4:
    case "BETWEEN_10L_AND_25L":
      return IncomeRange.BETWEEN_10L_AND_25L;
    case 5:
    case "BETWEEN_25L_AND_ABOVE":
      return IncomeRange.BETWEEN_25L_AND_ABOVE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return IncomeRange.UNRECOGNIZED;
  }
}

export function incomeRangeToJSON(object: IncomeRange): string {
  switch (object) {
    case IncomeRange.UNKNOWN_INCOME_RANGE:
      return "UNKNOWN_INCOME_RANGE";
    case IncomeRange.LESS_THAN_1L:
      return "LESS_THAN_1L";
    case IncomeRange.BETWEEN_1L_AND_5L:
      return "BETWEEN_1L_AND_5L";
    case IncomeRange.BETWEEN_5L_AND_10L:
      return "BETWEEN_5L_AND_10L";
    case IncomeRange.BETWEEN_10L_AND_25L:
      return "BETWEEN_10L_AND_25L";
    case IncomeRange.BETWEEN_25L_AND_ABOVE:
      return "BETWEEN_25L_AND_ABOVE";
    case IncomeRange.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum EmploymentType {
  UNKNOWN_EMPLOYMENT_TYPE = 0,
  PRIVATE_SECTOR_SERVICE = 1,
  PUBLIC_SECTOR = 2,
  BUSINESS = 3,
  PROFESSIONAL = 4,
  AGRICULTURIST = 5,
  RETIRED = 6,
  HOUSEWIFE = 7,
  STUDENT = 8,
  FOREX_DEALER = 9,
  GOVERNMENT_SERVICE = 10,
  OTHERS_EMPLOYMENT_TYPE = 11,
  SELF_EMPLOYED = 12,
  UNRECOGNIZED = -1,
}

export function employmentTypeFromJSON(object: any): EmploymentType {
  switch (object) {
    case 0:
    case "UNKNOWN_EMPLOYMENT_TYPE":
      return EmploymentType.UNKNOWN_EMPLOYMENT_TYPE;
    case 1:
    case "PRIVATE_SECTOR_SERVICE":
      return EmploymentType.PRIVATE_SECTOR_SERVICE;
    case 2:
    case "PUBLIC_SECTOR":
      return EmploymentType.PUBLIC_SECTOR;
    case 3:
    case "BUSINESS":
      return EmploymentType.BUSINESS;
    case 4:
    case "PROFESSIONAL":
      return EmploymentType.PROFESSIONAL;
    case 5:
    case "AGRICULTURIST":
      return EmploymentType.AGRICULTURIST;
    case 6:
    case "RETIRED":
      return EmploymentType.RETIRED;
    case 7:
    case "HOUSEWIFE":
      return EmploymentType.HOUSEWIFE;
    case 8:
    case "STUDENT":
      return EmploymentType.STUDENT;
    case 9:
    case "FOREX_DEALER":
      return EmploymentType.FOREX_DEALER;
    case 10:
    case "GOVERNMENT_SERVICE":
      return EmploymentType.GOVERNMENT_SERVICE;
    case 11:
    case "OTHERS_EMPLOYMENT_TYPE":
      return EmploymentType.OTHERS_EMPLOYMENT_TYPE;
    case 12:
    case "SELF_EMPLOYED":
      return EmploymentType.SELF_EMPLOYED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return EmploymentType.UNRECOGNIZED;
  }
}

export function employmentTypeToJSON(object: EmploymentType): string {
  switch (object) {
    case EmploymentType.UNKNOWN_EMPLOYMENT_TYPE:
      return "UNKNOWN_EMPLOYMENT_TYPE";
    case EmploymentType.PRIVATE_SECTOR_SERVICE:
      return "PRIVATE_SECTOR_SERVICE";
    case EmploymentType.PUBLIC_SECTOR:
      return "PUBLIC_SECTOR";
    case EmploymentType.BUSINESS:
      return "BUSINESS";
    case EmploymentType.PROFESSIONAL:
      return "PROFESSIONAL";
    case EmploymentType.AGRICULTURIST:
      return "AGRICULTURIST";
    case EmploymentType.RETIRED:
      return "RETIRED";
    case EmploymentType.HOUSEWIFE:
      return "HOUSEWIFE";
    case EmploymentType.STUDENT:
      return "STUDENT";
    case EmploymentType.FOREX_DEALER:
      return "FOREX_DEALER";
    case EmploymentType.GOVERNMENT_SERVICE:
      return "GOVERNMENT_SERVICE";
    case EmploymentType.OTHERS_EMPLOYMENT_TYPE:
      return "OTHERS_EMPLOYMENT_TYPE";
    case EmploymentType.SELF_EMPLOYED:
      return "SELF_EMPLOYED";
    case EmploymentType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum Gender {
  UNKNOWN_GENDER = 0,
  MALE = 1,
  FEMALE = 2,
  OTHER_GENDER = 3,
  UNRECOGNIZED = -1,
}

export function genderFromJSON(object: any): Gender {
  switch (object) {
    case 0:
    case "UNKNOWN_GENDER":
      return Gender.UNKNOWN_GENDER;
    case 1:
    case "MALE":
      return Gender.MALE;
    case 2:
    case "FEMALE":
      return Gender.FEMALE;
    case 3:
    case "OTHER_GENDER":
      return Gender.OTHER_GENDER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Gender.UNRECOGNIZED;
  }
}

export function genderToJSON(object: Gender): string {
  switch (object) {
    case Gender.UNKNOWN_GENDER:
      return "UNKNOWN_GENDER";
    case Gender.MALE:
      return "MALE";
    case Gender.FEMALE:
      return "FEMALE";
    case Gender.OTHER_GENDER:
      return "OTHER_GENDER";
    case Gender.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum MaritalStatus {
  UNKNOWN_MARITAL_STATUS = 0,
  SINGLE = 1,
  MARRIED = 2,
  UNRECOGNIZED = -1,
}

export function maritalStatusFromJSON(object: any): MaritalStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_MARITAL_STATUS":
      return MaritalStatus.UNKNOWN_MARITAL_STATUS;
    case 1:
    case "SINGLE":
      return MaritalStatus.SINGLE;
    case 2:
    case "MARRIED":
      return MaritalStatus.MARRIED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MaritalStatus.UNRECOGNIZED;
  }
}

export function maritalStatusToJSON(object: MaritalStatus): string {
  switch (object) {
    case MaritalStatus.UNKNOWN_MARITAL_STATUS:
      return "UNKNOWN_MARITAL_STATUS";
    case MaritalStatus.SINGLE:
      return "SINGLE";
    case MaritalStatus.MARRIED:
      return "MARRIED";
    case MaritalStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum TradingExperience {
  UNKNOWN_TRADING_EXPERIENCE = 0,
  LESS_THAN_1_MONTH = 1,
  BETWEEN_1_MONTH_AND_6_MONTH = 2,
  BETWEEN_6_MONTH_AND_1_YEAR = 3,
  BETWEEN_1_YEAR_AND_ABOVE = 4,
  UNRECOGNIZED = -1,
}

export function tradingExperienceFromJSON(object: any): TradingExperience {
  switch (object) {
    case 0:
    case "UNKNOWN_TRADING_EXPERIENCE":
      return TradingExperience.UNKNOWN_TRADING_EXPERIENCE;
    case 1:
    case "LESS_THAN_1_MONTH":
      return TradingExperience.LESS_THAN_1_MONTH;
    case 2:
    case "BETWEEN_1_MONTH_AND_6_MONTH":
      return TradingExperience.BETWEEN_1_MONTH_AND_6_MONTH;
    case 3:
    case "BETWEEN_6_MONTH_AND_1_YEAR":
      return TradingExperience.BETWEEN_6_MONTH_AND_1_YEAR;
    case 4:
    case "BETWEEN_1_YEAR_AND_ABOVE":
      return TradingExperience.BETWEEN_1_YEAR_AND_ABOVE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TradingExperience.UNRECOGNIZED;
  }
}

export function tradingExperienceToJSON(object: TradingExperience): string {
  switch (object) {
    case TradingExperience.UNKNOWN_TRADING_EXPERIENCE:
      return "UNKNOWN_TRADING_EXPERIENCE";
    case TradingExperience.LESS_THAN_1_MONTH:
      return "LESS_THAN_1_MONTH";
    case TradingExperience.BETWEEN_1_MONTH_AND_6_MONTH:
      return "BETWEEN_1_MONTH_AND_6_MONTH";
    case TradingExperience.BETWEEN_6_MONTH_AND_1_YEAR:
      return "BETWEEN_6_MONTH_AND_1_YEAR";
    case TradingExperience.BETWEEN_1_YEAR_AND_ABOVE:
      return "BETWEEN_1_YEAR_AND_ABOVE";
    case TradingExperience.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface UserProfileInternalResponse {
  profile: UserProfileResponse | undefined;
  userDevices: UserDevice[];
}

export interface UserData {
  id: string;
  email: string;
  emailVerified: boolean;
  mobile: string;
  mobileVerified: boolean;
  name: string;
  maskedEmail: string;
  firstName: string;
  lastName: string;
  socialName: string;
  profileImageUrl: string;
  userRegistrationTime: string;
}

export interface UserProfileResponse {
  data: UserData | undefined;
  profileData: UserProfileData | undefined;
  moduleStatus: OnboardingModuleStatusData[];
  lifeTimeStatus: string;
  currentStatus: string;
}

export interface UserProfileData {
  id: string;
  panNumber: string;
  aadharNumber: string;
  dob: string;
  gender: Gender;
  incomeRange: IncomeRange;
  employmentType: EmploymentType;
  tradingExperience: TradingExperience;
  maritalStatus: MaritalStatus;
  fatherName: string;
  motherName: string;
  eSignUrl: string;
  incomeTaxDepartmentName: string;
  kraName: string;
  fdBookingCount: number;
  firstFdRewardClaimed: boolean;
  isGoldMember: boolean;
  isUpswingTicketRaised: boolean;
  /** @deprecated */
  inAppReviewCohort: string;
  ticketCohort: string;
  hasLifetimeInvestment: boolean;
  /** 1. app_home_ui_250_reward  2. app_home_ui_200_reward 3. app_home_ui_no_reward */
  newUserHomeUiConfig: string;
  /** 1. app_reward_ui_250_reward  2. app_reward_ui_200_reward 3. app_reward_ui_no_reward */
  newUserRewardUiConfig: string;
  showCreditScore: boolean;
  refererName: string;
  rewardsInfoString: string;
  hasMyInvestments: boolean;
  isSpecialGoldMember: boolean;
  isRatingAvailable: boolean;
  showCreditRefresh: boolean;
  /** app_home_fincare/app_home/gt_home_fincare/gt_home */
  investmentUserHomeUiConfig: string;
  profileCompletionPercentage: number;
  isSeniorCitizen: boolean;
  viewSeniorCitizenRates: boolean;
  isRefererGoldMember: boolean;
  appsflyerReferralLink: string;
  /** This is the campaign name through which this user was referred */
  referralCampaign?: CampaignType | undefined;
}

export interface UpdateProfileRequest {
  dob?: string | undefined;
  incomeRange?: IncomeRange | undefined;
  employmentType?: EmploymentType | undefined;
  tradingExperience?: TradingExperience | undefined;
  maritalStatus?: MaritalStatus | undefined;
  fatherName?: string | undefined;
  motherName?: string | undefined;
  gender?: Gender | undefined;
}

export interface UpdateProfileResponse {
}

export interface OnboardingModuleStatusData {
  name: OnboardingModule;
  status: boolean;
}

export interface UpdateNameResponse {
}

export interface UpdateMediaSourceRequest {
  mediaSource: string;
}

export interface UpdateMediaSourceResponse {
}

export interface UpdateNameRequest {
  firstName: string;
  lastName: string;
  updateName?: boolean | undefined;
}

export interface UpdateAcquisitionParamsRequest {
  params: { [key: string]: string };
}

export interface UpdateAcquisitionParamsRequest_ParamsEntry {
  key: string;
  value: string;
}

export interface UpdateAcquisitionParamsResponse {
  paramsUpdated: boolean;
}

export interface DeleteUserRequest {
  userId: string;
  reason: string;
}

export interface DeleteUserResponse {
}

export interface CreditScorePanResponse {
  panNumber: string;
}

export interface CityDataResponse {
  isCityInvestorVisible: boolean;
  cityName: string;
  investorCityCount: number;
}

export interface PanDetailsResponse {
  panNumber: string;
  dob: string;
  isPanAvailable: boolean;
}

export interface UserDetailsResponse {
  userId: string;
  firstName?: string | undefined;
  lastName?: string | undefined;
  profileImageUrl?: string | undefined;
}

function createBaseUserProfileInternalResponse(): UserProfileInternalResponse {
  return { profile: undefined, userDevices: [] };
}

export const UserProfileInternalResponse: MessageFns<UserProfileInternalResponse> = {
  encode(message: UserProfileInternalResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.profile !== undefined) {
      UserProfileResponse.encode(message.profile, writer.uint32(10).fork()).join();
    }
    for (const v of message.userDevices) {
      UserDevice.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserProfileInternalResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserProfileInternalResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.profile = UserProfileResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userDevices.push(UserDevice.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserProfileInternalResponse {
    return {
      profile: isSet(object.profile) ? UserProfileResponse.fromJSON(object.profile) : undefined,
      userDevices: globalThis.Array.isArray(object?.userDevices)
        ? object.userDevices.map((e: any) => UserDevice.fromJSON(e))
        : [],
    };
  },

  toJSON(message: UserProfileInternalResponse): unknown {
    const obj: any = {};
    if (message.profile !== undefined) {
      obj.profile = UserProfileResponse.toJSON(message.profile);
    }
    if (message.userDevices?.length) {
      obj.userDevices = message.userDevices.map((e) => UserDevice.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserProfileInternalResponse>, I>>(base?: I): UserProfileInternalResponse {
    return UserProfileInternalResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserProfileInternalResponse>, I>>(object: I): UserProfileInternalResponse {
    const message = createBaseUserProfileInternalResponse();
    message.profile = (object.profile !== undefined && object.profile !== null)
      ? UserProfileResponse.fromPartial(object.profile)
      : undefined;
    message.userDevices = object.userDevices?.map((e) => UserDevice.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserData(): UserData {
  return {
    id: "",
    email: "",
    emailVerified: false,
    mobile: "",
    mobileVerified: false,
    name: "",
    maskedEmail: "",
    firstName: "",
    lastName: "",
    socialName: "",
    profileImageUrl: "",
    userRegistrationTime: "",
  };
}

export const UserData: MessageFns<UserData> = {
  encode(message: UserData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.email !== "") {
      writer.uint32(18).string(message.email);
    }
    if (message.emailVerified !== false) {
      writer.uint32(24).bool(message.emailVerified);
    }
    if (message.mobile !== "") {
      writer.uint32(34).string(message.mobile);
    }
    if (message.mobileVerified !== false) {
      writer.uint32(40).bool(message.mobileVerified);
    }
    if (message.name !== "") {
      writer.uint32(50).string(message.name);
    }
    if (message.maskedEmail !== "") {
      writer.uint32(66).string(message.maskedEmail);
    }
    if (message.firstName !== "") {
      writer.uint32(74).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(82).string(message.lastName);
    }
    if (message.socialName !== "") {
      writer.uint32(90).string(message.socialName);
    }
    if (message.profileImageUrl !== "") {
      writer.uint32(98).string(message.profileImageUrl);
    }
    if (message.userRegistrationTime !== "") {
      writer.uint32(106).string(message.userRegistrationTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.emailVerified = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.mobile = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.mobileVerified = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.maskedEmail = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.socialName = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.profileImageUrl = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.userRegistrationTime = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserData {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      email: isSet(object.email) ? globalThis.String(object.email) : "",
      emailVerified: isSet(object.emailVerified) ? globalThis.Boolean(object.emailVerified) : false,
      mobile: isSet(object.mobile) ? globalThis.String(object.mobile) : "",
      mobileVerified: isSet(object.mobileVerified) ? globalThis.Boolean(object.mobileVerified) : false,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      maskedEmail: isSet(object.maskedEmail) ? globalThis.String(object.maskedEmail) : "",
      firstName: isSet(object.firstName) ? globalThis.String(object.firstName) : "",
      lastName: isSet(object.lastName) ? globalThis.String(object.lastName) : "",
      socialName: isSet(object.socialName) ? globalThis.String(object.socialName) : "",
      profileImageUrl: isSet(object.profileImageUrl) ? globalThis.String(object.profileImageUrl) : "",
      userRegistrationTime: isSet(object.userRegistrationTime) ? globalThis.String(object.userRegistrationTime) : "",
    };
  },

  toJSON(message: UserData): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.email !== "") {
      obj.email = message.email;
    }
    if (message.emailVerified !== false) {
      obj.emailVerified = message.emailVerified;
    }
    if (message.mobile !== "") {
      obj.mobile = message.mobile;
    }
    if (message.mobileVerified !== false) {
      obj.mobileVerified = message.mobileVerified;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.maskedEmail !== "") {
      obj.maskedEmail = message.maskedEmail;
    }
    if (message.firstName !== "") {
      obj.firstName = message.firstName;
    }
    if (message.lastName !== "") {
      obj.lastName = message.lastName;
    }
    if (message.socialName !== "") {
      obj.socialName = message.socialName;
    }
    if (message.profileImageUrl !== "") {
      obj.profileImageUrl = message.profileImageUrl;
    }
    if (message.userRegistrationTime !== "") {
      obj.userRegistrationTime = message.userRegistrationTime;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserData>, I>>(base?: I): UserData {
    return UserData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserData>, I>>(object: I): UserData {
    const message = createBaseUserData();
    message.id = object.id ?? "";
    message.email = object.email ?? "";
    message.emailVerified = object.emailVerified ?? false;
    message.mobile = object.mobile ?? "";
    message.mobileVerified = object.mobileVerified ?? false;
    message.name = object.name ?? "";
    message.maskedEmail = object.maskedEmail ?? "";
    message.firstName = object.firstName ?? "";
    message.lastName = object.lastName ?? "";
    message.socialName = object.socialName ?? "";
    message.profileImageUrl = object.profileImageUrl ?? "";
    message.userRegistrationTime = object.userRegistrationTime ?? "";
    return message;
  },
};

function createBaseUserProfileResponse(): UserProfileResponse {
  return { data: undefined, profileData: undefined, moduleStatus: [], lifeTimeStatus: "", currentStatus: "" };
}

export const UserProfileResponse: MessageFns<UserProfileResponse> = {
  encode(message: UserProfileResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.data !== undefined) {
      UserData.encode(message.data, writer.uint32(18).fork()).join();
    }
    if (message.profileData !== undefined) {
      UserProfileData.encode(message.profileData, writer.uint32(26).fork()).join();
    }
    for (const v of message.moduleStatus) {
      OnboardingModuleStatusData.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.lifeTimeStatus !== "") {
      writer.uint32(42).string(message.lifeTimeStatus);
    }
    if (message.currentStatus !== "") {
      writer.uint32(50).string(message.currentStatus);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserProfileResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserProfileResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.data = UserData.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.profileData = UserProfileData.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.moduleStatus.push(OnboardingModuleStatusData.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.lifeTimeStatus = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.currentStatus = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserProfileResponse {
    return {
      data: isSet(object.data) ? UserData.fromJSON(object.data) : undefined,
      profileData: isSet(object.profileData) ? UserProfileData.fromJSON(object.profileData) : undefined,
      moduleStatus: globalThis.Array.isArray(object?.moduleStatus)
        ? object.moduleStatus.map((e: any) => OnboardingModuleStatusData.fromJSON(e))
        : [],
      lifeTimeStatus: isSet(object.lifeTimeStatus) ? globalThis.String(object.lifeTimeStatus) : "",
      currentStatus: isSet(object.currentStatus) ? globalThis.String(object.currentStatus) : "",
    };
  },

  toJSON(message: UserProfileResponse): unknown {
    const obj: any = {};
    if (message.data !== undefined) {
      obj.data = UserData.toJSON(message.data);
    }
    if (message.profileData !== undefined) {
      obj.profileData = UserProfileData.toJSON(message.profileData);
    }
    if (message.moduleStatus?.length) {
      obj.moduleStatus = message.moduleStatus.map((e) => OnboardingModuleStatusData.toJSON(e));
    }
    if (message.lifeTimeStatus !== "") {
      obj.lifeTimeStatus = message.lifeTimeStatus;
    }
    if (message.currentStatus !== "") {
      obj.currentStatus = message.currentStatus;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserProfileResponse>, I>>(base?: I): UserProfileResponse {
    return UserProfileResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserProfileResponse>, I>>(object: I): UserProfileResponse {
    const message = createBaseUserProfileResponse();
    message.data = (object.data !== undefined && object.data !== null) ? UserData.fromPartial(object.data) : undefined;
    message.profileData = (object.profileData !== undefined && object.profileData !== null)
      ? UserProfileData.fromPartial(object.profileData)
      : undefined;
    message.moduleStatus = object.moduleStatus?.map((e) => OnboardingModuleStatusData.fromPartial(e)) || [];
    message.lifeTimeStatus = object.lifeTimeStatus ?? "";
    message.currentStatus = object.currentStatus ?? "";
    return message;
  },
};

function createBaseUserProfileData(): UserProfileData {
  return {
    id: "",
    panNumber: "",
    aadharNumber: "",
    dob: "",
    gender: 0,
    incomeRange: 0,
    employmentType: 0,
    tradingExperience: 0,
    maritalStatus: 0,
    fatherName: "",
    motherName: "",
    eSignUrl: "",
    incomeTaxDepartmentName: "",
    kraName: "",
    fdBookingCount: 0,
    firstFdRewardClaimed: false,
    isGoldMember: false,
    isUpswingTicketRaised: false,
    inAppReviewCohort: "",
    ticketCohort: "",
    hasLifetimeInvestment: false,
    newUserHomeUiConfig: "",
    newUserRewardUiConfig: "",
    showCreditScore: false,
    refererName: "",
    rewardsInfoString: "",
    hasMyInvestments: false,
    isSpecialGoldMember: false,
    isRatingAvailable: false,
    showCreditRefresh: false,
    investmentUserHomeUiConfig: "",
    profileCompletionPercentage: 0,
    isSeniorCitizen: false,
    viewSeniorCitizenRates: false,
    isRefererGoldMember: false,
    appsflyerReferralLink: "",
    referralCampaign: undefined,
  };
}

export const UserProfileData: MessageFns<UserProfileData> = {
  encode(message: UserProfileData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.panNumber !== "") {
      writer.uint32(18).string(message.panNumber);
    }
    if (message.aadharNumber !== "") {
      writer.uint32(26).string(message.aadharNumber);
    }
    if (message.dob !== "") {
      writer.uint32(34).string(message.dob);
    }
    if (message.gender !== 0) {
      writer.uint32(40).int32(message.gender);
    }
    if (message.incomeRange !== 0) {
      writer.uint32(48).int32(message.incomeRange);
    }
    if (message.employmentType !== 0) {
      writer.uint32(56).int32(message.employmentType);
    }
    if (message.tradingExperience !== 0) {
      writer.uint32(64).int32(message.tradingExperience);
    }
    if (message.maritalStatus !== 0) {
      writer.uint32(72).int32(message.maritalStatus);
    }
    if (message.fatherName !== "") {
      writer.uint32(82).string(message.fatherName);
    }
    if (message.motherName !== "") {
      writer.uint32(90).string(message.motherName);
    }
    if (message.eSignUrl !== "") {
      writer.uint32(98).string(message.eSignUrl);
    }
    if (message.incomeTaxDepartmentName !== "") {
      writer.uint32(106).string(message.incomeTaxDepartmentName);
    }
    if (message.kraName !== "") {
      writer.uint32(114).string(message.kraName);
    }
    if (message.fdBookingCount !== 0) {
      writer.uint32(128).int32(message.fdBookingCount);
    }
    if (message.firstFdRewardClaimed !== false) {
      writer.uint32(136).bool(message.firstFdRewardClaimed);
    }
    if (message.isGoldMember !== false) {
      writer.uint32(144).bool(message.isGoldMember);
    }
    if (message.isUpswingTicketRaised !== false) {
      writer.uint32(152).bool(message.isUpswingTicketRaised);
    }
    if (message.inAppReviewCohort !== "") {
      writer.uint32(162).string(message.inAppReviewCohort);
    }
    if (message.ticketCohort !== "") {
      writer.uint32(170).string(message.ticketCohort);
    }
    if (message.hasLifetimeInvestment !== false) {
      writer.uint32(176).bool(message.hasLifetimeInvestment);
    }
    if (message.newUserHomeUiConfig !== "") {
      writer.uint32(186).string(message.newUserHomeUiConfig);
    }
    if (message.newUserRewardUiConfig !== "") {
      writer.uint32(194).string(message.newUserRewardUiConfig);
    }
    if (message.showCreditScore !== false) {
      writer.uint32(200).bool(message.showCreditScore);
    }
    if (message.refererName !== "") {
      writer.uint32(210).string(message.refererName);
    }
    if (message.rewardsInfoString !== "") {
      writer.uint32(218).string(message.rewardsInfoString);
    }
    if (message.hasMyInvestments !== false) {
      writer.uint32(224).bool(message.hasMyInvestments);
    }
    if (message.isSpecialGoldMember !== false) {
      writer.uint32(232).bool(message.isSpecialGoldMember);
    }
    if (message.isRatingAvailable !== false) {
      writer.uint32(240).bool(message.isRatingAvailable);
    }
    if (message.showCreditRefresh !== false) {
      writer.uint32(248).bool(message.showCreditRefresh);
    }
    if (message.investmentUserHomeUiConfig !== "") {
      writer.uint32(266).string(message.investmentUserHomeUiConfig);
    }
    if (message.profileCompletionPercentage !== 0) {
      writer.uint32(273).double(message.profileCompletionPercentage);
    }
    if (message.isSeniorCitizen !== false) {
      writer.uint32(280).bool(message.isSeniorCitizen);
    }
    if (message.viewSeniorCitizenRates !== false) {
      writer.uint32(288).bool(message.viewSeniorCitizenRates);
    }
    if (message.isRefererGoldMember !== false) {
      writer.uint32(296).bool(message.isRefererGoldMember);
    }
    if (message.appsflyerReferralLink !== "") {
      writer.uint32(306).string(message.appsflyerReferralLink);
    }
    if (message.referralCampaign !== undefined) {
      writer.uint32(312).int32(message.referralCampaign);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserProfileData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserProfileData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.panNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.aadharNumber = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.dob = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.gender = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.incomeRange = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.employmentType = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.tradingExperience = reader.int32() as any;
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.maritalStatus = reader.int32() as any;
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.fatherName = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.motherName = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.eSignUrl = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.incomeTaxDepartmentName = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.kraName = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.fdBookingCount = reader.int32();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.firstFdRewardClaimed = reader.bool();
          continue;
        }
        case 18: {
          if (tag !== 144) {
            break;
          }

          message.isGoldMember = reader.bool();
          continue;
        }
        case 19: {
          if (tag !== 152) {
            break;
          }

          message.isUpswingTicketRaised = reader.bool();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.inAppReviewCohort = reader.string();
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.ticketCohort = reader.string();
          continue;
        }
        case 22: {
          if (tag !== 176) {
            break;
          }

          message.hasLifetimeInvestment = reader.bool();
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.newUserHomeUiConfig = reader.string();
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.newUserRewardUiConfig = reader.string();
          continue;
        }
        case 25: {
          if (tag !== 200) {
            break;
          }

          message.showCreditScore = reader.bool();
          continue;
        }
        case 26: {
          if (tag !== 210) {
            break;
          }

          message.refererName = reader.string();
          continue;
        }
        case 27: {
          if (tag !== 218) {
            break;
          }

          message.rewardsInfoString = reader.string();
          continue;
        }
        case 28: {
          if (tag !== 224) {
            break;
          }

          message.hasMyInvestments = reader.bool();
          continue;
        }
        case 29: {
          if (tag !== 232) {
            break;
          }

          message.isSpecialGoldMember = reader.bool();
          continue;
        }
        case 30: {
          if (tag !== 240) {
            break;
          }

          message.isRatingAvailable = reader.bool();
          continue;
        }
        case 31: {
          if (tag !== 248) {
            break;
          }

          message.showCreditRefresh = reader.bool();
          continue;
        }
        case 33: {
          if (tag !== 266) {
            break;
          }

          message.investmentUserHomeUiConfig = reader.string();
          continue;
        }
        case 34: {
          if (tag !== 273) {
            break;
          }

          message.profileCompletionPercentage = reader.double();
          continue;
        }
        case 35: {
          if (tag !== 280) {
            break;
          }

          message.isSeniorCitizen = reader.bool();
          continue;
        }
        case 36: {
          if (tag !== 288) {
            break;
          }

          message.viewSeniorCitizenRates = reader.bool();
          continue;
        }
        case 37: {
          if (tag !== 296) {
            break;
          }

          message.isRefererGoldMember = reader.bool();
          continue;
        }
        case 38: {
          if (tag !== 306) {
            break;
          }

          message.appsflyerReferralLink = reader.string();
          continue;
        }
        case 39: {
          if (tag !== 312) {
            break;
          }

          message.referralCampaign = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserProfileData {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      panNumber: isSet(object.panNumber) ? globalThis.String(object.panNumber) : "",
      aadharNumber: isSet(object.aadharNumber) ? globalThis.String(object.aadharNumber) : "",
      dob: isSet(object.dob) ? globalThis.String(object.dob) : "",
      gender: isSet(object.gender) ? genderFromJSON(object.gender) : 0,
      incomeRange: isSet(object.incomeRange) ? incomeRangeFromJSON(object.incomeRange) : 0,
      employmentType: isSet(object.employmentType) ? employmentTypeFromJSON(object.employmentType) : 0,
      tradingExperience: isSet(object.tradingExperience) ? tradingExperienceFromJSON(object.tradingExperience) : 0,
      maritalStatus: isSet(object.maritalStatus) ? maritalStatusFromJSON(object.maritalStatus) : 0,
      fatherName: isSet(object.fatherName) ? globalThis.String(object.fatherName) : "",
      motherName: isSet(object.motherName) ? globalThis.String(object.motherName) : "",
      eSignUrl: isSet(object.eSignUrl) ? globalThis.String(object.eSignUrl) : "",
      incomeTaxDepartmentName: isSet(object.incomeTaxDepartmentName)
        ? globalThis.String(object.incomeTaxDepartmentName)
        : "",
      kraName: isSet(object.kraName) ? globalThis.String(object.kraName) : "",
      fdBookingCount: isSet(object.fdBookingCount) ? globalThis.Number(object.fdBookingCount) : 0,
      firstFdRewardClaimed: isSet(object.firstFdRewardClaimed)
        ? globalThis.Boolean(object.firstFdRewardClaimed)
        : false,
      isGoldMember: isSet(object.isGoldMember) ? globalThis.Boolean(object.isGoldMember) : false,
      isUpswingTicketRaised: isSet(object.isUpswingTicketRaised)
        ? globalThis.Boolean(object.isUpswingTicketRaised)
        : false,
      inAppReviewCohort: isSet(object.inAppReviewCohort) ? globalThis.String(object.inAppReviewCohort) : "",
      ticketCohort: isSet(object.ticketCohort) ? globalThis.String(object.ticketCohort) : "",
      hasLifetimeInvestment: isSet(object.hasLifetimeInvestment)
        ? globalThis.Boolean(object.hasLifetimeInvestment)
        : false,
      newUserHomeUiConfig: isSet(object.newUserHomeUiConfig) ? globalThis.String(object.newUserHomeUiConfig) : "",
      newUserRewardUiConfig: isSet(object.newUserRewardUiConfig) ? globalThis.String(object.newUserRewardUiConfig) : "",
      showCreditScore: isSet(object.showCreditScore) ? globalThis.Boolean(object.showCreditScore) : false,
      refererName: isSet(object.refererName) ? globalThis.String(object.refererName) : "",
      rewardsInfoString: isSet(object.rewardsInfoString) ? globalThis.String(object.rewardsInfoString) : "",
      hasMyInvestments: isSet(object.hasMyInvestments) ? globalThis.Boolean(object.hasMyInvestments) : false,
      isSpecialGoldMember: isSet(object.isSpecialGoldMember) ? globalThis.Boolean(object.isSpecialGoldMember) : false,
      isRatingAvailable: isSet(object.isRatingAvailable) ? globalThis.Boolean(object.isRatingAvailable) : false,
      showCreditRefresh: isSet(object.showCreditRefresh) ? globalThis.Boolean(object.showCreditRefresh) : false,
      investmentUserHomeUiConfig: isSet(object.investmentUserHomeUiConfig)
        ? globalThis.String(object.investmentUserHomeUiConfig)
        : "",
      profileCompletionPercentage: isSet(object.profileCompletionPercentage)
        ? globalThis.Number(object.profileCompletionPercentage)
        : 0,
      isSeniorCitizen: isSet(object.isSeniorCitizen) ? globalThis.Boolean(object.isSeniorCitizen) : false,
      viewSeniorCitizenRates: isSet(object.viewSeniorCitizenRates)
        ? globalThis.Boolean(object.viewSeniorCitizenRates)
        : false,
      isRefererGoldMember: isSet(object.isRefererGoldMember) ? globalThis.Boolean(object.isRefererGoldMember) : false,
      appsflyerReferralLink: isSet(object.appsflyerReferralLink) ? globalThis.String(object.appsflyerReferralLink) : "",
      referralCampaign: isSet(object.referralCampaign) ? campaignTypeFromJSON(object.referralCampaign) : undefined,
    };
  },

  toJSON(message: UserProfileData): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.panNumber !== "") {
      obj.panNumber = message.panNumber;
    }
    if (message.aadharNumber !== "") {
      obj.aadharNumber = message.aadharNumber;
    }
    if (message.dob !== "") {
      obj.dob = message.dob;
    }
    if (message.gender !== 0) {
      obj.gender = genderToJSON(message.gender);
    }
    if (message.incomeRange !== 0) {
      obj.incomeRange = incomeRangeToJSON(message.incomeRange);
    }
    if (message.employmentType !== 0) {
      obj.employmentType = employmentTypeToJSON(message.employmentType);
    }
    if (message.tradingExperience !== 0) {
      obj.tradingExperience = tradingExperienceToJSON(message.tradingExperience);
    }
    if (message.maritalStatus !== 0) {
      obj.maritalStatus = maritalStatusToJSON(message.maritalStatus);
    }
    if (message.fatherName !== "") {
      obj.fatherName = message.fatherName;
    }
    if (message.motherName !== "") {
      obj.motherName = message.motherName;
    }
    if (message.eSignUrl !== "") {
      obj.eSignUrl = message.eSignUrl;
    }
    if (message.incomeTaxDepartmentName !== "") {
      obj.incomeTaxDepartmentName = message.incomeTaxDepartmentName;
    }
    if (message.kraName !== "") {
      obj.kraName = message.kraName;
    }
    if (message.fdBookingCount !== 0) {
      obj.fdBookingCount = Math.round(message.fdBookingCount);
    }
    if (message.firstFdRewardClaimed !== false) {
      obj.firstFdRewardClaimed = message.firstFdRewardClaimed;
    }
    if (message.isGoldMember !== false) {
      obj.isGoldMember = message.isGoldMember;
    }
    if (message.isUpswingTicketRaised !== false) {
      obj.isUpswingTicketRaised = message.isUpswingTicketRaised;
    }
    if (message.inAppReviewCohort !== "") {
      obj.inAppReviewCohort = message.inAppReviewCohort;
    }
    if (message.ticketCohort !== "") {
      obj.ticketCohort = message.ticketCohort;
    }
    if (message.hasLifetimeInvestment !== false) {
      obj.hasLifetimeInvestment = message.hasLifetimeInvestment;
    }
    if (message.newUserHomeUiConfig !== "") {
      obj.newUserHomeUiConfig = message.newUserHomeUiConfig;
    }
    if (message.newUserRewardUiConfig !== "") {
      obj.newUserRewardUiConfig = message.newUserRewardUiConfig;
    }
    if (message.showCreditScore !== false) {
      obj.showCreditScore = message.showCreditScore;
    }
    if (message.refererName !== "") {
      obj.refererName = message.refererName;
    }
    if (message.rewardsInfoString !== "") {
      obj.rewardsInfoString = message.rewardsInfoString;
    }
    if (message.hasMyInvestments !== false) {
      obj.hasMyInvestments = message.hasMyInvestments;
    }
    if (message.isSpecialGoldMember !== false) {
      obj.isSpecialGoldMember = message.isSpecialGoldMember;
    }
    if (message.isRatingAvailable !== false) {
      obj.isRatingAvailable = message.isRatingAvailable;
    }
    if (message.showCreditRefresh !== false) {
      obj.showCreditRefresh = message.showCreditRefresh;
    }
    if (message.investmentUserHomeUiConfig !== "") {
      obj.investmentUserHomeUiConfig = message.investmentUserHomeUiConfig;
    }
    if (message.profileCompletionPercentage !== 0) {
      obj.profileCompletionPercentage = message.profileCompletionPercentage;
    }
    if (message.isSeniorCitizen !== false) {
      obj.isSeniorCitizen = message.isSeniorCitizen;
    }
    if (message.viewSeniorCitizenRates !== false) {
      obj.viewSeniorCitizenRates = message.viewSeniorCitizenRates;
    }
    if (message.isRefererGoldMember !== false) {
      obj.isRefererGoldMember = message.isRefererGoldMember;
    }
    if (message.appsflyerReferralLink !== "") {
      obj.appsflyerReferralLink = message.appsflyerReferralLink;
    }
    if (message.referralCampaign !== undefined) {
      obj.referralCampaign = campaignTypeToJSON(message.referralCampaign);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserProfileData>, I>>(base?: I): UserProfileData {
    return UserProfileData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserProfileData>, I>>(object: I): UserProfileData {
    const message = createBaseUserProfileData();
    message.id = object.id ?? "";
    message.panNumber = object.panNumber ?? "";
    message.aadharNumber = object.aadharNumber ?? "";
    message.dob = object.dob ?? "";
    message.gender = object.gender ?? 0;
    message.incomeRange = object.incomeRange ?? 0;
    message.employmentType = object.employmentType ?? 0;
    message.tradingExperience = object.tradingExperience ?? 0;
    message.maritalStatus = object.maritalStatus ?? 0;
    message.fatherName = object.fatherName ?? "";
    message.motherName = object.motherName ?? "";
    message.eSignUrl = object.eSignUrl ?? "";
    message.incomeTaxDepartmentName = object.incomeTaxDepartmentName ?? "";
    message.kraName = object.kraName ?? "";
    message.fdBookingCount = object.fdBookingCount ?? 0;
    message.firstFdRewardClaimed = object.firstFdRewardClaimed ?? false;
    message.isGoldMember = object.isGoldMember ?? false;
    message.isUpswingTicketRaised = object.isUpswingTicketRaised ?? false;
    message.inAppReviewCohort = object.inAppReviewCohort ?? "";
    message.ticketCohort = object.ticketCohort ?? "";
    message.hasLifetimeInvestment = object.hasLifetimeInvestment ?? false;
    message.newUserHomeUiConfig = object.newUserHomeUiConfig ?? "";
    message.newUserRewardUiConfig = object.newUserRewardUiConfig ?? "";
    message.showCreditScore = object.showCreditScore ?? false;
    message.refererName = object.refererName ?? "";
    message.rewardsInfoString = object.rewardsInfoString ?? "";
    message.hasMyInvestments = object.hasMyInvestments ?? false;
    message.isSpecialGoldMember = object.isSpecialGoldMember ?? false;
    message.isRatingAvailable = object.isRatingAvailable ?? false;
    message.showCreditRefresh = object.showCreditRefresh ?? false;
    message.investmentUserHomeUiConfig = object.investmentUserHomeUiConfig ?? "";
    message.profileCompletionPercentage = object.profileCompletionPercentage ?? 0;
    message.isSeniorCitizen = object.isSeniorCitizen ?? false;
    message.viewSeniorCitizenRates = object.viewSeniorCitizenRates ?? false;
    message.isRefererGoldMember = object.isRefererGoldMember ?? false;
    message.appsflyerReferralLink = object.appsflyerReferralLink ?? "";
    message.referralCampaign = object.referralCampaign ?? undefined;
    return message;
  },
};

function createBaseUpdateProfileRequest(): UpdateProfileRequest {
  return {
    dob: undefined,
    incomeRange: undefined,
    employmentType: undefined,
    tradingExperience: undefined,
    maritalStatus: undefined,
    fatherName: undefined,
    motherName: undefined,
    gender: undefined,
  };
}

export const UpdateProfileRequest: MessageFns<UpdateProfileRequest> = {
  encode(message: UpdateProfileRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.dob !== undefined) {
      writer.uint32(10).string(message.dob);
    }
    if (message.incomeRange !== undefined) {
      writer.uint32(16).int32(message.incomeRange);
    }
    if (message.employmentType !== undefined) {
      writer.uint32(24).int32(message.employmentType);
    }
    if (message.tradingExperience !== undefined) {
      writer.uint32(32).int32(message.tradingExperience);
    }
    if (message.maritalStatus !== undefined) {
      writer.uint32(40).int32(message.maritalStatus);
    }
    if (message.fatherName !== undefined) {
      writer.uint32(50).string(message.fatherName);
    }
    if (message.motherName !== undefined) {
      writer.uint32(58).string(message.motherName);
    }
    if (message.gender !== undefined) {
      writer.uint32(64).int32(message.gender);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateProfileRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateProfileRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.dob = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.incomeRange = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.employmentType = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.tradingExperience = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.maritalStatus = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.fatherName = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.motherName = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.gender = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateProfileRequest {
    return {
      dob: isSet(object.dob) ? globalThis.String(object.dob) : undefined,
      incomeRange: isSet(object.incomeRange) ? incomeRangeFromJSON(object.incomeRange) : undefined,
      employmentType: isSet(object.employmentType) ? employmentTypeFromJSON(object.employmentType) : undefined,
      tradingExperience: isSet(object.tradingExperience)
        ? tradingExperienceFromJSON(object.tradingExperience)
        : undefined,
      maritalStatus: isSet(object.maritalStatus) ? maritalStatusFromJSON(object.maritalStatus) : undefined,
      fatherName: isSet(object.fatherName) ? globalThis.String(object.fatherName) : undefined,
      motherName: isSet(object.motherName) ? globalThis.String(object.motherName) : undefined,
      gender: isSet(object.gender) ? genderFromJSON(object.gender) : undefined,
    };
  },

  toJSON(message: UpdateProfileRequest): unknown {
    const obj: any = {};
    if (message.dob !== undefined) {
      obj.dob = message.dob;
    }
    if (message.incomeRange !== undefined) {
      obj.incomeRange = incomeRangeToJSON(message.incomeRange);
    }
    if (message.employmentType !== undefined) {
      obj.employmentType = employmentTypeToJSON(message.employmentType);
    }
    if (message.tradingExperience !== undefined) {
      obj.tradingExperience = tradingExperienceToJSON(message.tradingExperience);
    }
    if (message.maritalStatus !== undefined) {
      obj.maritalStatus = maritalStatusToJSON(message.maritalStatus);
    }
    if (message.fatherName !== undefined) {
      obj.fatherName = message.fatherName;
    }
    if (message.motherName !== undefined) {
      obj.motherName = message.motherName;
    }
    if (message.gender !== undefined) {
      obj.gender = genderToJSON(message.gender);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateProfileRequest>, I>>(base?: I): UpdateProfileRequest {
    return UpdateProfileRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateProfileRequest>, I>>(object: I): UpdateProfileRequest {
    const message = createBaseUpdateProfileRequest();
    message.dob = object.dob ?? undefined;
    message.incomeRange = object.incomeRange ?? undefined;
    message.employmentType = object.employmentType ?? undefined;
    message.tradingExperience = object.tradingExperience ?? undefined;
    message.maritalStatus = object.maritalStatus ?? undefined;
    message.fatherName = object.fatherName ?? undefined;
    message.motherName = object.motherName ?? undefined;
    message.gender = object.gender ?? undefined;
    return message;
  },
};

function createBaseUpdateProfileResponse(): UpdateProfileResponse {
  return {};
}

export const UpdateProfileResponse: MessageFns<UpdateProfileResponse> = {
  encode(_: UpdateProfileResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateProfileResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateProfileResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateProfileResponse {
    return {};
  },

  toJSON(_: UpdateProfileResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateProfileResponse>, I>>(base?: I): UpdateProfileResponse {
    return UpdateProfileResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateProfileResponse>, I>>(_: I): UpdateProfileResponse {
    const message = createBaseUpdateProfileResponse();
    return message;
  },
};

function createBaseOnboardingModuleStatusData(): OnboardingModuleStatusData {
  return { name: 0, status: false };
}

export const OnboardingModuleStatusData: MessageFns<OnboardingModuleStatusData> = {
  encode(message: OnboardingModuleStatusData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== 0) {
      writer.uint32(8).int32(message.name);
    }
    if (message.status !== false) {
      writer.uint32(16).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): OnboardingModuleStatusData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOnboardingModuleStatusData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.name = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OnboardingModuleStatusData {
    return {
      name: isSet(object.name) ? onboardingModuleFromJSON(object.name) : 0,
      status: isSet(object.status) ? globalThis.Boolean(object.status) : false,
    };
  },

  toJSON(message: OnboardingModuleStatusData): unknown {
    const obj: any = {};
    if (message.name !== 0) {
      obj.name = onboardingModuleToJSON(message.name);
    }
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OnboardingModuleStatusData>, I>>(base?: I): OnboardingModuleStatusData {
    return OnboardingModuleStatusData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnboardingModuleStatusData>, I>>(object: I): OnboardingModuleStatusData {
    const message = createBaseOnboardingModuleStatusData();
    message.name = object.name ?? 0;
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseUpdateNameResponse(): UpdateNameResponse {
  return {};
}

export const UpdateNameResponse: MessageFns<UpdateNameResponse> = {
  encode(_: UpdateNameResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateNameResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateNameResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateNameResponse {
    return {};
  },

  toJSON(_: UpdateNameResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateNameResponse>, I>>(base?: I): UpdateNameResponse {
    return UpdateNameResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateNameResponse>, I>>(_: I): UpdateNameResponse {
    const message = createBaseUpdateNameResponse();
    return message;
  },
};

function createBaseUpdateMediaSourceRequest(): UpdateMediaSourceRequest {
  return { mediaSource: "" };
}

export const UpdateMediaSourceRequest: MessageFns<UpdateMediaSourceRequest> = {
  encode(message: UpdateMediaSourceRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mediaSource !== "") {
      writer.uint32(10).string(message.mediaSource);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateMediaSourceRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateMediaSourceRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.mediaSource = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateMediaSourceRequest {
    return { mediaSource: isSet(object.mediaSource) ? globalThis.String(object.mediaSource) : "" };
  },

  toJSON(message: UpdateMediaSourceRequest): unknown {
    const obj: any = {};
    if (message.mediaSource !== "") {
      obj.mediaSource = message.mediaSource;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateMediaSourceRequest>, I>>(base?: I): UpdateMediaSourceRequest {
    return UpdateMediaSourceRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateMediaSourceRequest>, I>>(object: I): UpdateMediaSourceRequest {
    const message = createBaseUpdateMediaSourceRequest();
    message.mediaSource = object.mediaSource ?? "";
    return message;
  },
};

function createBaseUpdateMediaSourceResponse(): UpdateMediaSourceResponse {
  return {};
}

export const UpdateMediaSourceResponse: MessageFns<UpdateMediaSourceResponse> = {
  encode(_: UpdateMediaSourceResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateMediaSourceResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateMediaSourceResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): UpdateMediaSourceResponse {
    return {};
  },

  toJSON(_: UpdateMediaSourceResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateMediaSourceResponse>, I>>(base?: I): UpdateMediaSourceResponse {
    return UpdateMediaSourceResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateMediaSourceResponse>, I>>(_: I): UpdateMediaSourceResponse {
    const message = createBaseUpdateMediaSourceResponse();
    return message;
  },
};

function createBaseUpdateNameRequest(): UpdateNameRequest {
  return { firstName: "", lastName: "", updateName: undefined };
}

export const UpdateNameRequest: MessageFns<UpdateNameRequest> = {
  encode(message: UpdateNameRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.firstName !== "") {
      writer.uint32(10).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(18).string(message.lastName);
    }
    if (message.updateName !== undefined) {
      writer.uint32(24).bool(message.updateName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateNameRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateNameRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.updateName = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateNameRequest {
    return {
      firstName: isSet(object.firstName) ? globalThis.String(object.firstName) : "",
      lastName: isSet(object.lastName) ? globalThis.String(object.lastName) : "",
      updateName: isSet(object.updateName) ? globalThis.Boolean(object.updateName) : undefined,
    };
  },

  toJSON(message: UpdateNameRequest): unknown {
    const obj: any = {};
    if (message.firstName !== "") {
      obj.firstName = message.firstName;
    }
    if (message.lastName !== "") {
      obj.lastName = message.lastName;
    }
    if (message.updateName !== undefined) {
      obj.updateName = message.updateName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateNameRequest>, I>>(base?: I): UpdateNameRequest {
    return UpdateNameRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateNameRequest>, I>>(object: I): UpdateNameRequest {
    const message = createBaseUpdateNameRequest();
    message.firstName = object.firstName ?? "";
    message.lastName = object.lastName ?? "";
    message.updateName = object.updateName ?? undefined;
    return message;
  },
};

function createBaseUpdateAcquisitionParamsRequest(): UpdateAcquisitionParamsRequest {
  return { params: {} };
}

export const UpdateAcquisitionParamsRequest: MessageFns<UpdateAcquisitionParamsRequest> = {
  encode(message: UpdateAcquisitionParamsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.params).forEach(([key, value]) => {
      UpdateAcquisitionParamsRequest_ParamsEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateAcquisitionParamsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateAcquisitionParamsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = UpdateAcquisitionParamsRequest_ParamsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.params[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateAcquisitionParamsRequest {
    return {
      params: isObject(object.params)
        ? Object.entries(object.params).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: UpdateAcquisitionParamsRequest): unknown {
    const obj: any = {};
    if (message.params) {
      const entries = Object.entries(message.params);
      if (entries.length > 0) {
        obj.params = {};
        entries.forEach(([k, v]) => {
          obj.params[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateAcquisitionParamsRequest>, I>>(base?: I): UpdateAcquisitionParamsRequest {
    return UpdateAcquisitionParamsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAcquisitionParamsRequest>, I>>(
    object: I,
  ): UpdateAcquisitionParamsRequest {
    const message = createBaseUpdateAcquisitionParamsRequest();
    message.params = Object.entries(object.params ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseUpdateAcquisitionParamsRequest_ParamsEntry(): UpdateAcquisitionParamsRequest_ParamsEntry {
  return { key: "", value: "" };
}

export const UpdateAcquisitionParamsRequest_ParamsEntry: MessageFns<UpdateAcquisitionParamsRequest_ParamsEntry> = {
  encode(message: UpdateAcquisitionParamsRequest_ParamsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateAcquisitionParamsRequest_ParamsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateAcquisitionParamsRequest_ParamsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateAcquisitionParamsRequest_ParamsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: UpdateAcquisitionParamsRequest_ParamsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateAcquisitionParamsRequest_ParamsEntry>, I>>(
    base?: I,
  ): UpdateAcquisitionParamsRequest_ParamsEntry {
    return UpdateAcquisitionParamsRequest_ParamsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAcquisitionParamsRequest_ParamsEntry>, I>>(
    object: I,
  ): UpdateAcquisitionParamsRequest_ParamsEntry {
    const message = createBaseUpdateAcquisitionParamsRequest_ParamsEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseUpdateAcquisitionParamsResponse(): UpdateAcquisitionParamsResponse {
  return { paramsUpdated: false };
}

export const UpdateAcquisitionParamsResponse: MessageFns<UpdateAcquisitionParamsResponse> = {
  encode(message: UpdateAcquisitionParamsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.paramsUpdated !== false) {
      writer.uint32(8).bool(message.paramsUpdated);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateAcquisitionParamsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateAcquisitionParamsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.paramsUpdated = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateAcquisitionParamsResponse {
    return { paramsUpdated: isSet(object.paramsUpdated) ? globalThis.Boolean(object.paramsUpdated) : false };
  },

  toJSON(message: UpdateAcquisitionParamsResponse): unknown {
    const obj: any = {};
    if (message.paramsUpdated !== false) {
      obj.paramsUpdated = message.paramsUpdated;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateAcquisitionParamsResponse>, I>>(base?: I): UpdateAcquisitionParamsResponse {
    return UpdateAcquisitionParamsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAcquisitionParamsResponse>, I>>(
    object: I,
  ): UpdateAcquisitionParamsResponse {
    const message = createBaseUpdateAcquisitionParamsResponse();
    message.paramsUpdated = object.paramsUpdated ?? false;
    return message;
  },
};

function createBaseDeleteUserRequest(): DeleteUserRequest {
  return { userId: "", reason: "" };
}

export const DeleteUserRequest: MessageFns<DeleteUserRequest> = {
  encode(message: DeleteUserRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.reason !== "") {
      writer.uint32(18).string(message.reason);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteUserRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteUserRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.reason = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteUserRequest {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      reason: isSet(object.reason) ? globalThis.String(object.reason) : "",
    };
  },

  toJSON(message: DeleteUserRequest): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.reason !== "") {
      obj.reason = message.reason;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteUserRequest>, I>>(base?: I): DeleteUserRequest {
    return DeleteUserRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteUserRequest>, I>>(object: I): DeleteUserRequest {
    const message = createBaseDeleteUserRequest();
    message.userId = object.userId ?? "";
    message.reason = object.reason ?? "";
    return message;
  },
};

function createBaseDeleteUserResponse(): DeleteUserResponse {
  return {};
}

export const DeleteUserResponse: MessageFns<DeleteUserResponse> = {
  encode(_: DeleteUserResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteUserResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteUserResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeleteUserResponse {
    return {};
  },

  toJSON(_: DeleteUserResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteUserResponse>, I>>(base?: I): DeleteUserResponse {
    return DeleteUserResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteUserResponse>, I>>(_: I): DeleteUserResponse {
    const message = createBaseDeleteUserResponse();
    return message;
  },
};

function createBaseCreditScorePanResponse(): CreditScorePanResponse {
  return { panNumber: "" };
}

export const CreditScorePanResponse: MessageFns<CreditScorePanResponse> = {
  encode(message: CreditScorePanResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.panNumber !== "") {
      writer.uint32(10).string(message.panNumber);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreditScorePanResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreditScorePanResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.panNumber = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreditScorePanResponse {
    return { panNumber: isSet(object.panNumber) ? globalThis.String(object.panNumber) : "" };
  },

  toJSON(message: CreditScorePanResponse): unknown {
    const obj: any = {};
    if (message.panNumber !== "") {
      obj.panNumber = message.panNumber;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreditScorePanResponse>, I>>(base?: I): CreditScorePanResponse {
    return CreditScorePanResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreditScorePanResponse>, I>>(object: I): CreditScorePanResponse {
    const message = createBaseCreditScorePanResponse();
    message.panNumber = object.panNumber ?? "";
    return message;
  },
};

function createBaseCityDataResponse(): CityDataResponse {
  return { isCityInvestorVisible: false, cityName: "", investorCityCount: 0 };
}

export const CityDataResponse: MessageFns<CityDataResponse> = {
  encode(message: CityDataResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isCityInvestorVisible !== false) {
      writer.uint32(8).bool(message.isCityInvestorVisible);
    }
    if (message.cityName !== "") {
      writer.uint32(18).string(message.cityName);
    }
    if (message.investorCityCount !== 0) {
      writer.uint32(24).int64(message.investorCityCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CityDataResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCityDataResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isCityInvestorVisible = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.cityName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.investorCityCount = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CityDataResponse {
    return {
      isCityInvestorVisible: isSet(object.isCityInvestorVisible)
        ? globalThis.Boolean(object.isCityInvestorVisible)
        : false,
      cityName: isSet(object.cityName) ? globalThis.String(object.cityName) : "",
      investorCityCount: isSet(object.investorCityCount) ? globalThis.Number(object.investorCityCount) : 0,
    };
  },

  toJSON(message: CityDataResponse): unknown {
    const obj: any = {};
    if (message.isCityInvestorVisible !== false) {
      obj.isCityInvestorVisible = message.isCityInvestorVisible;
    }
    if (message.cityName !== "") {
      obj.cityName = message.cityName;
    }
    if (message.investorCityCount !== 0) {
      obj.investorCityCount = Math.round(message.investorCityCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CityDataResponse>, I>>(base?: I): CityDataResponse {
    return CityDataResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CityDataResponse>, I>>(object: I): CityDataResponse {
    const message = createBaseCityDataResponse();
    message.isCityInvestorVisible = object.isCityInvestorVisible ?? false;
    message.cityName = object.cityName ?? "";
    message.investorCityCount = object.investorCityCount ?? 0;
    return message;
  },
};

function createBasePanDetailsResponse(): PanDetailsResponse {
  return { panNumber: "", dob: "", isPanAvailable: false };
}

export const PanDetailsResponse: MessageFns<PanDetailsResponse> = {
  encode(message: PanDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.panNumber !== "") {
      writer.uint32(10).string(message.panNumber);
    }
    if (message.dob !== "") {
      writer.uint32(18).string(message.dob);
    }
    if (message.isPanAvailable !== false) {
      writer.uint32(24).bool(message.isPanAvailable);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PanDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePanDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.panNumber = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.dob = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isPanAvailable = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PanDetailsResponse {
    return {
      panNumber: isSet(object.panNumber) ? globalThis.String(object.panNumber) : "",
      dob: isSet(object.dob) ? globalThis.String(object.dob) : "",
      isPanAvailable: isSet(object.isPanAvailable) ? globalThis.Boolean(object.isPanAvailable) : false,
    };
  },

  toJSON(message: PanDetailsResponse): unknown {
    const obj: any = {};
    if (message.panNumber !== "") {
      obj.panNumber = message.panNumber;
    }
    if (message.dob !== "") {
      obj.dob = message.dob;
    }
    if (message.isPanAvailable !== false) {
      obj.isPanAvailable = message.isPanAvailable;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PanDetailsResponse>, I>>(base?: I): PanDetailsResponse {
    return PanDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PanDetailsResponse>, I>>(object: I): PanDetailsResponse {
    const message = createBasePanDetailsResponse();
    message.panNumber = object.panNumber ?? "";
    message.dob = object.dob ?? "";
    message.isPanAvailable = object.isPanAvailable ?? false;
    return message;
  },
};

function createBaseUserDetailsResponse(): UserDetailsResponse {
  return { userId: "", firstName: undefined, lastName: undefined, profileImageUrl: undefined };
}

export const UserDetailsResponse: MessageFns<UserDetailsResponse> = {
  encode(message: UserDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.firstName !== undefined) {
      writer.uint32(18).string(message.firstName);
    }
    if (message.lastName !== undefined) {
      writer.uint32(26).string(message.lastName);
    }
    if (message.profileImageUrl !== undefined) {
      writer.uint32(34).string(message.profileImageUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.profileImageUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserDetailsResponse {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      firstName: isSet(object.firstName) ? globalThis.String(object.firstName) : undefined,
      lastName: isSet(object.lastName) ? globalThis.String(object.lastName) : undefined,
      profileImageUrl: isSet(object.profileImageUrl) ? globalThis.String(object.profileImageUrl) : undefined,
    };
  },

  toJSON(message: UserDetailsResponse): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.firstName !== undefined) {
      obj.firstName = message.firstName;
    }
    if (message.lastName !== undefined) {
      obj.lastName = message.lastName;
    }
    if (message.profileImageUrl !== undefined) {
      obj.profileImageUrl = message.profileImageUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserDetailsResponse>, I>>(base?: I): UserDetailsResponse {
    return UserDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserDetailsResponse>, I>>(object: I): UserDetailsResponse {
    const message = createBaseUserDetailsResponse();
    message.userId = object.userId ?? "";
    message.firstName = object.firstName ?? undefined;
    message.lastName = object.lastName ?? undefined;
    message.profileImageUrl = object.profileImageUrl ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
