// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: CollectionV2.proto

/* eslint-disable */
import { Binary<PERSON><PERSON>er, BinaryWriter } from "@bufbuild/protobuf/wire";
import { TagConfig, Tenure } from "./Bank";
import {
  InvestabilityStatus,
  investabilityStatusFromJSON,
  investabilityStatusToJSON,
  InvestorType,
  investorTypeFromJSON,
  investorTypeToJSON,
  RedirectDeeplink,
} from "./BusinessCommon";
import { PaginationRequest, PaginationResponse } from "./Common";
import { Empty } from "./google/protobuf/empty";

export const protobufPackage = "com.stablemoney.api.business.collection";

export enum CollectionType {
  MANUAL = 0,
  EXPRESSION = 1,
  UNRECOGNIZED = -1,
}

export function collectionTypeFromJSON(object: any): CollectionType {
  switch (object) {
    case 0:
    case "MANUAL":
      return CollectionType.MANUAL;
    case 1:
    case "EXPRESSION":
      return CollectionType.EXPRESSION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CollectionType.UNRECOGNIZED;
  }
}

export function collectionTypeToJSON(object: CollectionType): string {
  switch (object) {
    case CollectionType.MANUAL:
      return "MANUAL";
    case CollectionType.EXPRESSION:
      return "EXPRESSION";
    case CollectionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface CollectionItem {
  fdId: string;
  bankId: string;
  rate: number;
  tenure: Tenure | undefined;
  tagConfig: TagConfig | undefined;
  deeplink: RedirectDeeplink | undefined;
}

export interface BulkCollectionRequest {
  collectionNames: string[];
  investorType: InvestorType;
}

export interface BulkCollectionResponse {
  collections: CollectionResponse[];
}

export interface CollectionResponse {
  id: string;
  name: string;
  title: string;
  description: string;
  shortTitle: string;
  shortDescription: string;
  iconUrl: string;
  backgroundColor: string;
  collectionItems: CollectionItem[];
  banks: CollectionResponse_Bank[];
}

export interface CollectionResponse_Bank {
  id: string;
  name: string;
  shortName: string;
  logoUrl: string;
  isPopular: boolean;
  color: string;
  coverImageUrl: string;
  iconBgColor: string;
  investabilityStatus: InvestabilityStatus;
}

export interface CollectionEntity {
  id: string;
  name: string;
  title: string;
  description: string;
  shortTitle: string;
  shortDescription: string;
  iconUrl: string;
  collectionType: CollectionType;
  mappedCollectionConfig: string;
  excludeInactiveBanks: boolean;
  /** @deprecated */
  expressionWhereClause: string;
  /** @deprecated */
  expressionOrderByClause: string;
  createdAt: number;
  updatedAt: number;
  backgroundColor: string;
  isSortByInterestRate: boolean;
  isPickHighestInterestRate: boolean;
}

export interface UpdateCollectionRequestProto {
  id: string;
  name: string;
  title: string;
  description: string;
  shortTitle: string;
  shortDescription: string;
  iconUrl: string;
  collectionType: CollectionType;
  mappedCollectionConfig: string;
  excludeInactiveBanks: boolean;
  /** @deprecated */
  expressionWhereClause: string;
  /** @deprecated */
  expressionOrderByClause: string;
  backgroundColor: string;
  isSortByInterestRate: boolean;
  isPickHighestInterestRate: boolean;
}

export interface CreateCollectionRequestProto {
  name: string;
  title: string;
  description: string;
  shortTitle: string;
  shortDescription: string;
  iconUrl: string;
  collectionType: CollectionType;
  mappedCollectionConfig: string;
  excludeInactiveBanks: boolean;
  /** @deprecated */
  expressionWhereClause: string;
  /** @deprecated */
  expressionOrderByClause: string;
  backgroundColor: string;
  isSortByInterestRate: boolean;
  isPickHighestInterestRate: boolean;
}

export interface DeleteCollectionRequestProto {
  id: string;
}

export interface CollectionsRequestProto {
  pagination: PaginationRequest | undefined;
  q?: string | undefined;
}

export interface CollectionsResponseProto {
  collections: CollectionEntity[];
  pagination: PaginationResponse | undefined;
}

export interface StatusResponseProto {
  status: boolean;
}

export interface CollectionRequestProto {
  id: string;
}

export interface CollectionResponseProto {
  collection: CollectionEntity | undefined;
}

export interface CollectionItemsRequestProto {
  collectionId: string;
}

export interface CollectionItemEntity {
  id: string;
  bankId: string;
  bankName: string;
  minTenureInDays: number;
  maxTenureInDays: number;
  priority: number;
  displayTitle: string;
  tagConfig: TagConfig | undefined;
  redirectDeeplink: RedirectDeeplink | undefined;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

export interface CollectionItemsResponseProto {
  collectionItems: CollectionItemEntity[];
}

export interface CollectionItemRequestProto {
  id: string;
}

export interface CollectionItemResponseProto {
  collectionItem: CollectionItemEntity | undefined;
}

export interface DeleteCollectionItemRequestProto {
  id: string;
}

export interface UpdateCollectionItemRequestProto {
  id: string;
  collectionId: string;
  bankId: string;
  minTenureInDays: number;
  maxTenureInDays: number;
  priority: number;
  displayTitle: string;
  tagConfig: TagConfig | undefined;
  redirectDeeplink: RedirectDeeplink | undefined;
}

export interface CreateCollectionItemRequestProto {
  collectionId: string;
  bankId: string;
  minTenureInDays: number;
  maxTenureInDays: number;
  priority: number;
  displayTitle: string;
  tagConfig: TagConfig | undefined;
  redirectDeeplink: RedirectDeeplink | undefined;
}

export interface BankTenureRequest {
  empty: Empty | undefined;
}

export interface BankTenureEntity {
  bankName: string;
  bankId: string;
  tenures: BankTenureEntity_TenureRange[];
}

export interface BankTenureEntity_TenureRange {
  minTenureInDays: number;
  maxTenureInDays: number;
}

export interface BankTenureResponse {
  bankTenureList: BankTenureEntity[];
}

export interface BulkUpdateFdItemPriorityRequest {
  items: BulkUpdateFdItemPriorityRequest_Item[];
}

export interface BulkUpdateFdItemPriorityRequest_Item {
  id: string;
  newPriority: number;
}

export interface BulkUpdateFdItemPriorityResponse {
}

function createBaseCollectionItem(): CollectionItem {
  return { fdId: "", bankId: "", rate: 0, tenure: undefined, tagConfig: undefined, deeplink: undefined };
}

export const CollectionItem: MessageFns<CollectionItem> = {
  encode(message: CollectionItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdId !== "") {
      writer.uint32(10).string(message.fdId);
    }
    if (message.bankId !== "") {
      writer.uint32(26).string(message.bankId);
    }
    if (message.rate !== 0) {
      writer.uint32(41).double(message.rate);
    }
    if (message.tenure !== undefined) {
      Tenure.encode(message.tenure, writer.uint32(58).fork()).join();
    }
    if (message.tagConfig !== undefined) {
      TagConfig.encode(message.tagConfig, writer.uint32(74).fork()).join();
    }
    if (message.deeplink !== undefined) {
      RedirectDeeplink.encode(message.deeplink, writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fdId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.tenure = Tenure.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.tagConfig = TagConfig.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.deeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionItem {
    return {
      fdId: isSet(object.fdId) ? globalThis.String(object.fdId) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      tenure: isSet(object.tenure) ? Tenure.fromJSON(object.tenure) : undefined,
      tagConfig: isSet(object.tagConfig) ? TagConfig.fromJSON(object.tagConfig) : undefined,
      deeplink: isSet(object.deeplink) ? RedirectDeeplink.fromJSON(object.deeplink) : undefined,
    };
  },

  toJSON(message: CollectionItem): unknown {
    const obj: any = {};
    if (message.fdId !== "") {
      obj.fdId = message.fdId;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.tenure !== undefined) {
      obj.tenure = Tenure.toJSON(message.tenure);
    }
    if (message.tagConfig !== undefined) {
      obj.tagConfig = TagConfig.toJSON(message.tagConfig);
    }
    if (message.deeplink !== undefined) {
      obj.deeplink = RedirectDeeplink.toJSON(message.deeplink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionItem>, I>>(base?: I): CollectionItem {
    return CollectionItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionItem>, I>>(object: I): CollectionItem {
    const message = createBaseCollectionItem();
    message.fdId = object.fdId ?? "";
    message.bankId = object.bankId ?? "";
    message.rate = object.rate ?? 0;
    message.tenure = (object.tenure !== undefined && object.tenure !== null)
      ? Tenure.fromPartial(object.tenure)
      : undefined;
    message.tagConfig = (object.tagConfig !== undefined && object.tagConfig !== null)
      ? TagConfig.fromPartial(object.tagConfig)
      : undefined;
    message.deeplink = (object.deeplink !== undefined && object.deeplink !== null)
      ? RedirectDeeplink.fromPartial(object.deeplink)
      : undefined;
    return message;
  },
};

function createBaseBulkCollectionRequest(): BulkCollectionRequest {
  return { collectionNames: [], investorType: 0 };
}

export const BulkCollectionRequest: MessageFns<BulkCollectionRequest> = {
  encode(message: BulkCollectionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.collectionNames) {
      writer.uint32(10).string(v!);
    }
    if (message.investorType !== 0) {
      writer.uint32(16).int32(message.investorType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkCollectionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkCollectionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collectionNames.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.investorType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkCollectionRequest {
    return {
      collectionNames: globalThis.Array.isArray(object?.collectionNames)
        ? object.collectionNames.map((e: any) => globalThis.String(e))
        : [],
      investorType: isSet(object.investorType) ? investorTypeFromJSON(object.investorType) : 0,
    };
  },

  toJSON(message: BulkCollectionRequest): unknown {
    const obj: any = {};
    if (message.collectionNames?.length) {
      obj.collectionNames = message.collectionNames;
    }
    if (message.investorType !== 0) {
      obj.investorType = investorTypeToJSON(message.investorType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkCollectionRequest>, I>>(base?: I): BulkCollectionRequest {
    return BulkCollectionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkCollectionRequest>, I>>(object: I): BulkCollectionRequest {
    const message = createBaseBulkCollectionRequest();
    message.collectionNames = object.collectionNames?.map((e) => e) || [];
    message.investorType = object.investorType ?? 0;
    return message;
  },
};

function createBaseBulkCollectionResponse(): BulkCollectionResponse {
  return { collections: [] };
}

export const BulkCollectionResponse: MessageFns<BulkCollectionResponse> = {
  encode(message: BulkCollectionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.collections) {
      CollectionResponse.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkCollectionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkCollectionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collections.push(CollectionResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkCollectionResponse {
    return {
      collections: globalThis.Array.isArray(object?.collections)
        ? object.collections.map((e: any) => CollectionResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BulkCollectionResponse): unknown {
    const obj: any = {};
    if (message.collections?.length) {
      obj.collections = message.collections.map((e) => CollectionResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkCollectionResponse>, I>>(base?: I): BulkCollectionResponse {
    return BulkCollectionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkCollectionResponse>, I>>(object: I): BulkCollectionResponse {
    const message = createBaseBulkCollectionResponse();
    message.collections = object.collections?.map((e) => CollectionResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCollectionResponse(): CollectionResponse {
  return {
    id: "",
    name: "",
    title: "",
    description: "",
    shortTitle: "",
    shortDescription: "",
    iconUrl: "",
    backgroundColor: "",
    collectionItems: [],
    banks: [],
  };
}

export const CollectionResponse: MessageFns<CollectionResponse> = {
  encode(message: CollectionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.title !== "") {
      writer.uint32(26).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(42).string(message.description);
    }
    if (message.shortTitle !== "") {
      writer.uint32(34).string(message.shortTitle);
    }
    if (message.shortDescription !== "") {
      writer.uint32(58).string(message.shortDescription);
    }
    if (message.iconUrl !== "") {
      writer.uint32(74).string(message.iconUrl);
    }
    if (message.backgroundColor !== "") {
      writer.uint32(82).string(message.backgroundColor);
    }
    for (const v of message.collectionItems) {
      CollectionItem.encode(v!, writer.uint32(90).fork()).join();
    }
    for (const v of message.banks) {
      CollectionResponse_Bank.encode(v!, writer.uint32(106).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.shortTitle = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.shortDescription = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.backgroundColor = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.collectionItems.push(CollectionItem.decode(reader, reader.uint32()));
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.banks.push(CollectionResponse_Bank.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      shortTitle: isSet(object.shortTitle) ? globalThis.String(object.shortTitle) : "",
      shortDescription: isSet(object.shortDescription) ? globalThis.String(object.shortDescription) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      backgroundColor: isSet(object.backgroundColor) ? globalThis.String(object.backgroundColor) : "",
      collectionItems: globalThis.Array.isArray(object?.collectionItems)
        ? object.collectionItems.map((e: any) => CollectionItem.fromJSON(e))
        : [],
      banks: globalThis.Array.isArray(object?.banks)
        ? object.banks.map((e: any) => CollectionResponse_Bank.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CollectionResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.shortTitle !== "") {
      obj.shortTitle = message.shortTitle;
    }
    if (message.shortDescription !== "") {
      obj.shortDescription = message.shortDescription;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.backgroundColor !== "") {
      obj.backgroundColor = message.backgroundColor;
    }
    if (message.collectionItems?.length) {
      obj.collectionItems = message.collectionItems.map((e) => CollectionItem.toJSON(e));
    }
    if (message.banks?.length) {
      obj.banks = message.banks.map((e) => CollectionResponse_Bank.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionResponse>, I>>(base?: I): CollectionResponse {
    return CollectionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionResponse>, I>>(object: I): CollectionResponse {
    const message = createBaseCollectionResponse();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.shortTitle = object.shortTitle ?? "";
    message.shortDescription = object.shortDescription ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.backgroundColor = object.backgroundColor ?? "";
    message.collectionItems = object.collectionItems?.map((e) => CollectionItem.fromPartial(e)) || [];
    message.banks = object.banks?.map((e) => CollectionResponse_Bank.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCollectionResponse_Bank(): CollectionResponse_Bank {
  return {
    id: "",
    name: "",
    shortName: "",
    logoUrl: "",
    isPopular: false,
    color: "",
    coverImageUrl: "",
    iconBgColor: "",
    investabilityStatus: 0,
  };
}

export const CollectionResponse_Bank: MessageFns<CollectionResponse_Bank> = {
  encode(message: CollectionResponse_Bank, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(34).string(message.name);
    }
    if (message.shortName !== "") {
      writer.uint32(50).string(message.shortName);
    }
    if (message.logoUrl !== "") {
      writer.uint32(66).string(message.logoUrl);
    }
    if (message.isPopular !== false) {
      writer.uint32(96).bool(message.isPopular);
    }
    if (message.color !== "") {
      writer.uint32(130).string(message.color);
    }
    if (message.coverImageUrl !== "") {
      writer.uint32(162).string(message.coverImageUrl);
    }
    if (message.iconBgColor !== "") {
      writer.uint32(178).string(message.iconBgColor);
    }
    if (message.investabilityStatus !== 0) {
      writer.uint32(192).int32(message.investabilityStatus);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionResponse_Bank {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionResponse_Bank();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.shortName = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.isPopular = reader.bool();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.coverImageUrl = reader.string();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.iconBgColor = reader.string();
          continue;
        }
        case 24: {
          if (tag !== 192) {
            break;
          }

          message.investabilityStatus = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionResponse_Bank {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      shortName: isSet(object.shortName) ? globalThis.String(object.shortName) : "",
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
      isPopular: isSet(object.isPopular) ? globalThis.Boolean(object.isPopular) : false,
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      coverImageUrl: isSet(object.coverImageUrl) ? globalThis.String(object.coverImageUrl) : "",
      iconBgColor: isSet(object.iconBgColor) ? globalThis.String(object.iconBgColor) : "",
      investabilityStatus: isSet(object.investabilityStatus)
        ? investabilityStatusFromJSON(object.investabilityStatus)
        : 0,
    };
  },

  toJSON(message: CollectionResponse_Bank): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.shortName !== "") {
      obj.shortName = message.shortName;
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    if (message.isPopular !== false) {
      obj.isPopular = message.isPopular;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.coverImageUrl !== "") {
      obj.coverImageUrl = message.coverImageUrl;
    }
    if (message.iconBgColor !== "") {
      obj.iconBgColor = message.iconBgColor;
    }
    if (message.investabilityStatus !== 0) {
      obj.investabilityStatus = investabilityStatusToJSON(message.investabilityStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionResponse_Bank>, I>>(base?: I): CollectionResponse_Bank {
    return CollectionResponse_Bank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionResponse_Bank>, I>>(object: I): CollectionResponse_Bank {
    const message = createBaseCollectionResponse_Bank();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.shortName = object.shortName ?? "";
    message.logoUrl = object.logoUrl ?? "";
    message.isPopular = object.isPopular ?? false;
    message.color = object.color ?? "";
    message.coverImageUrl = object.coverImageUrl ?? "";
    message.iconBgColor = object.iconBgColor ?? "";
    message.investabilityStatus = object.investabilityStatus ?? 0;
    return message;
  },
};

function createBaseCollectionEntity(): CollectionEntity {
  return {
    id: "",
    name: "",
    title: "",
    description: "",
    shortTitle: "",
    shortDescription: "",
    iconUrl: "",
    collectionType: 0,
    mappedCollectionConfig: "",
    excludeInactiveBanks: false,
    expressionWhereClause: "",
    expressionOrderByClause: "",
    createdAt: 0,
    updatedAt: 0,
    backgroundColor: "",
    isSortByInterestRate: false,
    isPickHighestInterestRate: false,
  };
}

export const CollectionEntity: MessageFns<CollectionEntity> = {
  encode(message: CollectionEntity, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.title !== "") {
      writer.uint32(26).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.shortTitle !== "") {
      writer.uint32(42).string(message.shortTitle);
    }
    if (message.shortDescription !== "") {
      writer.uint32(50).string(message.shortDescription);
    }
    if (message.iconUrl !== "") {
      writer.uint32(58).string(message.iconUrl);
    }
    if (message.collectionType !== 0) {
      writer.uint32(64).int32(message.collectionType);
    }
    if (message.mappedCollectionConfig !== "") {
      writer.uint32(74).string(message.mappedCollectionConfig);
    }
    if (message.excludeInactiveBanks !== false) {
      writer.uint32(80).bool(message.excludeInactiveBanks);
    }
    if (message.expressionWhereClause !== "") {
      writer.uint32(90).string(message.expressionWhereClause);
    }
    if (message.expressionOrderByClause !== "") {
      writer.uint32(98).string(message.expressionOrderByClause);
    }
    if (message.createdAt !== 0) {
      writer.uint32(105).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(113).double(message.updatedAt);
    }
    if (message.backgroundColor !== "") {
      writer.uint32(122).string(message.backgroundColor);
    }
    if (message.isSortByInterestRate !== false) {
      writer.uint32(128).bool(message.isSortByInterestRate);
    }
    if (message.isPickHighestInterestRate !== false) {
      writer.uint32(136).bool(message.isPickHighestInterestRate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionEntity {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionEntity();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.shortTitle = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.shortDescription = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.collectionType = reader.int32() as any;
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.mappedCollectionConfig = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.excludeInactiveBanks = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.expressionWhereClause = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.expressionOrderByClause = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 105) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 14: {
          if (tag !== 113) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.backgroundColor = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.isSortByInterestRate = reader.bool();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.isPickHighestInterestRate = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionEntity {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      shortTitle: isSet(object.shortTitle) ? globalThis.String(object.shortTitle) : "",
      shortDescription: isSet(object.shortDescription) ? globalThis.String(object.shortDescription) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      collectionType: isSet(object.collectionType) ? collectionTypeFromJSON(object.collectionType) : 0,
      mappedCollectionConfig: isSet(object.mappedCollectionConfig)
        ? globalThis.String(object.mappedCollectionConfig)
        : "",
      excludeInactiveBanks: isSet(object.excludeInactiveBanks)
        ? globalThis.Boolean(object.excludeInactiveBanks)
        : false,
      expressionWhereClause: isSet(object.expressionWhereClause) ? globalThis.String(object.expressionWhereClause) : "",
      expressionOrderByClause: isSet(object.expressionOrderByClause)
        ? globalThis.String(object.expressionOrderByClause)
        : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      backgroundColor: isSet(object.backgroundColor) ? globalThis.String(object.backgroundColor) : "",
      isSortByInterestRate: isSet(object.isSortByInterestRate)
        ? globalThis.Boolean(object.isSortByInterestRate)
        : false,
      isPickHighestInterestRate: isSet(object.isPickHighestInterestRate)
        ? globalThis.Boolean(object.isPickHighestInterestRate)
        : false,
    };
  },

  toJSON(message: CollectionEntity): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.shortTitle !== "") {
      obj.shortTitle = message.shortTitle;
    }
    if (message.shortDescription !== "") {
      obj.shortDescription = message.shortDescription;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.collectionType !== 0) {
      obj.collectionType = collectionTypeToJSON(message.collectionType);
    }
    if (message.mappedCollectionConfig !== "") {
      obj.mappedCollectionConfig = message.mappedCollectionConfig;
    }
    if (message.excludeInactiveBanks !== false) {
      obj.excludeInactiveBanks = message.excludeInactiveBanks;
    }
    if (message.expressionWhereClause !== "") {
      obj.expressionWhereClause = message.expressionWhereClause;
    }
    if (message.expressionOrderByClause !== "") {
      obj.expressionOrderByClause = message.expressionOrderByClause;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.backgroundColor !== "") {
      obj.backgroundColor = message.backgroundColor;
    }
    if (message.isSortByInterestRate !== false) {
      obj.isSortByInterestRate = message.isSortByInterestRate;
    }
    if (message.isPickHighestInterestRate !== false) {
      obj.isPickHighestInterestRate = message.isPickHighestInterestRate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionEntity>, I>>(base?: I): CollectionEntity {
    return CollectionEntity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionEntity>, I>>(object: I): CollectionEntity {
    const message = createBaseCollectionEntity();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.shortTitle = object.shortTitle ?? "";
    message.shortDescription = object.shortDescription ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.collectionType = object.collectionType ?? 0;
    message.mappedCollectionConfig = object.mappedCollectionConfig ?? "";
    message.excludeInactiveBanks = object.excludeInactiveBanks ?? false;
    message.expressionWhereClause = object.expressionWhereClause ?? "";
    message.expressionOrderByClause = object.expressionOrderByClause ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.backgroundColor = object.backgroundColor ?? "";
    message.isSortByInterestRate = object.isSortByInterestRate ?? false;
    message.isPickHighestInterestRate = object.isPickHighestInterestRate ?? false;
    return message;
  },
};

function createBaseUpdateCollectionRequestProto(): UpdateCollectionRequestProto {
  return {
    id: "",
    name: "",
    title: "",
    description: "",
    shortTitle: "",
    shortDescription: "",
    iconUrl: "",
    collectionType: 0,
    mappedCollectionConfig: "",
    excludeInactiveBanks: false,
    expressionWhereClause: "",
    expressionOrderByClause: "",
    backgroundColor: "",
    isSortByInterestRate: false,
    isPickHighestInterestRate: false,
  };
}

export const UpdateCollectionRequestProto: MessageFns<UpdateCollectionRequestProto> = {
  encode(message: UpdateCollectionRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.title !== "") {
      writer.uint32(26).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.shortTitle !== "") {
      writer.uint32(42).string(message.shortTitle);
    }
    if (message.shortDescription !== "") {
      writer.uint32(50).string(message.shortDescription);
    }
    if (message.iconUrl !== "") {
      writer.uint32(58).string(message.iconUrl);
    }
    if (message.collectionType !== 0) {
      writer.uint32(64).int32(message.collectionType);
    }
    if (message.mappedCollectionConfig !== "") {
      writer.uint32(74).string(message.mappedCollectionConfig);
    }
    if (message.excludeInactiveBanks !== false) {
      writer.uint32(80).bool(message.excludeInactiveBanks);
    }
    if (message.expressionWhereClause !== "") {
      writer.uint32(90).string(message.expressionWhereClause);
    }
    if (message.expressionOrderByClause !== "") {
      writer.uint32(98).string(message.expressionOrderByClause);
    }
    if (message.backgroundColor !== "") {
      writer.uint32(106).string(message.backgroundColor);
    }
    if (message.isSortByInterestRate !== false) {
      writer.uint32(112).bool(message.isSortByInterestRate);
    }
    if (message.isPickHighestInterestRate !== false) {
      writer.uint32(120).bool(message.isPickHighestInterestRate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCollectionRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCollectionRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.shortTitle = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.shortDescription = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.collectionType = reader.int32() as any;
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.mappedCollectionConfig = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.excludeInactiveBanks = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.expressionWhereClause = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.expressionOrderByClause = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.backgroundColor = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.isSortByInterestRate = reader.bool();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.isPickHighestInterestRate = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateCollectionRequestProto {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      shortTitle: isSet(object.shortTitle) ? globalThis.String(object.shortTitle) : "",
      shortDescription: isSet(object.shortDescription) ? globalThis.String(object.shortDescription) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      collectionType: isSet(object.collectionType) ? collectionTypeFromJSON(object.collectionType) : 0,
      mappedCollectionConfig: isSet(object.mappedCollectionConfig)
        ? globalThis.String(object.mappedCollectionConfig)
        : "",
      excludeInactiveBanks: isSet(object.excludeInactiveBanks)
        ? globalThis.Boolean(object.excludeInactiveBanks)
        : false,
      expressionWhereClause: isSet(object.expressionWhereClause) ? globalThis.String(object.expressionWhereClause) : "",
      expressionOrderByClause: isSet(object.expressionOrderByClause)
        ? globalThis.String(object.expressionOrderByClause)
        : "",
      backgroundColor: isSet(object.backgroundColor) ? globalThis.String(object.backgroundColor) : "",
      isSortByInterestRate: isSet(object.isSortByInterestRate)
        ? globalThis.Boolean(object.isSortByInterestRate)
        : false,
      isPickHighestInterestRate: isSet(object.isPickHighestInterestRate)
        ? globalThis.Boolean(object.isPickHighestInterestRate)
        : false,
    };
  },

  toJSON(message: UpdateCollectionRequestProto): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.shortTitle !== "") {
      obj.shortTitle = message.shortTitle;
    }
    if (message.shortDescription !== "") {
      obj.shortDescription = message.shortDescription;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.collectionType !== 0) {
      obj.collectionType = collectionTypeToJSON(message.collectionType);
    }
    if (message.mappedCollectionConfig !== "") {
      obj.mappedCollectionConfig = message.mappedCollectionConfig;
    }
    if (message.excludeInactiveBanks !== false) {
      obj.excludeInactiveBanks = message.excludeInactiveBanks;
    }
    if (message.expressionWhereClause !== "") {
      obj.expressionWhereClause = message.expressionWhereClause;
    }
    if (message.expressionOrderByClause !== "") {
      obj.expressionOrderByClause = message.expressionOrderByClause;
    }
    if (message.backgroundColor !== "") {
      obj.backgroundColor = message.backgroundColor;
    }
    if (message.isSortByInterestRate !== false) {
      obj.isSortByInterestRate = message.isSortByInterestRate;
    }
    if (message.isPickHighestInterestRate !== false) {
      obj.isPickHighestInterestRate = message.isPickHighestInterestRate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateCollectionRequestProto>, I>>(base?: I): UpdateCollectionRequestProto {
    return UpdateCollectionRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateCollectionRequestProto>, I>>(object: I): UpdateCollectionRequestProto {
    const message = createBaseUpdateCollectionRequestProto();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.shortTitle = object.shortTitle ?? "";
    message.shortDescription = object.shortDescription ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.collectionType = object.collectionType ?? 0;
    message.mappedCollectionConfig = object.mappedCollectionConfig ?? "";
    message.excludeInactiveBanks = object.excludeInactiveBanks ?? false;
    message.expressionWhereClause = object.expressionWhereClause ?? "";
    message.expressionOrderByClause = object.expressionOrderByClause ?? "";
    message.backgroundColor = object.backgroundColor ?? "";
    message.isSortByInterestRate = object.isSortByInterestRate ?? false;
    message.isPickHighestInterestRate = object.isPickHighestInterestRate ?? false;
    return message;
  },
};

function createBaseCreateCollectionRequestProto(): CreateCollectionRequestProto {
  return {
    name: "",
    title: "",
    description: "",
    shortTitle: "",
    shortDescription: "",
    iconUrl: "",
    collectionType: 0,
    mappedCollectionConfig: "",
    excludeInactiveBanks: false,
    expressionWhereClause: "",
    expressionOrderByClause: "",
    backgroundColor: "",
    isSortByInterestRate: false,
    isPickHighestInterestRate: false,
  };
}

export const CreateCollectionRequestProto: MessageFns<CreateCollectionRequestProto> = {
  encode(message: CreateCollectionRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.title !== "") {
      writer.uint32(26).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.shortTitle !== "") {
      writer.uint32(42).string(message.shortTitle);
    }
    if (message.shortDescription !== "") {
      writer.uint32(50).string(message.shortDescription);
    }
    if (message.iconUrl !== "") {
      writer.uint32(58).string(message.iconUrl);
    }
    if (message.collectionType !== 0) {
      writer.uint32(64).int32(message.collectionType);
    }
    if (message.mappedCollectionConfig !== "") {
      writer.uint32(74).string(message.mappedCollectionConfig);
    }
    if (message.excludeInactiveBanks !== false) {
      writer.uint32(80).bool(message.excludeInactiveBanks);
    }
    if (message.expressionWhereClause !== "") {
      writer.uint32(90).string(message.expressionWhereClause);
    }
    if (message.expressionOrderByClause !== "") {
      writer.uint32(98).string(message.expressionOrderByClause);
    }
    if (message.backgroundColor !== "") {
      writer.uint32(106).string(message.backgroundColor);
    }
    if (message.isSortByInterestRate !== false) {
      writer.uint32(112).bool(message.isSortByInterestRate);
    }
    if (message.isPickHighestInterestRate !== false) {
      writer.uint32(120).bool(message.isPickHighestInterestRate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateCollectionRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateCollectionRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.shortTitle = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.shortDescription = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.collectionType = reader.int32() as any;
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.mappedCollectionConfig = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.excludeInactiveBanks = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.expressionWhereClause = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.expressionOrderByClause = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.backgroundColor = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.isSortByInterestRate = reader.bool();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.isPickHighestInterestRate = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateCollectionRequestProto {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      shortTitle: isSet(object.shortTitle) ? globalThis.String(object.shortTitle) : "",
      shortDescription: isSet(object.shortDescription) ? globalThis.String(object.shortDescription) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      collectionType: isSet(object.collectionType) ? collectionTypeFromJSON(object.collectionType) : 0,
      mappedCollectionConfig: isSet(object.mappedCollectionConfig)
        ? globalThis.String(object.mappedCollectionConfig)
        : "",
      excludeInactiveBanks: isSet(object.excludeInactiveBanks)
        ? globalThis.Boolean(object.excludeInactiveBanks)
        : false,
      expressionWhereClause: isSet(object.expressionWhereClause) ? globalThis.String(object.expressionWhereClause) : "",
      expressionOrderByClause: isSet(object.expressionOrderByClause)
        ? globalThis.String(object.expressionOrderByClause)
        : "",
      backgroundColor: isSet(object.backgroundColor) ? globalThis.String(object.backgroundColor) : "",
      isSortByInterestRate: isSet(object.isSortByInterestRate)
        ? globalThis.Boolean(object.isSortByInterestRate)
        : false,
      isPickHighestInterestRate: isSet(object.isPickHighestInterestRate)
        ? globalThis.Boolean(object.isPickHighestInterestRate)
        : false,
    };
  },

  toJSON(message: CreateCollectionRequestProto): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.shortTitle !== "") {
      obj.shortTitle = message.shortTitle;
    }
    if (message.shortDescription !== "") {
      obj.shortDescription = message.shortDescription;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.collectionType !== 0) {
      obj.collectionType = collectionTypeToJSON(message.collectionType);
    }
    if (message.mappedCollectionConfig !== "") {
      obj.mappedCollectionConfig = message.mappedCollectionConfig;
    }
    if (message.excludeInactiveBanks !== false) {
      obj.excludeInactiveBanks = message.excludeInactiveBanks;
    }
    if (message.expressionWhereClause !== "") {
      obj.expressionWhereClause = message.expressionWhereClause;
    }
    if (message.expressionOrderByClause !== "") {
      obj.expressionOrderByClause = message.expressionOrderByClause;
    }
    if (message.backgroundColor !== "") {
      obj.backgroundColor = message.backgroundColor;
    }
    if (message.isSortByInterestRate !== false) {
      obj.isSortByInterestRate = message.isSortByInterestRate;
    }
    if (message.isPickHighestInterestRate !== false) {
      obj.isPickHighestInterestRate = message.isPickHighestInterestRate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateCollectionRequestProto>, I>>(base?: I): CreateCollectionRequestProto {
    return CreateCollectionRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateCollectionRequestProto>, I>>(object: I): CreateCollectionRequestProto {
    const message = createBaseCreateCollectionRequestProto();
    message.name = object.name ?? "";
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.shortTitle = object.shortTitle ?? "";
    message.shortDescription = object.shortDescription ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.collectionType = object.collectionType ?? 0;
    message.mappedCollectionConfig = object.mappedCollectionConfig ?? "";
    message.excludeInactiveBanks = object.excludeInactiveBanks ?? false;
    message.expressionWhereClause = object.expressionWhereClause ?? "";
    message.expressionOrderByClause = object.expressionOrderByClause ?? "";
    message.backgroundColor = object.backgroundColor ?? "";
    message.isSortByInterestRate = object.isSortByInterestRate ?? false;
    message.isPickHighestInterestRate = object.isPickHighestInterestRate ?? false;
    return message;
  },
};

function createBaseDeleteCollectionRequestProto(): DeleteCollectionRequestProto {
  return { id: "" };
}

export const DeleteCollectionRequestProto: MessageFns<DeleteCollectionRequestProto> = {
  encode(message: DeleteCollectionRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteCollectionRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteCollectionRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteCollectionRequestProto {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: DeleteCollectionRequestProto): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteCollectionRequestProto>, I>>(base?: I): DeleteCollectionRequestProto {
    return DeleteCollectionRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteCollectionRequestProto>, I>>(object: I): DeleteCollectionRequestProto {
    const message = createBaseDeleteCollectionRequestProto();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseCollectionsRequestProto(): CollectionsRequestProto {
  return { pagination: undefined, q: undefined };
}

export const CollectionsRequestProto: MessageFns<CollectionsRequestProto> = {
  encode(message: CollectionsRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pagination !== undefined) {
      PaginationRequest.encode(message.pagination, writer.uint32(10).fork()).join();
    }
    if (message.q !== undefined) {
      writer.uint32(18).string(message.q);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionsRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionsRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pagination = PaginationRequest.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.q = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionsRequestProto {
    return {
      pagination: isSet(object.pagination) ? PaginationRequest.fromJSON(object.pagination) : undefined,
      q: isSet(object.q) ? globalThis.String(object.q) : undefined,
    };
  },

  toJSON(message: CollectionsRequestProto): unknown {
    const obj: any = {};
    if (message.pagination !== undefined) {
      obj.pagination = PaginationRequest.toJSON(message.pagination);
    }
    if (message.q !== undefined) {
      obj.q = message.q;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionsRequestProto>, I>>(base?: I): CollectionsRequestProto {
    return CollectionsRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionsRequestProto>, I>>(object: I): CollectionsRequestProto {
    const message = createBaseCollectionsRequestProto();
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationRequest.fromPartial(object.pagination)
      : undefined;
    message.q = object.q ?? undefined;
    return message;
  },
};

function createBaseCollectionsResponseProto(): CollectionsResponseProto {
  return { collections: [], pagination: undefined };
}

export const CollectionsResponseProto: MessageFns<CollectionsResponseProto> = {
  encode(message: CollectionsResponseProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.collections) {
      CollectionEntity.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.pagination !== undefined) {
      PaginationResponse.encode(message.pagination, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionsResponseProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionsResponseProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collections.push(CollectionEntity.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pagination = PaginationResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionsResponseProto {
    return {
      collections: globalThis.Array.isArray(object?.collections)
        ? object.collections.map((e: any) => CollectionEntity.fromJSON(e))
        : [],
      pagination: isSet(object.pagination) ? PaginationResponse.fromJSON(object.pagination) : undefined,
    };
  },

  toJSON(message: CollectionsResponseProto): unknown {
    const obj: any = {};
    if (message.collections?.length) {
      obj.collections = message.collections.map((e) => CollectionEntity.toJSON(e));
    }
    if (message.pagination !== undefined) {
      obj.pagination = PaginationResponse.toJSON(message.pagination);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionsResponseProto>, I>>(base?: I): CollectionsResponseProto {
    return CollectionsResponseProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionsResponseProto>, I>>(object: I): CollectionsResponseProto {
    const message = createBaseCollectionsResponseProto();
    message.collections = object.collections?.map((e) => CollectionEntity.fromPartial(e)) || [];
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationResponse.fromPartial(object.pagination)
      : undefined;
    return message;
  },
};

function createBaseStatusResponseProto(): StatusResponseProto {
  return { status: false };
}

export const StatusResponseProto: MessageFns<StatusResponseProto> = {
  encode(message: StatusResponseProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StatusResponseProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStatusResponseProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StatusResponseProto {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: StatusResponseProto): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StatusResponseProto>, I>>(base?: I): StatusResponseProto {
    return StatusResponseProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StatusResponseProto>, I>>(object: I): StatusResponseProto {
    const message = createBaseStatusResponseProto();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseCollectionRequestProto(): CollectionRequestProto {
  return { id: "" };
}

export const CollectionRequestProto: MessageFns<CollectionRequestProto> = {
  encode(message: CollectionRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionRequestProto {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: CollectionRequestProto): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionRequestProto>, I>>(base?: I): CollectionRequestProto {
    return CollectionRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionRequestProto>, I>>(object: I): CollectionRequestProto {
    const message = createBaseCollectionRequestProto();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseCollectionResponseProto(): CollectionResponseProto {
  return { collection: undefined };
}

export const CollectionResponseProto: MessageFns<CollectionResponseProto> = {
  encode(message: CollectionResponseProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.collection !== undefined) {
      CollectionEntity.encode(message.collection, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionResponseProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionResponseProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collection = CollectionEntity.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionResponseProto {
    return { collection: isSet(object.collection) ? CollectionEntity.fromJSON(object.collection) : undefined };
  },

  toJSON(message: CollectionResponseProto): unknown {
    const obj: any = {};
    if (message.collection !== undefined) {
      obj.collection = CollectionEntity.toJSON(message.collection);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionResponseProto>, I>>(base?: I): CollectionResponseProto {
    return CollectionResponseProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionResponseProto>, I>>(object: I): CollectionResponseProto {
    const message = createBaseCollectionResponseProto();
    message.collection = (object.collection !== undefined && object.collection !== null)
      ? CollectionEntity.fromPartial(object.collection)
      : undefined;
    return message;
  },
};

function createBaseCollectionItemsRequestProto(): CollectionItemsRequestProto {
  return { collectionId: "" };
}

export const CollectionItemsRequestProto: MessageFns<CollectionItemsRequestProto> = {
  encode(message: CollectionItemsRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.collectionId !== "") {
      writer.uint32(10).string(message.collectionId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionItemsRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionItemsRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionItemsRequestProto {
    return { collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "" };
  },

  toJSON(message: CollectionItemsRequestProto): unknown {
    const obj: any = {};
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionItemsRequestProto>, I>>(base?: I): CollectionItemsRequestProto {
    return CollectionItemsRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionItemsRequestProto>, I>>(object: I): CollectionItemsRequestProto {
    const message = createBaseCollectionItemsRequestProto();
    message.collectionId = object.collectionId ?? "";
    return message;
  },
};

function createBaseCollectionItemEntity(): CollectionItemEntity {
  return {
    id: "",
    bankId: "",
    bankName: "",
    minTenureInDays: 0,
    maxTenureInDays: 0,
    priority: 0,
    displayTitle: "",
    tagConfig: undefined,
    redirectDeeplink: undefined,
    isActive: false,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const CollectionItemEntity: MessageFns<CollectionItemEntity> = {
  encode(message: CollectionItemEntity, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.bankId !== "") {
      writer.uint32(18).string(message.bankId);
    }
    if (message.bankName !== "") {
      writer.uint32(26).string(message.bankName);
    }
    if (message.minTenureInDays !== 0) {
      writer.uint32(32).int32(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      writer.uint32(40).int32(message.maxTenureInDays);
    }
    if (message.priority !== 0) {
      writer.uint32(48).int32(message.priority);
    }
    if (message.displayTitle !== "") {
      writer.uint32(58).string(message.displayTitle);
    }
    if (message.tagConfig !== undefined) {
      TagConfig.encode(message.tagConfig, writer.uint32(66).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(74).fork()).join();
    }
    if (message.isActive !== false) {
      writer.uint32(80).bool(message.isActive);
    }
    if (message.createdAt !== 0) {
      writer.uint32(89).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(97).double(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionItemEntity {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionItemEntity();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.minTenureInDays = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.maxTenureInDays = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.displayTitle = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.tagConfig = TagConfig.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 89) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionItemEntity {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      minTenureInDays: isSet(object.minTenureInDays) ? globalThis.Number(object.minTenureInDays) : 0,
      maxTenureInDays: isSet(object.maxTenureInDays) ? globalThis.Number(object.maxTenureInDays) : 0,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      displayTitle: isSet(object.displayTitle) ? globalThis.String(object.displayTitle) : "",
      tagConfig: isSet(object.tagConfig) ? TagConfig.fromJSON(object.tagConfig) : undefined,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: CollectionItemEntity): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.minTenureInDays !== 0) {
      obj.minTenureInDays = Math.round(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      obj.maxTenureInDays = Math.round(message.maxTenureInDays);
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.displayTitle !== "") {
      obj.displayTitle = message.displayTitle;
    }
    if (message.tagConfig !== undefined) {
      obj.tagConfig = TagConfig.toJSON(message.tagConfig);
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionItemEntity>, I>>(base?: I): CollectionItemEntity {
    return CollectionItemEntity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionItemEntity>, I>>(object: I): CollectionItemEntity {
    const message = createBaseCollectionItemEntity();
    message.id = object.id ?? "";
    message.bankId = object.bankId ?? "";
    message.bankName = object.bankName ?? "";
    message.minTenureInDays = object.minTenureInDays ?? 0;
    message.maxTenureInDays = object.maxTenureInDays ?? 0;
    message.priority = object.priority ?? 0;
    message.displayTitle = object.displayTitle ?? "";
    message.tagConfig = (object.tagConfig !== undefined && object.tagConfig !== null)
      ? TagConfig.fromPartial(object.tagConfig)
      : undefined;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.isActive = object.isActive ?? false;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseCollectionItemsResponseProto(): CollectionItemsResponseProto {
  return { collectionItems: [] };
}

export const CollectionItemsResponseProto: MessageFns<CollectionItemsResponseProto> = {
  encode(message: CollectionItemsResponseProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.collectionItems) {
      CollectionItemEntity.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionItemsResponseProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionItemsResponseProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collectionItems.push(CollectionItemEntity.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionItemsResponseProto {
    return {
      collectionItems: globalThis.Array.isArray(object?.collectionItems)
        ? object.collectionItems.map((e: any) => CollectionItemEntity.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CollectionItemsResponseProto): unknown {
    const obj: any = {};
    if (message.collectionItems?.length) {
      obj.collectionItems = message.collectionItems.map((e) => CollectionItemEntity.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionItemsResponseProto>, I>>(base?: I): CollectionItemsResponseProto {
    return CollectionItemsResponseProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionItemsResponseProto>, I>>(object: I): CollectionItemsResponseProto {
    const message = createBaseCollectionItemsResponseProto();
    message.collectionItems = object.collectionItems?.map((e) => CollectionItemEntity.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCollectionItemRequestProto(): CollectionItemRequestProto {
  return { id: "" };
}

export const CollectionItemRequestProto: MessageFns<CollectionItemRequestProto> = {
  encode(message: CollectionItemRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionItemRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionItemRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionItemRequestProto {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: CollectionItemRequestProto): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionItemRequestProto>, I>>(base?: I): CollectionItemRequestProto {
    return CollectionItemRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionItemRequestProto>, I>>(object: I): CollectionItemRequestProto {
    const message = createBaseCollectionItemRequestProto();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseCollectionItemResponseProto(): CollectionItemResponseProto {
  return { collectionItem: undefined };
}

export const CollectionItemResponseProto: MessageFns<CollectionItemResponseProto> = {
  encode(message: CollectionItemResponseProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.collectionItem !== undefined) {
      CollectionItemEntity.encode(message.collectionItem, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CollectionItemResponseProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCollectionItemResponseProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collectionItem = CollectionItemEntity.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CollectionItemResponseProto {
    return {
      collectionItem: isSet(object.collectionItem) ? CollectionItemEntity.fromJSON(object.collectionItem) : undefined,
    };
  },

  toJSON(message: CollectionItemResponseProto): unknown {
    const obj: any = {};
    if (message.collectionItem !== undefined) {
      obj.collectionItem = CollectionItemEntity.toJSON(message.collectionItem);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CollectionItemResponseProto>, I>>(base?: I): CollectionItemResponseProto {
    return CollectionItemResponseProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CollectionItemResponseProto>, I>>(object: I): CollectionItemResponseProto {
    const message = createBaseCollectionItemResponseProto();
    message.collectionItem = (object.collectionItem !== undefined && object.collectionItem !== null)
      ? CollectionItemEntity.fromPartial(object.collectionItem)
      : undefined;
    return message;
  },
};

function createBaseDeleteCollectionItemRequestProto(): DeleteCollectionItemRequestProto {
  return { id: "" };
}

export const DeleteCollectionItemRequestProto: MessageFns<DeleteCollectionItemRequestProto> = {
  encode(message: DeleteCollectionItemRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteCollectionItemRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteCollectionItemRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteCollectionItemRequestProto {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: DeleteCollectionItemRequestProto): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteCollectionItemRequestProto>, I>>(
    base?: I,
  ): DeleteCollectionItemRequestProto {
    return DeleteCollectionItemRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteCollectionItemRequestProto>, I>>(
    object: I,
  ): DeleteCollectionItemRequestProto {
    const message = createBaseDeleteCollectionItemRequestProto();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseUpdateCollectionItemRequestProto(): UpdateCollectionItemRequestProto {
  return {
    id: "",
    collectionId: "",
    bankId: "",
    minTenureInDays: 0,
    maxTenureInDays: 0,
    priority: 0,
    displayTitle: "",
    tagConfig: undefined,
    redirectDeeplink: undefined,
  };
}

export const UpdateCollectionItemRequestProto: MessageFns<UpdateCollectionItemRequestProto> = {
  encode(message: UpdateCollectionItemRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.collectionId !== "") {
      writer.uint32(18).string(message.collectionId);
    }
    if (message.bankId !== "") {
      writer.uint32(26).string(message.bankId);
    }
    if (message.minTenureInDays !== 0) {
      writer.uint32(32).int32(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      writer.uint32(40).int32(message.maxTenureInDays);
    }
    if (message.priority !== 0) {
      writer.uint32(48).int32(message.priority);
    }
    if (message.displayTitle !== "") {
      writer.uint32(58).string(message.displayTitle);
    }
    if (message.tagConfig !== undefined) {
      TagConfig.encode(message.tagConfig, writer.uint32(66).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(74).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateCollectionItemRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateCollectionItemRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.minTenureInDays = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.maxTenureInDays = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.displayTitle = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.tagConfig = TagConfig.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateCollectionItemRequestProto {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      minTenureInDays: isSet(object.minTenureInDays) ? globalThis.Number(object.minTenureInDays) : 0,
      maxTenureInDays: isSet(object.maxTenureInDays) ? globalThis.Number(object.maxTenureInDays) : 0,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      displayTitle: isSet(object.displayTitle) ? globalThis.String(object.displayTitle) : "",
      tagConfig: isSet(object.tagConfig) ? TagConfig.fromJSON(object.tagConfig) : undefined,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
    };
  },

  toJSON(message: UpdateCollectionItemRequestProto): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.minTenureInDays !== 0) {
      obj.minTenureInDays = Math.round(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      obj.maxTenureInDays = Math.round(message.maxTenureInDays);
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.displayTitle !== "") {
      obj.displayTitle = message.displayTitle;
    }
    if (message.tagConfig !== undefined) {
      obj.tagConfig = TagConfig.toJSON(message.tagConfig);
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateCollectionItemRequestProto>, I>>(
    base?: I,
  ): UpdateCollectionItemRequestProto {
    return UpdateCollectionItemRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateCollectionItemRequestProto>, I>>(
    object: I,
  ): UpdateCollectionItemRequestProto {
    const message = createBaseUpdateCollectionItemRequestProto();
    message.id = object.id ?? "";
    message.collectionId = object.collectionId ?? "";
    message.bankId = object.bankId ?? "";
    message.minTenureInDays = object.minTenureInDays ?? 0;
    message.maxTenureInDays = object.maxTenureInDays ?? 0;
    message.priority = object.priority ?? 0;
    message.displayTitle = object.displayTitle ?? "";
    message.tagConfig = (object.tagConfig !== undefined && object.tagConfig !== null)
      ? TagConfig.fromPartial(object.tagConfig)
      : undefined;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    return message;
  },
};

function createBaseCreateCollectionItemRequestProto(): CreateCollectionItemRequestProto {
  return {
    collectionId: "",
    bankId: "",
    minTenureInDays: 0,
    maxTenureInDays: 0,
    priority: 0,
    displayTitle: "",
    tagConfig: undefined,
    redirectDeeplink: undefined,
  };
}

export const CreateCollectionItemRequestProto: MessageFns<CreateCollectionItemRequestProto> = {
  encode(message: CreateCollectionItemRequestProto, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.collectionId !== "") {
      writer.uint32(10).string(message.collectionId);
    }
    if (message.bankId !== "") {
      writer.uint32(18).string(message.bankId);
    }
    if (message.minTenureInDays !== 0) {
      writer.uint32(24).int32(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      writer.uint32(32).int32(message.maxTenureInDays);
    }
    if (message.priority !== 0) {
      writer.uint32(40).int32(message.priority);
    }
    if (message.displayTitle !== "") {
      writer.uint32(50).string(message.displayTitle);
    }
    if (message.tagConfig !== undefined) {
      TagConfig.encode(message.tagConfig, writer.uint32(58).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(66).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateCollectionItemRequestProto {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateCollectionItemRequestProto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.minTenureInDays = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.maxTenureInDays = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.displayTitle = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.tagConfig = TagConfig.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateCollectionItemRequestProto {
    return {
      collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      minTenureInDays: isSet(object.minTenureInDays) ? globalThis.Number(object.minTenureInDays) : 0,
      maxTenureInDays: isSet(object.maxTenureInDays) ? globalThis.Number(object.maxTenureInDays) : 0,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      displayTitle: isSet(object.displayTitle) ? globalThis.String(object.displayTitle) : "",
      tagConfig: isSet(object.tagConfig) ? TagConfig.fromJSON(object.tagConfig) : undefined,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
    };
  },

  toJSON(message: CreateCollectionItemRequestProto): unknown {
    const obj: any = {};
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.minTenureInDays !== 0) {
      obj.minTenureInDays = Math.round(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      obj.maxTenureInDays = Math.round(message.maxTenureInDays);
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.displayTitle !== "") {
      obj.displayTitle = message.displayTitle;
    }
    if (message.tagConfig !== undefined) {
      obj.tagConfig = TagConfig.toJSON(message.tagConfig);
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateCollectionItemRequestProto>, I>>(
    base?: I,
  ): CreateCollectionItemRequestProto {
    return CreateCollectionItemRequestProto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateCollectionItemRequestProto>, I>>(
    object: I,
  ): CreateCollectionItemRequestProto {
    const message = createBaseCreateCollectionItemRequestProto();
    message.collectionId = object.collectionId ?? "";
    message.bankId = object.bankId ?? "";
    message.minTenureInDays = object.minTenureInDays ?? 0;
    message.maxTenureInDays = object.maxTenureInDays ?? 0;
    message.priority = object.priority ?? 0;
    message.displayTitle = object.displayTitle ?? "";
    message.tagConfig = (object.tagConfig !== undefined && object.tagConfig !== null)
      ? TagConfig.fromPartial(object.tagConfig)
      : undefined;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    return message;
  },
};

function createBaseBankTenureRequest(): BankTenureRequest {
  return { empty: undefined };
}

export const BankTenureRequest: MessageFns<BankTenureRequest> = {
  encode(message: BankTenureRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.empty !== undefined) {
      Empty.encode(message.empty, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankTenureRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankTenureRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.empty = Empty.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankTenureRequest {
    return { empty: isSet(object.empty) ? Empty.fromJSON(object.empty) : undefined };
  },

  toJSON(message: BankTenureRequest): unknown {
    const obj: any = {};
    if (message.empty !== undefined) {
      obj.empty = Empty.toJSON(message.empty);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankTenureRequest>, I>>(base?: I): BankTenureRequest {
    return BankTenureRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankTenureRequest>, I>>(object: I): BankTenureRequest {
    const message = createBaseBankTenureRequest();
    message.empty = (object.empty !== undefined && object.empty !== null) ? Empty.fromPartial(object.empty) : undefined;
    return message;
  },
};

function createBaseBankTenureEntity(): BankTenureEntity {
  return { bankName: "", bankId: "", tenures: [] };
}

export const BankTenureEntity: MessageFns<BankTenureEntity> = {
  encode(message: BankTenureEntity, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankName !== "") {
      writer.uint32(10).string(message.bankName);
    }
    if (message.bankId !== "") {
      writer.uint32(18).string(message.bankId);
    }
    for (const v of message.tenures) {
      BankTenureEntity_TenureRange.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankTenureEntity {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankTenureEntity();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tenures.push(BankTenureEntity_TenureRange.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankTenureEntity {
    return {
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      tenures: globalThis.Array.isArray(object?.tenures)
        ? object.tenures.map((e: any) => BankTenureEntity_TenureRange.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BankTenureEntity): unknown {
    const obj: any = {};
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.tenures?.length) {
      obj.tenures = message.tenures.map((e) => BankTenureEntity_TenureRange.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankTenureEntity>, I>>(base?: I): BankTenureEntity {
    return BankTenureEntity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankTenureEntity>, I>>(object: I): BankTenureEntity {
    const message = createBaseBankTenureEntity();
    message.bankName = object.bankName ?? "";
    message.bankId = object.bankId ?? "";
    message.tenures = object.tenures?.map((e) => BankTenureEntity_TenureRange.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBankTenureEntity_TenureRange(): BankTenureEntity_TenureRange {
  return { minTenureInDays: 0, maxTenureInDays: 0 };
}

export const BankTenureEntity_TenureRange: MessageFns<BankTenureEntity_TenureRange> = {
  encode(message: BankTenureEntity_TenureRange, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.minTenureInDays !== 0) {
      writer.uint32(16).int32(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      writer.uint32(24).int32(message.maxTenureInDays);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankTenureEntity_TenureRange {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankTenureEntity_TenureRange();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.minTenureInDays = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.maxTenureInDays = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankTenureEntity_TenureRange {
    return {
      minTenureInDays: isSet(object.minTenureInDays) ? globalThis.Number(object.minTenureInDays) : 0,
      maxTenureInDays: isSet(object.maxTenureInDays) ? globalThis.Number(object.maxTenureInDays) : 0,
    };
  },

  toJSON(message: BankTenureEntity_TenureRange): unknown {
    const obj: any = {};
    if (message.minTenureInDays !== 0) {
      obj.minTenureInDays = Math.round(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      obj.maxTenureInDays = Math.round(message.maxTenureInDays);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankTenureEntity_TenureRange>, I>>(base?: I): BankTenureEntity_TenureRange {
    return BankTenureEntity_TenureRange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankTenureEntity_TenureRange>, I>>(object: I): BankTenureEntity_TenureRange {
    const message = createBaseBankTenureEntity_TenureRange();
    message.minTenureInDays = object.minTenureInDays ?? 0;
    message.maxTenureInDays = object.maxTenureInDays ?? 0;
    return message;
  },
};

function createBaseBankTenureResponse(): BankTenureResponse {
  return { bankTenureList: [] };
}

export const BankTenureResponse: MessageFns<BankTenureResponse> = {
  encode(message: BankTenureResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bankTenureList) {
      BankTenureEntity.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankTenureResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankTenureResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankTenureList.push(BankTenureEntity.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankTenureResponse {
    return {
      bankTenureList: globalThis.Array.isArray(object?.bankTenureList)
        ? object.bankTenureList.map((e: any) => BankTenureEntity.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BankTenureResponse): unknown {
    const obj: any = {};
    if (message.bankTenureList?.length) {
      obj.bankTenureList = message.bankTenureList.map((e) => BankTenureEntity.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankTenureResponse>, I>>(base?: I): BankTenureResponse {
    return BankTenureResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankTenureResponse>, I>>(object: I): BankTenureResponse {
    const message = createBaseBankTenureResponse();
    message.bankTenureList = object.bankTenureList?.map((e) => BankTenureEntity.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBulkUpdateFdItemPriorityRequest(): BulkUpdateFdItemPriorityRequest {
  return { items: [] };
}

export const BulkUpdateFdItemPriorityRequest: MessageFns<BulkUpdateFdItemPriorityRequest> = {
  encode(message: BulkUpdateFdItemPriorityRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.items) {
      BulkUpdateFdItemPriorityRequest_Item.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkUpdateFdItemPriorityRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkUpdateFdItemPriorityRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.items.push(BulkUpdateFdItemPriorityRequest_Item.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkUpdateFdItemPriorityRequest {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => BulkUpdateFdItemPriorityRequest_Item.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BulkUpdateFdItemPriorityRequest): unknown {
    const obj: any = {};
    if (message.items?.length) {
      obj.items = message.items.map((e) => BulkUpdateFdItemPriorityRequest_Item.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkUpdateFdItemPriorityRequest>, I>>(base?: I): BulkUpdateFdItemPriorityRequest {
    return BulkUpdateFdItemPriorityRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkUpdateFdItemPriorityRequest>, I>>(
    object: I,
  ): BulkUpdateFdItemPriorityRequest {
    const message = createBaseBulkUpdateFdItemPriorityRequest();
    message.items = object.items?.map((e) => BulkUpdateFdItemPriorityRequest_Item.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBulkUpdateFdItemPriorityRequest_Item(): BulkUpdateFdItemPriorityRequest_Item {
  return { id: "", newPriority: 0 };
}

export const BulkUpdateFdItemPriorityRequest_Item: MessageFns<BulkUpdateFdItemPriorityRequest_Item> = {
  encode(message: BulkUpdateFdItemPriorityRequest_Item, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.newPriority !== 0) {
      writer.uint32(16).int32(message.newPriority);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkUpdateFdItemPriorityRequest_Item {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkUpdateFdItemPriorityRequest_Item();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.newPriority = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkUpdateFdItemPriorityRequest_Item {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      newPriority: isSet(object.newPriority) ? globalThis.Number(object.newPriority) : 0,
    };
  },

  toJSON(message: BulkUpdateFdItemPriorityRequest_Item): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.newPriority !== 0) {
      obj.newPriority = Math.round(message.newPriority);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkUpdateFdItemPriorityRequest_Item>, I>>(
    base?: I,
  ): BulkUpdateFdItemPriorityRequest_Item {
    return BulkUpdateFdItemPriorityRequest_Item.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkUpdateFdItemPriorityRequest_Item>, I>>(
    object: I,
  ): BulkUpdateFdItemPriorityRequest_Item {
    const message = createBaseBulkUpdateFdItemPriorityRequest_Item();
    message.id = object.id ?? "";
    message.newPriority = object.newPriority ?? 0;
    return message;
  },
};

function createBaseBulkUpdateFdItemPriorityResponse(): BulkUpdateFdItemPriorityResponse {
  return {};
}

export const BulkUpdateFdItemPriorityResponse: MessageFns<BulkUpdateFdItemPriorityResponse> = {
  encode(_: BulkUpdateFdItemPriorityResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkUpdateFdItemPriorityResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkUpdateFdItemPriorityResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): BulkUpdateFdItemPriorityResponse {
    return {};
  },

  toJSON(_: BulkUpdateFdItemPriorityResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkUpdateFdItemPriorityResponse>, I>>(
    base?: I,
  ): BulkUpdateFdItemPriorityResponse {
    return BulkUpdateFdItemPriorityResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkUpdateFdItemPriorityResponse>, I>>(
    _: I,
  ): BulkUpdateFdItemPriorityResponse {
    const message = createBaseBulkUpdateFdItemPriorityResponse();
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
