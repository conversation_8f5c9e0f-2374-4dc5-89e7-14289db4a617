// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: BrokingCollection.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Empty } from "./google/protobuf/empty";

export const protobufPackage = "com.stablemoney.api.broking";

export enum CollectionType {
  UNKNOWN = 0,
  MANUAL = 1,
  EXPRESSION = 2,
  UNRECOGNIZED = -1,
}

export function collectionTypeFromJSON(object: any): CollectionType {
  switch (object) {
    case 0:
    case "UNKNOWN":
      return CollectionType.UNKNOWN;
    case 1:
    case "MANUAL":
      return CollectionType.MANUAL;
    case 2:
    case "EXPRESSION":
      return CollectionType.EXPRESSION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CollectionType.UNRECOGNIZED;
  }
}

export function collectionTypeToJSON(object: CollectionType): string {
  switch (object) {
    case CollectionType.UNKNOWN:
      return "UNKNOWN";
    case CollectionType.MANUAL:
      return "MANUAL";
    case CollectionType.EXPRESSION:
      return "EXPRESSION";
    case CollectionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum DisplayType {
  DISPLAY_TYPE_UNKNOWN = 0,
  DEFAULT = 1,
  MINIMUM_INVESTMENT = 2,
  SHORT_TERM = 4,
  SHORT_TERM_XIRR = 6,
  SELLING_OUT_SOON = 8,
  UNRECOGNIZED = -1,
}

export function displayTypeFromJSON(object: any): DisplayType {
  switch (object) {
    case 0:
    case "DISPLAY_TYPE_UNKNOWN":
      return DisplayType.DISPLAY_TYPE_UNKNOWN;
    case 1:
    case "DEFAULT":
      return DisplayType.DEFAULT;
    case 2:
    case "MINIMUM_INVESTMENT":
      return DisplayType.MINIMUM_INVESTMENT;
    case 4:
    case "SHORT_TERM":
      return DisplayType.SHORT_TERM;
    case 6:
    case "SHORT_TERM_XIRR":
      return DisplayType.SHORT_TERM_XIRR;
    case 8:
    case "SELLING_OUT_SOON":
      return DisplayType.SELLING_OUT_SOON;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DisplayType.UNRECOGNIZED;
  }
}

export function displayTypeToJSON(object: DisplayType): string {
  switch (object) {
    case DisplayType.DISPLAY_TYPE_UNKNOWN:
      return "DISPLAY_TYPE_UNKNOWN";
    case DisplayType.DEFAULT:
      return "DEFAULT";
    case DisplayType.MINIMUM_INVESTMENT:
      return "MINIMUM_INVESTMENT";
    case DisplayType.SHORT_TERM:
      return "SHORT_TERM";
    case DisplayType.SHORT_TERM_XIRR:
      return "SHORT_TERM_XIRR";
    case DisplayType.SELLING_OUT_SOON:
      return "SELLING_OUT_SOON";
    case DisplayType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface AllCollectionsResponse {
  collections: AllCollectionsResponse_Collection[];
}

export interface AllCollectionsResponse_Collection {
  name: string;
  title: string;
  id: string;
  collectionType: CollectionType;
  isActive: boolean;
}

export interface TagConfig {
  name: string;
  iconUrl: string;
  color: string;
  bgColor: string;
  type: string;
}

export interface CreateBondCollectionRequest {
  description: string;
  iconUrl: string;
  isActive: boolean;
  name: string;
  title: string;
  preFilterCriteria: string;
  postFilterCriteria: string;
  sortCriteria: string;
  collectionItemLimit: number;
  collectionType: CollectionType;
  displayType: DisplayType;
  excludedIsin: string;
}

export interface CreateBondCollectionResponse {
  status: boolean;
}

export interface UpdateBondCollectionResponse {
  status: boolean;
}

export interface IdRequest {
  id: string;
}

export interface UpdateBondItemPriorityRequest {
  id: string;
  newPriority: number;
}

export interface UpdateBondItemPriorityResponse {
  status: boolean;
}

export interface BondCollectionResponse {
  description: string;
  iconUrl: string;
  isActive: boolean;
  name: string;
  title: string;
  preFilterCriteria: string;
  postFilterCriteria: string;
  sortCriteria: string;
  collectionItemLimit: number;
  collectionType: CollectionType;
  id: string;
  displayType: DisplayType;
  excludedIsin: string;
}

export interface DeleteBondCollectionResponse {
  status: boolean;
}

export interface UpdateBondCollectionRequest {
  id: string;
  description: string;
  iconUrl: string;
  isActive: boolean;
  name: string;
  title: string;
  preFilterCriteria: string;
  postFilterCriteria: string;
  sortCriteria: string;
  collectionItemLimit: number;
  includeSoldOut: boolean;
  collectionType: CollectionType;
  displayType: DisplayType;
  excludedIsin: string;
}

export interface BondsCollection {
  updatedAt: string;
  description: string;
  name: string;
  createdAt: string;
  id: string;
}

export interface GetBondsCollectionRequest {
  page: number;
  size: number;
  searchString: string;
}

export interface GetBondsCollectionResponse {
  data: BondsCollection[];
  hasNextPage: boolean;
}

export interface CreateAndUpdateBondCollectionItemRequest {
  id: string;
  displayTitle: string;
  priority: number;
  isinCode: string;
  isActive: boolean;
  collectionId: string;
  createdAt: string;
  updatedAt: string;
  sellingPoint: string;
  buttonCta: string;
  struckenYield: number;
  showTag: boolean;
  isDynamicTag: boolean;
}

export interface BondCollectionItemResponse {
  id: string;
  displayTitle: string;
  priority: number;
  tagConfig: TagConfig | undefined;
  isinCode: string;
  isActive: boolean;
  collectionId: string;
  createdAt: string;
  updatedAt: string;
  sellingPoint: string;
  buttonCta: string;
  struckenYield: number;
  showTag: boolean;
  isDynamicTag: boolean;
}

export interface DeleteBondCollectionItemResponse {
  status: boolean;
}

export interface GetBondsCollectionItemRequest {
  collectionId: string;
}

export interface GetBondsCollectionItemResponse {
  data: BondCollectionItemResponse[];
}

export interface GetBondDetailsRequest {
}

export interface BondDetails {
  isinCode: string;
  displayTitle: string;
  name: string;
}

export interface GetBondDetailsResponse {
  data: BondDetails[];
}

export interface BondsCollectionItemStatusResponse {
  status: boolean;
}

export interface BulkUpdateBondItemPriorityRequest {
  items: BulkUpdateBondItemPriorityRequest_Item[];
}

export interface BulkUpdateBondItemPriorityRequest_Item {
  id: string;
  newPriority: number;
}

export interface BulkUpdateBondItemPriorityResponse {
  status: boolean;
}

export interface GetAllCollectionExpressionsResponse {
  collectionExpressions: GetAllCollectionExpressionsResponse_CollectionExpression[];
}

export interface GetAllCollectionExpressionsResponse_CollectionExpression {
  name: string;
  preFilterCriteria: string;
  postFilterCriteria: string;
  sortCriteria: string;
}

function createBaseAllCollectionsResponse(): AllCollectionsResponse {
  return { collections: [] };
}

export const AllCollectionsResponse: MessageFns<AllCollectionsResponse> = {
  encode(message: AllCollectionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.collections) {
      AllCollectionsResponse_Collection.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllCollectionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllCollectionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collections.push(AllCollectionsResponse_Collection.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllCollectionsResponse {
    return {
      collections: globalThis.Array.isArray(object?.collections)
        ? object.collections.map((e: any) => AllCollectionsResponse_Collection.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AllCollectionsResponse): unknown {
    const obj: any = {};
    if (message.collections?.length) {
      obj.collections = message.collections.map((e) => AllCollectionsResponse_Collection.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllCollectionsResponse>, I>>(base?: I): AllCollectionsResponse {
    return AllCollectionsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllCollectionsResponse>, I>>(object: I): AllCollectionsResponse {
    const message = createBaseAllCollectionsResponse();
    message.collections = object.collections?.map((e) => AllCollectionsResponse_Collection.fromPartial(e)) || [];
    return message;
  },
};

function createBaseAllCollectionsResponse_Collection(): AllCollectionsResponse_Collection {
  return { name: "", title: "", id: "", collectionType: 0, isActive: false };
}

export const AllCollectionsResponse_Collection: MessageFns<AllCollectionsResponse_Collection> = {
  encode(message: AllCollectionsResponse_Collection, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.title !== "") {
      writer.uint32(18).string(message.title);
    }
    if (message.id !== "") {
      writer.uint32(26).string(message.id);
    }
    if (message.collectionType !== 0) {
      writer.uint32(32).int32(message.collectionType);
    }
    if (message.isActive !== false) {
      writer.uint32(40).bool(message.isActive);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllCollectionsResponse_Collection {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllCollectionsResponse_Collection();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.collectionType = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllCollectionsResponse_Collection {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      collectionType: isSet(object.collectionType) ? collectionTypeFromJSON(object.collectionType) : 0,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
    };
  },

  toJSON(message: AllCollectionsResponse_Collection): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.collectionType !== 0) {
      obj.collectionType = collectionTypeToJSON(message.collectionType);
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllCollectionsResponse_Collection>, I>>(
    base?: I,
  ): AllCollectionsResponse_Collection {
    return AllCollectionsResponse_Collection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllCollectionsResponse_Collection>, I>>(
    object: I,
  ): AllCollectionsResponse_Collection {
    const message = createBaseAllCollectionsResponse_Collection();
    message.name = object.name ?? "";
    message.title = object.title ?? "";
    message.id = object.id ?? "";
    message.collectionType = object.collectionType ?? 0;
    message.isActive = object.isActive ?? false;
    return message;
  },
};

function createBaseTagConfig(): TagConfig {
  return { name: "", iconUrl: "", color: "", bgColor: "", type: "" };
}

export const TagConfig: MessageFns<TagConfig> = {
  encode(message: TagConfig, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.iconUrl !== "") {
      writer.uint32(18).string(message.iconUrl);
    }
    if (message.color !== "") {
      writer.uint32(26).string(message.color);
    }
    if (message.bgColor !== "") {
      writer.uint32(34).string(message.bgColor);
    }
    if (message.type !== "") {
      writer.uint32(42).string(message.type);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TagConfig {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTagConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.color = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bgColor = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.type = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TagConfig {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      bgColor: isSet(object.bgColor) ? globalThis.String(object.bgColor) : "",
      type: isSet(object.type) ? globalThis.String(object.type) : "",
    };
  },

  toJSON(message: TagConfig): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.bgColor !== "") {
      obj.bgColor = message.bgColor;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TagConfig>, I>>(base?: I): TagConfig {
    return TagConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TagConfig>, I>>(object: I): TagConfig {
    const message = createBaseTagConfig();
    message.name = object.name ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.color = object.color ?? "";
    message.bgColor = object.bgColor ?? "";
    message.type = object.type ?? "";
    return message;
  },
};

function createBaseCreateBondCollectionRequest(): CreateBondCollectionRequest {
  return {
    description: "",
    iconUrl: "",
    isActive: false,
    name: "",
    title: "",
    preFilterCriteria: "",
    postFilterCriteria: "",
    sortCriteria: "",
    collectionItemLimit: 0,
    collectionType: 0,
    displayType: 0,
    excludedIsin: "",
  };
}

export const CreateBondCollectionRequest: MessageFns<CreateBondCollectionRequest> = {
  encode(message: CreateBondCollectionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.description !== "") {
      writer.uint32(10).string(message.description);
    }
    if (message.iconUrl !== "") {
      writer.uint32(18).string(message.iconUrl);
    }
    if (message.isActive !== false) {
      writer.uint32(24).bool(message.isActive);
    }
    if (message.name !== "") {
      writer.uint32(34).string(message.name);
    }
    if (message.title !== "") {
      writer.uint32(42).string(message.title);
    }
    if (message.preFilterCriteria !== "") {
      writer.uint32(66).string(message.preFilterCriteria);
    }
    if (message.postFilterCriteria !== "") {
      writer.uint32(74).string(message.postFilterCriteria);
    }
    if (message.sortCriteria !== "") {
      writer.uint32(82).string(message.sortCriteria);
    }
    if (message.collectionItemLimit !== 0) {
      writer.uint32(88).int32(message.collectionItemLimit);
    }
    if (message.collectionType !== 0) {
      writer.uint32(96).int32(message.collectionType);
    }
    if (message.displayType !== 0) {
      writer.uint32(104).int32(message.displayType);
    }
    if (message.excludedIsin !== "") {
      writer.uint32(114).string(message.excludedIsin);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateBondCollectionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateBondCollectionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.preFilterCriteria = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.postFilterCriteria = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.sortCriteria = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.collectionItemLimit = reader.int32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.collectionType = reader.int32() as any;
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.displayType = reader.int32() as any;
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.excludedIsin = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateBondCollectionRequest {
    return {
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      preFilterCriteria: isSet(object.preFilterCriteria) ? globalThis.String(object.preFilterCriteria) : "",
      postFilterCriteria: isSet(object.postFilterCriteria) ? globalThis.String(object.postFilterCriteria) : "",
      sortCriteria: isSet(object.sortCriteria) ? globalThis.String(object.sortCriteria) : "",
      collectionItemLimit: isSet(object.collectionItemLimit) ? globalThis.Number(object.collectionItemLimit) : 0,
      collectionType: isSet(object.collectionType) ? collectionTypeFromJSON(object.collectionType) : 0,
      displayType: isSet(object.displayType) ? displayTypeFromJSON(object.displayType) : 0,
      excludedIsin: isSet(object.excludedIsin) ? globalThis.String(object.excludedIsin) : "",
    };
  },

  toJSON(message: CreateBondCollectionRequest): unknown {
    const obj: any = {};
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.preFilterCriteria !== "") {
      obj.preFilterCriteria = message.preFilterCriteria;
    }
    if (message.postFilterCriteria !== "") {
      obj.postFilterCriteria = message.postFilterCriteria;
    }
    if (message.sortCriteria !== "") {
      obj.sortCriteria = message.sortCriteria;
    }
    if (message.collectionItemLimit !== 0) {
      obj.collectionItemLimit = Math.round(message.collectionItemLimit);
    }
    if (message.collectionType !== 0) {
      obj.collectionType = collectionTypeToJSON(message.collectionType);
    }
    if (message.displayType !== 0) {
      obj.displayType = displayTypeToJSON(message.displayType);
    }
    if (message.excludedIsin !== "") {
      obj.excludedIsin = message.excludedIsin;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateBondCollectionRequest>, I>>(base?: I): CreateBondCollectionRequest {
    return CreateBondCollectionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateBondCollectionRequest>, I>>(object: I): CreateBondCollectionRequest {
    const message = createBaseCreateBondCollectionRequest();
    message.description = object.description ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.isActive = object.isActive ?? false;
    message.name = object.name ?? "";
    message.title = object.title ?? "";
    message.preFilterCriteria = object.preFilterCriteria ?? "";
    message.postFilterCriteria = object.postFilterCriteria ?? "";
    message.sortCriteria = object.sortCriteria ?? "";
    message.collectionItemLimit = object.collectionItemLimit ?? 0;
    message.collectionType = object.collectionType ?? 0;
    message.displayType = object.displayType ?? 0;
    message.excludedIsin = object.excludedIsin ?? "";
    return message;
  },
};

function createBaseCreateBondCollectionResponse(): CreateBondCollectionResponse {
  return { status: false };
}

export const CreateBondCollectionResponse: MessageFns<CreateBondCollectionResponse> = {
  encode(message: CreateBondCollectionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateBondCollectionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateBondCollectionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateBondCollectionResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: CreateBondCollectionResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateBondCollectionResponse>, I>>(base?: I): CreateBondCollectionResponse {
    return CreateBondCollectionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateBondCollectionResponse>, I>>(object: I): CreateBondCollectionResponse {
    const message = createBaseCreateBondCollectionResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseUpdateBondCollectionResponse(): UpdateBondCollectionResponse {
  return { status: false };
}

export const UpdateBondCollectionResponse: MessageFns<UpdateBondCollectionResponse> = {
  encode(message: UpdateBondCollectionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateBondCollectionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateBondCollectionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateBondCollectionResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: UpdateBondCollectionResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateBondCollectionResponse>, I>>(base?: I): UpdateBondCollectionResponse {
    return UpdateBondCollectionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBondCollectionResponse>, I>>(object: I): UpdateBondCollectionResponse {
    const message = createBaseUpdateBondCollectionResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseIdRequest(): IdRequest {
  return { id: "" };
}

export const IdRequest: MessageFns<IdRequest> = {
  encode(message: IdRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IdRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdRequest {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: IdRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdRequest>, I>>(base?: I): IdRequest {
    return IdRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdRequest>, I>>(object: I): IdRequest {
    const message = createBaseIdRequest();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseUpdateBondItemPriorityRequest(): UpdateBondItemPriorityRequest {
  return { id: "", newPriority: 0 };
}

export const UpdateBondItemPriorityRequest: MessageFns<UpdateBondItemPriorityRequest> = {
  encode(message: UpdateBondItemPriorityRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.newPriority !== 0) {
      writer.uint32(16).int32(message.newPriority);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateBondItemPriorityRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateBondItemPriorityRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.newPriority = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateBondItemPriorityRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      newPriority: isSet(object.newPriority) ? globalThis.Number(object.newPriority) : 0,
    };
  },

  toJSON(message: UpdateBondItemPriorityRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.newPriority !== 0) {
      obj.newPriority = Math.round(message.newPriority);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateBondItemPriorityRequest>, I>>(base?: I): UpdateBondItemPriorityRequest {
    return UpdateBondItemPriorityRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBondItemPriorityRequest>, I>>(
    object: I,
  ): UpdateBondItemPriorityRequest {
    const message = createBaseUpdateBondItemPriorityRequest();
    message.id = object.id ?? "";
    message.newPriority = object.newPriority ?? 0;
    return message;
  },
};

function createBaseUpdateBondItemPriorityResponse(): UpdateBondItemPriorityResponse {
  return { status: false };
}

export const UpdateBondItemPriorityResponse: MessageFns<UpdateBondItemPriorityResponse> = {
  encode(message: UpdateBondItemPriorityResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateBondItemPriorityResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateBondItemPriorityResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateBondItemPriorityResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: UpdateBondItemPriorityResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateBondItemPriorityResponse>, I>>(base?: I): UpdateBondItemPriorityResponse {
    return UpdateBondItemPriorityResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBondItemPriorityResponse>, I>>(
    object: I,
  ): UpdateBondItemPriorityResponse {
    const message = createBaseUpdateBondItemPriorityResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseBondCollectionResponse(): BondCollectionResponse {
  return {
    description: "",
    iconUrl: "",
    isActive: false,
    name: "",
    title: "",
    preFilterCriteria: "",
    postFilterCriteria: "",
    sortCriteria: "",
    collectionItemLimit: 0,
    collectionType: 0,
    id: "",
    displayType: 0,
    excludedIsin: "",
  };
}

export const BondCollectionResponse: MessageFns<BondCollectionResponse> = {
  encode(message: BondCollectionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.description !== "") {
      writer.uint32(10).string(message.description);
    }
    if (message.iconUrl !== "") {
      writer.uint32(18).string(message.iconUrl);
    }
    if (message.isActive !== false) {
      writer.uint32(24).bool(message.isActive);
    }
    if (message.name !== "") {
      writer.uint32(34).string(message.name);
    }
    if (message.title !== "") {
      writer.uint32(42).string(message.title);
    }
    if (message.preFilterCriteria !== "") {
      writer.uint32(66).string(message.preFilterCriteria);
    }
    if (message.postFilterCriteria !== "") {
      writer.uint32(74).string(message.postFilterCriteria);
    }
    if (message.sortCriteria !== "") {
      writer.uint32(82).string(message.sortCriteria);
    }
    if (message.collectionItemLimit !== 0) {
      writer.uint32(88).int32(message.collectionItemLimit);
    }
    if (message.collectionType !== 0) {
      writer.uint32(96).int32(message.collectionType);
    }
    if (message.id !== "") {
      writer.uint32(106).string(message.id);
    }
    if (message.displayType !== 0) {
      writer.uint32(120).int32(message.displayType);
    }
    if (message.excludedIsin !== "") {
      writer.uint32(130).string(message.excludedIsin);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondCollectionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondCollectionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.preFilterCriteria = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.postFilterCriteria = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.sortCriteria = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.collectionItemLimit = reader.int32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.collectionType = reader.int32() as any;
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.displayType = reader.int32() as any;
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.excludedIsin = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondCollectionResponse {
    return {
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      preFilterCriteria: isSet(object.preFilterCriteria) ? globalThis.String(object.preFilterCriteria) : "",
      postFilterCriteria: isSet(object.postFilterCriteria) ? globalThis.String(object.postFilterCriteria) : "",
      sortCriteria: isSet(object.sortCriteria) ? globalThis.String(object.sortCriteria) : "",
      collectionItemLimit: isSet(object.collectionItemLimit) ? globalThis.Number(object.collectionItemLimit) : 0,
      collectionType: isSet(object.collectionType) ? collectionTypeFromJSON(object.collectionType) : 0,
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      displayType: isSet(object.displayType) ? displayTypeFromJSON(object.displayType) : 0,
      excludedIsin: isSet(object.excludedIsin) ? globalThis.String(object.excludedIsin) : "",
    };
  },

  toJSON(message: BondCollectionResponse): unknown {
    const obj: any = {};
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.preFilterCriteria !== "") {
      obj.preFilterCriteria = message.preFilterCriteria;
    }
    if (message.postFilterCriteria !== "") {
      obj.postFilterCriteria = message.postFilterCriteria;
    }
    if (message.sortCriteria !== "") {
      obj.sortCriteria = message.sortCriteria;
    }
    if (message.collectionItemLimit !== 0) {
      obj.collectionItemLimit = Math.round(message.collectionItemLimit);
    }
    if (message.collectionType !== 0) {
      obj.collectionType = collectionTypeToJSON(message.collectionType);
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.displayType !== 0) {
      obj.displayType = displayTypeToJSON(message.displayType);
    }
    if (message.excludedIsin !== "") {
      obj.excludedIsin = message.excludedIsin;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondCollectionResponse>, I>>(base?: I): BondCollectionResponse {
    return BondCollectionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondCollectionResponse>, I>>(object: I): BondCollectionResponse {
    const message = createBaseBondCollectionResponse();
    message.description = object.description ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.isActive = object.isActive ?? false;
    message.name = object.name ?? "";
    message.title = object.title ?? "";
    message.preFilterCriteria = object.preFilterCriteria ?? "";
    message.postFilterCriteria = object.postFilterCriteria ?? "";
    message.sortCriteria = object.sortCriteria ?? "";
    message.collectionItemLimit = object.collectionItemLimit ?? 0;
    message.collectionType = object.collectionType ?? 0;
    message.id = object.id ?? "";
    message.displayType = object.displayType ?? 0;
    message.excludedIsin = object.excludedIsin ?? "";
    return message;
  },
};

function createBaseDeleteBondCollectionResponse(): DeleteBondCollectionResponse {
  return { status: false };
}

export const DeleteBondCollectionResponse: MessageFns<DeleteBondCollectionResponse> = {
  encode(message: DeleteBondCollectionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteBondCollectionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteBondCollectionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteBondCollectionResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: DeleteBondCollectionResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteBondCollectionResponse>, I>>(base?: I): DeleteBondCollectionResponse {
    return DeleteBondCollectionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBondCollectionResponse>, I>>(object: I): DeleteBondCollectionResponse {
    const message = createBaseDeleteBondCollectionResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseUpdateBondCollectionRequest(): UpdateBondCollectionRequest {
  return {
    id: "",
    description: "",
    iconUrl: "",
    isActive: false,
    name: "",
    title: "",
    preFilterCriteria: "",
    postFilterCriteria: "",
    sortCriteria: "",
    collectionItemLimit: 0,
    includeSoldOut: false,
    collectionType: 0,
    displayType: 0,
    excludedIsin: "",
  };
}

export const UpdateBondCollectionRequest: MessageFns<UpdateBondCollectionRequest> = {
  encode(message: UpdateBondCollectionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.iconUrl !== "") {
      writer.uint32(26).string(message.iconUrl);
    }
    if (message.isActive !== false) {
      writer.uint32(32).bool(message.isActive);
    }
    if (message.name !== "") {
      writer.uint32(42).string(message.name);
    }
    if (message.title !== "") {
      writer.uint32(50).string(message.title);
    }
    if (message.preFilterCriteria !== "") {
      writer.uint32(74).string(message.preFilterCriteria);
    }
    if (message.postFilterCriteria !== "") {
      writer.uint32(82).string(message.postFilterCriteria);
    }
    if (message.sortCriteria !== "") {
      writer.uint32(90).string(message.sortCriteria);
    }
    if (message.collectionItemLimit !== 0) {
      writer.uint32(96).int32(message.collectionItemLimit);
    }
    if (message.includeSoldOut !== false) {
      writer.uint32(104).bool(message.includeSoldOut);
    }
    if (message.collectionType !== 0) {
      writer.uint32(112).int32(message.collectionType);
    }
    if (message.displayType !== 0) {
      writer.uint32(120).int32(message.displayType);
    }
    if (message.excludedIsin !== "") {
      writer.uint32(130).string(message.excludedIsin);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateBondCollectionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateBondCollectionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.preFilterCriteria = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.postFilterCriteria = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.sortCriteria = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.collectionItemLimit = reader.int32();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.includeSoldOut = reader.bool();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.collectionType = reader.int32() as any;
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.displayType = reader.int32() as any;
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.excludedIsin = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateBondCollectionRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      preFilterCriteria: isSet(object.preFilterCriteria) ? globalThis.String(object.preFilterCriteria) : "",
      postFilterCriteria: isSet(object.postFilterCriteria) ? globalThis.String(object.postFilterCriteria) : "",
      sortCriteria: isSet(object.sortCriteria) ? globalThis.String(object.sortCriteria) : "",
      collectionItemLimit: isSet(object.collectionItemLimit) ? globalThis.Number(object.collectionItemLimit) : 0,
      includeSoldOut: isSet(object.includeSoldOut) ? globalThis.Boolean(object.includeSoldOut) : false,
      collectionType: isSet(object.collectionType) ? collectionTypeFromJSON(object.collectionType) : 0,
      displayType: isSet(object.displayType) ? displayTypeFromJSON(object.displayType) : 0,
      excludedIsin: isSet(object.excludedIsin) ? globalThis.String(object.excludedIsin) : "",
    };
  },

  toJSON(message: UpdateBondCollectionRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.preFilterCriteria !== "") {
      obj.preFilterCriteria = message.preFilterCriteria;
    }
    if (message.postFilterCriteria !== "") {
      obj.postFilterCriteria = message.postFilterCriteria;
    }
    if (message.sortCriteria !== "") {
      obj.sortCriteria = message.sortCriteria;
    }
    if (message.collectionItemLimit !== 0) {
      obj.collectionItemLimit = Math.round(message.collectionItemLimit);
    }
    if (message.includeSoldOut !== false) {
      obj.includeSoldOut = message.includeSoldOut;
    }
    if (message.collectionType !== 0) {
      obj.collectionType = collectionTypeToJSON(message.collectionType);
    }
    if (message.displayType !== 0) {
      obj.displayType = displayTypeToJSON(message.displayType);
    }
    if (message.excludedIsin !== "") {
      obj.excludedIsin = message.excludedIsin;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateBondCollectionRequest>, I>>(base?: I): UpdateBondCollectionRequest {
    return UpdateBondCollectionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBondCollectionRequest>, I>>(object: I): UpdateBondCollectionRequest {
    const message = createBaseUpdateBondCollectionRequest();
    message.id = object.id ?? "";
    message.description = object.description ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.isActive = object.isActive ?? false;
    message.name = object.name ?? "";
    message.title = object.title ?? "";
    message.preFilterCriteria = object.preFilterCriteria ?? "";
    message.postFilterCriteria = object.postFilterCriteria ?? "";
    message.sortCriteria = object.sortCriteria ?? "";
    message.collectionItemLimit = object.collectionItemLimit ?? 0;
    message.includeSoldOut = object.includeSoldOut ?? false;
    message.collectionType = object.collectionType ?? 0;
    message.displayType = object.displayType ?? 0;
    message.excludedIsin = object.excludedIsin ?? "";
    return message;
  },
};

function createBaseBondsCollection(): BondsCollection {
  return { updatedAt: "", description: "", name: "", createdAt: "", id: "" };
}

export const BondsCollection: MessageFns<BondsCollection> = {
  encode(message: BondsCollection, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.updatedAt !== "") {
      writer.uint32(10).string(message.updatedAt);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    if (message.createdAt !== "") {
      writer.uint32(34).string(message.createdAt);
    }
    if (message.id !== "") {
      writer.uint32(42).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondsCollection {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondsCollection();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.updatedAt = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.createdAt = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondsCollection {
    return {
      updatedAt: isSet(object.updatedAt) ? globalThis.String(object.updatedAt) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      createdAt: isSet(object.createdAt) ? globalThis.String(object.createdAt) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
    };
  },

  toJSON(message: BondsCollection): unknown {
    const obj: any = {};
    if (message.updatedAt !== "") {
      obj.updatedAt = message.updatedAt;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.createdAt !== "") {
      obj.createdAt = message.createdAt;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondsCollection>, I>>(base?: I): BondsCollection {
    return BondsCollection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondsCollection>, I>>(object: I): BondsCollection {
    const message = createBaseBondsCollection();
    message.updatedAt = object.updatedAt ?? "";
    message.description = object.description ?? "";
    message.name = object.name ?? "";
    message.createdAt = object.createdAt ?? "";
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseGetBondsCollectionRequest(): GetBondsCollectionRequest {
  return { page: 0, size: 0, searchString: "" };
}

export const GetBondsCollectionRequest: MessageFns<GetBondsCollectionRequest> = {
  encode(message: GetBondsCollectionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.page !== 0) {
      writer.uint32(8).int32(message.page);
    }
    if (message.size !== 0) {
      writer.uint32(16).int32(message.size);
    }
    if (message.searchString !== "") {
      writer.uint32(34).string(message.searchString);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondsCollectionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondsCollectionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.page = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.size = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.searchString = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBondsCollectionRequest {
    return {
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      size: isSet(object.size) ? globalThis.Number(object.size) : 0,
      searchString: isSet(object.searchString) ? globalThis.String(object.searchString) : "",
    };
  },

  toJSON(message: GetBondsCollectionRequest): unknown {
    const obj: any = {};
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.size !== 0) {
      obj.size = Math.round(message.size);
    }
    if (message.searchString !== "") {
      obj.searchString = message.searchString;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondsCollectionRequest>, I>>(base?: I): GetBondsCollectionRequest {
    return GetBondsCollectionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondsCollectionRequest>, I>>(object: I): GetBondsCollectionRequest {
    const message = createBaseGetBondsCollectionRequest();
    message.page = object.page ?? 0;
    message.size = object.size ?? 0;
    message.searchString = object.searchString ?? "";
    return message;
  },
};

function createBaseGetBondsCollectionResponse(): GetBondsCollectionResponse {
  return { data: [], hasNextPage: false };
}

export const GetBondsCollectionResponse: MessageFns<GetBondsCollectionResponse> = {
  encode(message: GetBondsCollectionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      BondsCollection.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.hasNextPage !== false) {
      writer.uint32(16).bool(message.hasNextPage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondsCollectionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondsCollectionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(BondsCollection.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasNextPage = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBondsCollectionResponse {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => BondsCollection.fromJSON(e)) : [],
      hasNextPage: isSet(object.hasNextPage) ? globalThis.Boolean(object.hasNextPage) : false,
    };
  },

  toJSON(message: GetBondsCollectionResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => BondsCollection.toJSON(e));
    }
    if (message.hasNextPage !== false) {
      obj.hasNextPage = message.hasNextPage;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondsCollectionResponse>, I>>(base?: I): GetBondsCollectionResponse {
    return GetBondsCollectionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondsCollectionResponse>, I>>(object: I): GetBondsCollectionResponse {
    const message = createBaseGetBondsCollectionResponse();
    message.data = object.data?.map((e) => BondsCollection.fromPartial(e)) || [];
    message.hasNextPage = object.hasNextPage ?? false;
    return message;
  },
};

function createBaseCreateAndUpdateBondCollectionItemRequest(): CreateAndUpdateBondCollectionItemRequest {
  return {
    id: "",
    displayTitle: "",
    priority: 0,
    isinCode: "",
    isActive: false,
    collectionId: "",
    createdAt: "",
    updatedAt: "",
    sellingPoint: "",
    buttonCta: "",
    struckenYield: 0,
    showTag: false,
    isDynamicTag: false,
  };
}

export const CreateAndUpdateBondCollectionItemRequest: MessageFns<CreateAndUpdateBondCollectionItemRequest> = {
  encode(message: CreateAndUpdateBondCollectionItemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.displayTitle !== "") {
      writer.uint32(18).string(message.displayTitle);
    }
    if (message.priority !== 0) {
      writer.uint32(24).int32(message.priority);
    }
    if (message.isinCode !== "") {
      writer.uint32(42).string(message.isinCode);
    }
    if (message.isActive !== false) {
      writer.uint32(48).bool(message.isActive);
    }
    if (message.collectionId !== "") {
      writer.uint32(58).string(message.collectionId);
    }
    if (message.createdAt !== "") {
      writer.uint32(66).string(message.createdAt);
    }
    if (message.updatedAt !== "") {
      writer.uint32(74).string(message.updatedAt);
    }
    if (message.sellingPoint !== "") {
      writer.uint32(82).string(message.sellingPoint);
    }
    if (message.buttonCta !== "") {
      writer.uint32(90).string(message.buttonCta);
    }
    if (message.struckenYield !== 0) {
      writer.uint32(97).double(message.struckenYield);
    }
    if (message.showTag !== false) {
      writer.uint32(104).bool(message.showTag);
    }
    if (message.isDynamicTag !== false) {
      writer.uint32(112).bool(message.isDynamicTag);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateAndUpdateBondCollectionItemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateAndUpdateBondCollectionItemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.displayTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.isinCode = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.createdAt = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.updatedAt = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.sellingPoint = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.buttonCta = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.struckenYield = reader.double();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.showTag = reader.bool();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.isDynamicTag = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateAndUpdateBondCollectionItemRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      displayTitle: isSet(object.displayTitle) ? globalThis.String(object.displayTitle) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      isinCode: isSet(object.isinCode) ? globalThis.String(object.isinCode) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "",
      createdAt: isSet(object.createdAt) ? globalThis.String(object.createdAt) : "",
      updatedAt: isSet(object.updatedAt) ? globalThis.String(object.updatedAt) : "",
      sellingPoint: isSet(object.sellingPoint) ? globalThis.String(object.sellingPoint) : "",
      buttonCta: isSet(object.buttonCta) ? globalThis.String(object.buttonCta) : "",
      struckenYield: isSet(object.struckenYield) ? globalThis.Number(object.struckenYield) : 0,
      showTag: isSet(object.showTag) ? globalThis.Boolean(object.showTag) : false,
      isDynamicTag: isSet(object.isDynamicTag) ? globalThis.Boolean(object.isDynamicTag) : false,
    };
  },

  toJSON(message: CreateAndUpdateBondCollectionItemRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.displayTitle !== "") {
      obj.displayTitle = message.displayTitle;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.isinCode !== "") {
      obj.isinCode = message.isinCode;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    if (message.createdAt !== "") {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== "") {
      obj.updatedAt = message.updatedAt;
    }
    if (message.sellingPoint !== "") {
      obj.sellingPoint = message.sellingPoint;
    }
    if (message.buttonCta !== "") {
      obj.buttonCta = message.buttonCta;
    }
    if (message.struckenYield !== 0) {
      obj.struckenYield = message.struckenYield;
    }
    if (message.showTag !== false) {
      obj.showTag = message.showTag;
    }
    if (message.isDynamicTag !== false) {
      obj.isDynamicTag = message.isDynamicTag;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateAndUpdateBondCollectionItemRequest>, I>>(
    base?: I,
  ): CreateAndUpdateBondCollectionItemRequest {
    return CreateAndUpdateBondCollectionItemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateAndUpdateBondCollectionItemRequest>, I>>(
    object: I,
  ): CreateAndUpdateBondCollectionItemRequest {
    const message = createBaseCreateAndUpdateBondCollectionItemRequest();
    message.id = object.id ?? "";
    message.displayTitle = object.displayTitle ?? "";
    message.priority = object.priority ?? 0;
    message.isinCode = object.isinCode ?? "";
    message.isActive = object.isActive ?? false;
    message.collectionId = object.collectionId ?? "";
    message.createdAt = object.createdAt ?? "";
    message.updatedAt = object.updatedAt ?? "";
    message.sellingPoint = object.sellingPoint ?? "";
    message.buttonCta = object.buttonCta ?? "";
    message.struckenYield = object.struckenYield ?? 0;
    message.showTag = object.showTag ?? false;
    message.isDynamicTag = object.isDynamicTag ?? false;
    return message;
  },
};

function createBaseBondCollectionItemResponse(): BondCollectionItemResponse {
  return {
    id: "",
    displayTitle: "",
    priority: 0,
    tagConfig: undefined,
    isinCode: "",
    isActive: false,
    collectionId: "",
    createdAt: "",
    updatedAt: "",
    sellingPoint: "",
    buttonCta: "",
    struckenYield: 0,
    showTag: false,
    isDynamicTag: false,
  };
}

export const BondCollectionItemResponse: MessageFns<BondCollectionItemResponse> = {
  encode(message: BondCollectionItemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.displayTitle !== "") {
      writer.uint32(18).string(message.displayTitle);
    }
    if (message.priority !== 0) {
      writer.uint32(24).int32(message.priority);
    }
    if (message.tagConfig !== undefined) {
      TagConfig.encode(message.tagConfig, writer.uint32(34).fork()).join();
    }
    if (message.isinCode !== "") {
      writer.uint32(42).string(message.isinCode);
    }
    if (message.isActive !== false) {
      writer.uint32(48).bool(message.isActive);
    }
    if (message.collectionId !== "") {
      writer.uint32(58).string(message.collectionId);
    }
    if (message.createdAt !== "") {
      writer.uint32(66).string(message.createdAt);
    }
    if (message.updatedAt !== "") {
      writer.uint32(74).string(message.updatedAt);
    }
    if (message.sellingPoint !== "") {
      writer.uint32(82).string(message.sellingPoint);
    }
    if (message.buttonCta !== "") {
      writer.uint32(90).string(message.buttonCta);
    }
    if (message.struckenYield !== 0) {
      writer.uint32(97).double(message.struckenYield);
    }
    if (message.showTag !== false) {
      writer.uint32(104).bool(message.showTag);
    }
    if (message.isDynamicTag !== false) {
      writer.uint32(112).bool(message.isDynamicTag);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondCollectionItemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondCollectionItemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.displayTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.tagConfig = TagConfig.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.isinCode = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.createdAt = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.updatedAt = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.sellingPoint = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.buttonCta = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.struckenYield = reader.double();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.showTag = reader.bool();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.isDynamicTag = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondCollectionItemResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      displayTitle: isSet(object.displayTitle) ? globalThis.String(object.displayTitle) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      tagConfig: isSet(object.tagConfig) ? TagConfig.fromJSON(object.tagConfig) : undefined,
      isinCode: isSet(object.isinCode) ? globalThis.String(object.isinCode) : "",
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
      collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "",
      createdAt: isSet(object.createdAt) ? globalThis.String(object.createdAt) : "",
      updatedAt: isSet(object.updatedAt) ? globalThis.String(object.updatedAt) : "",
      sellingPoint: isSet(object.sellingPoint) ? globalThis.String(object.sellingPoint) : "",
      buttonCta: isSet(object.buttonCta) ? globalThis.String(object.buttonCta) : "",
      struckenYield: isSet(object.struckenYield) ? globalThis.Number(object.struckenYield) : 0,
      showTag: isSet(object.showTag) ? globalThis.Boolean(object.showTag) : false,
      isDynamicTag: isSet(object.isDynamicTag) ? globalThis.Boolean(object.isDynamicTag) : false,
    };
  },

  toJSON(message: BondCollectionItemResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.displayTitle !== "") {
      obj.displayTitle = message.displayTitle;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.tagConfig !== undefined) {
      obj.tagConfig = TagConfig.toJSON(message.tagConfig);
    }
    if (message.isinCode !== "") {
      obj.isinCode = message.isinCode;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    if (message.createdAt !== "") {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== "") {
      obj.updatedAt = message.updatedAt;
    }
    if (message.sellingPoint !== "") {
      obj.sellingPoint = message.sellingPoint;
    }
    if (message.buttonCta !== "") {
      obj.buttonCta = message.buttonCta;
    }
    if (message.struckenYield !== 0) {
      obj.struckenYield = message.struckenYield;
    }
    if (message.showTag !== false) {
      obj.showTag = message.showTag;
    }
    if (message.isDynamicTag !== false) {
      obj.isDynamicTag = message.isDynamicTag;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondCollectionItemResponse>, I>>(base?: I): BondCollectionItemResponse {
    return BondCollectionItemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondCollectionItemResponse>, I>>(object: I): BondCollectionItemResponse {
    const message = createBaseBondCollectionItemResponse();
    message.id = object.id ?? "";
    message.displayTitle = object.displayTitle ?? "";
    message.priority = object.priority ?? 0;
    message.tagConfig = (object.tagConfig !== undefined && object.tagConfig !== null)
      ? TagConfig.fromPartial(object.tagConfig)
      : undefined;
    message.isinCode = object.isinCode ?? "";
    message.isActive = object.isActive ?? false;
    message.collectionId = object.collectionId ?? "";
    message.createdAt = object.createdAt ?? "";
    message.updatedAt = object.updatedAt ?? "";
    message.sellingPoint = object.sellingPoint ?? "";
    message.buttonCta = object.buttonCta ?? "";
    message.struckenYield = object.struckenYield ?? 0;
    message.showTag = object.showTag ?? false;
    message.isDynamicTag = object.isDynamicTag ?? false;
    return message;
  },
};

function createBaseDeleteBondCollectionItemResponse(): DeleteBondCollectionItemResponse {
  return { status: false };
}

export const DeleteBondCollectionItemResponse: MessageFns<DeleteBondCollectionItemResponse> = {
  encode(message: DeleteBondCollectionItemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteBondCollectionItemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteBondCollectionItemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteBondCollectionItemResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: DeleteBondCollectionItemResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteBondCollectionItemResponse>, I>>(
    base?: I,
  ): DeleteBondCollectionItemResponse {
    return DeleteBondCollectionItemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBondCollectionItemResponse>, I>>(
    object: I,
  ): DeleteBondCollectionItemResponse {
    const message = createBaseDeleteBondCollectionItemResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseGetBondsCollectionItemRequest(): GetBondsCollectionItemRequest {
  return { collectionId: "" };
}

export const GetBondsCollectionItemRequest: MessageFns<GetBondsCollectionItemRequest> = {
  encode(message: GetBondsCollectionItemRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.collectionId !== "") {
      writer.uint32(10).string(message.collectionId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondsCollectionItemRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondsCollectionItemRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collectionId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBondsCollectionItemRequest {
    return { collectionId: isSet(object.collectionId) ? globalThis.String(object.collectionId) : "" };
  },

  toJSON(message: GetBondsCollectionItemRequest): unknown {
    const obj: any = {};
    if (message.collectionId !== "") {
      obj.collectionId = message.collectionId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondsCollectionItemRequest>, I>>(base?: I): GetBondsCollectionItemRequest {
    return GetBondsCollectionItemRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondsCollectionItemRequest>, I>>(
    object: I,
  ): GetBondsCollectionItemRequest {
    const message = createBaseGetBondsCollectionItemRequest();
    message.collectionId = object.collectionId ?? "";
    return message;
  },
};

function createBaseGetBondsCollectionItemResponse(): GetBondsCollectionItemResponse {
  return { data: [] };
}

export const GetBondsCollectionItemResponse: MessageFns<GetBondsCollectionItemResponse> = {
  encode(message: GetBondsCollectionItemResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      BondCollectionItemResponse.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondsCollectionItemResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondsCollectionItemResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(BondCollectionItemResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBondsCollectionItemResponse {
    return {
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => BondCollectionItemResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetBondsCollectionItemResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => BondCollectionItemResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondsCollectionItemResponse>, I>>(base?: I): GetBondsCollectionItemResponse {
    return GetBondsCollectionItemResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondsCollectionItemResponse>, I>>(
    object: I,
  ): GetBondsCollectionItemResponse {
    const message = createBaseGetBondsCollectionItemResponse();
    message.data = object.data?.map((e) => BondCollectionItemResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetBondDetailsRequest(): GetBondDetailsRequest {
  return {};
}

export const GetBondDetailsRequest: MessageFns<GetBondDetailsRequest> = {
  encode(_: GetBondDetailsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondDetailsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondDetailsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): GetBondDetailsRequest {
    return {};
  },

  toJSON(_: GetBondDetailsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondDetailsRequest>, I>>(base?: I): GetBondDetailsRequest {
    return GetBondDetailsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondDetailsRequest>, I>>(_: I): GetBondDetailsRequest {
    const message = createBaseGetBondDetailsRequest();
    return message;
  },
};

function createBaseBondDetails(): BondDetails {
  return { isinCode: "", displayTitle: "", name: "" };
}

export const BondDetails: MessageFns<BondDetails> = {
  encode(message: BondDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isinCode !== "") {
      writer.uint32(10).string(message.isinCode);
    }
    if (message.displayTitle !== "") {
      writer.uint32(18).string(message.displayTitle);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.isinCode = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.displayTitle = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondDetails {
    return {
      isinCode: isSet(object.isinCode) ? globalThis.String(object.isinCode) : "",
      displayTitle: isSet(object.displayTitle) ? globalThis.String(object.displayTitle) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: BondDetails): unknown {
    const obj: any = {};
    if (message.isinCode !== "") {
      obj.isinCode = message.isinCode;
    }
    if (message.displayTitle !== "") {
      obj.displayTitle = message.displayTitle;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondDetails>, I>>(base?: I): BondDetails {
    return BondDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondDetails>, I>>(object: I): BondDetails {
    const message = createBaseBondDetails();
    message.isinCode = object.isinCode ?? "";
    message.displayTitle = object.displayTitle ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseGetBondDetailsResponse(): GetBondDetailsResponse {
  return { data: [] };
}

export const GetBondDetailsResponse: MessageFns<GetBondDetailsResponse> = {
  encode(message: GetBondDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      BondDetails.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetBondDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBondDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(BondDetails.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBondDetailsResponse {
    return { data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => BondDetails.fromJSON(e)) : [] };
  },

  toJSON(message: GetBondDetailsResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => BondDetails.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetBondDetailsResponse>, I>>(base?: I): GetBondDetailsResponse {
    return GetBondDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBondDetailsResponse>, I>>(object: I): GetBondDetailsResponse {
    const message = createBaseGetBondDetailsResponse();
    message.data = object.data?.map((e) => BondDetails.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBondsCollectionItemStatusResponse(): BondsCollectionItemStatusResponse {
  return { status: false };
}

export const BondsCollectionItemStatusResponse: MessageFns<BondsCollectionItemStatusResponse> = {
  encode(message: BondsCollectionItemStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BondsCollectionItemStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBondsCollectionItemStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BondsCollectionItemStatusResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: BondsCollectionItemStatusResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BondsCollectionItemStatusResponse>, I>>(
    base?: I,
  ): BondsCollectionItemStatusResponse {
    return BondsCollectionItemStatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BondsCollectionItemStatusResponse>, I>>(
    object: I,
  ): BondsCollectionItemStatusResponse {
    const message = createBaseBondsCollectionItemStatusResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseBulkUpdateBondItemPriorityRequest(): BulkUpdateBondItemPriorityRequest {
  return { items: [] };
}

export const BulkUpdateBondItemPriorityRequest: MessageFns<BulkUpdateBondItemPriorityRequest> = {
  encode(message: BulkUpdateBondItemPriorityRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.items) {
      BulkUpdateBondItemPriorityRequest_Item.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkUpdateBondItemPriorityRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkUpdateBondItemPriorityRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.items.push(BulkUpdateBondItemPriorityRequest_Item.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkUpdateBondItemPriorityRequest {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => BulkUpdateBondItemPriorityRequest_Item.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BulkUpdateBondItemPriorityRequest): unknown {
    const obj: any = {};
    if (message.items?.length) {
      obj.items = message.items.map((e) => BulkUpdateBondItemPriorityRequest_Item.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkUpdateBondItemPriorityRequest>, I>>(
    base?: I,
  ): BulkUpdateBondItemPriorityRequest {
    return BulkUpdateBondItemPriorityRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkUpdateBondItemPriorityRequest>, I>>(
    object: I,
  ): BulkUpdateBondItemPriorityRequest {
    const message = createBaseBulkUpdateBondItemPriorityRequest();
    message.items = object.items?.map((e) => BulkUpdateBondItemPriorityRequest_Item.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBulkUpdateBondItemPriorityRequest_Item(): BulkUpdateBondItemPriorityRequest_Item {
  return { id: "", newPriority: 0 };
}

export const BulkUpdateBondItemPriorityRequest_Item: MessageFns<BulkUpdateBondItemPriorityRequest_Item> = {
  encode(message: BulkUpdateBondItemPriorityRequest_Item, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.newPriority !== 0) {
      writer.uint32(16).int32(message.newPriority);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkUpdateBondItemPriorityRequest_Item {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkUpdateBondItemPriorityRequest_Item();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.newPriority = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkUpdateBondItemPriorityRequest_Item {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      newPriority: isSet(object.newPriority) ? globalThis.Number(object.newPriority) : 0,
    };
  },

  toJSON(message: BulkUpdateBondItemPriorityRequest_Item): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.newPriority !== 0) {
      obj.newPriority = Math.round(message.newPriority);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkUpdateBondItemPriorityRequest_Item>, I>>(
    base?: I,
  ): BulkUpdateBondItemPriorityRequest_Item {
    return BulkUpdateBondItemPriorityRequest_Item.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkUpdateBondItemPriorityRequest_Item>, I>>(
    object: I,
  ): BulkUpdateBondItemPriorityRequest_Item {
    const message = createBaseBulkUpdateBondItemPriorityRequest_Item();
    message.id = object.id ?? "";
    message.newPriority = object.newPriority ?? 0;
    return message;
  },
};

function createBaseBulkUpdateBondItemPriorityResponse(): BulkUpdateBondItemPriorityResponse {
  return { status: false };
}

export const BulkUpdateBondItemPriorityResponse: MessageFns<BulkUpdateBondItemPriorityResponse> = {
  encode(message: BulkUpdateBondItemPriorityResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkUpdateBondItemPriorityResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkUpdateBondItemPriorityResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkUpdateBondItemPriorityResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: BulkUpdateBondItemPriorityResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkUpdateBondItemPriorityResponse>, I>>(
    base?: I,
  ): BulkUpdateBondItemPriorityResponse {
    return BulkUpdateBondItemPriorityResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkUpdateBondItemPriorityResponse>, I>>(
    object: I,
  ): BulkUpdateBondItemPriorityResponse {
    const message = createBaseBulkUpdateBondItemPriorityResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseGetAllCollectionExpressionsResponse(): GetAllCollectionExpressionsResponse {
  return { collectionExpressions: [] };
}

export const GetAllCollectionExpressionsResponse: MessageFns<GetAllCollectionExpressionsResponse> = {
  encode(message: GetAllCollectionExpressionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.collectionExpressions) {
      GetAllCollectionExpressionsResponse_CollectionExpression.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllCollectionExpressionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllCollectionExpressionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.collectionExpressions.push(
            GetAllCollectionExpressionsResponse_CollectionExpression.decode(reader, reader.uint32()),
          );
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAllCollectionExpressionsResponse {
    return {
      collectionExpressions: globalThis.Array.isArray(object?.collectionExpressions)
        ? object.collectionExpressions.map((e: any) =>
          GetAllCollectionExpressionsResponse_CollectionExpression.fromJSON(e)
        )
        : [],
    };
  },

  toJSON(message: GetAllCollectionExpressionsResponse): unknown {
    const obj: any = {};
    if (message.collectionExpressions?.length) {
      obj.collectionExpressions = message.collectionExpressions.map((e) =>
        GetAllCollectionExpressionsResponse_CollectionExpression.toJSON(e)
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAllCollectionExpressionsResponse>, I>>(
    base?: I,
  ): GetAllCollectionExpressionsResponse {
    return GetAllCollectionExpressionsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAllCollectionExpressionsResponse>, I>>(
    object: I,
  ): GetAllCollectionExpressionsResponse {
    const message = createBaseGetAllCollectionExpressionsResponse();
    message.collectionExpressions =
      object.collectionExpressions?.map((e) =>
        GetAllCollectionExpressionsResponse_CollectionExpression.fromPartial(e)
      ) || [];
    return message;
  },
};

function createBaseGetAllCollectionExpressionsResponse_CollectionExpression(): GetAllCollectionExpressionsResponse_CollectionExpression {
  return { name: "", preFilterCriteria: "", postFilterCriteria: "", sortCriteria: "" };
}

export const GetAllCollectionExpressionsResponse_CollectionExpression: MessageFns<
  GetAllCollectionExpressionsResponse_CollectionExpression
> = {
  encode(
    message: GetAllCollectionExpressionsResponse_CollectionExpression,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.preFilterCriteria !== "") {
      writer.uint32(18).string(message.preFilterCriteria);
    }
    if (message.postFilterCriteria !== "") {
      writer.uint32(26).string(message.postFilterCriteria);
    }
    if (message.sortCriteria !== "") {
      writer.uint32(34).string(message.sortCriteria);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAllCollectionExpressionsResponse_CollectionExpression {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAllCollectionExpressionsResponse_CollectionExpression();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.preFilterCriteria = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.postFilterCriteria = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.sortCriteria = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAllCollectionExpressionsResponse_CollectionExpression {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      preFilterCriteria: isSet(object.preFilterCriteria) ? globalThis.String(object.preFilterCriteria) : "",
      postFilterCriteria: isSet(object.postFilterCriteria) ? globalThis.String(object.postFilterCriteria) : "",
      sortCriteria: isSet(object.sortCriteria) ? globalThis.String(object.sortCriteria) : "",
    };
  },

  toJSON(message: GetAllCollectionExpressionsResponse_CollectionExpression): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.preFilterCriteria !== "") {
      obj.preFilterCriteria = message.preFilterCriteria;
    }
    if (message.postFilterCriteria !== "") {
      obj.postFilterCriteria = message.postFilterCriteria;
    }
    if (message.sortCriteria !== "") {
      obj.sortCriteria = message.sortCriteria;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAllCollectionExpressionsResponse_CollectionExpression>, I>>(
    base?: I,
  ): GetAllCollectionExpressionsResponse_CollectionExpression {
    return GetAllCollectionExpressionsResponse_CollectionExpression.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAllCollectionExpressionsResponse_CollectionExpression>, I>>(
    object: I,
  ): GetAllCollectionExpressionsResponse_CollectionExpression {
    const message = createBaseGetAllCollectionExpressionsResponse_CollectionExpression();
    message.name = object.name ?? "";
    message.preFilterCriteria = object.preFilterCriteria ?? "";
    message.postFilterCriteria = object.postFilterCriteria ?? "";
    message.sortCriteria = object.sortCriteria ?? "";
    return message;
  },
};

export interface BrokingCollectionAdminService {
  CreateBondCollection(request: CreateBondCollectionRequest): Promise<CreateBondCollectionResponse>;
  UpdateBondCollection(request: UpdateBondCollectionRequest): Promise<UpdateBondCollectionResponse>;
  DeleteBondCollection(request: IdRequest): Promise<DeleteBondCollectionResponse>;
  GetBondCollection(request: IdRequest): Promise<BondCollectionResponse>;
  GetBondsCollection(request: GetBondsCollectionRequest): Promise<GetBondsCollectionResponse>;
  CreateAndUpdateBondItemCollection(
    request: CreateAndUpdateBondCollectionItemRequest,
  ): Promise<BondsCollectionItemStatusResponse>;
  DeleteBondItemCollection(request: IdRequest): Promise<DeleteBondCollectionItemResponse>;
  GetBondItemCollection(request: IdRequest): Promise<BondCollectionItemResponse>;
  GetBondsItemCollection(request: GetBondsCollectionItemRequest): Promise<GetBondsCollectionItemResponse>;
  GetBondsDetails(request: GetBondDetailsRequest): Promise<GetBondDetailsResponse>;
  GetAllBondCollections(request: Empty): Promise<AllCollectionsResponse>;
  UpdateBondItemPriority(request: UpdateBondItemPriorityRequest): Promise<UpdateBondItemPriorityResponse>;
  BulkUpdateBondItemPriority(request: BulkUpdateBondItemPriorityRequest): Promise<BulkUpdateBondItemPriorityResponse>;
  GetAllCollectionExpressions(request: Empty): Promise<GetAllCollectionExpressionsResponse>;
}

export const BrokingCollectionAdminServiceServiceName = "com.stablemoney.api.broking.BrokingCollectionAdminService";
export class BrokingCollectionAdminServiceClientImpl implements BrokingCollectionAdminService {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || BrokingCollectionAdminServiceServiceName;
    this.rpc = rpc;
    this.CreateBondCollection = this.CreateBondCollection.bind(this);
    this.UpdateBondCollection = this.UpdateBondCollection.bind(this);
    this.DeleteBondCollection = this.DeleteBondCollection.bind(this);
    this.GetBondCollection = this.GetBondCollection.bind(this);
    this.GetBondsCollection = this.GetBondsCollection.bind(this);
    this.CreateAndUpdateBondItemCollection = this.CreateAndUpdateBondItemCollection.bind(this);
    this.DeleteBondItemCollection = this.DeleteBondItemCollection.bind(this);
    this.GetBondItemCollection = this.GetBondItemCollection.bind(this);
    this.GetBondsItemCollection = this.GetBondsItemCollection.bind(this);
    this.GetBondsDetails = this.GetBondsDetails.bind(this);
    this.GetAllBondCollections = this.GetAllBondCollections.bind(this);
    this.UpdateBondItemPriority = this.UpdateBondItemPriority.bind(this);
    this.BulkUpdateBondItemPriority = this.BulkUpdateBondItemPriority.bind(this);
    this.GetAllCollectionExpressions = this.GetAllCollectionExpressions.bind(this);
  }
  CreateBondCollection(request: CreateBondCollectionRequest): Promise<CreateBondCollectionResponse> {
    const data = CreateBondCollectionRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "CreateBondCollection", data);
    return promise.then((data) => CreateBondCollectionResponse.decode(new BinaryReader(data)));
  }

  UpdateBondCollection(request: UpdateBondCollectionRequest): Promise<UpdateBondCollectionResponse> {
    const data = UpdateBondCollectionRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "UpdateBondCollection", data);
    return promise.then((data) => UpdateBondCollectionResponse.decode(new BinaryReader(data)));
  }

  DeleteBondCollection(request: IdRequest): Promise<DeleteBondCollectionResponse> {
    const data = IdRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "DeleteBondCollection", data);
    return promise.then((data) => DeleteBondCollectionResponse.decode(new BinaryReader(data)));
  }

  GetBondCollection(request: IdRequest): Promise<BondCollectionResponse> {
    const data = IdRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "GetBondCollection", data);
    return promise.then((data) => BondCollectionResponse.decode(new BinaryReader(data)));
  }

  GetBondsCollection(request: GetBondsCollectionRequest): Promise<GetBondsCollectionResponse> {
    const data = GetBondsCollectionRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "GetBondsCollection", data);
    return promise.then((data) => GetBondsCollectionResponse.decode(new BinaryReader(data)));
  }

  CreateAndUpdateBondItemCollection(
    request: CreateAndUpdateBondCollectionItemRequest,
  ): Promise<BondsCollectionItemStatusResponse> {
    const data = CreateAndUpdateBondCollectionItemRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "CreateAndUpdateBondItemCollection", data);
    return promise.then((data) => BondsCollectionItemStatusResponse.decode(new BinaryReader(data)));
  }

  DeleteBondItemCollection(request: IdRequest): Promise<DeleteBondCollectionItemResponse> {
    const data = IdRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "DeleteBondItemCollection", data);
    return promise.then((data) => DeleteBondCollectionItemResponse.decode(new BinaryReader(data)));
  }

  GetBondItemCollection(request: IdRequest): Promise<BondCollectionItemResponse> {
    const data = IdRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "GetBondItemCollection", data);
    return promise.then((data) => BondCollectionItemResponse.decode(new BinaryReader(data)));
  }

  GetBondsItemCollection(request: GetBondsCollectionItemRequest): Promise<GetBondsCollectionItemResponse> {
    const data = GetBondsCollectionItemRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "GetBondsItemCollection", data);
    return promise.then((data) => GetBondsCollectionItemResponse.decode(new BinaryReader(data)));
  }

  GetBondsDetails(request: GetBondDetailsRequest): Promise<GetBondDetailsResponse> {
    const data = GetBondDetailsRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "GetBondsDetails", data);
    return promise.then((data) => GetBondDetailsResponse.decode(new BinaryReader(data)));
  }

  GetAllBondCollections(request: Empty): Promise<AllCollectionsResponse> {
    const data = Empty.encode(request).finish();
    const promise = this.rpc.request(this.service, "GetAllBondCollections", data);
    return promise.then((data) => AllCollectionsResponse.decode(new BinaryReader(data)));
  }

  UpdateBondItemPriority(request: UpdateBondItemPriorityRequest): Promise<UpdateBondItemPriorityResponse> {
    const data = UpdateBondItemPriorityRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "UpdateBondItemPriority", data);
    return promise.then((data) => UpdateBondItemPriorityResponse.decode(new BinaryReader(data)));
  }

  BulkUpdateBondItemPriority(request: BulkUpdateBondItemPriorityRequest): Promise<BulkUpdateBondItemPriorityResponse> {
    const data = BulkUpdateBondItemPriorityRequest.encode(request).finish();
    const promise = this.rpc.request(this.service, "BulkUpdateBondItemPriority", data);
    return promise.then((data) => BulkUpdateBondItemPriorityResponse.decode(new BinaryReader(data)));
  }

  GetAllCollectionExpressions(request: Empty): Promise<GetAllCollectionExpressionsResponse> {
    const data = Empty.encode(request).finish();
    const promise = this.rpc.request(this.service, "GetAllCollectionExpressions", data);
    return promise.then((data) => GetAllCollectionExpressionsResponse.decode(new BinaryReader(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
