// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: DeviceBinding.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum DeviceBindingStatus {
  DEVICE_BINDING_STATUS_UNKNOWN = 0,
  DEVICE_BINDING_STATUS_PENDING = 1,
  DEVICE_BINDING_STATUS_APPROVED = 2,
  DEVICE_BINDING_STATUS_REJECTED = 3,
  REQUEST_ID_NOT_FOUND = 4,
  UNRECOGNIZED = -1,
}

export function deviceBindingStatusFromJSON(object: any): DeviceBindingStatus {
  switch (object) {
    case 0:
    case "DEVICE_BINDING_STATUS_UNKNOWN":
      return DeviceBindingStatus.DEVICE_BINDING_STATUS_UNKNOWN;
    case 1:
    case "DEVICE_BINDING_STATUS_PENDING":
      return DeviceBindingStatus.DEVICE_BINDING_STATUS_PENDING;
    case 2:
    case "DEVICE_BINDING_STATUS_APPROVED":
      return DeviceBindingStatus.DEVICE_BINDING_STATUS_APPROVED;
    case 3:
    case "DEVICE_BINDING_STATUS_REJECTED":
      return DeviceBindingStatus.DEVICE_BINDING_STATUS_REJECTED;
    case 4:
    case "REQUEST_ID_NOT_FOUND":
      return DeviceBindingStatus.REQUEST_ID_NOT_FOUND;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DeviceBindingStatus.UNRECOGNIZED;
  }
}

export function deviceBindingStatusToJSON(object: DeviceBindingStatus): string {
  switch (object) {
    case DeviceBindingStatus.DEVICE_BINDING_STATUS_UNKNOWN:
      return "DEVICE_BINDING_STATUS_UNKNOWN";
    case DeviceBindingStatus.DEVICE_BINDING_STATUS_PENDING:
      return "DEVICE_BINDING_STATUS_PENDING";
    case DeviceBindingStatus.DEVICE_BINDING_STATUS_APPROVED:
      return "DEVICE_BINDING_STATUS_APPROVED";
    case DeviceBindingStatus.DEVICE_BINDING_STATUS_REJECTED:
      return "DEVICE_BINDING_STATUS_REJECTED";
    case DeviceBindingStatus.REQUEST_ID_NOT_FOUND:
      return "REQUEST_ID_NOT_FOUND";
    case DeviceBindingStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface DeviceBindingRequest {
  deviceId: string;
  simData: SimData[];
}

export interface SimData {
  simSerialNumber: string;
  simName: string;
}

export interface DeviceBindingResponse {
  message: string;
  requestId: string;
  mobileNumber: string;
}

export interface InboundSmsRequest {
  longcodeNumber: string;
  customerNumber: string;
  price: string;
  status: string;
  circle: string;
  companyId: number;
  message: string;
  keyword: string;
  receivedAt: string;
}

export interface DeviceBindingStatusResponse {
  status: DeviceBindingStatus;
  message: string;
}

function createBaseDeviceBindingRequest(): DeviceBindingRequest {
  return { deviceId: "", simData: [] };
}

export const DeviceBindingRequest: MessageFns<DeviceBindingRequest> = {
  encode(message: DeviceBindingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.deviceId !== "") {
      writer.uint32(10).string(message.deviceId);
    }
    for (const v of message.simData) {
      SimData.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeviceBindingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeviceBindingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.deviceId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.simData.push(SimData.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeviceBindingRequest {
    return {
      deviceId: isSet(object.deviceId) ? globalThis.String(object.deviceId) : "",
      simData: globalThis.Array.isArray(object?.simData) ? object.simData.map((e: any) => SimData.fromJSON(e)) : [],
    };
  },

  toJSON(message: DeviceBindingRequest): unknown {
    const obj: any = {};
    if (message.deviceId !== "") {
      obj.deviceId = message.deviceId;
    }
    if (message.simData?.length) {
      obj.simData = message.simData.map((e) => SimData.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeviceBindingRequest>, I>>(base?: I): DeviceBindingRequest {
    return DeviceBindingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeviceBindingRequest>, I>>(object: I): DeviceBindingRequest {
    const message = createBaseDeviceBindingRequest();
    message.deviceId = object.deviceId ?? "";
    message.simData = object.simData?.map((e) => SimData.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSimData(): SimData {
  return { simSerialNumber: "", simName: "" };
}

export const SimData: MessageFns<SimData> = {
  encode(message: SimData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.simSerialNumber !== "") {
      writer.uint32(10).string(message.simSerialNumber);
    }
    if (message.simName !== "") {
      writer.uint32(18).string(message.simName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SimData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.simSerialNumber = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.simName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SimData {
    return {
      simSerialNumber: isSet(object.simSerialNumber) ? globalThis.String(object.simSerialNumber) : "",
      simName: isSet(object.simName) ? globalThis.String(object.simName) : "",
    };
  },

  toJSON(message: SimData): unknown {
    const obj: any = {};
    if (message.simSerialNumber !== "") {
      obj.simSerialNumber = message.simSerialNumber;
    }
    if (message.simName !== "") {
      obj.simName = message.simName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SimData>, I>>(base?: I): SimData {
    return SimData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SimData>, I>>(object: I): SimData {
    const message = createBaseSimData();
    message.simSerialNumber = object.simSerialNumber ?? "";
    message.simName = object.simName ?? "";
    return message;
  },
};

function createBaseDeviceBindingResponse(): DeviceBindingResponse {
  return { message: "", requestId: "", mobileNumber: "" };
}

export const DeviceBindingResponse: MessageFns<DeviceBindingResponse> = {
  encode(message: DeviceBindingResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.message !== "") {
      writer.uint32(10).string(message.message);
    }
    if (message.requestId !== "") {
      writer.uint32(18).string(message.requestId);
    }
    if (message.mobileNumber !== "") {
      writer.uint32(26).string(message.mobileNumber);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeviceBindingResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeviceBindingResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.requestId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.mobileNumber = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeviceBindingResponse {
    return {
      message: isSet(object.message) ? globalThis.String(object.message) : "",
      requestId: isSet(object.requestId) ? globalThis.String(object.requestId) : "",
      mobileNumber: isSet(object.mobileNumber) ? globalThis.String(object.mobileNumber) : "",
    };
  },

  toJSON(message: DeviceBindingResponse): unknown {
    const obj: any = {};
    if (message.message !== "") {
      obj.message = message.message;
    }
    if (message.requestId !== "") {
      obj.requestId = message.requestId;
    }
    if (message.mobileNumber !== "") {
      obj.mobileNumber = message.mobileNumber;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeviceBindingResponse>, I>>(base?: I): DeviceBindingResponse {
    return DeviceBindingResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeviceBindingResponse>, I>>(object: I): DeviceBindingResponse {
    const message = createBaseDeviceBindingResponse();
    message.message = object.message ?? "";
    message.requestId = object.requestId ?? "";
    message.mobileNumber = object.mobileNumber ?? "";
    return message;
  },
};

function createBaseInboundSmsRequest(): InboundSmsRequest {
  return {
    longcodeNumber: "",
    customerNumber: "",
    price: "",
    status: "",
    circle: "",
    companyId: 0,
    message: "",
    keyword: "",
    receivedAt: "",
  };
}

export const InboundSmsRequest: MessageFns<InboundSmsRequest> = {
  encode(message: InboundSmsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.longcodeNumber !== "") {
      writer.uint32(10).string(message.longcodeNumber);
    }
    if (message.customerNumber !== "") {
      writer.uint32(18).string(message.customerNumber);
    }
    if (message.price !== "") {
      writer.uint32(66).string(message.price);
    }
    if (message.status !== "") {
      writer.uint32(74).string(message.status);
    }
    if (message.circle !== "") {
      writer.uint32(26).string(message.circle);
    }
    if (message.companyId !== 0) {
      writer.uint32(32).int32(message.companyId);
    }
    if (message.message !== "") {
      writer.uint32(42).string(message.message);
    }
    if (message.keyword !== "") {
      writer.uint32(50).string(message.keyword);
    }
    if (message.receivedAt !== "") {
      writer.uint32(58).string(message.receivedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InboundSmsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInboundSmsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.longcodeNumber = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.customerNumber = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.price = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.status = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.circle = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.companyId = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.keyword = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.receivedAt = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InboundSmsRequest {
    return {
      longcodeNumber: isSet(object.longcodeNumber) ? globalThis.String(object.longcodeNumber) : "",
      customerNumber: isSet(object.customerNumber) ? globalThis.String(object.customerNumber) : "",
      price: isSet(object.price) ? globalThis.String(object.price) : "",
      status: isSet(object.status) ? globalThis.String(object.status) : "",
      circle: isSet(object.circle) ? globalThis.String(object.circle) : "",
      companyId: isSet(object.companyId) ? globalThis.Number(object.companyId) : 0,
      message: isSet(object.message) ? globalThis.String(object.message) : "",
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      receivedAt: isSet(object.receivedAt) ? globalThis.String(object.receivedAt) : "",
    };
  },

  toJSON(message: InboundSmsRequest): unknown {
    const obj: any = {};
    if (message.longcodeNumber !== "") {
      obj.longcodeNumber = message.longcodeNumber;
    }
    if (message.customerNumber !== "") {
      obj.customerNumber = message.customerNumber;
    }
    if (message.price !== "") {
      obj.price = message.price;
    }
    if (message.status !== "") {
      obj.status = message.status;
    }
    if (message.circle !== "") {
      obj.circle = message.circle;
    }
    if (message.companyId !== 0) {
      obj.companyId = Math.round(message.companyId);
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.receivedAt !== "") {
      obj.receivedAt = message.receivedAt;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InboundSmsRequest>, I>>(base?: I): InboundSmsRequest {
    return InboundSmsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InboundSmsRequest>, I>>(object: I): InboundSmsRequest {
    const message = createBaseInboundSmsRequest();
    message.longcodeNumber = object.longcodeNumber ?? "";
    message.customerNumber = object.customerNumber ?? "";
    message.price = object.price ?? "";
    message.status = object.status ?? "";
    message.circle = object.circle ?? "";
    message.companyId = object.companyId ?? 0;
    message.message = object.message ?? "";
    message.keyword = object.keyword ?? "";
    message.receivedAt = object.receivedAt ?? "";
    return message;
  },
};

function createBaseDeviceBindingStatusResponse(): DeviceBindingStatusResponse {
  return { status: 0, message: "" };
}

export const DeviceBindingStatusResponse: MessageFns<DeviceBindingStatusResponse> = {
  encode(message: DeviceBindingStatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== 0) {
      writer.uint32(8).int32(message.status);
    }
    if (message.message !== "") {
      writer.uint32(18).string(message.message);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeviceBindingStatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeviceBindingStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.message = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeviceBindingStatusResponse {
    return {
      status: isSet(object.status) ? deviceBindingStatusFromJSON(object.status) : 0,
      message: isSet(object.message) ? globalThis.String(object.message) : "",
    };
  },

  toJSON(message: DeviceBindingStatusResponse): unknown {
    const obj: any = {};
    if (message.status !== 0) {
      obj.status = deviceBindingStatusToJSON(message.status);
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeviceBindingStatusResponse>, I>>(base?: I): DeviceBindingStatusResponse {
    return DeviceBindingStatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeviceBindingStatusResponse>, I>>(object: I): DeviceBindingStatusResponse {
    const message = createBaseDeviceBindingStatusResponse();
    message.status = object.status ?? 0;
    message.message = object.message ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
