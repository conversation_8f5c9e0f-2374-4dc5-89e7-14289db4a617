// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: FindMyFd.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RedirectDeeplink } from "./BusinessCommon";
import { FixedDepositResponse } from "./Collection";

export const protobufPackage = "com.stablemoney.api.identity";

export enum QuestionResponseType {
  UNKNOWN_QUESTION_RESPONSE_TYPE = 0,
  SINGLE_RESPONSE_QUESTION = 1,
  MULTIPLE_RESPONSE_QUESTION = 2,
  UNRECOGNIZED = -1,
}

export function questionResponseTypeFromJSON(object: any): QuestionResponseType {
  switch (object) {
    case 0:
    case "UNKNOWN_QUESTION_RESPONSE_TYPE":
      return QuestionResponseType.UNKNOWN_QUESTION_RESPONSE_TYPE;
    case 1:
    case "SINGLE_RESPONSE_QUESTION":
      return QuestionResponseType.SINGLE_RESPONSE_QUESTION;
    case 2:
    case "MULTIPLE_RESPONSE_QUESTION":
      return QuestionResponseType.MULTIPLE_RESPONSE_QUESTION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return QuestionResponseType.UNRECOGNIZED;
  }
}

export function questionResponseTypeToJSON(object: QuestionResponseType): string {
  switch (object) {
    case QuestionResponseType.UNKNOWN_QUESTION_RESPONSE_TYPE:
      return "UNKNOWN_QUESTION_RESPONSE_TYPE";
    case QuestionResponseType.SINGLE_RESPONSE_QUESTION:
      return "SINGLE_RESPONSE_QUESTION";
    case QuestionResponseType.MULTIPLE_RESPONSE_QUESTION:
      return "MULTIPLE_RESPONSE_QUESTION";
    case QuestionResponseType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum QuestionnaireType {
  QUESTIONNAIRE_TYPE_UNKNOWN = 0,
  FIND_MY_FD = 1,
  UNRECOGNIZED = -1,
}

export function questionnaireTypeFromJSON(object: any): QuestionnaireType {
  switch (object) {
    case 0:
    case "QUESTIONNAIRE_TYPE_UNKNOWN":
      return QuestionnaireType.QUESTIONNAIRE_TYPE_UNKNOWN;
    case 1:
    case "FIND_MY_FD":
      return QuestionnaireType.FIND_MY_FD;
    case -1:
    case "UNRECOGNIZED":
    default:
      return QuestionnaireType.UNRECOGNIZED;
  }
}

export function questionnaireTypeToJSON(object: QuestionnaireType): string {
  switch (object) {
    case QuestionnaireType.QUESTIONNAIRE_TYPE_UNKNOWN:
      return "QUESTIONNAIRE_TYPE_UNKNOWN";
    case QuestionnaireType.FIND_MY_FD:
      return "FIND_MY_FD";
    case QuestionnaireType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface FindMyFdResponse {
  questionnaire: Question[];
}

export interface Question {
  id: string;
  question: string;
  description: string;
  submitButtonText: string;
  isSkippable: boolean;
  questionResponseType: QuestionResponseType;
  answer: AnswerResponse[];
  parameterAsked: string;
}

export interface AnswerResponse {
  id: string;
  answer: string;
  description: string;
  parameterValue: string;
  shortDescription: string;
}

export interface FindMyFdSubmitRequest {
  responses: Response[];
  isSkipped: boolean;
  investmentAmount: number;
  numberOfNibRecommendations?: number | undefined;
  numberOfIbRecommendations?: number | undefined;
}

export interface Response {
  questionId: string;
  answerId: string[];
}

export interface FindMyFdSubmitResponse {
  recommendations: FindMyFdRecommendation[];
  recommendationId: string;
}

export interface RecommendationReviewRequest {
  didRecommendationHelpedYou: boolean;
  recommendationId: string;
}

export interface RecommendationReviewResponse {
}

export interface FindMyFdRecommendation {
  fixedDeposit: FixedDepositResponse | undefined;
  investmentAmount: number;
  returns: number;
  maturityAmount: number;
  redirectDeeplink: RedirectDeeplink | undefined;
  benefitsDetails: BenefitsDetails[];
}

export interface BenefitsDetails {
  benefit: string;
  iconUrl: string;
}

export interface RecommendationReviewSubmitRequest {
  recommendationId: string;
  hasHelpedYou: boolean;
}

export interface RecommendationReviewSubmitResponse {
}

export interface InitiateFindMyFdResponse {
  isFindMyFdInitiated: boolean;
  recommendations: FindMyFdRecommendation[];
  isRecommendationFeedbackSubmitted: boolean;
  investmentAmount: number;
  recommendationId: string;
  findMyFdSubmitRequest: FindMyFdSubmitRequest | undefined;
}

function createBaseFindMyFdResponse(): FindMyFdResponse {
  return { questionnaire: [] };
}

export const FindMyFdResponse: MessageFns<FindMyFdResponse> = {
  encode(message: FindMyFdResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.questionnaire) {
      Question.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FindMyFdResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFindMyFdResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.questionnaire.push(Question.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FindMyFdResponse {
    return {
      questionnaire: globalThis.Array.isArray(object?.questionnaire)
        ? object.questionnaire.map((e: any) => Question.fromJSON(e))
        : [],
    };
  },

  toJSON(message: FindMyFdResponse): unknown {
    const obj: any = {};
    if (message.questionnaire?.length) {
      obj.questionnaire = message.questionnaire.map((e) => Question.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FindMyFdResponse>, I>>(base?: I): FindMyFdResponse {
    return FindMyFdResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FindMyFdResponse>, I>>(object: I): FindMyFdResponse {
    const message = createBaseFindMyFdResponse();
    message.questionnaire = object.questionnaire?.map((e) => Question.fromPartial(e)) || [];
    return message;
  },
};

function createBaseQuestion(): Question {
  return {
    id: "",
    question: "",
    description: "",
    submitButtonText: "",
    isSkippable: false,
    questionResponseType: 0,
    answer: [],
    parameterAsked: "",
  };
}

export const Question: MessageFns<Question> = {
  encode(message: Question, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.question !== "") {
      writer.uint32(18).string(message.question);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.submitButtonText !== "") {
      writer.uint32(34).string(message.submitButtonText);
    }
    if (message.isSkippable !== false) {
      writer.uint32(40).bool(message.isSkippable);
    }
    if (message.questionResponseType !== 0) {
      writer.uint32(48).int32(message.questionResponseType);
    }
    for (const v of message.answer) {
      AnswerResponse.encode(v!, writer.uint32(58).fork()).join();
    }
    if (message.parameterAsked !== "") {
      writer.uint32(74).string(message.parameterAsked);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Question {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQuestion();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.question = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.submitButtonText = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isSkippable = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.questionResponseType = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.answer.push(AnswerResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.parameterAsked = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Question {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      question: isSet(object.question) ? globalThis.String(object.question) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      submitButtonText: isSet(object.submitButtonText) ? globalThis.String(object.submitButtonText) : "",
      isSkippable: isSet(object.isSkippable) ? globalThis.Boolean(object.isSkippable) : false,
      questionResponseType: isSet(object.questionResponseType)
        ? questionResponseTypeFromJSON(object.questionResponseType)
        : 0,
      answer: globalThis.Array.isArray(object?.answer) ? object.answer.map((e: any) => AnswerResponse.fromJSON(e)) : [],
      parameterAsked: isSet(object.parameterAsked) ? globalThis.String(object.parameterAsked) : "",
    };
  },

  toJSON(message: Question): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.question !== "") {
      obj.question = message.question;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.submitButtonText !== "") {
      obj.submitButtonText = message.submitButtonText;
    }
    if (message.isSkippable !== false) {
      obj.isSkippable = message.isSkippable;
    }
    if (message.questionResponseType !== 0) {
      obj.questionResponseType = questionResponseTypeToJSON(message.questionResponseType);
    }
    if (message.answer?.length) {
      obj.answer = message.answer.map((e) => AnswerResponse.toJSON(e));
    }
    if (message.parameterAsked !== "") {
      obj.parameterAsked = message.parameterAsked;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Question>, I>>(base?: I): Question {
    return Question.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Question>, I>>(object: I): Question {
    const message = createBaseQuestion();
    message.id = object.id ?? "";
    message.question = object.question ?? "";
    message.description = object.description ?? "";
    message.submitButtonText = object.submitButtonText ?? "";
    message.isSkippable = object.isSkippable ?? false;
    message.questionResponseType = object.questionResponseType ?? 0;
    message.answer = object.answer?.map((e) => AnswerResponse.fromPartial(e)) || [];
    message.parameterAsked = object.parameterAsked ?? "";
    return message;
  },
};

function createBaseAnswerResponse(): AnswerResponse {
  return { id: "", answer: "", description: "", parameterValue: "", shortDescription: "" };
}

export const AnswerResponse: MessageFns<AnswerResponse> = {
  encode(message: AnswerResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.answer !== "") {
      writer.uint32(18).string(message.answer);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.parameterValue !== "") {
      writer.uint32(42).string(message.parameterValue);
    }
    if (message.shortDescription !== "") {
      writer.uint32(50).string(message.shortDescription);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AnswerResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAnswerResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.answer = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.parameterValue = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.shortDescription = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AnswerResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      answer: isSet(object.answer) ? globalThis.String(object.answer) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      parameterValue: isSet(object.parameterValue) ? globalThis.String(object.parameterValue) : "",
      shortDescription: isSet(object.shortDescription) ? globalThis.String(object.shortDescription) : "",
    };
  },

  toJSON(message: AnswerResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.answer !== "") {
      obj.answer = message.answer;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.parameterValue !== "") {
      obj.parameterValue = message.parameterValue;
    }
    if (message.shortDescription !== "") {
      obj.shortDescription = message.shortDescription;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AnswerResponse>, I>>(base?: I): AnswerResponse {
    return AnswerResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AnswerResponse>, I>>(object: I): AnswerResponse {
    const message = createBaseAnswerResponse();
    message.id = object.id ?? "";
    message.answer = object.answer ?? "";
    message.description = object.description ?? "";
    message.parameterValue = object.parameterValue ?? "";
    message.shortDescription = object.shortDescription ?? "";
    return message;
  },
};

function createBaseFindMyFdSubmitRequest(): FindMyFdSubmitRequest {
  return {
    responses: [],
    isSkipped: false,
    investmentAmount: 0,
    numberOfNibRecommendations: undefined,
    numberOfIbRecommendations: undefined,
  };
}

export const FindMyFdSubmitRequest: MessageFns<FindMyFdSubmitRequest> = {
  encode(message: FindMyFdSubmitRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.responses) {
      Response.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.isSkipped !== false) {
      writer.uint32(16).bool(message.isSkipped);
    }
    if (message.investmentAmount !== 0) {
      writer.uint32(25).double(message.investmentAmount);
    }
    if (message.numberOfNibRecommendations !== undefined) {
      writer.uint32(32).int32(message.numberOfNibRecommendations);
    }
    if (message.numberOfIbRecommendations !== undefined) {
      writer.uint32(40).int32(message.numberOfIbRecommendations);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FindMyFdSubmitRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFindMyFdSubmitRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.responses.push(Response.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isSkipped = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.numberOfNibRecommendations = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.numberOfIbRecommendations = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FindMyFdSubmitRequest {
    return {
      responses: globalThis.Array.isArray(object?.responses)
        ? object.responses.map((e: any) => Response.fromJSON(e))
        : [],
      isSkipped: isSet(object.isSkipped) ? globalThis.Boolean(object.isSkipped) : false,
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
      numberOfNibRecommendations: isSet(object.numberOfNibRecommendations)
        ? globalThis.Number(object.numberOfNibRecommendations)
        : undefined,
      numberOfIbRecommendations: isSet(object.numberOfIbRecommendations)
        ? globalThis.Number(object.numberOfIbRecommendations)
        : undefined,
    };
  },

  toJSON(message: FindMyFdSubmitRequest): unknown {
    const obj: any = {};
    if (message.responses?.length) {
      obj.responses = message.responses.map((e) => Response.toJSON(e));
    }
    if (message.isSkipped !== false) {
      obj.isSkipped = message.isSkipped;
    }
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    if (message.numberOfNibRecommendations !== undefined) {
      obj.numberOfNibRecommendations = Math.round(message.numberOfNibRecommendations);
    }
    if (message.numberOfIbRecommendations !== undefined) {
      obj.numberOfIbRecommendations = Math.round(message.numberOfIbRecommendations);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FindMyFdSubmitRequest>, I>>(base?: I): FindMyFdSubmitRequest {
    return FindMyFdSubmitRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FindMyFdSubmitRequest>, I>>(object: I): FindMyFdSubmitRequest {
    const message = createBaseFindMyFdSubmitRequest();
    message.responses = object.responses?.map((e) => Response.fromPartial(e)) || [];
    message.isSkipped = object.isSkipped ?? false;
    message.investmentAmount = object.investmentAmount ?? 0;
    message.numberOfNibRecommendations = object.numberOfNibRecommendations ?? undefined;
    message.numberOfIbRecommendations = object.numberOfIbRecommendations ?? undefined;
    return message;
  },
};

function createBaseResponse(): Response {
  return { questionId: "", answerId: [] };
}

export const Response: MessageFns<Response> = {
  encode(message: Response, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.questionId !== "") {
      writer.uint32(10).string(message.questionId);
    }
    for (const v of message.answerId) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Response {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.questionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.answerId.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Response {
    return {
      questionId: isSet(object.questionId) ? globalThis.String(object.questionId) : "",
      answerId: globalThis.Array.isArray(object?.answerId) ? object.answerId.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: Response): unknown {
    const obj: any = {};
    if (message.questionId !== "") {
      obj.questionId = message.questionId;
    }
    if (message.answerId?.length) {
      obj.answerId = message.answerId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Response>, I>>(base?: I): Response {
    return Response.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Response>, I>>(object: I): Response {
    const message = createBaseResponse();
    message.questionId = object.questionId ?? "";
    message.answerId = object.answerId?.map((e) => e) || [];
    return message;
  },
};

function createBaseFindMyFdSubmitResponse(): FindMyFdSubmitResponse {
  return { recommendations: [], recommendationId: "" };
}

export const FindMyFdSubmitResponse: MessageFns<FindMyFdSubmitResponse> = {
  encode(message: FindMyFdSubmitResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.recommendations) {
      FindMyFdRecommendation.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.recommendationId !== "") {
      writer.uint32(18).string(message.recommendationId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FindMyFdSubmitResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFindMyFdSubmitResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recommendations.push(FindMyFdRecommendation.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recommendationId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FindMyFdSubmitResponse {
    return {
      recommendations: globalThis.Array.isArray(object?.recommendations)
        ? object.recommendations.map((e: any) => FindMyFdRecommendation.fromJSON(e))
        : [],
      recommendationId: isSet(object.recommendationId) ? globalThis.String(object.recommendationId) : "",
    };
  },

  toJSON(message: FindMyFdSubmitResponse): unknown {
    const obj: any = {};
    if (message.recommendations?.length) {
      obj.recommendations = message.recommendations.map((e) => FindMyFdRecommendation.toJSON(e));
    }
    if (message.recommendationId !== "") {
      obj.recommendationId = message.recommendationId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FindMyFdSubmitResponse>, I>>(base?: I): FindMyFdSubmitResponse {
    return FindMyFdSubmitResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FindMyFdSubmitResponse>, I>>(object: I): FindMyFdSubmitResponse {
    const message = createBaseFindMyFdSubmitResponse();
    message.recommendations = object.recommendations?.map((e) => FindMyFdRecommendation.fromPartial(e)) || [];
    message.recommendationId = object.recommendationId ?? "";
    return message;
  },
};

function createBaseRecommendationReviewRequest(): RecommendationReviewRequest {
  return { didRecommendationHelpedYou: false, recommendationId: "" };
}

export const RecommendationReviewRequest: MessageFns<RecommendationReviewRequest> = {
  encode(message: RecommendationReviewRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.didRecommendationHelpedYou !== false) {
      writer.uint32(8).bool(message.didRecommendationHelpedYou);
    }
    if (message.recommendationId !== "") {
      writer.uint32(18).string(message.recommendationId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendationReviewRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendationReviewRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.didRecommendationHelpedYou = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recommendationId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecommendationReviewRequest {
    return {
      didRecommendationHelpedYou: isSet(object.didRecommendationHelpedYou)
        ? globalThis.Boolean(object.didRecommendationHelpedYou)
        : false,
      recommendationId: isSet(object.recommendationId) ? globalThis.String(object.recommendationId) : "",
    };
  },

  toJSON(message: RecommendationReviewRequest): unknown {
    const obj: any = {};
    if (message.didRecommendationHelpedYou !== false) {
      obj.didRecommendationHelpedYou = message.didRecommendationHelpedYou;
    }
    if (message.recommendationId !== "") {
      obj.recommendationId = message.recommendationId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendationReviewRequest>, I>>(base?: I): RecommendationReviewRequest {
    return RecommendationReviewRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendationReviewRequest>, I>>(object: I): RecommendationReviewRequest {
    const message = createBaseRecommendationReviewRequest();
    message.didRecommendationHelpedYou = object.didRecommendationHelpedYou ?? false;
    message.recommendationId = object.recommendationId ?? "";
    return message;
  },
};

function createBaseRecommendationReviewResponse(): RecommendationReviewResponse {
  return {};
}

export const RecommendationReviewResponse: MessageFns<RecommendationReviewResponse> = {
  encode(_: RecommendationReviewResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendationReviewResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendationReviewResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): RecommendationReviewResponse {
    return {};
  },

  toJSON(_: RecommendationReviewResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendationReviewResponse>, I>>(base?: I): RecommendationReviewResponse {
    return RecommendationReviewResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendationReviewResponse>, I>>(_: I): RecommendationReviewResponse {
    const message = createBaseRecommendationReviewResponse();
    return message;
  },
};

function createBaseFindMyFdRecommendation(): FindMyFdRecommendation {
  return {
    fixedDeposit: undefined,
    investmentAmount: 0,
    returns: 0,
    maturityAmount: 0,
    redirectDeeplink: undefined,
    benefitsDetails: [],
  };
}

export const FindMyFdRecommendation: MessageFns<FindMyFdRecommendation> = {
  encode(message: FindMyFdRecommendation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fixedDeposit !== undefined) {
      FixedDepositResponse.encode(message.fixedDeposit, writer.uint32(10).fork()).join();
    }
    if (message.investmentAmount !== 0) {
      writer.uint32(17).double(message.investmentAmount);
    }
    if (message.returns !== 0) {
      writer.uint32(25).double(message.returns);
    }
    if (message.maturityAmount !== 0) {
      writer.uint32(33).double(message.maturityAmount);
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(42).fork()).join();
    }
    for (const v of message.benefitsDetails) {
      BenefitsDetails.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FindMyFdRecommendation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFindMyFdRecommendation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fixedDeposit = FixedDepositResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.returns = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.maturityAmount = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.benefitsDetails.push(BenefitsDetails.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FindMyFdRecommendation {
    return {
      fixedDeposit: isSet(object.fixedDeposit) ? FixedDepositResponse.fromJSON(object.fixedDeposit) : undefined,
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
      returns: isSet(object.returns) ? globalThis.Number(object.returns) : 0,
      maturityAmount: isSet(object.maturityAmount) ? globalThis.Number(object.maturityAmount) : 0,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      benefitsDetails: globalThis.Array.isArray(object?.benefitsDetails)
        ? object.benefitsDetails.map((e: any) => BenefitsDetails.fromJSON(e))
        : [],
    };
  },

  toJSON(message: FindMyFdRecommendation): unknown {
    const obj: any = {};
    if (message.fixedDeposit !== undefined) {
      obj.fixedDeposit = FixedDepositResponse.toJSON(message.fixedDeposit);
    }
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    if (message.returns !== 0) {
      obj.returns = message.returns;
    }
    if (message.maturityAmount !== 0) {
      obj.maturityAmount = message.maturityAmount;
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.benefitsDetails?.length) {
      obj.benefitsDetails = message.benefitsDetails.map((e) => BenefitsDetails.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FindMyFdRecommendation>, I>>(base?: I): FindMyFdRecommendation {
    return FindMyFdRecommendation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FindMyFdRecommendation>, I>>(object: I): FindMyFdRecommendation {
    const message = createBaseFindMyFdRecommendation();
    message.fixedDeposit = (object.fixedDeposit !== undefined && object.fixedDeposit !== null)
      ? FixedDepositResponse.fromPartial(object.fixedDeposit)
      : undefined;
    message.investmentAmount = object.investmentAmount ?? 0;
    message.returns = object.returns ?? 0;
    message.maturityAmount = object.maturityAmount ?? 0;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.benefitsDetails = object.benefitsDetails?.map((e) => BenefitsDetails.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBenefitsDetails(): BenefitsDetails {
  return { benefit: "", iconUrl: "" };
}

export const BenefitsDetails: MessageFns<BenefitsDetails> = {
  encode(message: BenefitsDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.benefit !== "") {
      writer.uint32(10).string(message.benefit);
    }
    if (message.iconUrl !== "") {
      writer.uint32(18).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BenefitsDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBenefitsDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.benefit = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BenefitsDetails {
    return {
      benefit: isSet(object.benefit) ? globalThis.String(object.benefit) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: BenefitsDetails): unknown {
    const obj: any = {};
    if (message.benefit !== "") {
      obj.benefit = message.benefit;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BenefitsDetails>, I>>(base?: I): BenefitsDetails {
    return BenefitsDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BenefitsDetails>, I>>(object: I): BenefitsDetails {
    const message = createBaseBenefitsDetails();
    message.benefit = object.benefit ?? "";
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBaseRecommendationReviewSubmitRequest(): RecommendationReviewSubmitRequest {
  return { recommendationId: "", hasHelpedYou: false };
}

export const RecommendationReviewSubmitRequest: MessageFns<RecommendationReviewSubmitRequest> = {
  encode(message: RecommendationReviewSubmitRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recommendationId !== "") {
      writer.uint32(10).string(message.recommendationId);
    }
    if (message.hasHelpedYou !== false) {
      writer.uint32(16).bool(message.hasHelpedYou);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendationReviewSubmitRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendationReviewSubmitRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recommendationId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasHelpedYou = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecommendationReviewSubmitRequest {
    return {
      recommendationId: isSet(object.recommendationId) ? globalThis.String(object.recommendationId) : "",
      hasHelpedYou: isSet(object.hasHelpedYou) ? globalThis.Boolean(object.hasHelpedYou) : false,
    };
  },

  toJSON(message: RecommendationReviewSubmitRequest): unknown {
    const obj: any = {};
    if (message.recommendationId !== "") {
      obj.recommendationId = message.recommendationId;
    }
    if (message.hasHelpedYou !== false) {
      obj.hasHelpedYou = message.hasHelpedYou;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendationReviewSubmitRequest>, I>>(
    base?: I,
  ): RecommendationReviewSubmitRequest {
    return RecommendationReviewSubmitRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendationReviewSubmitRequest>, I>>(
    object: I,
  ): RecommendationReviewSubmitRequest {
    const message = createBaseRecommendationReviewSubmitRequest();
    message.recommendationId = object.recommendationId ?? "";
    message.hasHelpedYou = object.hasHelpedYou ?? false;
    return message;
  },
};

function createBaseRecommendationReviewSubmitResponse(): RecommendationReviewSubmitResponse {
  return {};
}

export const RecommendationReviewSubmitResponse: MessageFns<RecommendationReviewSubmitResponse> = {
  encode(_: RecommendationReviewSubmitResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendationReviewSubmitResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendationReviewSubmitResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): RecommendationReviewSubmitResponse {
    return {};
  },

  toJSON(_: RecommendationReviewSubmitResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendationReviewSubmitResponse>, I>>(
    base?: I,
  ): RecommendationReviewSubmitResponse {
    return RecommendationReviewSubmitResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendationReviewSubmitResponse>, I>>(
    _: I,
  ): RecommendationReviewSubmitResponse {
    const message = createBaseRecommendationReviewSubmitResponse();
    return message;
  },
};

function createBaseInitiateFindMyFdResponse(): InitiateFindMyFdResponse {
  return {
    isFindMyFdInitiated: false,
    recommendations: [],
    isRecommendationFeedbackSubmitted: false,
    investmentAmount: 0,
    recommendationId: "",
    findMyFdSubmitRequest: undefined,
  };
}

export const InitiateFindMyFdResponse: MessageFns<InitiateFindMyFdResponse> = {
  encode(message: InitiateFindMyFdResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isFindMyFdInitiated !== false) {
      writer.uint32(8).bool(message.isFindMyFdInitiated);
    }
    for (const v of message.recommendations) {
      FindMyFdRecommendation.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.isRecommendationFeedbackSubmitted !== false) {
      writer.uint32(24).bool(message.isRecommendationFeedbackSubmitted);
    }
    if (message.investmentAmount !== 0) {
      writer.uint32(33).double(message.investmentAmount);
    }
    if (message.recommendationId !== "") {
      writer.uint32(42).string(message.recommendationId);
    }
    if (message.findMyFdSubmitRequest !== undefined) {
      FindMyFdSubmitRequest.encode(message.findMyFdSubmitRequest, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InitiateFindMyFdResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInitiateFindMyFdResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isFindMyFdInitiated = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recommendations.push(FindMyFdRecommendation.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isRecommendationFeedbackSubmitted = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.recommendationId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.findMyFdSubmitRequest = FindMyFdSubmitRequest.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InitiateFindMyFdResponse {
    return {
      isFindMyFdInitiated: isSet(object.isFindMyFdInitiated) ? globalThis.Boolean(object.isFindMyFdInitiated) : false,
      recommendations: globalThis.Array.isArray(object?.recommendations)
        ? object.recommendations.map((e: any) => FindMyFdRecommendation.fromJSON(e))
        : [],
      isRecommendationFeedbackSubmitted: isSet(object.isRecommendationFeedbackSubmitted)
        ? globalThis.Boolean(object.isRecommendationFeedbackSubmitted)
        : false,
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
      recommendationId: isSet(object.recommendationId) ? globalThis.String(object.recommendationId) : "",
      findMyFdSubmitRequest: isSet(object.findMyFdSubmitRequest)
        ? FindMyFdSubmitRequest.fromJSON(object.findMyFdSubmitRequest)
        : undefined,
    };
  },

  toJSON(message: InitiateFindMyFdResponse): unknown {
    const obj: any = {};
    if (message.isFindMyFdInitiated !== false) {
      obj.isFindMyFdInitiated = message.isFindMyFdInitiated;
    }
    if (message.recommendations?.length) {
      obj.recommendations = message.recommendations.map((e) => FindMyFdRecommendation.toJSON(e));
    }
    if (message.isRecommendationFeedbackSubmitted !== false) {
      obj.isRecommendationFeedbackSubmitted = message.isRecommendationFeedbackSubmitted;
    }
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    if (message.recommendationId !== "") {
      obj.recommendationId = message.recommendationId;
    }
    if (message.findMyFdSubmitRequest !== undefined) {
      obj.findMyFdSubmitRequest = FindMyFdSubmitRequest.toJSON(message.findMyFdSubmitRequest);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InitiateFindMyFdResponse>, I>>(base?: I): InitiateFindMyFdResponse {
    return InitiateFindMyFdResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InitiateFindMyFdResponse>, I>>(object: I): InitiateFindMyFdResponse {
    const message = createBaseInitiateFindMyFdResponse();
    message.isFindMyFdInitiated = object.isFindMyFdInitiated ?? false;
    message.recommendations = object.recommendations?.map((e) => FindMyFdRecommendation.fromPartial(e)) || [];
    message.isRecommendationFeedbackSubmitted = object.isRecommendationFeedbackSubmitted ?? false;
    message.investmentAmount = object.investmentAmount ?? 0;
    message.recommendationId = object.recommendationId ?? "";
    message.findMyFdSubmitRequest =
      (object.findMyFdSubmitRequest !== undefined && object.findMyFdSubmitRequest !== null)
        ? FindMyFdSubmitRequest.fromPartial(object.findMyFdSubmitRequest)
        : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
