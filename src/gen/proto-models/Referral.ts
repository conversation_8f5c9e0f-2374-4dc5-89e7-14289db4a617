// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Referral.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { CampaignType, campaignTypeFromJSON, campaignTypeToJSON } from "./Campaign";
import { PaginationFilter } from "./Common";

export const protobufPackage = "com.stablemoney.api.identity";

export enum ReferralItemStatus {
  UNKNOWN_REFERRAL_STATUS = 0,
  BOOKING_PENDING_REFERRAL_STATUS = 1,
  FD_BOOKED_REFERRAL_STATUS = 2,
  FD_WITHDRAWN_REFERRAL_STATUS = 3,
  FD_BOOKED_INELIGIBLE_STATUS = 4,
  UNRECOGNIZED = -1,
}

export function referralItemStatusFromJSON(object: any): ReferralItemStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_REFERRAL_STATUS":
      return ReferralItemStatus.UNKNOWN_REFERRAL_STATUS;
    case 1:
    case "BOOKING_PENDING_REFERRAL_STATUS":
      return ReferralItemStatus.BOOKING_PENDING_REFERRAL_STATUS;
    case 2:
    case "FD_BOOKED_REFERRAL_STATUS":
      return ReferralItemStatus.FD_BOOKED_REFERRAL_STATUS;
    case 3:
    case "FD_WITHDRAWN_REFERRAL_STATUS":
      return ReferralItemStatus.FD_WITHDRAWN_REFERRAL_STATUS;
    case 4:
    case "FD_BOOKED_INELIGIBLE_STATUS":
      return ReferralItemStatus.FD_BOOKED_INELIGIBLE_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ReferralItemStatus.UNRECOGNIZED;
  }
}

export function referralItemStatusToJSON(object: ReferralItemStatus): string {
  switch (object) {
    case ReferralItemStatus.UNKNOWN_REFERRAL_STATUS:
      return "UNKNOWN_REFERRAL_STATUS";
    case ReferralItemStatus.BOOKING_PENDING_REFERRAL_STATUS:
      return "BOOKING_PENDING_REFERRAL_STATUS";
    case ReferralItemStatus.FD_BOOKED_REFERRAL_STATUS:
      return "FD_BOOKED_REFERRAL_STATUS";
    case ReferralItemStatus.FD_WITHDRAWN_REFERRAL_STATUS:
      return "FD_WITHDRAWN_REFERRAL_STATUS";
    case ReferralItemStatus.FD_BOOKED_INELIGIBLE_STATUS:
      return "FD_BOOKED_INELIGIBLE_STATUS";
    case ReferralItemStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ReferralItem {
  refereeName: string;
  refereeDp: string;
  referralLevel: string;
  referralLevelPicUrl: string;
  referralStatusDescription: string;
  referralStatus: ReferralItemStatus;
  rewardAmount?: number | undefined;
  daysLeft?: string | undefined;
  updatedAt: string;
  shareText: string;
  phoneNumber: string;
  ctaText: string;
  refereeId: string;
}

export interface ReferralSummary {
  referralEarnings: number;
  withdrawableEarnings: number;
  maximumEarnings: number;
  totalReferrals: number;
  successfulReferrals: number;
  title: string;
  currentEarningsPercentage: number;
}

export interface ReferralDashboardResponse {
  referralSummary: ReferralSummary | undefined;
  referralItemsList: ReferralItem[];
}

export interface ReferrerItem {
  name: string;
  id: string;
}

export interface ReferralDashboardResponseV2 {
  referralSummary: ReferralSummary | undefined;
  referralItemsList: ReferralItem[];
  potentialEarningAmount?: number | undefined;
  referrer: ReferrerItem | undefined;
}

export interface ReferralLeaderboardItem {
  name: string;
  rank: number;
  referralCount: number;
  referralEarning: number;
  iconUrl: string;
}

export interface ReferralLeaderboardResponse {
  referralItemsList: ReferralLeaderboardItem[];
}

export interface GetRefereesRequest {
  paginationFilter: PaginationFilter | undefined;
  userId: string;
  campaignTypes: CampaignType[];
}

export interface Referee {
  userId: string;
  firstName: string;
  lastName: string;
  campaignType?: CampaignType | undefined;
}

export interface Referer {
  userId: string;
  firstName: string;
  lastName: string;
  campaignType?: CampaignType | undefined;
}

export interface GetRefereesResponse {
  referees: Referee[];
}

export interface ReferralLinkRequest {
  userId: string;
  campaignType: CampaignType;
}

function createBaseReferralItem(): ReferralItem {
  return {
    refereeName: "",
    refereeDp: "",
    referralLevel: "",
    referralLevelPicUrl: "",
    referralStatusDescription: "",
    referralStatus: 0,
    rewardAmount: undefined,
    daysLeft: undefined,
    updatedAt: "",
    shareText: "",
    phoneNumber: "",
    ctaText: "",
    refereeId: "",
  };
}

export const ReferralItem: MessageFns<ReferralItem> = {
  encode(message: ReferralItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.refereeName !== "") {
      writer.uint32(10).string(message.refereeName);
    }
    if (message.refereeDp !== "") {
      writer.uint32(18).string(message.refereeDp);
    }
    if (message.referralLevel !== "") {
      writer.uint32(26).string(message.referralLevel);
    }
    if (message.referralLevelPicUrl !== "") {
      writer.uint32(34).string(message.referralLevelPicUrl);
    }
    if (message.referralStatusDescription !== "") {
      writer.uint32(42).string(message.referralStatusDescription);
    }
    if (message.referralStatus !== 0) {
      writer.uint32(48).int32(message.referralStatus);
    }
    if (message.rewardAmount !== undefined) {
      writer.uint32(57).double(message.rewardAmount);
    }
    if (message.daysLeft !== undefined) {
      writer.uint32(66).string(message.daysLeft);
    }
    if (message.updatedAt !== "") {
      writer.uint32(74).string(message.updatedAt);
    }
    if (message.shareText !== "") {
      writer.uint32(82).string(message.shareText);
    }
    if (message.phoneNumber !== "") {
      writer.uint32(90).string(message.phoneNumber);
    }
    if (message.ctaText !== "") {
      writer.uint32(98).string(message.ctaText);
    }
    if (message.refereeId !== "") {
      writer.uint32(106).string(message.refereeId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferralItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferralItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.refereeName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.refereeDp = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.referralLevel = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.referralLevelPicUrl = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.referralStatusDescription = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.referralStatus = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.rewardAmount = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.daysLeft = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.updatedAt = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.shareText = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.phoneNumber = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.ctaText = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.refereeId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReferralItem {
    return {
      refereeName: isSet(object.refereeName) ? globalThis.String(object.refereeName) : "",
      refereeDp: isSet(object.refereeDp) ? globalThis.String(object.refereeDp) : "",
      referralLevel: isSet(object.referralLevel) ? globalThis.String(object.referralLevel) : "",
      referralLevelPicUrl: isSet(object.referralLevelPicUrl) ? globalThis.String(object.referralLevelPicUrl) : "",
      referralStatusDescription: isSet(object.referralStatusDescription)
        ? globalThis.String(object.referralStatusDescription)
        : "",
      referralStatus: isSet(object.referralStatus) ? referralItemStatusFromJSON(object.referralStatus) : 0,
      rewardAmount: isSet(object.rewardAmount) ? globalThis.Number(object.rewardAmount) : undefined,
      daysLeft: isSet(object.daysLeft) ? globalThis.String(object.daysLeft) : undefined,
      updatedAt: isSet(object.updatedAt) ? globalThis.String(object.updatedAt) : "",
      shareText: isSet(object.shareText) ? globalThis.String(object.shareText) : "",
      phoneNumber: isSet(object.phoneNumber) ? globalThis.String(object.phoneNumber) : "",
      ctaText: isSet(object.ctaText) ? globalThis.String(object.ctaText) : "",
      refereeId: isSet(object.refereeId) ? globalThis.String(object.refereeId) : "",
    };
  },

  toJSON(message: ReferralItem): unknown {
    const obj: any = {};
    if (message.refereeName !== "") {
      obj.refereeName = message.refereeName;
    }
    if (message.refereeDp !== "") {
      obj.refereeDp = message.refereeDp;
    }
    if (message.referralLevel !== "") {
      obj.referralLevel = message.referralLevel;
    }
    if (message.referralLevelPicUrl !== "") {
      obj.referralLevelPicUrl = message.referralLevelPicUrl;
    }
    if (message.referralStatusDescription !== "") {
      obj.referralStatusDescription = message.referralStatusDescription;
    }
    if (message.referralStatus !== 0) {
      obj.referralStatus = referralItemStatusToJSON(message.referralStatus);
    }
    if (message.rewardAmount !== undefined) {
      obj.rewardAmount = message.rewardAmount;
    }
    if (message.daysLeft !== undefined) {
      obj.daysLeft = message.daysLeft;
    }
    if (message.updatedAt !== "") {
      obj.updatedAt = message.updatedAt;
    }
    if (message.shareText !== "") {
      obj.shareText = message.shareText;
    }
    if (message.phoneNumber !== "") {
      obj.phoneNumber = message.phoneNumber;
    }
    if (message.ctaText !== "") {
      obj.ctaText = message.ctaText;
    }
    if (message.refereeId !== "") {
      obj.refereeId = message.refereeId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReferralItem>, I>>(base?: I): ReferralItem {
    return ReferralItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReferralItem>, I>>(object: I): ReferralItem {
    const message = createBaseReferralItem();
    message.refereeName = object.refereeName ?? "";
    message.refereeDp = object.refereeDp ?? "";
    message.referralLevel = object.referralLevel ?? "";
    message.referralLevelPicUrl = object.referralLevelPicUrl ?? "";
    message.referralStatusDescription = object.referralStatusDescription ?? "";
    message.referralStatus = object.referralStatus ?? 0;
    message.rewardAmount = object.rewardAmount ?? undefined;
    message.daysLeft = object.daysLeft ?? undefined;
    message.updatedAt = object.updatedAt ?? "";
    message.shareText = object.shareText ?? "";
    message.phoneNumber = object.phoneNumber ?? "";
    message.ctaText = object.ctaText ?? "";
    message.refereeId = object.refereeId ?? "";
    return message;
  },
};

function createBaseReferralSummary(): ReferralSummary {
  return {
    referralEarnings: 0,
    withdrawableEarnings: 0,
    maximumEarnings: 0,
    totalReferrals: 0,
    successfulReferrals: 0,
    title: "",
    currentEarningsPercentage: 0,
  };
}

export const ReferralSummary: MessageFns<ReferralSummary> = {
  encode(message: ReferralSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.referralEarnings !== 0) {
      writer.uint32(9).double(message.referralEarnings);
    }
    if (message.withdrawableEarnings !== 0) {
      writer.uint32(17).double(message.withdrawableEarnings);
    }
    if (message.maximumEarnings !== 0) {
      writer.uint32(25).double(message.maximumEarnings);
    }
    if (message.totalReferrals !== 0) {
      writer.uint32(32).int32(message.totalReferrals);
    }
    if (message.successfulReferrals !== 0) {
      writer.uint32(40).int32(message.successfulReferrals);
    }
    if (message.title !== "") {
      writer.uint32(50).string(message.title);
    }
    if (message.currentEarningsPercentage !== 0) {
      writer.uint32(57).double(message.currentEarningsPercentage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferralSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferralSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.referralEarnings = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.withdrawableEarnings = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.maximumEarnings = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.totalReferrals = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.successfulReferrals = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.currentEarningsPercentage = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReferralSummary {
    return {
      referralEarnings: isSet(object.referralEarnings) ? globalThis.Number(object.referralEarnings) : 0,
      withdrawableEarnings: isSet(object.withdrawableEarnings) ? globalThis.Number(object.withdrawableEarnings) : 0,
      maximumEarnings: isSet(object.maximumEarnings) ? globalThis.Number(object.maximumEarnings) : 0,
      totalReferrals: isSet(object.totalReferrals) ? globalThis.Number(object.totalReferrals) : 0,
      successfulReferrals: isSet(object.successfulReferrals) ? globalThis.Number(object.successfulReferrals) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      currentEarningsPercentage: isSet(object.currentEarningsPercentage)
        ? globalThis.Number(object.currentEarningsPercentage)
        : 0,
    };
  },

  toJSON(message: ReferralSummary): unknown {
    const obj: any = {};
    if (message.referralEarnings !== 0) {
      obj.referralEarnings = message.referralEarnings;
    }
    if (message.withdrawableEarnings !== 0) {
      obj.withdrawableEarnings = message.withdrawableEarnings;
    }
    if (message.maximumEarnings !== 0) {
      obj.maximumEarnings = message.maximumEarnings;
    }
    if (message.totalReferrals !== 0) {
      obj.totalReferrals = Math.round(message.totalReferrals);
    }
    if (message.successfulReferrals !== 0) {
      obj.successfulReferrals = Math.round(message.successfulReferrals);
    }
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.currentEarningsPercentage !== 0) {
      obj.currentEarningsPercentage = message.currentEarningsPercentage;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReferralSummary>, I>>(base?: I): ReferralSummary {
    return ReferralSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReferralSummary>, I>>(object: I): ReferralSummary {
    const message = createBaseReferralSummary();
    message.referralEarnings = object.referralEarnings ?? 0;
    message.withdrawableEarnings = object.withdrawableEarnings ?? 0;
    message.maximumEarnings = object.maximumEarnings ?? 0;
    message.totalReferrals = object.totalReferrals ?? 0;
    message.successfulReferrals = object.successfulReferrals ?? 0;
    message.title = object.title ?? "";
    message.currentEarningsPercentage = object.currentEarningsPercentage ?? 0;
    return message;
  },
};

function createBaseReferralDashboardResponse(): ReferralDashboardResponse {
  return { referralSummary: undefined, referralItemsList: [] };
}

export const ReferralDashboardResponse: MessageFns<ReferralDashboardResponse> = {
  encode(message: ReferralDashboardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.referralSummary !== undefined) {
      ReferralSummary.encode(message.referralSummary, writer.uint32(10).fork()).join();
    }
    for (const v of message.referralItemsList) {
      ReferralItem.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferralDashboardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferralDashboardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.referralSummary = ReferralSummary.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.referralItemsList.push(ReferralItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReferralDashboardResponse {
    return {
      referralSummary: isSet(object.referralSummary) ? ReferralSummary.fromJSON(object.referralSummary) : undefined,
      referralItemsList: globalThis.Array.isArray(object?.referralItemsList)
        ? object.referralItemsList.map((e: any) => ReferralItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ReferralDashboardResponse): unknown {
    const obj: any = {};
    if (message.referralSummary !== undefined) {
      obj.referralSummary = ReferralSummary.toJSON(message.referralSummary);
    }
    if (message.referralItemsList?.length) {
      obj.referralItemsList = message.referralItemsList.map((e) => ReferralItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReferralDashboardResponse>, I>>(base?: I): ReferralDashboardResponse {
    return ReferralDashboardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReferralDashboardResponse>, I>>(object: I): ReferralDashboardResponse {
    const message = createBaseReferralDashboardResponse();
    message.referralSummary = (object.referralSummary !== undefined && object.referralSummary !== null)
      ? ReferralSummary.fromPartial(object.referralSummary)
      : undefined;
    message.referralItemsList = object.referralItemsList?.map((e) => ReferralItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseReferrerItem(): ReferrerItem {
  return { name: "", id: "" };
}

export const ReferrerItem: MessageFns<ReferrerItem> = {
  encode(message: ReferrerItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferrerItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferrerItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReferrerItem {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
    };
  },

  toJSON(message: ReferrerItem): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReferrerItem>, I>>(base?: I): ReferrerItem {
    return ReferrerItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReferrerItem>, I>>(object: I): ReferrerItem {
    const message = createBaseReferrerItem();
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseReferralDashboardResponseV2(): ReferralDashboardResponseV2 {
  return { referralSummary: undefined, referralItemsList: [], potentialEarningAmount: undefined, referrer: undefined };
}

export const ReferralDashboardResponseV2: MessageFns<ReferralDashboardResponseV2> = {
  encode(message: ReferralDashboardResponseV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.referralSummary !== undefined) {
      ReferralSummary.encode(message.referralSummary, writer.uint32(10).fork()).join();
    }
    for (const v of message.referralItemsList) {
      ReferralItem.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.potentialEarningAmount !== undefined) {
      writer.uint32(25).double(message.potentialEarningAmount);
    }
    if (message.referrer !== undefined) {
      ReferrerItem.encode(message.referrer, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferralDashboardResponseV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferralDashboardResponseV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.referralSummary = ReferralSummary.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.referralItemsList.push(ReferralItem.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.potentialEarningAmount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.referrer = ReferrerItem.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReferralDashboardResponseV2 {
    return {
      referralSummary: isSet(object.referralSummary) ? ReferralSummary.fromJSON(object.referralSummary) : undefined,
      referralItemsList: globalThis.Array.isArray(object?.referralItemsList)
        ? object.referralItemsList.map((e: any) => ReferralItem.fromJSON(e))
        : [],
      potentialEarningAmount: isSet(object.potentialEarningAmount)
        ? globalThis.Number(object.potentialEarningAmount)
        : undefined,
      referrer: isSet(object.referrer) ? ReferrerItem.fromJSON(object.referrer) : undefined,
    };
  },

  toJSON(message: ReferralDashboardResponseV2): unknown {
    const obj: any = {};
    if (message.referralSummary !== undefined) {
      obj.referralSummary = ReferralSummary.toJSON(message.referralSummary);
    }
    if (message.referralItemsList?.length) {
      obj.referralItemsList = message.referralItemsList.map((e) => ReferralItem.toJSON(e));
    }
    if (message.potentialEarningAmount !== undefined) {
      obj.potentialEarningAmount = message.potentialEarningAmount;
    }
    if (message.referrer !== undefined) {
      obj.referrer = ReferrerItem.toJSON(message.referrer);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReferralDashboardResponseV2>, I>>(base?: I): ReferralDashboardResponseV2 {
    return ReferralDashboardResponseV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReferralDashboardResponseV2>, I>>(object: I): ReferralDashboardResponseV2 {
    const message = createBaseReferralDashboardResponseV2();
    message.referralSummary = (object.referralSummary !== undefined && object.referralSummary !== null)
      ? ReferralSummary.fromPartial(object.referralSummary)
      : undefined;
    message.referralItemsList = object.referralItemsList?.map((e) => ReferralItem.fromPartial(e)) || [];
    message.potentialEarningAmount = object.potentialEarningAmount ?? undefined;
    message.referrer = (object.referrer !== undefined && object.referrer !== null)
      ? ReferrerItem.fromPartial(object.referrer)
      : undefined;
    return message;
  },
};

function createBaseReferralLeaderboardItem(): ReferralLeaderboardItem {
  return { name: "", rank: 0, referralCount: 0, referralEarning: 0, iconUrl: "" };
}

export const ReferralLeaderboardItem: MessageFns<ReferralLeaderboardItem> = {
  encode(message: ReferralLeaderboardItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.rank !== 0) {
      writer.uint32(16).int32(message.rank);
    }
    if (message.referralCount !== 0) {
      writer.uint32(24).int32(message.referralCount);
    }
    if (message.referralEarning !== 0) {
      writer.uint32(33).double(message.referralEarning);
    }
    if (message.iconUrl !== "") {
      writer.uint32(42).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferralLeaderboardItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferralLeaderboardItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.rank = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.referralCount = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.referralEarning = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReferralLeaderboardItem {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      rank: isSet(object.rank) ? globalThis.Number(object.rank) : 0,
      referralCount: isSet(object.referralCount) ? globalThis.Number(object.referralCount) : 0,
      referralEarning: isSet(object.referralEarning) ? globalThis.Number(object.referralEarning) : 0,
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: ReferralLeaderboardItem): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.rank !== 0) {
      obj.rank = Math.round(message.rank);
    }
    if (message.referralCount !== 0) {
      obj.referralCount = Math.round(message.referralCount);
    }
    if (message.referralEarning !== 0) {
      obj.referralEarning = message.referralEarning;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReferralLeaderboardItem>, I>>(base?: I): ReferralLeaderboardItem {
    return ReferralLeaderboardItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReferralLeaderboardItem>, I>>(object: I): ReferralLeaderboardItem {
    const message = createBaseReferralLeaderboardItem();
    message.name = object.name ?? "";
    message.rank = object.rank ?? 0;
    message.referralCount = object.referralCount ?? 0;
    message.referralEarning = object.referralEarning ?? 0;
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBaseReferralLeaderboardResponse(): ReferralLeaderboardResponse {
  return { referralItemsList: [] };
}

export const ReferralLeaderboardResponse: MessageFns<ReferralLeaderboardResponse> = {
  encode(message: ReferralLeaderboardResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.referralItemsList) {
      ReferralLeaderboardItem.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferralLeaderboardResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferralLeaderboardResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.referralItemsList.push(ReferralLeaderboardItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReferralLeaderboardResponse {
    return {
      referralItemsList: globalThis.Array.isArray(object?.referralItemsList)
        ? object.referralItemsList.map((e: any) => ReferralLeaderboardItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ReferralLeaderboardResponse): unknown {
    const obj: any = {};
    if (message.referralItemsList?.length) {
      obj.referralItemsList = message.referralItemsList.map((e) => ReferralLeaderboardItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReferralLeaderboardResponse>, I>>(base?: I): ReferralLeaderboardResponse {
    return ReferralLeaderboardResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReferralLeaderboardResponse>, I>>(object: I): ReferralLeaderboardResponse {
    const message = createBaseReferralLeaderboardResponse();
    message.referralItemsList = object.referralItemsList?.map((e) => ReferralLeaderboardItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetRefereesRequest(): GetRefereesRequest {
  return { paginationFilter: undefined, userId: "", campaignTypes: [] };
}

export const GetRefereesRequest: MessageFns<GetRefereesRequest> = {
  encode(message: GetRefereesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.paginationFilter !== undefined) {
      PaginationFilter.encode(message.paginationFilter, writer.uint32(10).fork()).join();
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    writer.uint32(26).fork();
    for (const v of message.campaignTypes) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRefereesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRefereesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.paginationFilter = PaginationFilter.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 3: {
          if (tag === 24) {
            message.campaignTypes.push(reader.int32() as any);

            continue;
          }

          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.campaignTypes.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetRefereesRequest {
    return {
      paginationFilter: isSet(object.paginationFilter) ? PaginationFilter.fromJSON(object.paginationFilter) : undefined,
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      campaignTypes: globalThis.Array.isArray(object?.campaignTypes)
        ? object.campaignTypes.map((e: any) => campaignTypeFromJSON(e))
        : [],
    };
  },

  toJSON(message: GetRefereesRequest): unknown {
    const obj: any = {};
    if (message.paginationFilter !== undefined) {
      obj.paginationFilter = PaginationFilter.toJSON(message.paginationFilter);
    }
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.campaignTypes?.length) {
      obj.campaignTypes = message.campaignTypes.map((e) => campaignTypeToJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetRefereesRequest>, I>>(base?: I): GetRefereesRequest {
    return GetRefereesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRefereesRequest>, I>>(object: I): GetRefereesRequest {
    const message = createBaseGetRefereesRequest();
    message.paginationFilter = (object.paginationFilter !== undefined && object.paginationFilter !== null)
      ? PaginationFilter.fromPartial(object.paginationFilter)
      : undefined;
    message.userId = object.userId ?? "";
    message.campaignTypes = object.campaignTypes?.map((e) => e) || [];
    return message;
  },
};

function createBaseReferee(): Referee {
  return { userId: "", firstName: "", lastName: "", campaignType: undefined };
}

export const Referee: MessageFns<Referee> = {
  encode(message: Referee, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.firstName !== "") {
      writer.uint32(18).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(26).string(message.lastName);
    }
    if (message.campaignType !== undefined) {
      writer.uint32(32).int32(message.campaignType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Referee {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferee();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.campaignType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Referee {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      firstName: isSet(object.firstName) ? globalThis.String(object.firstName) : "",
      lastName: isSet(object.lastName) ? globalThis.String(object.lastName) : "",
      campaignType: isSet(object.campaignType) ? campaignTypeFromJSON(object.campaignType) : undefined,
    };
  },

  toJSON(message: Referee): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.firstName !== "") {
      obj.firstName = message.firstName;
    }
    if (message.lastName !== "") {
      obj.lastName = message.lastName;
    }
    if (message.campaignType !== undefined) {
      obj.campaignType = campaignTypeToJSON(message.campaignType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Referee>, I>>(base?: I): Referee {
    return Referee.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Referee>, I>>(object: I): Referee {
    const message = createBaseReferee();
    message.userId = object.userId ?? "";
    message.firstName = object.firstName ?? "";
    message.lastName = object.lastName ?? "";
    message.campaignType = object.campaignType ?? undefined;
    return message;
  },
};

function createBaseReferer(): Referer {
  return { userId: "", firstName: "", lastName: "", campaignType: undefined };
}

export const Referer: MessageFns<Referer> = {
  encode(message: Referer, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.firstName !== "") {
      writer.uint32(18).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(26).string(message.lastName);
    }
    if (message.campaignType !== undefined) {
      writer.uint32(32).int32(message.campaignType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Referer {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.campaignType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Referer {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      firstName: isSet(object.firstName) ? globalThis.String(object.firstName) : "",
      lastName: isSet(object.lastName) ? globalThis.String(object.lastName) : "",
      campaignType: isSet(object.campaignType) ? campaignTypeFromJSON(object.campaignType) : undefined,
    };
  },

  toJSON(message: Referer): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.firstName !== "") {
      obj.firstName = message.firstName;
    }
    if (message.lastName !== "") {
      obj.lastName = message.lastName;
    }
    if (message.campaignType !== undefined) {
      obj.campaignType = campaignTypeToJSON(message.campaignType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Referer>, I>>(base?: I): Referer {
    return Referer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Referer>, I>>(object: I): Referer {
    const message = createBaseReferer();
    message.userId = object.userId ?? "";
    message.firstName = object.firstName ?? "";
    message.lastName = object.lastName ?? "";
    message.campaignType = object.campaignType ?? undefined;
    return message;
  },
};

function createBaseGetRefereesResponse(): GetRefereesResponse {
  return { referees: [] };
}

export const GetRefereesResponse: MessageFns<GetRefereesResponse> = {
  encode(message: GetRefereesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.referees) {
      Referee.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRefereesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRefereesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.referees.push(Referee.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetRefereesResponse {
    return {
      referees: globalThis.Array.isArray(object?.referees) ? object.referees.map((e: any) => Referee.fromJSON(e)) : [],
    };
  },

  toJSON(message: GetRefereesResponse): unknown {
    const obj: any = {};
    if (message.referees?.length) {
      obj.referees = message.referees.map((e) => Referee.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetRefereesResponse>, I>>(base?: I): GetRefereesResponse {
    return GetRefereesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRefereesResponse>, I>>(object: I): GetRefereesResponse {
    const message = createBaseGetRefereesResponse();
    message.referees = object.referees?.map((e) => Referee.fromPartial(e)) || [];
    return message;
  },
};

function createBaseReferralLinkRequest(): ReferralLinkRequest {
  return { userId: "", campaignType: 0 };
}

export const ReferralLinkRequest: MessageFns<ReferralLinkRequest> = {
  encode(message: ReferralLinkRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userId !== "") {
      writer.uint32(10).string(message.userId);
    }
    if (message.campaignType !== 0) {
      writer.uint32(16).int32(message.campaignType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReferralLinkRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReferralLinkRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.campaignType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReferralLinkRequest {
    return {
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      campaignType: isSet(object.campaignType) ? campaignTypeFromJSON(object.campaignType) : 0,
    };
  },

  toJSON(message: ReferralLinkRequest): unknown {
    const obj: any = {};
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.campaignType !== 0) {
      obj.campaignType = campaignTypeToJSON(message.campaignType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReferralLinkRequest>, I>>(base?: I): ReferralLinkRequest {
    return ReferralLinkRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReferralLinkRequest>, I>>(object: I): ReferralLinkRequest {
    const message = createBaseReferralLinkRequest();
    message.userId = object.userId ?? "";
    message.campaignType = object.campaignType ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
