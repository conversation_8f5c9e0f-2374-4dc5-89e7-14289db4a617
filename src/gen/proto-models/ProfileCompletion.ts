// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: ProfileCompletion.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { CityResponse } from "./City";
import {
  Gender,
  genderFromJSON,
  genderToJ<PERSON><PERSON>,
  MaritalStatus,
  maritalStatusFromJSON,
  maritalStatusToJSON,
} from "./Profile";

export const protobufPackage = "com.stablemoney.api.identity";

export enum ProfileCompletionIncomeRange {
  UNKNOWN_PROFILE_COMPLETION_INCOME_RANGE = 0,
  LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE = 1,
  BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE = 2,
  BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE = 3,
  BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE = 4,
  BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE = 5,
  ABOVE_5L_PROFILE_COMPLETION_INCOME = 6,
  UNRECOGNIZED = -1,
}

export function profileCompletionIncomeRangeFromJSON(object: any): ProfileCompletionIncomeRange {
  switch (object) {
    case 0:
    case "UNKNOWN_PROFILE_COMPLETION_INCOME_RANGE":
      return ProfileCompletionIncomeRange.UNKNOWN_PROFILE_COMPLETION_INCOME_RANGE;
    case 1:
    case "LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE":
      return ProfileCompletionIncomeRange.LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE;
    case 2:
    case "BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE":
      return ProfileCompletionIncomeRange.BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE;
    case 3:
    case "BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE":
      return ProfileCompletionIncomeRange.BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE;
    case 4:
    case "BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE":
      return ProfileCompletionIncomeRange.BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE;
    case 5:
    case "BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE":
      return ProfileCompletionIncomeRange.BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE;
    case 6:
    case "ABOVE_5L_PROFILE_COMPLETION_INCOME":
      return ProfileCompletionIncomeRange.ABOVE_5L_PROFILE_COMPLETION_INCOME;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ProfileCompletionIncomeRange.UNRECOGNIZED;
  }
}

export function profileCompletionIncomeRangeToJSON(object: ProfileCompletionIncomeRange): string {
  switch (object) {
    case ProfileCompletionIncomeRange.UNKNOWN_PROFILE_COMPLETION_INCOME_RANGE:
      return "UNKNOWN_PROFILE_COMPLETION_INCOME_RANGE";
    case ProfileCompletionIncomeRange.LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE:
      return "LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE";
    case ProfileCompletionIncomeRange.BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE:
      return "BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE";
    case ProfileCompletionIncomeRange.BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE:
      return "BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE";
    case ProfileCompletionIncomeRange.BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE:
      return "BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE";
    case ProfileCompletionIncomeRange.BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE:
      return "BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE";
    case ProfileCompletionIncomeRange.ABOVE_5L_PROFILE_COMPLETION_INCOME:
      return "ABOVE_5L_PROFILE_COMPLETION_INCOME";
    case ProfileCompletionIncomeRange.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ProfileCompletionEmploymentType {
  UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 0,
  SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 1,
  SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 2,
  RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 3,
  HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 4,
  OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 5,
  UNRECOGNIZED = -1,
}

export function profileCompletionEmploymentTypeFromJSON(object: any): ProfileCompletionEmploymentType {
  switch (object) {
    case 0:
    case "UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE":
      return ProfileCompletionEmploymentType.UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE;
    case 1:
    case "SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE":
      return ProfileCompletionEmploymentType.SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE;
    case 2:
    case "SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE":
      return ProfileCompletionEmploymentType.SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE;
    case 3:
    case "RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE":
      return ProfileCompletionEmploymentType.RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE;
    case 4:
    case "HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE":
      return ProfileCompletionEmploymentType.HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE;
    case 5:
    case "OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE":
      return ProfileCompletionEmploymentType.OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ProfileCompletionEmploymentType.UNRECOGNIZED;
  }
}

export function profileCompletionEmploymentTypeToJSON(object: ProfileCompletionEmploymentType): string {
  switch (object) {
    case ProfileCompletionEmploymentType.UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE:
      return "UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE";
    case ProfileCompletionEmploymentType.SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE:
      return "SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE";
    case ProfileCompletionEmploymentType.SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE:
      return "SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE";
    case ProfileCompletionEmploymentType.RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE:
      return "RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE";
    case ProfileCompletionEmploymentType.HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE:
      return "HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE";
    case ProfileCompletionEmploymentType.OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE:
      return "OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE";
    case ProfileCompletionEmploymentType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ProfileCompletionStepsResponse {
  steps: ProfileCompletionStep[];
  nextStep: string;
  isCompleted: boolean;
  completionPercentage: number;
}

export interface ProfileCompletionStep {
  stepName: string;
  isCompleted: boolean;
  priority: number;
}

export interface ProfileCompletionRequest {
  stepName: string;
  personalDetails?: PersonalDetails | undefined;
  dob?: string | undefined;
  demographicDetails?: DemographicDetails | undefined;
  employmentDetails?: EmploymentDetails | undefined;
  userBank: UserBank[];
}

export interface UserBank {
  bankId: string;
  hasFdWithBank: boolean;
}

export interface ProfileCompletionResponse {
  title: string;
  imageUrl: string;
  imageType: string;
  description: string;
  completionPercentage: number;
  buttonText: string;
  nextStep: string;
}

export interface PersonalDetails {
  firstName: string;
  lastName: string;
}

export interface DemographicDetails {
  gender: Gender;
  maritalStatus: MaritalStatus;
  cityId: string;
}

export interface EmploymentDetails {
  employmentType: ProfileCompletionEmploymentType;
  monthlyIncome: ProfileCompletionIncomeRange;
}

export interface GetProfileResponse {
  personalDetails: PersonalDetails | undefined;
  dob: string;
  demographicDetails: DemographicDetailsResponse | undefined;
  employmentDetails: EmploymentDetails | undefined;
  banks: Bank[];
  backgroundImageUrl: string;
}

export interface DemographicDetailsResponse {
  gender: Gender;
  maritalStatus: MaritalStatus;
  city: CityResponse | undefined;
}

export interface Bank {
  name: string;
  id: string;
  iconUrl: string;
  hasFdWithBank: boolean;
}

function createBaseProfileCompletionStepsResponse(): ProfileCompletionStepsResponse {
  return { steps: [], nextStep: "", isCompleted: false, completionPercentage: 0 };
}

export const ProfileCompletionStepsResponse: MessageFns<ProfileCompletionStepsResponse> = {
  encode(message: ProfileCompletionStepsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.steps) {
      ProfileCompletionStep.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.nextStep !== "") {
      writer.uint32(18).string(message.nextStep);
    }
    if (message.isCompleted !== false) {
      writer.uint32(24).bool(message.isCompleted);
    }
    if (message.completionPercentage !== 0) {
      writer.uint32(33).double(message.completionPercentage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProfileCompletionStepsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProfileCompletionStepsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.steps.push(ProfileCompletionStep.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nextStep = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isCompleted = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.completionPercentage = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProfileCompletionStepsResponse {
    return {
      steps: globalThis.Array.isArray(object?.steps)
        ? object.steps.map((e: any) => ProfileCompletionStep.fromJSON(e))
        : [],
      nextStep: isSet(object.nextStep) ? globalThis.String(object.nextStep) : "",
      isCompleted: isSet(object.isCompleted) ? globalThis.Boolean(object.isCompleted) : false,
      completionPercentage: isSet(object.completionPercentage) ? globalThis.Number(object.completionPercentage) : 0,
    };
  },

  toJSON(message: ProfileCompletionStepsResponse): unknown {
    const obj: any = {};
    if (message.steps?.length) {
      obj.steps = message.steps.map((e) => ProfileCompletionStep.toJSON(e));
    }
    if (message.nextStep !== "") {
      obj.nextStep = message.nextStep;
    }
    if (message.isCompleted !== false) {
      obj.isCompleted = message.isCompleted;
    }
    if (message.completionPercentage !== 0) {
      obj.completionPercentage = message.completionPercentage;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProfileCompletionStepsResponse>, I>>(base?: I): ProfileCompletionStepsResponse {
    return ProfileCompletionStepsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProfileCompletionStepsResponse>, I>>(
    object: I,
  ): ProfileCompletionStepsResponse {
    const message = createBaseProfileCompletionStepsResponse();
    message.steps = object.steps?.map((e) => ProfileCompletionStep.fromPartial(e)) || [];
    message.nextStep = object.nextStep ?? "";
    message.isCompleted = object.isCompleted ?? false;
    message.completionPercentage = object.completionPercentage ?? 0;
    return message;
  },
};

function createBaseProfileCompletionStep(): ProfileCompletionStep {
  return { stepName: "", isCompleted: false, priority: 0 };
}

export const ProfileCompletionStep: MessageFns<ProfileCompletionStep> = {
  encode(message: ProfileCompletionStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.stepName !== "") {
      writer.uint32(10).string(message.stepName);
    }
    if (message.isCompleted !== false) {
      writer.uint32(16).bool(message.isCompleted);
    }
    if (message.priority !== 0) {
      writer.uint32(24).int32(message.priority);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProfileCompletionStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProfileCompletionStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.stepName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.isCompleted = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProfileCompletionStep {
    return {
      stepName: isSet(object.stepName) ? globalThis.String(object.stepName) : "",
      isCompleted: isSet(object.isCompleted) ? globalThis.Boolean(object.isCompleted) : false,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
    };
  },

  toJSON(message: ProfileCompletionStep): unknown {
    const obj: any = {};
    if (message.stepName !== "") {
      obj.stepName = message.stepName;
    }
    if (message.isCompleted !== false) {
      obj.isCompleted = message.isCompleted;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProfileCompletionStep>, I>>(base?: I): ProfileCompletionStep {
    return ProfileCompletionStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProfileCompletionStep>, I>>(object: I): ProfileCompletionStep {
    const message = createBaseProfileCompletionStep();
    message.stepName = object.stepName ?? "";
    message.isCompleted = object.isCompleted ?? false;
    message.priority = object.priority ?? 0;
    return message;
  },
};

function createBaseProfileCompletionRequest(): ProfileCompletionRequest {
  return {
    stepName: "",
    personalDetails: undefined,
    dob: undefined,
    demographicDetails: undefined,
    employmentDetails: undefined,
    userBank: [],
  };
}

export const ProfileCompletionRequest: MessageFns<ProfileCompletionRequest> = {
  encode(message: ProfileCompletionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.stepName !== "") {
      writer.uint32(10).string(message.stepName);
    }
    if (message.personalDetails !== undefined) {
      PersonalDetails.encode(message.personalDetails, writer.uint32(18).fork()).join();
    }
    if (message.dob !== undefined) {
      writer.uint32(26).string(message.dob);
    }
    if (message.demographicDetails !== undefined) {
      DemographicDetails.encode(message.demographicDetails, writer.uint32(34).fork()).join();
    }
    if (message.employmentDetails !== undefined) {
      EmploymentDetails.encode(message.employmentDetails, writer.uint32(42).fork()).join();
    }
    for (const v of message.userBank) {
      UserBank.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProfileCompletionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProfileCompletionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.stepName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.personalDetails = PersonalDetails.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.dob = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.demographicDetails = DemographicDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.employmentDetails = EmploymentDetails.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.userBank.push(UserBank.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProfileCompletionRequest {
    return {
      stepName: isSet(object.stepName) ? globalThis.String(object.stepName) : "",
      personalDetails: isSet(object.personalDetails) ? PersonalDetails.fromJSON(object.personalDetails) : undefined,
      dob: isSet(object.dob) ? globalThis.String(object.dob) : undefined,
      demographicDetails: isSet(object.demographicDetails)
        ? DemographicDetails.fromJSON(object.demographicDetails)
        : undefined,
      employmentDetails: isSet(object.employmentDetails)
        ? EmploymentDetails.fromJSON(object.employmentDetails)
        : undefined,
      userBank: globalThis.Array.isArray(object?.userBank) ? object.userBank.map((e: any) => UserBank.fromJSON(e)) : [],
    };
  },

  toJSON(message: ProfileCompletionRequest): unknown {
    const obj: any = {};
    if (message.stepName !== "") {
      obj.stepName = message.stepName;
    }
    if (message.personalDetails !== undefined) {
      obj.personalDetails = PersonalDetails.toJSON(message.personalDetails);
    }
    if (message.dob !== undefined) {
      obj.dob = message.dob;
    }
    if (message.demographicDetails !== undefined) {
      obj.demographicDetails = DemographicDetails.toJSON(message.demographicDetails);
    }
    if (message.employmentDetails !== undefined) {
      obj.employmentDetails = EmploymentDetails.toJSON(message.employmentDetails);
    }
    if (message.userBank?.length) {
      obj.userBank = message.userBank.map((e) => UserBank.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProfileCompletionRequest>, I>>(base?: I): ProfileCompletionRequest {
    return ProfileCompletionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProfileCompletionRequest>, I>>(object: I): ProfileCompletionRequest {
    const message = createBaseProfileCompletionRequest();
    message.stepName = object.stepName ?? "";
    message.personalDetails = (object.personalDetails !== undefined && object.personalDetails !== null)
      ? PersonalDetails.fromPartial(object.personalDetails)
      : undefined;
    message.dob = object.dob ?? undefined;
    message.demographicDetails = (object.demographicDetails !== undefined && object.demographicDetails !== null)
      ? DemographicDetails.fromPartial(object.demographicDetails)
      : undefined;
    message.employmentDetails = (object.employmentDetails !== undefined && object.employmentDetails !== null)
      ? EmploymentDetails.fromPartial(object.employmentDetails)
      : undefined;
    message.userBank = object.userBank?.map((e) => UserBank.fromPartial(e)) || [];
    return message;
  },
};

function createBaseUserBank(): UserBank {
  return { bankId: "", hasFdWithBank: false };
}

export const UserBank: MessageFns<UserBank> = {
  encode(message: UserBank, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    if (message.hasFdWithBank !== false) {
      writer.uint32(16).bool(message.hasFdWithBank);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserBank {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserBank();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.hasFdWithBank = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserBank {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      hasFdWithBank: isSet(object.hasFdWithBank) ? globalThis.Boolean(object.hasFdWithBank) : false,
    };
  },

  toJSON(message: UserBank): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.hasFdWithBank !== false) {
      obj.hasFdWithBank = message.hasFdWithBank;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserBank>, I>>(base?: I): UserBank {
    return UserBank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserBank>, I>>(object: I): UserBank {
    const message = createBaseUserBank();
    message.bankId = object.bankId ?? "";
    message.hasFdWithBank = object.hasFdWithBank ?? false;
    return message;
  },
};

function createBaseProfileCompletionResponse(): ProfileCompletionResponse {
  return {
    title: "",
    imageUrl: "",
    imageType: "",
    description: "",
    completionPercentage: 0,
    buttonText: "",
    nextStep: "",
  };
}

export const ProfileCompletionResponse: MessageFns<ProfileCompletionResponse> = {
  encode(message: ProfileCompletionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.imageUrl !== "") {
      writer.uint32(18).string(message.imageUrl);
    }
    if (message.imageType !== "") {
      writer.uint32(26).string(message.imageType);
    }
    if (message.description !== "") {
      writer.uint32(34).string(message.description);
    }
    if (message.completionPercentage !== 0) {
      writer.uint32(41).double(message.completionPercentage);
    }
    if (message.buttonText !== "") {
      writer.uint32(50).string(message.buttonText);
    }
    if (message.nextStep !== "") {
      writer.uint32(58).string(message.nextStep);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProfileCompletionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProfileCompletionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.imageUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.imageType = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.completionPercentage = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.buttonText = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.nextStep = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProfileCompletionResponse {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      imageUrl: isSet(object.imageUrl) ? globalThis.String(object.imageUrl) : "",
      imageType: isSet(object.imageType) ? globalThis.String(object.imageType) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      completionPercentage: isSet(object.completionPercentage) ? globalThis.Number(object.completionPercentage) : 0,
      buttonText: isSet(object.buttonText) ? globalThis.String(object.buttonText) : "",
      nextStep: isSet(object.nextStep) ? globalThis.String(object.nextStep) : "",
    };
  },

  toJSON(message: ProfileCompletionResponse): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.imageUrl !== "") {
      obj.imageUrl = message.imageUrl;
    }
    if (message.imageType !== "") {
      obj.imageType = message.imageType;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.completionPercentage !== 0) {
      obj.completionPercentage = message.completionPercentage;
    }
    if (message.buttonText !== "") {
      obj.buttonText = message.buttonText;
    }
    if (message.nextStep !== "") {
      obj.nextStep = message.nextStep;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProfileCompletionResponse>, I>>(base?: I): ProfileCompletionResponse {
    return ProfileCompletionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProfileCompletionResponse>, I>>(object: I): ProfileCompletionResponse {
    const message = createBaseProfileCompletionResponse();
    message.title = object.title ?? "";
    message.imageUrl = object.imageUrl ?? "";
    message.imageType = object.imageType ?? "";
    message.description = object.description ?? "";
    message.completionPercentage = object.completionPercentage ?? 0;
    message.buttonText = object.buttonText ?? "";
    message.nextStep = object.nextStep ?? "";
    return message;
  },
};

function createBasePersonalDetails(): PersonalDetails {
  return { firstName: "", lastName: "" };
}

export const PersonalDetails: MessageFns<PersonalDetails> = {
  encode(message: PersonalDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.firstName !== "") {
      writer.uint32(10).string(message.firstName);
    }
    if (message.lastName !== "") {
      writer.uint32(18).string(message.lastName);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PersonalDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePersonalDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.firstName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.lastName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PersonalDetails {
    return {
      firstName: isSet(object.firstName) ? globalThis.String(object.firstName) : "",
      lastName: isSet(object.lastName) ? globalThis.String(object.lastName) : "",
    };
  },

  toJSON(message: PersonalDetails): unknown {
    const obj: any = {};
    if (message.firstName !== "") {
      obj.firstName = message.firstName;
    }
    if (message.lastName !== "") {
      obj.lastName = message.lastName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PersonalDetails>, I>>(base?: I): PersonalDetails {
    return PersonalDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PersonalDetails>, I>>(object: I): PersonalDetails {
    const message = createBasePersonalDetails();
    message.firstName = object.firstName ?? "";
    message.lastName = object.lastName ?? "";
    return message;
  },
};

function createBaseDemographicDetails(): DemographicDetails {
  return { gender: 0, maritalStatus: 0, cityId: "" };
}

export const DemographicDetails: MessageFns<DemographicDetails> = {
  encode(message: DemographicDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.gender !== 0) {
      writer.uint32(8).int32(message.gender);
    }
    if (message.maritalStatus !== 0) {
      writer.uint32(16).int32(message.maritalStatus);
    }
    if (message.cityId !== "") {
      writer.uint32(26).string(message.cityId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DemographicDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDemographicDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.gender = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.maritalStatus = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.cityId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DemographicDetails {
    return {
      gender: isSet(object.gender) ? genderFromJSON(object.gender) : 0,
      maritalStatus: isSet(object.maritalStatus) ? maritalStatusFromJSON(object.maritalStatus) : 0,
      cityId: isSet(object.cityId) ? globalThis.String(object.cityId) : "",
    };
  },

  toJSON(message: DemographicDetails): unknown {
    const obj: any = {};
    if (message.gender !== 0) {
      obj.gender = genderToJSON(message.gender);
    }
    if (message.maritalStatus !== 0) {
      obj.maritalStatus = maritalStatusToJSON(message.maritalStatus);
    }
    if (message.cityId !== "") {
      obj.cityId = message.cityId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DemographicDetails>, I>>(base?: I): DemographicDetails {
    return DemographicDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DemographicDetails>, I>>(object: I): DemographicDetails {
    const message = createBaseDemographicDetails();
    message.gender = object.gender ?? 0;
    message.maritalStatus = object.maritalStatus ?? 0;
    message.cityId = object.cityId ?? "";
    return message;
  },
};

function createBaseEmploymentDetails(): EmploymentDetails {
  return { employmentType: 0, monthlyIncome: 0 };
}

export const EmploymentDetails: MessageFns<EmploymentDetails> = {
  encode(message: EmploymentDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.employmentType !== 0) {
      writer.uint32(8).int32(message.employmentType);
    }
    if (message.monthlyIncome !== 0) {
      writer.uint32(16).int32(message.monthlyIncome);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): EmploymentDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEmploymentDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.employmentType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.monthlyIncome = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EmploymentDetails {
    return {
      employmentType: isSet(object.employmentType) ? profileCompletionEmploymentTypeFromJSON(object.employmentType) : 0,
      monthlyIncome: isSet(object.monthlyIncome) ? profileCompletionIncomeRangeFromJSON(object.monthlyIncome) : 0,
    };
  },

  toJSON(message: EmploymentDetails): unknown {
    const obj: any = {};
    if (message.employmentType !== 0) {
      obj.employmentType = profileCompletionEmploymentTypeToJSON(message.employmentType);
    }
    if (message.monthlyIncome !== 0) {
      obj.monthlyIncome = profileCompletionIncomeRangeToJSON(message.monthlyIncome);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<EmploymentDetails>, I>>(base?: I): EmploymentDetails {
    return EmploymentDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmploymentDetails>, I>>(object: I): EmploymentDetails {
    const message = createBaseEmploymentDetails();
    message.employmentType = object.employmentType ?? 0;
    message.monthlyIncome = object.monthlyIncome ?? 0;
    return message;
  },
};

function createBaseGetProfileResponse(): GetProfileResponse {
  return {
    personalDetails: undefined,
    dob: "",
    demographicDetails: undefined,
    employmentDetails: undefined,
    banks: [],
    backgroundImageUrl: "",
  };
}

export const GetProfileResponse: MessageFns<GetProfileResponse> = {
  encode(message: GetProfileResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.personalDetails !== undefined) {
      PersonalDetails.encode(message.personalDetails, writer.uint32(10).fork()).join();
    }
    if (message.dob !== "") {
      writer.uint32(18).string(message.dob);
    }
    if (message.demographicDetails !== undefined) {
      DemographicDetailsResponse.encode(message.demographicDetails, writer.uint32(26).fork()).join();
    }
    if (message.employmentDetails !== undefined) {
      EmploymentDetails.encode(message.employmentDetails, writer.uint32(34).fork()).join();
    }
    for (const v of message.banks) {
      Bank.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.backgroundImageUrl !== "") {
      writer.uint32(50).string(message.backgroundImageUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetProfileResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetProfileResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.personalDetails = PersonalDetails.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.dob = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.demographicDetails = DemographicDetailsResponse.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.employmentDetails = EmploymentDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.banks.push(Bank.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.backgroundImageUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetProfileResponse {
    return {
      personalDetails: isSet(object.personalDetails) ? PersonalDetails.fromJSON(object.personalDetails) : undefined,
      dob: isSet(object.dob) ? globalThis.String(object.dob) : "",
      demographicDetails: isSet(object.demographicDetails)
        ? DemographicDetailsResponse.fromJSON(object.demographicDetails)
        : undefined,
      employmentDetails: isSet(object.employmentDetails)
        ? EmploymentDetails.fromJSON(object.employmentDetails)
        : undefined,
      banks: globalThis.Array.isArray(object?.banks) ? object.banks.map((e: any) => Bank.fromJSON(e)) : [],
      backgroundImageUrl: isSet(object.backgroundImageUrl) ? globalThis.String(object.backgroundImageUrl) : "",
    };
  },

  toJSON(message: GetProfileResponse): unknown {
    const obj: any = {};
    if (message.personalDetails !== undefined) {
      obj.personalDetails = PersonalDetails.toJSON(message.personalDetails);
    }
    if (message.dob !== "") {
      obj.dob = message.dob;
    }
    if (message.demographicDetails !== undefined) {
      obj.demographicDetails = DemographicDetailsResponse.toJSON(message.demographicDetails);
    }
    if (message.employmentDetails !== undefined) {
      obj.employmentDetails = EmploymentDetails.toJSON(message.employmentDetails);
    }
    if (message.banks?.length) {
      obj.banks = message.banks.map((e) => Bank.toJSON(e));
    }
    if (message.backgroundImageUrl !== "") {
      obj.backgroundImageUrl = message.backgroundImageUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetProfileResponse>, I>>(base?: I): GetProfileResponse {
    return GetProfileResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetProfileResponse>, I>>(object: I): GetProfileResponse {
    const message = createBaseGetProfileResponse();
    message.personalDetails = (object.personalDetails !== undefined && object.personalDetails !== null)
      ? PersonalDetails.fromPartial(object.personalDetails)
      : undefined;
    message.dob = object.dob ?? "";
    message.demographicDetails = (object.demographicDetails !== undefined && object.demographicDetails !== null)
      ? DemographicDetailsResponse.fromPartial(object.demographicDetails)
      : undefined;
    message.employmentDetails = (object.employmentDetails !== undefined && object.employmentDetails !== null)
      ? EmploymentDetails.fromPartial(object.employmentDetails)
      : undefined;
    message.banks = object.banks?.map((e) => Bank.fromPartial(e)) || [];
    message.backgroundImageUrl = object.backgroundImageUrl ?? "";
    return message;
  },
};

function createBaseDemographicDetailsResponse(): DemographicDetailsResponse {
  return { gender: 0, maritalStatus: 0, city: undefined };
}

export const DemographicDetailsResponse: MessageFns<DemographicDetailsResponse> = {
  encode(message: DemographicDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.gender !== 0) {
      writer.uint32(8).int32(message.gender);
    }
    if (message.maritalStatus !== 0) {
      writer.uint32(16).int32(message.maritalStatus);
    }
    if (message.city !== undefined) {
      CityResponse.encode(message.city, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DemographicDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDemographicDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.gender = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.maritalStatus = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.city = CityResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DemographicDetailsResponse {
    return {
      gender: isSet(object.gender) ? genderFromJSON(object.gender) : 0,
      maritalStatus: isSet(object.maritalStatus) ? maritalStatusFromJSON(object.maritalStatus) : 0,
      city: isSet(object.city) ? CityResponse.fromJSON(object.city) : undefined,
    };
  },

  toJSON(message: DemographicDetailsResponse): unknown {
    const obj: any = {};
    if (message.gender !== 0) {
      obj.gender = genderToJSON(message.gender);
    }
    if (message.maritalStatus !== 0) {
      obj.maritalStatus = maritalStatusToJSON(message.maritalStatus);
    }
    if (message.city !== undefined) {
      obj.city = CityResponse.toJSON(message.city);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DemographicDetailsResponse>, I>>(base?: I): DemographicDetailsResponse {
    return DemographicDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DemographicDetailsResponse>, I>>(object: I): DemographicDetailsResponse {
    const message = createBaseDemographicDetailsResponse();
    message.gender = object.gender ?? 0;
    message.maritalStatus = object.maritalStatus ?? 0;
    message.city = (object.city !== undefined && object.city !== null)
      ? CityResponse.fromPartial(object.city)
      : undefined;
    return message;
  },
};

function createBaseBank(): Bank {
  return { name: "", id: "", iconUrl: "", hasFdWithBank: false };
}

export const Bank: MessageFns<Bank> = {
  encode(message: Bank, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    if (message.iconUrl !== "") {
      writer.uint32(26).string(message.iconUrl);
    }
    if (message.hasFdWithBank !== false) {
      writer.uint32(32).bool(message.hasFdWithBank);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Bank {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBank();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.hasFdWithBank = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Bank {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      hasFdWithBank: isSet(object.hasFdWithBank) ? globalThis.Boolean(object.hasFdWithBank) : false,
    };
  },

  toJSON(message: Bank): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.hasFdWithBank !== false) {
      obj.hasFdWithBank = message.hasFdWithBank;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Bank>, I>>(base?: I): Bank {
    return Bank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Bank>, I>>(object: I): Bank {
    const message = createBaseBank();
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.hasFdWithBank = object.hasFdWithBank ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
