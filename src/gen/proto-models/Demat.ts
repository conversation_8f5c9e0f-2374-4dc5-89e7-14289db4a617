// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Demat.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export interface GetProviderDetailsRequest {
  partialDematAccountNumber: string;
}

export interface GetDematProviderDetailsResponse {
  providerId: string;
  name: string;
  iconUrl: string;
}

export interface AddDematAccountRequest {
  dematAccountNumber: string;
}

export interface AddDematAccountResponse {
}

function createBaseGetProviderDetailsRequest(): GetProviderDetailsRequest {
  return { partialDematAccountNumber: "" };
}

export const GetProviderDetailsRequest: MessageFns<GetProviderDetailsRequest> = {
  encode(message: GetProviderDetailsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.partialDematAccountNumber !== "") {
      writer.uint32(10).string(message.partialDematAccountNumber);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetProviderDetailsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetProviderDetailsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.partialDematAccountNumber = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetProviderDetailsRequest {
    return {
      partialDematAccountNumber: isSet(object.partialDematAccountNumber)
        ? globalThis.String(object.partialDematAccountNumber)
        : "",
    };
  },

  toJSON(message: GetProviderDetailsRequest): unknown {
    const obj: any = {};
    if (message.partialDematAccountNumber !== "") {
      obj.partialDematAccountNumber = message.partialDematAccountNumber;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetProviderDetailsRequest>, I>>(base?: I): GetProviderDetailsRequest {
    return GetProviderDetailsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetProviderDetailsRequest>, I>>(object: I): GetProviderDetailsRequest {
    const message = createBaseGetProviderDetailsRequest();
    message.partialDematAccountNumber = object.partialDematAccountNumber ?? "";
    return message;
  },
};

function createBaseGetDematProviderDetailsResponse(): GetDematProviderDetailsResponse {
  return { providerId: "", name: "", iconUrl: "" };
}

export const GetDematProviderDetailsResponse: MessageFns<GetDematProviderDetailsResponse> = {
  encode(message: GetDematProviderDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.providerId !== "") {
      writer.uint32(10).string(message.providerId);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.iconUrl !== "") {
      writer.uint32(26).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetDematProviderDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetDematProviderDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.providerId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetDematProviderDetailsResponse {
    return {
      providerId: isSet(object.providerId) ? globalThis.String(object.providerId) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: GetDematProviderDetailsResponse): unknown {
    const obj: any = {};
    if (message.providerId !== "") {
      obj.providerId = message.providerId;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetDematProviderDetailsResponse>, I>>(base?: I): GetDematProviderDetailsResponse {
    return GetDematProviderDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetDematProviderDetailsResponse>, I>>(
    object: I,
  ): GetDematProviderDetailsResponse {
    const message = createBaseGetDematProviderDetailsResponse();
    message.providerId = object.providerId ?? "";
    message.name = object.name ?? "";
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBaseAddDematAccountRequest(): AddDematAccountRequest {
  return { dematAccountNumber: "" };
}

export const AddDematAccountRequest: MessageFns<AddDematAccountRequest> = {
  encode(message: AddDematAccountRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.dematAccountNumber !== "") {
      writer.uint32(26).string(message.dematAccountNumber);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddDematAccountRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddDematAccountRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.dematAccountNumber = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddDematAccountRequest {
    return { dematAccountNumber: isSet(object.dematAccountNumber) ? globalThis.String(object.dematAccountNumber) : "" };
  },

  toJSON(message: AddDematAccountRequest): unknown {
    const obj: any = {};
    if (message.dematAccountNumber !== "") {
      obj.dematAccountNumber = message.dematAccountNumber;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddDematAccountRequest>, I>>(base?: I): AddDematAccountRequest {
    return AddDematAccountRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddDematAccountRequest>, I>>(object: I): AddDematAccountRequest {
    const message = createBaseAddDematAccountRequest();
    message.dematAccountNumber = object.dematAccountNumber ?? "";
    return message;
  },
};

function createBaseAddDematAccountResponse(): AddDematAccountResponse {
  return {};
}

export const AddDematAccountResponse: MessageFns<AddDematAccountResponse> = {
  encode(_: AddDematAccountResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddDematAccountResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddDematAccountResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): AddDematAccountResponse {
    return {};
  },

  toJSON(_: AddDematAccountResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<AddDematAccountResponse>, I>>(base?: I): AddDematAccountResponse {
    return AddDematAccountResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddDematAccountResponse>, I>>(_: I): AddDematAccountResponse {
    const message = createBaseAddDematAccountResponse();
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
