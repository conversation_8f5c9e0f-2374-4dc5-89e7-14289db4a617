// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: StableExchange.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export interface QuestionData {
  question: string;
  questionId: string;
  perspective: string;
  userName: string;
  userAge: number;
  userGender: string;
  userCity: string;
  userImage: string;
  responseCount: string;
  registrationDate: string;
  answerData: AnswerList[];
  isReminderSet: boolean;
  reminderCount: number;
  questionPostedDate: string;
  isQuestionAnswered: boolean;
  isQuestionPosted: boolean;
  agreePercentage: string;
  welDoneString: string;
  responderImages: string[];
  reminderAskString: ReminderAskString | undefined;
  userFirstName: string;
  questionPostedDateString: string;
  questionPostedEndDate: string;
}

export interface TodayQuestionResponse {
  questionData: QuestionData | undefined;
}

export interface AnswerList {
  answerId: string;
  answer: string;
  percentage: number;
}

export interface AllQuestionResponse {
  questionData: QuestionData[];
}

export interface ReminderAskString {
  reminderString: string;
  askString: string[];
  completionString: string;
}

export interface SubmitAnswerRequest {
  answerId: string;
  questionId: string;
}

export interface SubmitAnswerResponse {
  questionData: QuestionData | undefined;
}

export interface SubmitQuestionRequest {
  question: string;
}

export interface SubmitQuestionResponse {
}

export interface SubmitReminderResponse {
  questionData: QuestionData | undefined;
}

export interface SubmitReminderRequest {
  isReminder: boolean;
}

function createBaseQuestionData(): QuestionData {
  return {
    question: "",
    questionId: "",
    perspective: "",
    userName: "",
    userAge: 0,
    userGender: "",
    userCity: "",
    userImage: "",
    responseCount: "",
    registrationDate: "",
    answerData: [],
    isReminderSet: false,
    reminderCount: 0,
    questionPostedDate: "",
    isQuestionAnswered: false,
    isQuestionPosted: false,
    agreePercentage: "",
    welDoneString: "",
    responderImages: [],
    reminderAskString: undefined,
    userFirstName: "",
    questionPostedDateString: "",
    questionPostedEndDate: "",
  };
}

export const QuestionData: MessageFns<QuestionData> = {
  encode(message: QuestionData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.question !== "") {
      writer.uint32(10).string(message.question);
    }
    if (message.questionId !== "") {
      writer.uint32(18).string(message.questionId);
    }
    if (message.perspective !== "") {
      writer.uint32(26).string(message.perspective);
    }
    if (message.userName !== "") {
      writer.uint32(34).string(message.userName);
    }
    if (message.userAge !== 0) {
      writer.uint32(40).int32(message.userAge);
    }
    if (message.userGender !== "") {
      writer.uint32(50).string(message.userGender);
    }
    if (message.userCity !== "") {
      writer.uint32(58).string(message.userCity);
    }
    if (message.userImage !== "") {
      writer.uint32(66).string(message.userImage);
    }
    if (message.responseCount !== "") {
      writer.uint32(74).string(message.responseCount);
    }
    if (message.registrationDate !== "") {
      writer.uint32(82).string(message.registrationDate);
    }
    for (const v of message.answerData) {
      AnswerList.encode(v!, writer.uint32(90).fork()).join();
    }
    if (message.isReminderSet !== false) {
      writer.uint32(96).bool(message.isReminderSet);
    }
    if (message.reminderCount !== 0) {
      writer.uint32(104).int32(message.reminderCount);
    }
    if (message.questionPostedDate !== "") {
      writer.uint32(114).string(message.questionPostedDate);
    }
    if (message.isQuestionAnswered !== false) {
      writer.uint32(120).bool(message.isQuestionAnswered);
    }
    if (message.isQuestionPosted !== false) {
      writer.uint32(128).bool(message.isQuestionPosted);
    }
    if (message.agreePercentage !== "") {
      writer.uint32(138).string(message.agreePercentage);
    }
    if (message.welDoneString !== "") {
      writer.uint32(146).string(message.welDoneString);
    }
    for (const v of message.responderImages) {
      writer.uint32(154).string(v!);
    }
    if (message.reminderAskString !== undefined) {
      ReminderAskString.encode(message.reminderAskString, writer.uint32(162).fork()).join();
    }
    if (message.userFirstName !== "") {
      writer.uint32(170).string(message.userFirstName);
    }
    if (message.questionPostedDateString !== "") {
      writer.uint32(178).string(message.questionPostedDateString);
    }
    if (message.questionPostedEndDate !== "") {
      writer.uint32(186).string(message.questionPostedEndDate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): QuestionData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQuestionData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.question = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.questionId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.perspective = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.userName = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.userAge = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.userGender = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.userCity = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.userImage = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.responseCount = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.registrationDate = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.answerData.push(AnswerList.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.isReminderSet = reader.bool();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.reminderCount = reader.int32();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.questionPostedDate = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.isQuestionAnswered = reader.bool();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.isQuestionPosted = reader.bool();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.agreePercentage = reader.string();
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.welDoneString = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.responderImages.push(reader.string());
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.reminderAskString = ReminderAskString.decode(reader, reader.uint32());
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.userFirstName = reader.string();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.questionPostedDateString = reader.string();
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.questionPostedEndDate = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QuestionData {
    return {
      question: isSet(object.question) ? globalThis.String(object.question) : "",
      questionId: isSet(object.questionId) ? globalThis.String(object.questionId) : "",
      perspective: isSet(object.perspective) ? globalThis.String(object.perspective) : "",
      userName: isSet(object.userName) ? globalThis.String(object.userName) : "",
      userAge: isSet(object.userAge) ? globalThis.Number(object.userAge) : 0,
      userGender: isSet(object.userGender) ? globalThis.String(object.userGender) : "",
      userCity: isSet(object.userCity) ? globalThis.String(object.userCity) : "",
      userImage: isSet(object.userImage) ? globalThis.String(object.userImage) : "",
      responseCount: isSet(object.responseCount) ? globalThis.String(object.responseCount) : "",
      registrationDate: isSet(object.registrationDate) ? globalThis.String(object.registrationDate) : "",
      answerData: globalThis.Array.isArray(object?.answerData)
        ? object.answerData.map((e: any) => AnswerList.fromJSON(e))
        : [],
      isReminderSet: isSet(object.isReminderSet) ? globalThis.Boolean(object.isReminderSet) : false,
      reminderCount: isSet(object.reminderCount) ? globalThis.Number(object.reminderCount) : 0,
      questionPostedDate: isSet(object.questionPostedDate) ? globalThis.String(object.questionPostedDate) : "",
      isQuestionAnswered: isSet(object.isQuestionAnswered) ? globalThis.Boolean(object.isQuestionAnswered) : false,
      isQuestionPosted: isSet(object.isQuestionPosted) ? globalThis.Boolean(object.isQuestionPosted) : false,
      agreePercentage: isSet(object.agreePercentage) ? globalThis.String(object.agreePercentage) : "",
      welDoneString: isSet(object.welDoneString) ? globalThis.String(object.welDoneString) : "",
      responderImages: globalThis.Array.isArray(object?.responderImages)
        ? object.responderImages.map((e: any) => globalThis.String(e))
        : [],
      reminderAskString: isSet(object.reminderAskString)
        ? ReminderAskString.fromJSON(object.reminderAskString)
        : undefined,
      userFirstName: isSet(object.userFirstName) ? globalThis.String(object.userFirstName) : "",
      questionPostedDateString: isSet(object.questionPostedDateString)
        ? globalThis.String(object.questionPostedDateString)
        : "",
      questionPostedEndDate: isSet(object.questionPostedEndDate) ? globalThis.String(object.questionPostedEndDate) : "",
    };
  },

  toJSON(message: QuestionData): unknown {
    const obj: any = {};
    if (message.question !== "") {
      obj.question = message.question;
    }
    if (message.questionId !== "") {
      obj.questionId = message.questionId;
    }
    if (message.perspective !== "") {
      obj.perspective = message.perspective;
    }
    if (message.userName !== "") {
      obj.userName = message.userName;
    }
    if (message.userAge !== 0) {
      obj.userAge = Math.round(message.userAge);
    }
    if (message.userGender !== "") {
      obj.userGender = message.userGender;
    }
    if (message.userCity !== "") {
      obj.userCity = message.userCity;
    }
    if (message.userImage !== "") {
      obj.userImage = message.userImage;
    }
    if (message.responseCount !== "") {
      obj.responseCount = message.responseCount;
    }
    if (message.registrationDate !== "") {
      obj.registrationDate = message.registrationDate;
    }
    if (message.answerData?.length) {
      obj.answerData = message.answerData.map((e) => AnswerList.toJSON(e));
    }
    if (message.isReminderSet !== false) {
      obj.isReminderSet = message.isReminderSet;
    }
    if (message.reminderCount !== 0) {
      obj.reminderCount = Math.round(message.reminderCount);
    }
    if (message.questionPostedDate !== "") {
      obj.questionPostedDate = message.questionPostedDate;
    }
    if (message.isQuestionAnswered !== false) {
      obj.isQuestionAnswered = message.isQuestionAnswered;
    }
    if (message.isQuestionPosted !== false) {
      obj.isQuestionPosted = message.isQuestionPosted;
    }
    if (message.agreePercentage !== "") {
      obj.agreePercentage = message.agreePercentage;
    }
    if (message.welDoneString !== "") {
      obj.welDoneString = message.welDoneString;
    }
    if (message.responderImages?.length) {
      obj.responderImages = message.responderImages;
    }
    if (message.reminderAskString !== undefined) {
      obj.reminderAskString = ReminderAskString.toJSON(message.reminderAskString);
    }
    if (message.userFirstName !== "") {
      obj.userFirstName = message.userFirstName;
    }
    if (message.questionPostedDateString !== "") {
      obj.questionPostedDateString = message.questionPostedDateString;
    }
    if (message.questionPostedEndDate !== "") {
      obj.questionPostedEndDate = message.questionPostedEndDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QuestionData>, I>>(base?: I): QuestionData {
    return QuestionData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QuestionData>, I>>(object: I): QuestionData {
    const message = createBaseQuestionData();
    message.question = object.question ?? "";
    message.questionId = object.questionId ?? "";
    message.perspective = object.perspective ?? "";
    message.userName = object.userName ?? "";
    message.userAge = object.userAge ?? 0;
    message.userGender = object.userGender ?? "";
    message.userCity = object.userCity ?? "";
    message.userImage = object.userImage ?? "";
    message.responseCount = object.responseCount ?? "";
    message.registrationDate = object.registrationDate ?? "";
    message.answerData = object.answerData?.map((e) => AnswerList.fromPartial(e)) || [];
    message.isReminderSet = object.isReminderSet ?? false;
    message.reminderCount = object.reminderCount ?? 0;
    message.questionPostedDate = object.questionPostedDate ?? "";
    message.isQuestionAnswered = object.isQuestionAnswered ?? false;
    message.isQuestionPosted = object.isQuestionPosted ?? false;
    message.agreePercentage = object.agreePercentage ?? "";
    message.welDoneString = object.welDoneString ?? "";
    message.responderImages = object.responderImages?.map((e) => e) || [];
    message.reminderAskString = (object.reminderAskString !== undefined && object.reminderAskString !== null)
      ? ReminderAskString.fromPartial(object.reminderAskString)
      : undefined;
    message.userFirstName = object.userFirstName ?? "";
    message.questionPostedDateString = object.questionPostedDateString ?? "";
    message.questionPostedEndDate = object.questionPostedEndDate ?? "";
    return message;
  },
};

function createBaseTodayQuestionResponse(): TodayQuestionResponse {
  return { questionData: undefined };
}

export const TodayQuestionResponse: MessageFns<TodayQuestionResponse> = {
  encode(message: TodayQuestionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.questionData !== undefined) {
      QuestionData.encode(message.questionData, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TodayQuestionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTodayQuestionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.questionData = QuestionData.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TodayQuestionResponse {
    return { questionData: isSet(object.questionData) ? QuestionData.fromJSON(object.questionData) : undefined };
  },

  toJSON(message: TodayQuestionResponse): unknown {
    const obj: any = {};
    if (message.questionData !== undefined) {
      obj.questionData = QuestionData.toJSON(message.questionData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TodayQuestionResponse>, I>>(base?: I): TodayQuestionResponse {
    return TodayQuestionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TodayQuestionResponse>, I>>(object: I): TodayQuestionResponse {
    const message = createBaseTodayQuestionResponse();
    message.questionData = (object.questionData !== undefined && object.questionData !== null)
      ? QuestionData.fromPartial(object.questionData)
      : undefined;
    return message;
  },
};

function createBaseAnswerList(): AnswerList {
  return { answerId: "", answer: "", percentage: 0 };
}

export const AnswerList: MessageFns<AnswerList> = {
  encode(message: AnswerList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.answerId !== "") {
      writer.uint32(10).string(message.answerId);
    }
    if (message.answer !== "") {
      writer.uint32(18).string(message.answer);
    }
    if (message.percentage !== 0) {
      writer.uint32(25).double(message.percentage);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AnswerList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAnswerList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.answerId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.answer = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.percentage = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AnswerList {
    return {
      answerId: isSet(object.answerId) ? globalThis.String(object.answerId) : "",
      answer: isSet(object.answer) ? globalThis.String(object.answer) : "",
      percentage: isSet(object.percentage) ? globalThis.Number(object.percentage) : 0,
    };
  },

  toJSON(message: AnswerList): unknown {
    const obj: any = {};
    if (message.answerId !== "") {
      obj.answerId = message.answerId;
    }
    if (message.answer !== "") {
      obj.answer = message.answer;
    }
    if (message.percentage !== 0) {
      obj.percentage = message.percentage;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AnswerList>, I>>(base?: I): AnswerList {
    return AnswerList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AnswerList>, I>>(object: I): AnswerList {
    const message = createBaseAnswerList();
    message.answerId = object.answerId ?? "";
    message.answer = object.answer ?? "";
    message.percentage = object.percentage ?? 0;
    return message;
  },
};

function createBaseAllQuestionResponse(): AllQuestionResponse {
  return { questionData: [] };
}

export const AllQuestionResponse: MessageFns<AllQuestionResponse> = {
  encode(message: AllQuestionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.questionData) {
      QuestionData.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllQuestionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllQuestionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.questionData.push(QuestionData.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllQuestionResponse {
    return {
      questionData: globalThis.Array.isArray(object?.questionData)
        ? object.questionData.map((e: any) => QuestionData.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AllQuestionResponse): unknown {
    const obj: any = {};
    if (message.questionData?.length) {
      obj.questionData = message.questionData.map((e) => QuestionData.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllQuestionResponse>, I>>(base?: I): AllQuestionResponse {
    return AllQuestionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllQuestionResponse>, I>>(object: I): AllQuestionResponse {
    const message = createBaseAllQuestionResponse();
    message.questionData = object.questionData?.map((e) => QuestionData.fromPartial(e)) || [];
    return message;
  },
};

function createBaseReminderAskString(): ReminderAskString {
  return { reminderString: "", askString: [], completionString: "" };
}

export const ReminderAskString: MessageFns<ReminderAskString> = {
  encode(message: ReminderAskString, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.reminderString !== "") {
      writer.uint32(10).string(message.reminderString);
    }
    for (const v of message.askString) {
      writer.uint32(18).string(v!);
    }
    if (message.completionString !== "") {
      writer.uint32(26).string(message.completionString);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ReminderAskString {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseReminderAskString();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.reminderString = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.askString.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.completionString = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ReminderAskString {
    return {
      reminderString: isSet(object.reminderString) ? globalThis.String(object.reminderString) : "",
      askString: globalThis.Array.isArray(object?.askString)
        ? object.askString.map((e: any) => globalThis.String(e))
        : [],
      completionString: isSet(object.completionString) ? globalThis.String(object.completionString) : "",
    };
  },

  toJSON(message: ReminderAskString): unknown {
    const obj: any = {};
    if (message.reminderString !== "") {
      obj.reminderString = message.reminderString;
    }
    if (message.askString?.length) {
      obj.askString = message.askString;
    }
    if (message.completionString !== "") {
      obj.completionString = message.completionString;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ReminderAskString>, I>>(base?: I): ReminderAskString {
    return ReminderAskString.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReminderAskString>, I>>(object: I): ReminderAskString {
    const message = createBaseReminderAskString();
    message.reminderString = object.reminderString ?? "";
    message.askString = object.askString?.map((e) => e) || [];
    message.completionString = object.completionString ?? "";
    return message;
  },
};

function createBaseSubmitAnswerRequest(): SubmitAnswerRequest {
  return { answerId: "", questionId: "" };
}

export const SubmitAnswerRequest: MessageFns<SubmitAnswerRequest> = {
  encode(message: SubmitAnswerRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.answerId !== "") {
      writer.uint32(10).string(message.answerId);
    }
    if (message.questionId !== "") {
      writer.uint32(18).string(message.questionId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubmitAnswerRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitAnswerRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.answerId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.questionId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitAnswerRequest {
    return {
      answerId: isSet(object.answerId) ? globalThis.String(object.answerId) : "",
      questionId: isSet(object.questionId) ? globalThis.String(object.questionId) : "",
    };
  },

  toJSON(message: SubmitAnswerRequest): unknown {
    const obj: any = {};
    if (message.answerId !== "") {
      obj.answerId = message.answerId;
    }
    if (message.questionId !== "") {
      obj.questionId = message.questionId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitAnswerRequest>, I>>(base?: I): SubmitAnswerRequest {
    return SubmitAnswerRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitAnswerRequest>, I>>(object: I): SubmitAnswerRequest {
    const message = createBaseSubmitAnswerRequest();
    message.answerId = object.answerId ?? "";
    message.questionId = object.questionId ?? "";
    return message;
  },
};

function createBaseSubmitAnswerResponse(): SubmitAnswerResponse {
  return { questionData: undefined };
}

export const SubmitAnswerResponse: MessageFns<SubmitAnswerResponse> = {
  encode(message: SubmitAnswerResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.questionData !== undefined) {
      QuestionData.encode(message.questionData, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubmitAnswerResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitAnswerResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.questionData = QuestionData.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitAnswerResponse {
    return { questionData: isSet(object.questionData) ? QuestionData.fromJSON(object.questionData) : undefined };
  },

  toJSON(message: SubmitAnswerResponse): unknown {
    const obj: any = {};
    if (message.questionData !== undefined) {
      obj.questionData = QuestionData.toJSON(message.questionData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitAnswerResponse>, I>>(base?: I): SubmitAnswerResponse {
    return SubmitAnswerResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitAnswerResponse>, I>>(object: I): SubmitAnswerResponse {
    const message = createBaseSubmitAnswerResponse();
    message.questionData = (object.questionData !== undefined && object.questionData !== null)
      ? QuestionData.fromPartial(object.questionData)
      : undefined;
    return message;
  },
};

function createBaseSubmitQuestionRequest(): SubmitQuestionRequest {
  return { question: "" };
}

export const SubmitQuestionRequest: MessageFns<SubmitQuestionRequest> = {
  encode(message: SubmitQuestionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.question !== "") {
      writer.uint32(10).string(message.question);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubmitQuestionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitQuestionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.question = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitQuestionRequest {
    return { question: isSet(object.question) ? globalThis.String(object.question) : "" };
  },

  toJSON(message: SubmitQuestionRequest): unknown {
    const obj: any = {};
    if (message.question !== "") {
      obj.question = message.question;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitQuestionRequest>, I>>(base?: I): SubmitQuestionRequest {
    return SubmitQuestionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitQuestionRequest>, I>>(object: I): SubmitQuestionRequest {
    const message = createBaseSubmitQuestionRequest();
    message.question = object.question ?? "";
    return message;
  },
};

function createBaseSubmitQuestionResponse(): SubmitQuestionResponse {
  return {};
}

export const SubmitQuestionResponse: MessageFns<SubmitQuestionResponse> = {
  encode(_: SubmitQuestionResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubmitQuestionResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitQuestionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): SubmitQuestionResponse {
    return {};
  },

  toJSON(_: SubmitQuestionResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitQuestionResponse>, I>>(base?: I): SubmitQuestionResponse {
    return SubmitQuestionResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitQuestionResponse>, I>>(_: I): SubmitQuestionResponse {
    const message = createBaseSubmitQuestionResponse();
    return message;
  },
};

function createBaseSubmitReminderResponse(): SubmitReminderResponse {
  return { questionData: undefined };
}

export const SubmitReminderResponse: MessageFns<SubmitReminderResponse> = {
  encode(message: SubmitReminderResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.questionData !== undefined) {
      QuestionData.encode(message.questionData, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubmitReminderResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitReminderResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.questionData = QuestionData.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitReminderResponse {
    return { questionData: isSet(object.questionData) ? QuestionData.fromJSON(object.questionData) : undefined };
  },

  toJSON(message: SubmitReminderResponse): unknown {
    const obj: any = {};
    if (message.questionData !== undefined) {
      obj.questionData = QuestionData.toJSON(message.questionData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitReminderResponse>, I>>(base?: I): SubmitReminderResponse {
    return SubmitReminderResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitReminderResponse>, I>>(object: I): SubmitReminderResponse {
    const message = createBaseSubmitReminderResponse();
    message.questionData = (object.questionData !== undefined && object.questionData !== null)
      ? QuestionData.fromPartial(object.questionData)
      : undefined;
    return message;
  },
};

function createBaseSubmitReminderRequest(): SubmitReminderRequest {
  return { isReminder: false };
}

export const SubmitReminderRequest: MessageFns<SubmitReminderRequest> = {
  encode(message: SubmitReminderRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isReminder !== false) {
      writer.uint32(8).bool(message.isReminder);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubmitReminderRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitReminderRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isReminder = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitReminderRequest {
    return { isReminder: isSet(object.isReminder) ? globalThis.Boolean(object.isReminder) : false };
  },

  toJSON(message: SubmitReminderRequest): unknown {
    const obj: any = {};
    if (message.isReminder !== false) {
      obj.isReminder = message.isReminder;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitReminderRequest>, I>>(base?: I): SubmitReminderRequest {
    return SubmitReminderRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitReminderRequest>, I>>(object: I): SubmitReminderRequest {
    const message = createBaseSubmitReminderRequest();
    message.isReminder = object.isReminder ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
