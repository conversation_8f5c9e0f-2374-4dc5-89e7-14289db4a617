// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: PersonalizationAdmin.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { PaginationRequest, PaginationResponse } from "./Common";

export const protobufPackage = "com.stablemoney.api.personalization";

export enum ReferenceType {
  PAGE = 0,
  FRAME = 1,
  UNRECOGNIZED = -1,
}

export function referenceTypeFromJSON(object: any): ReferenceType {
  switch (object) {
    case 0:
    case "PAGE":
      return ReferenceType.PAGE;
    case 1:
    case "FRAME":
      return ReferenceType.FRAME;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ReferenceType.UNRECOGNIZED;
  }
}

export function referenceTypeToJSON(object: ReferenceType): string {
  switch (object) {
    case ReferenceType.PAGE:
      return "PAGE";
    case ReferenceType.FRAME:
      return "FRAME";
    case ReferenceType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum BannerType {
  BANK = 0,
  GENERAL = 1,
  BOND = 2,
  UNRECOGNIZED = -1,
}

export function bannerTypeFromJSON(object: any): BannerType {
  switch (object) {
    case 0:
    case "BANK":
      return BannerType.BANK;
    case 1:
    case "GENERAL":
      return BannerType.GENERAL;
    case 2:
    case "BOND":
      return BannerType.BOND;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BannerType.UNRECOGNIZED;
  }
}

export function bannerTypeToJSON(object: BannerType): string {
  switch (object) {
    case BannerType.BANK:
      return "BANK";
    case BannerType.GENERAL:
      return "GENERAL";
    case BannerType.BOND:
      return "BOND";
    case BannerType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface PageResponse {
  path: string;
  name: string;
  id: string;
  createdAt: number;
  updatedAt: number;
  content: string;
  config: string;
  isPublic: boolean;
  featureFlag?: string | undefined;
  variant?: string | undefined;
}

export interface FeatureFlagMappingResponse {
  featureFlag: string;
  widgetId: string;
  variant: string;
  createdAt: number;
  updatedAt: number;
}

export interface PageSummary {
  path: string;
  name: string;
  id: string;
  createdAt: number;
  updatedAt: number;
  deletedAt?: number | undefined;
  featureFlag?: string | undefined;
  variant?: string | undefined;
}

export interface FeatureFlagMappingSummary {
  id: string;
  featureFlag: string;
  widgetId: string;
  variant: string;
  referenceType: ReferenceType;
  createdAt: number;
  updatedAt: number;
  deletedAt?: number | undefined;
}

export interface PagesResponse {
  data: PageSummary[];
  pagination: PaginationResponse | undefined;
}

export interface PagesRequest {
  pagination: PaginationRequest | undefined;
  includeDeleted: boolean;
  q?: string | undefined;
}

export interface GetAuditLogsRequest {
  entity: string;
  identifier: string;
  pageNumber: number;
  pageSize: number;
}

export interface GetAuditLogsResponse {
  auditLogs: GetAuditLogsResponse_AuditLog[];
}

export interface GetAuditLogsResponse_AuditLog {
  field: string;
  operation: string;
  diff: string;
  updatedBy: string;
  createdAt: number;
}

export interface CreatePageRequest {
  path: string;
  name: string;
  content: string;
  config: string;
  isPublic: boolean;
  featureFlag?: string | undefined;
  variant?: string | undefined;
  username?: string | undefined;
}

export interface IdRequest {
  id: string;
}

export interface UpdatePageRequest {
  id: string;
  path: string;
  name: string;
  content: string;
  config: string;
  isPublic: boolean;
  featureFlag?: string | undefined;
  variant?: string | undefined;
  username?: string | undefined;
}

export interface DeletePageResponse {
  success: boolean;
}

export interface UpdateFeatureFlagMappingRequest {
  id: string;
  featureFlag: string;
  widgetId: string;
  variant: string;
  pageId: string;
}

export interface StatusResponse {
  success: boolean;
}

export interface CreateFeatureFlagMappingRequest {
  featureFlag: string;
  widgetId: string;
  typeId: string;
  variant: string;
  referenceType: ReferenceType;
}

export interface DeleteFeatureFlagMappingResponse {
  success: boolean;
}

export interface FeatureFlagMappingsRequest {
  typeId: string;
  referenceType: ReferenceType;
}

export interface FeatureFlagMappingsResponse {
  featureFlagMappings: FeatureFlagMappingSummary[];
}

export interface FrameSummary {
  name: string;
  id: string;
  createdAt: number;
  updatedAt: number;
  deletedAt?: number | undefined;
}

export interface CreateFrameRequest {
  name: string;
  content: string;
  config: string;
  username?: string | undefined;
}

export interface FrameResponse {
  name: string;
  id: string;
  createdAt: number;
  updatedAt: number;
  content: string;
  config: string;
}

export interface UpdateFrameRequest {
  id: string;
  name: string;
  content: string;
  config: string;
  username?: string | undefined;
}

export interface DeleteFrameResponse {
  success: boolean;
}

export interface FramesRequest {
  pagination: PaginationRequest | undefined;
  includeDeleted: boolean;
  q?: string | undefined;
}

export interface FramesResponse {
  data: FrameSummary[];
  pagination: PaginationResponse | undefined;
}

export interface BannerSummary {
  name: string;
  id: string;
  createdAt: number;
  updatedAt: number;
  isActive?: boolean | undefined;
}

export interface BannersRequest {
  pagination: PaginationRequest | undefined;
  includeDeleted: boolean;
  q?: string | undefined;
}

export interface BannersResponse {
  data: BannerSummary[];
  pagination: PaginationResponse | undefined;
}

export interface CreateBannerRequest {
  name: string;
  content: string;
  groupName: string;
  bannerType: BannerType;
  entityIdentifier: string;
}

export interface BannerResponse {
  name: string;
  id: string;
  createdAt: number;
  updatedAt: number;
  groupName: string;
  bannerType: BannerType;
  content: string;
  entityIdentifier: string;
}

export interface UpdateBannerRequest {
  id: string;
  name: string;
  groupName: string;
  bannerType: BannerType;
  content: string;
  entityIdentifier: string;
}

export interface DeleteBannerResponse {
  status: boolean;
}

export interface RuleSummary {
  page: string;
  name: string;
  id: string;
  featureFlag: string;
  expression: string;
  priority: number;
  outputA: string;
  outputB: string;
  createdAt: number;
  updatedAt: number;
  isActive: boolean;
}

export interface RulesRequest {
  pagination: PaginationRequest | undefined;
  includeDeleted: boolean;
  q?: string | undefined;
}

export interface RulesResponse {
  data: RuleSummary[];
  pagination: PaginationResponse | undefined;
}

export interface CreateRuleRequest {
  page: string;
  name: string;
  featureFlag: string;
  expression: string;
  priority: number;
  outputA: string;
  outputB: string;
}

export interface RuleResponse {
  page: string;
  name: string;
  id: string;
  featureFlag: string;
  expression: string;
  priority: number;
  outputA: string;
  outputB: string;
  createdAt: number;
  updatedAt: number;
}

export interface UpdateRuleRequest {
  page: string;
  name: string;
  id: string;
  featureFlag: string;
  expression: string;
  priority: number;
  outputA: string;
  outputB: string;
}

export interface DeleteRuleResponse {
  status: boolean;
}

export interface BannersHeadingRequest {
}

export interface BannersHeadingResponse {
  banners: string[];
}

export interface StorySummary {
  name: string;
  featureFlag: string;
  variant: string;
  page: string;
  isShareable: boolean;
  isLikeable: boolean;
  priority: number;
  createdAt: number;
  updatedAt: number;
  id: string;
  startTime: string;
  endTime: string;
  content: string;
}

export interface StoriesRequest {
  pagination: PaginationRequest | undefined;
  q?: string | undefined;
  includeDeleted: boolean;
}

export interface StoriesResponse {
  data: StorySummary[];
  pagination: PaginationResponse | undefined;
}

export interface CreateStoryRequest {
  name: string;
  featureFlag: string;
  variant: string;
  page: string;
  isShareable: boolean;
  isLikeable: boolean;
  priority: number;
  startTime: string;
  endTime: string;
  content: string;
}

export interface UpdateStoryRequest {
  name: string;
  featureFlag: string;
  variant: string;
  page: string;
  isShareable: boolean;
  isLikeable: boolean;
  priority: number;
  startTime: string;
  endTime: string;
  id: string;
  content: string;
}

export interface DeleteStoryRequest {
  id: string;
}

export interface DeleteStoryResponse {
  status: boolean;
}

export interface StoryResponse {
  name: string;
  featureFlag: string;
  variant: string;
  page: string;
  isShareable: boolean;
  isLikeable: boolean;
  priority: number;
  startTime: string;
  endTime: string;
  id: string;
  content: string;
}

function createBasePageResponse(): PageResponse {
  return {
    path: "",
    name: "",
    id: "",
    createdAt: 0,
    updatedAt: 0,
    content: "",
    config: "",
    isPublic: false,
    featureFlag: undefined,
    variant: undefined,
  };
}

export const PageResponse: MessageFns<PageResponse> = {
  encode(message: PageResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.path !== "") {
      writer.uint32(10).string(message.path);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(26).string(message.id);
    }
    if (message.createdAt !== 0) {
      writer.uint32(33).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(41).double(message.updatedAt);
    }
    if (message.content !== "") {
      writer.uint32(50).string(message.content);
    }
    if (message.config !== "") {
      writer.uint32(58).string(message.config);
    }
    if (message.isPublic !== false) {
      writer.uint32(64).bool(message.isPublic);
    }
    if (message.featureFlag !== undefined) {
      writer.uint32(74).string(message.featureFlag);
    }
    if (message.variant !== undefined) {
      writer.uint32(82).string(message.variant);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.path = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.config = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isPublic = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageResponse {
    return {
      path: isSet(object.path) ? globalThis.String(object.path) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      config: isSet(object.config) ? globalThis.String(object.config) : "",
      isPublic: isSet(object.isPublic) ? globalThis.Boolean(object.isPublic) : false,
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : undefined,
      variant: isSet(object.variant) ? globalThis.String(object.variant) : undefined,
    };
  },

  toJSON(message: PageResponse): unknown {
    const obj: any = {};
    if (message.path !== "") {
      obj.path = message.path;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.config !== "") {
      obj.config = message.config;
    }
    if (message.isPublic !== false) {
      obj.isPublic = message.isPublic;
    }
    if (message.featureFlag !== undefined) {
      obj.featureFlag = message.featureFlag;
    }
    if (message.variant !== undefined) {
      obj.variant = message.variant;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageResponse>, I>>(base?: I): PageResponse {
    return PageResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageResponse>, I>>(object: I): PageResponse {
    const message = createBasePageResponse();
    message.path = object.path ?? "";
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.content = object.content ?? "";
    message.config = object.config ?? "";
    message.isPublic = object.isPublic ?? false;
    message.featureFlag = object.featureFlag ?? undefined;
    message.variant = object.variant ?? undefined;
    return message;
  },
};

function createBaseFeatureFlagMappingResponse(): FeatureFlagMappingResponse {
  return { featureFlag: "", widgetId: "", variant: "", createdAt: 0, updatedAt: 0 };
}

export const FeatureFlagMappingResponse: MessageFns<FeatureFlagMappingResponse> = {
  encode(message: FeatureFlagMappingResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.featureFlag !== "") {
      writer.uint32(10).string(message.featureFlag);
    }
    if (message.widgetId !== "") {
      writer.uint32(18).string(message.widgetId);
    }
    if (message.variant !== "") {
      writer.uint32(26).string(message.variant);
    }
    if (message.createdAt !== 0) {
      writer.uint32(33).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(41).double(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FeatureFlagMappingResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFeatureFlagMappingResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.widgetId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FeatureFlagMappingResponse {
    return {
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      widgetId: isSet(object.widgetId) ? globalThis.String(object.widgetId) : "",
      variant: isSet(object.variant) ? globalThis.String(object.variant) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: FeatureFlagMappingResponse): unknown {
    const obj: any = {};
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.widgetId !== "") {
      obj.widgetId = message.widgetId;
    }
    if (message.variant !== "") {
      obj.variant = message.variant;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FeatureFlagMappingResponse>, I>>(base?: I): FeatureFlagMappingResponse {
    return FeatureFlagMappingResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FeatureFlagMappingResponse>, I>>(object: I): FeatureFlagMappingResponse {
    const message = createBaseFeatureFlagMappingResponse();
    message.featureFlag = object.featureFlag ?? "";
    message.widgetId = object.widgetId ?? "";
    message.variant = object.variant ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBasePageSummary(): PageSummary {
  return {
    path: "",
    name: "",
    id: "",
    createdAt: 0,
    updatedAt: 0,
    deletedAt: undefined,
    featureFlag: undefined,
    variant: undefined,
  };
}

export const PageSummary: MessageFns<PageSummary> = {
  encode(message: PageSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.path !== "") {
      writer.uint32(10).string(message.path);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(26).string(message.id);
    }
    if (message.createdAt !== 0) {
      writer.uint32(33).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(41).double(message.updatedAt);
    }
    if (message.deletedAt !== undefined) {
      writer.uint32(49).double(message.deletedAt);
    }
    if (message.featureFlag !== undefined) {
      writer.uint32(58).string(message.featureFlag);
    }
    if (message.variant !== undefined) {
      writer.uint32(66).string(message.variant);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PageSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePageSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.path = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.deletedAt = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PageSummary {
    return {
      path: isSet(object.path) ? globalThis.String(object.path) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      deletedAt: isSet(object.deletedAt) ? globalThis.Number(object.deletedAt) : undefined,
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : undefined,
      variant: isSet(object.variant) ? globalThis.String(object.variant) : undefined,
    };
  },

  toJSON(message: PageSummary): unknown {
    const obj: any = {};
    if (message.path !== "") {
      obj.path = message.path;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.deletedAt !== undefined) {
      obj.deletedAt = message.deletedAt;
    }
    if (message.featureFlag !== undefined) {
      obj.featureFlag = message.featureFlag;
    }
    if (message.variant !== undefined) {
      obj.variant = message.variant;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PageSummary>, I>>(base?: I): PageSummary {
    return PageSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageSummary>, I>>(object: I): PageSummary {
    const message = createBasePageSummary();
    message.path = object.path ?? "";
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.deletedAt = object.deletedAt ?? undefined;
    message.featureFlag = object.featureFlag ?? undefined;
    message.variant = object.variant ?? undefined;
    return message;
  },
};

function createBaseFeatureFlagMappingSummary(): FeatureFlagMappingSummary {
  return {
    id: "",
    featureFlag: "",
    widgetId: "",
    variant: "",
    referenceType: 0,
    createdAt: 0,
    updatedAt: 0,
    deletedAt: undefined,
  };
}

export const FeatureFlagMappingSummary: MessageFns<FeatureFlagMappingSummary> = {
  encode(message: FeatureFlagMappingSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.featureFlag !== "") {
      writer.uint32(18).string(message.featureFlag);
    }
    if (message.widgetId !== "") {
      writer.uint32(26).string(message.widgetId);
    }
    if (message.variant !== "") {
      writer.uint32(34).string(message.variant);
    }
    if (message.referenceType !== 0) {
      writer.uint32(40).int32(message.referenceType);
    }
    if (message.createdAt !== 0) {
      writer.uint32(49).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(57).double(message.updatedAt);
    }
    if (message.deletedAt !== undefined) {
      writer.uint32(65).double(message.deletedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FeatureFlagMappingSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFeatureFlagMappingSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.widgetId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.referenceType = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.deletedAt = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FeatureFlagMappingSummary {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      widgetId: isSet(object.widgetId) ? globalThis.String(object.widgetId) : "",
      variant: isSet(object.variant) ? globalThis.String(object.variant) : "",
      referenceType: isSet(object.referenceType) ? referenceTypeFromJSON(object.referenceType) : 0,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      deletedAt: isSet(object.deletedAt) ? globalThis.Number(object.deletedAt) : undefined,
    };
  },

  toJSON(message: FeatureFlagMappingSummary): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.widgetId !== "") {
      obj.widgetId = message.widgetId;
    }
    if (message.variant !== "") {
      obj.variant = message.variant;
    }
    if (message.referenceType !== 0) {
      obj.referenceType = referenceTypeToJSON(message.referenceType);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.deletedAt !== undefined) {
      obj.deletedAt = message.deletedAt;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FeatureFlagMappingSummary>, I>>(base?: I): FeatureFlagMappingSummary {
    return FeatureFlagMappingSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FeatureFlagMappingSummary>, I>>(object: I): FeatureFlagMappingSummary {
    const message = createBaseFeatureFlagMappingSummary();
    message.id = object.id ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.widgetId = object.widgetId ?? "";
    message.variant = object.variant ?? "";
    message.referenceType = object.referenceType ?? 0;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.deletedAt = object.deletedAt ?? undefined;
    return message;
  },
};

function createBasePagesResponse(): PagesResponse {
  return { data: [], pagination: undefined };
}

export const PagesResponse: MessageFns<PagesResponse> = {
  encode(message: PagesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      PageSummary.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.pagination !== undefined) {
      PaginationResponse.encode(message.pagination, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PagesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePagesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(PageSummary.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pagination = PaginationResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PagesResponse {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => PageSummary.fromJSON(e)) : [],
      pagination: isSet(object.pagination) ? PaginationResponse.fromJSON(object.pagination) : undefined,
    };
  },

  toJSON(message: PagesResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => PageSummary.toJSON(e));
    }
    if (message.pagination !== undefined) {
      obj.pagination = PaginationResponse.toJSON(message.pagination);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PagesResponse>, I>>(base?: I): PagesResponse {
    return PagesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PagesResponse>, I>>(object: I): PagesResponse {
    const message = createBasePagesResponse();
    message.data = object.data?.map((e) => PageSummary.fromPartial(e)) || [];
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationResponse.fromPartial(object.pagination)
      : undefined;
    return message;
  },
};

function createBasePagesRequest(): PagesRequest {
  return { pagination: undefined, includeDeleted: false, q: undefined };
}

export const PagesRequest: MessageFns<PagesRequest> = {
  encode(message: PagesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pagination !== undefined) {
      PaginationRequest.encode(message.pagination, writer.uint32(10).fork()).join();
    }
    if (message.includeDeleted !== false) {
      writer.uint32(16).bool(message.includeDeleted);
    }
    if (message.q !== undefined) {
      writer.uint32(26).string(message.q);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PagesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePagesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pagination = PaginationRequest.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.includeDeleted = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.q = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PagesRequest {
    return {
      pagination: isSet(object.pagination) ? PaginationRequest.fromJSON(object.pagination) : undefined,
      includeDeleted: isSet(object.includeDeleted) ? globalThis.Boolean(object.includeDeleted) : false,
      q: isSet(object.q) ? globalThis.String(object.q) : undefined,
    };
  },

  toJSON(message: PagesRequest): unknown {
    const obj: any = {};
    if (message.pagination !== undefined) {
      obj.pagination = PaginationRequest.toJSON(message.pagination);
    }
    if (message.includeDeleted !== false) {
      obj.includeDeleted = message.includeDeleted;
    }
    if (message.q !== undefined) {
      obj.q = message.q;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PagesRequest>, I>>(base?: I): PagesRequest {
    return PagesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PagesRequest>, I>>(object: I): PagesRequest {
    const message = createBasePagesRequest();
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationRequest.fromPartial(object.pagination)
      : undefined;
    message.includeDeleted = object.includeDeleted ?? false;
    message.q = object.q ?? undefined;
    return message;
  },
};

function createBaseGetAuditLogsRequest(): GetAuditLogsRequest {
  return { entity: "", identifier: "", pageNumber: 0, pageSize: 0 };
}

export const GetAuditLogsRequest: MessageFns<GetAuditLogsRequest> = {
  encode(message: GetAuditLogsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.entity !== "") {
      writer.uint32(10).string(message.entity);
    }
    if (message.identifier !== "") {
      writer.uint32(18).string(message.identifier);
    }
    if (message.pageNumber !== 0) {
      writer.uint32(24).int32(message.pageNumber);
    }
    if (message.pageSize !== 0) {
      writer.uint32(32).int32(message.pageSize);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAuditLogsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAuditLogsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.entity = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.identifier = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.pageNumber = reader.int32();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAuditLogsRequest {
    return {
      entity: isSet(object.entity) ? globalThis.String(object.entity) : "",
      identifier: isSet(object.identifier) ? globalThis.String(object.identifier) : "",
      pageNumber: isSet(object.pageNumber) ? globalThis.Number(object.pageNumber) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
    };
  },

  toJSON(message: GetAuditLogsRequest): unknown {
    const obj: any = {};
    if (message.entity !== "") {
      obj.entity = message.entity;
    }
    if (message.identifier !== "") {
      obj.identifier = message.identifier;
    }
    if (message.pageNumber !== 0) {
      obj.pageNumber = Math.round(message.pageNumber);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAuditLogsRequest>, I>>(base?: I): GetAuditLogsRequest {
    return GetAuditLogsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAuditLogsRequest>, I>>(object: I): GetAuditLogsRequest {
    const message = createBaseGetAuditLogsRequest();
    message.entity = object.entity ?? "";
    message.identifier = object.identifier ?? "";
    message.pageNumber = object.pageNumber ?? 0;
    message.pageSize = object.pageSize ?? 0;
    return message;
  },
};

function createBaseGetAuditLogsResponse(): GetAuditLogsResponse {
  return { auditLogs: [] };
}

export const GetAuditLogsResponse: MessageFns<GetAuditLogsResponse> = {
  encode(message: GetAuditLogsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.auditLogs) {
      GetAuditLogsResponse_AuditLog.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAuditLogsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAuditLogsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.auditLogs.push(GetAuditLogsResponse_AuditLog.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAuditLogsResponse {
    return {
      auditLogs: globalThis.Array.isArray(object?.auditLogs)
        ? object.auditLogs.map((e: any) => GetAuditLogsResponse_AuditLog.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetAuditLogsResponse): unknown {
    const obj: any = {};
    if (message.auditLogs?.length) {
      obj.auditLogs = message.auditLogs.map((e) => GetAuditLogsResponse_AuditLog.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAuditLogsResponse>, I>>(base?: I): GetAuditLogsResponse {
    return GetAuditLogsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAuditLogsResponse>, I>>(object: I): GetAuditLogsResponse {
    const message = createBaseGetAuditLogsResponse();
    message.auditLogs = object.auditLogs?.map((e) => GetAuditLogsResponse_AuditLog.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetAuditLogsResponse_AuditLog(): GetAuditLogsResponse_AuditLog {
  return { field: "", operation: "", diff: "", updatedBy: "", createdAt: 0 };
}

export const GetAuditLogsResponse_AuditLog: MessageFns<GetAuditLogsResponse_AuditLog> = {
  encode(message: GetAuditLogsResponse_AuditLog, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.field !== "") {
      writer.uint32(10).string(message.field);
    }
    if (message.operation !== "") {
      writer.uint32(18).string(message.operation);
    }
    if (message.diff !== "") {
      writer.uint32(26).string(message.diff);
    }
    if (message.updatedBy !== "") {
      writer.uint32(34).string(message.updatedBy);
    }
    if (message.createdAt !== 0) {
      writer.uint32(40).int64(message.createdAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetAuditLogsResponse_AuditLog {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAuditLogsResponse_AuditLog();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.field = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.operation = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.diff = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.updatedBy = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAuditLogsResponse_AuditLog {
    return {
      field: isSet(object.field) ? globalThis.String(object.field) : "",
      operation: isSet(object.operation) ? globalThis.String(object.operation) : "",
      diff: isSet(object.diff) ? globalThis.String(object.diff) : "",
      updatedBy: isSet(object.updatedBy) ? globalThis.String(object.updatedBy) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
    };
  },

  toJSON(message: GetAuditLogsResponse_AuditLog): unknown {
    const obj: any = {};
    if (message.field !== "") {
      obj.field = message.field;
    }
    if (message.operation !== "") {
      obj.operation = message.operation;
    }
    if (message.diff !== "") {
      obj.diff = message.diff;
    }
    if (message.updatedBy !== "") {
      obj.updatedBy = message.updatedBy;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetAuditLogsResponse_AuditLog>, I>>(base?: I): GetAuditLogsResponse_AuditLog {
    return GetAuditLogsResponse_AuditLog.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAuditLogsResponse_AuditLog>, I>>(
    object: I,
  ): GetAuditLogsResponse_AuditLog {
    const message = createBaseGetAuditLogsResponse_AuditLog();
    message.field = object.field ?? "";
    message.operation = object.operation ?? "";
    message.diff = object.diff ?? "";
    message.updatedBy = object.updatedBy ?? "";
    message.createdAt = object.createdAt ?? 0;
    return message;
  },
};

function createBaseCreatePageRequest(): CreatePageRequest {
  return {
    path: "",
    name: "",
    content: "",
    config: "",
    isPublic: false,
    featureFlag: undefined,
    variant: undefined,
    username: undefined,
  };
}

export const CreatePageRequest: MessageFns<CreatePageRequest> = {
  encode(message: CreatePageRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.path !== "") {
      writer.uint32(10).string(message.path);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.content !== "") {
      writer.uint32(26).string(message.content);
    }
    if (message.config !== "") {
      writer.uint32(34).string(message.config);
    }
    if (message.isPublic !== false) {
      writer.uint32(40).bool(message.isPublic);
    }
    if (message.featureFlag !== undefined) {
      writer.uint32(50).string(message.featureFlag);
    }
    if (message.variant !== undefined) {
      writer.uint32(58).string(message.variant);
    }
    if (message.username !== undefined) {
      writer.uint32(66).string(message.username);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreatePageRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreatePageRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.path = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.config = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isPublic = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.username = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreatePageRequest {
    return {
      path: isSet(object.path) ? globalThis.String(object.path) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      config: isSet(object.config) ? globalThis.String(object.config) : "",
      isPublic: isSet(object.isPublic) ? globalThis.Boolean(object.isPublic) : false,
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : undefined,
      variant: isSet(object.variant) ? globalThis.String(object.variant) : undefined,
      username: isSet(object.username) ? globalThis.String(object.username) : undefined,
    };
  },

  toJSON(message: CreatePageRequest): unknown {
    const obj: any = {};
    if (message.path !== "") {
      obj.path = message.path;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.config !== "") {
      obj.config = message.config;
    }
    if (message.isPublic !== false) {
      obj.isPublic = message.isPublic;
    }
    if (message.featureFlag !== undefined) {
      obj.featureFlag = message.featureFlag;
    }
    if (message.variant !== undefined) {
      obj.variant = message.variant;
    }
    if (message.username !== undefined) {
      obj.username = message.username;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreatePageRequest>, I>>(base?: I): CreatePageRequest {
    return CreatePageRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePageRequest>, I>>(object: I): CreatePageRequest {
    const message = createBaseCreatePageRequest();
    message.path = object.path ?? "";
    message.name = object.name ?? "";
    message.content = object.content ?? "";
    message.config = object.config ?? "";
    message.isPublic = object.isPublic ?? false;
    message.featureFlag = object.featureFlag ?? undefined;
    message.variant = object.variant ?? undefined;
    message.username = object.username ?? undefined;
    return message;
  },
};

function createBaseIdRequest(): IdRequest {
  return { id: "" };
}

export const IdRequest: MessageFns<IdRequest> = {
  encode(message: IdRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): IdRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIdRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): IdRequest {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: IdRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<IdRequest>, I>>(base?: I): IdRequest {
    return IdRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IdRequest>, I>>(object: I): IdRequest {
    const message = createBaseIdRequest();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseUpdatePageRequest(): UpdatePageRequest {
  return {
    id: "",
    path: "",
    name: "",
    content: "",
    config: "",
    isPublic: false,
    featureFlag: undefined,
    variant: undefined,
    username: undefined,
  };
}

export const UpdatePageRequest: MessageFns<UpdatePageRequest> = {
  encode(message: UpdatePageRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.path !== "") {
      writer.uint32(18).string(message.path);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    if (message.content !== "") {
      writer.uint32(34).string(message.content);
    }
    if (message.config !== "") {
      writer.uint32(42).string(message.config);
    }
    if (message.isPublic !== false) {
      writer.uint32(48).bool(message.isPublic);
    }
    if (message.featureFlag !== undefined) {
      writer.uint32(58).string(message.featureFlag);
    }
    if (message.variant !== undefined) {
      writer.uint32(66).string(message.variant);
    }
    if (message.username !== undefined) {
      writer.uint32(74).string(message.username);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdatePageRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdatePageRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.path = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.config = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isPublic = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.username = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdatePageRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      path: isSet(object.path) ? globalThis.String(object.path) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      config: isSet(object.config) ? globalThis.String(object.config) : "",
      isPublic: isSet(object.isPublic) ? globalThis.Boolean(object.isPublic) : false,
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : undefined,
      variant: isSet(object.variant) ? globalThis.String(object.variant) : undefined,
      username: isSet(object.username) ? globalThis.String(object.username) : undefined,
    };
  },

  toJSON(message: UpdatePageRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.path !== "") {
      obj.path = message.path;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.config !== "") {
      obj.config = message.config;
    }
    if (message.isPublic !== false) {
      obj.isPublic = message.isPublic;
    }
    if (message.featureFlag !== undefined) {
      obj.featureFlag = message.featureFlag;
    }
    if (message.variant !== undefined) {
      obj.variant = message.variant;
    }
    if (message.username !== undefined) {
      obj.username = message.username;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdatePageRequest>, I>>(base?: I): UpdatePageRequest {
    return UpdatePageRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePageRequest>, I>>(object: I): UpdatePageRequest {
    const message = createBaseUpdatePageRequest();
    message.id = object.id ?? "";
    message.path = object.path ?? "";
    message.name = object.name ?? "";
    message.content = object.content ?? "";
    message.config = object.config ?? "";
    message.isPublic = object.isPublic ?? false;
    message.featureFlag = object.featureFlag ?? undefined;
    message.variant = object.variant ?? undefined;
    message.username = object.username ?? undefined;
    return message;
  },
};

function createBaseDeletePageResponse(): DeletePageResponse {
  return { success: false };
}

export const DeletePageResponse: MessageFns<DeletePageResponse> = {
  encode(message: DeletePageResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeletePageResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeletePageResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeletePageResponse {
    return { success: isSet(object.success) ? globalThis.Boolean(object.success) : false };
  },

  toJSON(message: DeletePageResponse): unknown {
    const obj: any = {};
    if (message.success !== false) {
      obj.success = message.success;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeletePageResponse>, I>>(base?: I): DeletePageResponse {
    return DeletePageResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePageResponse>, I>>(object: I): DeletePageResponse {
    const message = createBaseDeletePageResponse();
    message.success = object.success ?? false;
    return message;
  },
};

function createBaseUpdateFeatureFlagMappingRequest(): UpdateFeatureFlagMappingRequest {
  return { id: "", featureFlag: "", widgetId: "", variant: "", pageId: "" };
}

export const UpdateFeatureFlagMappingRequest: MessageFns<UpdateFeatureFlagMappingRequest> = {
  encode(message: UpdateFeatureFlagMappingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.featureFlag !== "") {
      writer.uint32(18).string(message.featureFlag);
    }
    if (message.widgetId !== "") {
      writer.uint32(26).string(message.widgetId);
    }
    if (message.variant !== "") {
      writer.uint32(34).string(message.variant);
    }
    if (message.pageId !== "") {
      writer.uint32(42).string(message.pageId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateFeatureFlagMappingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateFeatureFlagMappingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.widgetId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.pageId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateFeatureFlagMappingRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      widgetId: isSet(object.widgetId) ? globalThis.String(object.widgetId) : "",
      variant: isSet(object.variant) ? globalThis.String(object.variant) : "",
      pageId: isSet(object.pageId) ? globalThis.String(object.pageId) : "",
    };
  },

  toJSON(message: UpdateFeatureFlagMappingRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.widgetId !== "") {
      obj.widgetId = message.widgetId;
    }
    if (message.variant !== "") {
      obj.variant = message.variant;
    }
    if (message.pageId !== "") {
      obj.pageId = message.pageId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateFeatureFlagMappingRequest>, I>>(base?: I): UpdateFeatureFlagMappingRequest {
    return UpdateFeatureFlagMappingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateFeatureFlagMappingRequest>, I>>(
    object: I,
  ): UpdateFeatureFlagMappingRequest {
    const message = createBaseUpdateFeatureFlagMappingRequest();
    message.id = object.id ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.widgetId = object.widgetId ?? "";
    message.variant = object.variant ?? "";
    message.pageId = object.pageId ?? "";
    return message;
  },
};

function createBaseStatusResponse(): StatusResponse {
  return { success: false };
}

export const StatusResponse: MessageFns<StatusResponse> = {
  encode(message: StatusResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StatusResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStatusResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StatusResponse {
    return { success: isSet(object.success) ? globalThis.Boolean(object.success) : false };
  },

  toJSON(message: StatusResponse): unknown {
    const obj: any = {};
    if (message.success !== false) {
      obj.success = message.success;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StatusResponse>, I>>(base?: I): StatusResponse {
    return StatusResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StatusResponse>, I>>(object: I): StatusResponse {
    const message = createBaseStatusResponse();
    message.success = object.success ?? false;
    return message;
  },
};

function createBaseCreateFeatureFlagMappingRequest(): CreateFeatureFlagMappingRequest {
  return { featureFlag: "", widgetId: "", typeId: "", variant: "", referenceType: 0 };
}

export const CreateFeatureFlagMappingRequest: MessageFns<CreateFeatureFlagMappingRequest> = {
  encode(message: CreateFeatureFlagMappingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.featureFlag !== "") {
      writer.uint32(10).string(message.featureFlag);
    }
    if (message.widgetId !== "") {
      writer.uint32(18).string(message.widgetId);
    }
    if (message.typeId !== "") {
      writer.uint32(26).string(message.typeId);
    }
    if (message.variant !== "") {
      writer.uint32(34).string(message.variant);
    }
    if (message.referenceType !== 0) {
      writer.uint32(40).int32(message.referenceType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateFeatureFlagMappingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateFeatureFlagMappingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.widgetId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.typeId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.referenceType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateFeatureFlagMappingRequest {
    return {
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      widgetId: isSet(object.widgetId) ? globalThis.String(object.widgetId) : "",
      typeId: isSet(object.typeId) ? globalThis.String(object.typeId) : "",
      variant: isSet(object.variant) ? globalThis.String(object.variant) : "",
      referenceType: isSet(object.referenceType) ? referenceTypeFromJSON(object.referenceType) : 0,
    };
  },

  toJSON(message: CreateFeatureFlagMappingRequest): unknown {
    const obj: any = {};
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.widgetId !== "") {
      obj.widgetId = message.widgetId;
    }
    if (message.typeId !== "") {
      obj.typeId = message.typeId;
    }
    if (message.variant !== "") {
      obj.variant = message.variant;
    }
    if (message.referenceType !== 0) {
      obj.referenceType = referenceTypeToJSON(message.referenceType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateFeatureFlagMappingRequest>, I>>(base?: I): CreateFeatureFlagMappingRequest {
    return CreateFeatureFlagMappingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateFeatureFlagMappingRequest>, I>>(
    object: I,
  ): CreateFeatureFlagMappingRequest {
    const message = createBaseCreateFeatureFlagMappingRequest();
    message.featureFlag = object.featureFlag ?? "";
    message.widgetId = object.widgetId ?? "";
    message.typeId = object.typeId ?? "";
    message.variant = object.variant ?? "";
    message.referenceType = object.referenceType ?? 0;
    return message;
  },
};

function createBaseDeleteFeatureFlagMappingResponse(): DeleteFeatureFlagMappingResponse {
  return { success: false };
}

export const DeleteFeatureFlagMappingResponse: MessageFns<DeleteFeatureFlagMappingResponse> = {
  encode(message: DeleteFeatureFlagMappingResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteFeatureFlagMappingResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteFeatureFlagMappingResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteFeatureFlagMappingResponse {
    return { success: isSet(object.success) ? globalThis.Boolean(object.success) : false };
  },

  toJSON(message: DeleteFeatureFlagMappingResponse): unknown {
    const obj: any = {};
    if (message.success !== false) {
      obj.success = message.success;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteFeatureFlagMappingResponse>, I>>(
    base?: I,
  ): DeleteFeatureFlagMappingResponse {
    return DeleteFeatureFlagMappingResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteFeatureFlagMappingResponse>, I>>(
    object: I,
  ): DeleteFeatureFlagMappingResponse {
    const message = createBaseDeleteFeatureFlagMappingResponse();
    message.success = object.success ?? false;
    return message;
  },
};

function createBaseFeatureFlagMappingsRequest(): FeatureFlagMappingsRequest {
  return { typeId: "", referenceType: 0 };
}

export const FeatureFlagMappingsRequest: MessageFns<FeatureFlagMappingsRequest> = {
  encode(message: FeatureFlagMappingsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.typeId !== "") {
      writer.uint32(10).string(message.typeId);
    }
    if (message.referenceType !== 0) {
      writer.uint32(16).int32(message.referenceType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FeatureFlagMappingsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFeatureFlagMappingsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.typeId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.referenceType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FeatureFlagMappingsRequest {
    return {
      typeId: isSet(object.typeId) ? globalThis.String(object.typeId) : "",
      referenceType: isSet(object.referenceType) ? referenceTypeFromJSON(object.referenceType) : 0,
    };
  },

  toJSON(message: FeatureFlagMappingsRequest): unknown {
    const obj: any = {};
    if (message.typeId !== "") {
      obj.typeId = message.typeId;
    }
    if (message.referenceType !== 0) {
      obj.referenceType = referenceTypeToJSON(message.referenceType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FeatureFlagMappingsRequest>, I>>(base?: I): FeatureFlagMappingsRequest {
    return FeatureFlagMappingsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FeatureFlagMappingsRequest>, I>>(object: I): FeatureFlagMappingsRequest {
    const message = createBaseFeatureFlagMappingsRequest();
    message.typeId = object.typeId ?? "";
    message.referenceType = object.referenceType ?? 0;
    return message;
  },
};

function createBaseFeatureFlagMappingsResponse(): FeatureFlagMappingsResponse {
  return { featureFlagMappings: [] };
}

export const FeatureFlagMappingsResponse: MessageFns<FeatureFlagMappingsResponse> = {
  encode(message: FeatureFlagMappingsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.featureFlagMappings) {
      FeatureFlagMappingSummary.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FeatureFlagMappingsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFeatureFlagMappingsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.featureFlagMappings.push(FeatureFlagMappingSummary.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FeatureFlagMappingsResponse {
    return {
      featureFlagMappings: globalThis.Array.isArray(object?.featureFlagMappings)
        ? object.featureFlagMappings.map((e: any) => FeatureFlagMappingSummary.fromJSON(e))
        : [],
    };
  },

  toJSON(message: FeatureFlagMappingsResponse): unknown {
    const obj: any = {};
    if (message.featureFlagMappings?.length) {
      obj.featureFlagMappings = message.featureFlagMappings.map((e) => FeatureFlagMappingSummary.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FeatureFlagMappingsResponse>, I>>(base?: I): FeatureFlagMappingsResponse {
    return FeatureFlagMappingsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FeatureFlagMappingsResponse>, I>>(object: I): FeatureFlagMappingsResponse {
    const message = createBaseFeatureFlagMappingsResponse();
    message.featureFlagMappings = object.featureFlagMappings?.map((e) => FeatureFlagMappingSummary.fromPartial(e)) ||
      [];
    return message;
  },
};

function createBaseFrameSummary(): FrameSummary {
  return { name: "", id: "", createdAt: 0, updatedAt: 0, deletedAt: undefined };
}

export const FrameSummary: MessageFns<FrameSummary> = {
  encode(message: FrameSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    if (message.createdAt !== 0) {
      writer.uint32(25).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(33).double(message.updatedAt);
    }
    if (message.deletedAt !== undefined) {
      writer.uint32(41).double(message.deletedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FrameSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFrameSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.deletedAt = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FrameSummary {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      deletedAt: isSet(object.deletedAt) ? globalThis.Number(object.deletedAt) : undefined,
    };
  },

  toJSON(message: FrameSummary): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.deletedAt !== undefined) {
      obj.deletedAt = message.deletedAt;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FrameSummary>, I>>(base?: I): FrameSummary {
    return FrameSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FrameSummary>, I>>(object: I): FrameSummary {
    const message = createBaseFrameSummary();
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.deletedAt = object.deletedAt ?? undefined;
    return message;
  },
};

function createBaseCreateFrameRequest(): CreateFrameRequest {
  return { name: "", content: "", config: "", username: undefined };
}

export const CreateFrameRequest: MessageFns<CreateFrameRequest> = {
  encode(message: CreateFrameRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.content !== "") {
      writer.uint32(18).string(message.content);
    }
    if (message.config !== "") {
      writer.uint32(26).string(message.config);
    }
    if (message.username !== undefined) {
      writer.uint32(34).string(message.username);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateFrameRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateFrameRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.config = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.username = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateFrameRequest {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      config: isSet(object.config) ? globalThis.String(object.config) : "",
      username: isSet(object.username) ? globalThis.String(object.username) : undefined,
    };
  },

  toJSON(message: CreateFrameRequest): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.config !== "") {
      obj.config = message.config;
    }
    if (message.username !== undefined) {
      obj.username = message.username;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateFrameRequest>, I>>(base?: I): CreateFrameRequest {
    return CreateFrameRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateFrameRequest>, I>>(object: I): CreateFrameRequest {
    const message = createBaseCreateFrameRequest();
    message.name = object.name ?? "";
    message.content = object.content ?? "";
    message.config = object.config ?? "";
    message.username = object.username ?? undefined;
    return message;
  },
};

function createBaseFrameResponse(): FrameResponse {
  return { name: "", id: "", createdAt: 0, updatedAt: 0, content: "", config: "" };
}

export const FrameResponse: MessageFns<FrameResponse> = {
  encode(message: FrameResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    if (message.createdAt !== 0) {
      writer.uint32(25).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(33).double(message.updatedAt);
    }
    if (message.content !== "") {
      writer.uint32(42).string(message.content);
    }
    if (message.config !== "") {
      writer.uint32(50).string(message.config);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FrameResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFrameResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.config = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FrameResponse {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      config: isSet(object.config) ? globalThis.String(object.config) : "",
    };
  },

  toJSON(message: FrameResponse): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.config !== "") {
      obj.config = message.config;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FrameResponse>, I>>(base?: I): FrameResponse {
    return FrameResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FrameResponse>, I>>(object: I): FrameResponse {
    const message = createBaseFrameResponse();
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.content = object.content ?? "";
    message.config = object.config ?? "";
    return message;
  },
};

function createBaseUpdateFrameRequest(): UpdateFrameRequest {
  return { id: "", name: "", content: "", config: "", username: undefined };
}

export const UpdateFrameRequest: MessageFns<UpdateFrameRequest> = {
  encode(message: UpdateFrameRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.content !== "") {
      writer.uint32(26).string(message.content);
    }
    if (message.config !== "") {
      writer.uint32(34).string(message.config);
    }
    if (message.username !== undefined) {
      writer.uint32(42).string(message.username);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateFrameRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateFrameRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.config = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.username = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateFrameRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      config: isSet(object.config) ? globalThis.String(object.config) : "",
      username: isSet(object.username) ? globalThis.String(object.username) : undefined,
    };
  },

  toJSON(message: UpdateFrameRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.config !== "") {
      obj.config = message.config;
    }
    if (message.username !== undefined) {
      obj.username = message.username;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateFrameRequest>, I>>(base?: I): UpdateFrameRequest {
    return UpdateFrameRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateFrameRequest>, I>>(object: I): UpdateFrameRequest {
    const message = createBaseUpdateFrameRequest();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.content = object.content ?? "";
    message.config = object.config ?? "";
    message.username = object.username ?? undefined;
    return message;
  },
};

function createBaseDeleteFrameResponse(): DeleteFrameResponse {
  return { success: false };
}

export const DeleteFrameResponse: MessageFns<DeleteFrameResponse> = {
  encode(message: DeleteFrameResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.success !== false) {
      writer.uint32(8).bool(message.success);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteFrameResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteFrameResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.success = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteFrameResponse {
    return { success: isSet(object.success) ? globalThis.Boolean(object.success) : false };
  },

  toJSON(message: DeleteFrameResponse): unknown {
    const obj: any = {};
    if (message.success !== false) {
      obj.success = message.success;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteFrameResponse>, I>>(base?: I): DeleteFrameResponse {
    return DeleteFrameResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteFrameResponse>, I>>(object: I): DeleteFrameResponse {
    const message = createBaseDeleteFrameResponse();
    message.success = object.success ?? false;
    return message;
  },
};

function createBaseFramesRequest(): FramesRequest {
  return { pagination: undefined, includeDeleted: false, q: undefined };
}

export const FramesRequest: MessageFns<FramesRequest> = {
  encode(message: FramesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pagination !== undefined) {
      PaginationRequest.encode(message.pagination, writer.uint32(10).fork()).join();
    }
    if (message.includeDeleted !== false) {
      writer.uint32(16).bool(message.includeDeleted);
    }
    if (message.q !== undefined) {
      writer.uint32(26).string(message.q);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FramesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFramesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pagination = PaginationRequest.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.includeDeleted = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.q = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FramesRequest {
    return {
      pagination: isSet(object.pagination) ? PaginationRequest.fromJSON(object.pagination) : undefined,
      includeDeleted: isSet(object.includeDeleted) ? globalThis.Boolean(object.includeDeleted) : false,
      q: isSet(object.q) ? globalThis.String(object.q) : undefined,
    };
  },

  toJSON(message: FramesRequest): unknown {
    const obj: any = {};
    if (message.pagination !== undefined) {
      obj.pagination = PaginationRequest.toJSON(message.pagination);
    }
    if (message.includeDeleted !== false) {
      obj.includeDeleted = message.includeDeleted;
    }
    if (message.q !== undefined) {
      obj.q = message.q;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FramesRequest>, I>>(base?: I): FramesRequest {
    return FramesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FramesRequest>, I>>(object: I): FramesRequest {
    const message = createBaseFramesRequest();
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationRequest.fromPartial(object.pagination)
      : undefined;
    message.includeDeleted = object.includeDeleted ?? false;
    message.q = object.q ?? undefined;
    return message;
  },
};

function createBaseFramesResponse(): FramesResponse {
  return { data: [], pagination: undefined };
}

export const FramesResponse: MessageFns<FramesResponse> = {
  encode(message: FramesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      FrameSummary.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.pagination !== undefined) {
      PaginationResponse.encode(message.pagination, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FramesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFramesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(FrameSummary.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pagination = PaginationResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FramesResponse {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => FrameSummary.fromJSON(e)) : [],
      pagination: isSet(object.pagination) ? PaginationResponse.fromJSON(object.pagination) : undefined,
    };
  },

  toJSON(message: FramesResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => FrameSummary.toJSON(e));
    }
    if (message.pagination !== undefined) {
      obj.pagination = PaginationResponse.toJSON(message.pagination);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FramesResponse>, I>>(base?: I): FramesResponse {
    return FramesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FramesResponse>, I>>(object: I): FramesResponse {
    const message = createBaseFramesResponse();
    message.data = object.data?.map((e) => FrameSummary.fromPartial(e)) || [];
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationResponse.fromPartial(object.pagination)
      : undefined;
    return message;
  },
};

function createBaseBannerSummary(): BannerSummary {
  return { name: "", id: "", createdAt: 0, updatedAt: 0, isActive: undefined };
}

export const BannerSummary: MessageFns<BannerSummary> = {
  encode(message: BannerSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    if (message.createdAt !== 0) {
      writer.uint32(25).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(33).double(message.updatedAt);
    }
    if (message.isActive !== undefined) {
      writer.uint32(40).bool(message.isActive);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BannerSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBannerSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BannerSummary {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : undefined,
    };
  },

  toJSON(message: BannerSummary): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.isActive !== undefined) {
      obj.isActive = message.isActive;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BannerSummary>, I>>(base?: I): BannerSummary {
    return BannerSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BannerSummary>, I>>(object: I): BannerSummary {
    const message = createBaseBannerSummary();
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.isActive = object.isActive ?? undefined;
    return message;
  },
};

function createBaseBannersRequest(): BannersRequest {
  return { pagination: undefined, includeDeleted: false, q: undefined };
}

export const BannersRequest: MessageFns<BannersRequest> = {
  encode(message: BannersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pagination !== undefined) {
      PaginationRequest.encode(message.pagination, writer.uint32(10).fork()).join();
    }
    if (message.includeDeleted !== false) {
      writer.uint32(16).bool(message.includeDeleted);
    }
    if (message.q !== undefined) {
      writer.uint32(26).string(message.q);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BannersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBannersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pagination = PaginationRequest.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.includeDeleted = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.q = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BannersRequest {
    return {
      pagination: isSet(object.pagination) ? PaginationRequest.fromJSON(object.pagination) : undefined,
      includeDeleted: isSet(object.includeDeleted) ? globalThis.Boolean(object.includeDeleted) : false,
      q: isSet(object.q) ? globalThis.String(object.q) : undefined,
    };
  },

  toJSON(message: BannersRequest): unknown {
    const obj: any = {};
    if (message.pagination !== undefined) {
      obj.pagination = PaginationRequest.toJSON(message.pagination);
    }
    if (message.includeDeleted !== false) {
      obj.includeDeleted = message.includeDeleted;
    }
    if (message.q !== undefined) {
      obj.q = message.q;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BannersRequest>, I>>(base?: I): BannersRequest {
    return BannersRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BannersRequest>, I>>(object: I): BannersRequest {
    const message = createBaseBannersRequest();
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationRequest.fromPartial(object.pagination)
      : undefined;
    message.includeDeleted = object.includeDeleted ?? false;
    message.q = object.q ?? undefined;
    return message;
  },
};

function createBaseBannersResponse(): BannersResponse {
  return { data: [], pagination: undefined };
}

export const BannersResponse: MessageFns<BannersResponse> = {
  encode(message: BannersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      BannerSummary.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.pagination !== undefined) {
      PaginationResponse.encode(message.pagination, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BannersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBannersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(BannerSummary.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pagination = PaginationResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BannersResponse {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => BannerSummary.fromJSON(e)) : [],
      pagination: isSet(object.pagination) ? PaginationResponse.fromJSON(object.pagination) : undefined,
    };
  },

  toJSON(message: BannersResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => BannerSummary.toJSON(e));
    }
    if (message.pagination !== undefined) {
      obj.pagination = PaginationResponse.toJSON(message.pagination);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BannersResponse>, I>>(base?: I): BannersResponse {
    return BannersResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BannersResponse>, I>>(object: I): BannersResponse {
    const message = createBaseBannersResponse();
    message.data = object.data?.map((e) => BannerSummary.fromPartial(e)) || [];
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationResponse.fromPartial(object.pagination)
      : undefined;
    return message;
  },
};

function createBaseCreateBannerRequest(): CreateBannerRequest {
  return { name: "", content: "", groupName: "", bannerType: 0, entityIdentifier: "" };
}

export const CreateBannerRequest: MessageFns<CreateBannerRequest> = {
  encode(message: CreateBannerRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.content !== "") {
      writer.uint32(18).string(message.content);
    }
    if (message.groupName !== "") {
      writer.uint32(26).string(message.groupName);
    }
    if (message.bannerType !== 0) {
      writer.uint32(32).int32(message.bannerType);
    }
    if (message.entityIdentifier !== "") {
      writer.uint32(42).string(message.entityIdentifier);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateBannerRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateBannerRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.groupName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.bannerType = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.entityIdentifier = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateBannerRequest {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      groupName: isSet(object.groupName) ? globalThis.String(object.groupName) : "",
      bannerType: isSet(object.bannerType) ? bannerTypeFromJSON(object.bannerType) : 0,
      entityIdentifier: isSet(object.entityIdentifier) ? globalThis.String(object.entityIdentifier) : "",
    };
  },

  toJSON(message: CreateBannerRequest): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.groupName !== "") {
      obj.groupName = message.groupName;
    }
    if (message.bannerType !== 0) {
      obj.bannerType = bannerTypeToJSON(message.bannerType);
    }
    if (message.entityIdentifier !== "") {
      obj.entityIdentifier = message.entityIdentifier;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateBannerRequest>, I>>(base?: I): CreateBannerRequest {
    return CreateBannerRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateBannerRequest>, I>>(object: I): CreateBannerRequest {
    const message = createBaseCreateBannerRequest();
    message.name = object.name ?? "";
    message.content = object.content ?? "";
    message.groupName = object.groupName ?? "";
    message.bannerType = object.bannerType ?? 0;
    message.entityIdentifier = object.entityIdentifier ?? "";
    return message;
  },
};

function createBaseBannerResponse(): BannerResponse {
  return {
    name: "",
    id: "",
    createdAt: 0,
    updatedAt: 0,
    groupName: "",
    bannerType: 0,
    content: "",
    entityIdentifier: "",
  };
}

export const BannerResponse: MessageFns<BannerResponse> = {
  encode(message: BannerResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(18).string(message.id);
    }
    if (message.createdAt !== 0) {
      writer.uint32(25).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(33).double(message.updatedAt);
    }
    if (message.groupName !== "") {
      writer.uint32(42).string(message.groupName);
    }
    if (message.bannerType !== 0) {
      writer.uint32(48).int32(message.bannerType);
    }
    if (message.content !== "") {
      writer.uint32(58).string(message.content);
    }
    if (message.entityIdentifier !== "") {
      writer.uint32(66).string(message.entityIdentifier);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BannerResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBannerResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.groupName = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.bannerType = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.entityIdentifier = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BannerResponse {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      groupName: isSet(object.groupName) ? globalThis.String(object.groupName) : "",
      bannerType: isSet(object.bannerType) ? bannerTypeFromJSON(object.bannerType) : 0,
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      entityIdentifier: isSet(object.entityIdentifier) ? globalThis.String(object.entityIdentifier) : "",
    };
  },

  toJSON(message: BannerResponse): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.groupName !== "") {
      obj.groupName = message.groupName;
    }
    if (message.bannerType !== 0) {
      obj.bannerType = bannerTypeToJSON(message.bannerType);
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.entityIdentifier !== "") {
      obj.entityIdentifier = message.entityIdentifier;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BannerResponse>, I>>(base?: I): BannerResponse {
    return BannerResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BannerResponse>, I>>(object: I): BannerResponse {
    const message = createBaseBannerResponse();
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.groupName = object.groupName ?? "";
    message.bannerType = object.bannerType ?? 0;
    message.content = object.content ?? "";
    message.entityIdentifier = object.entityIdentifier ?? "";
    return message;
  },
};

function createBaseUpdateBannerRequest(): UpdateBannerRequest {
  return { id: "", name: "", groupName: "", bannerType: 0, content: "", entityIdentifier: "" };
}

export const UpdateBannerRequest: MessageFns<UpdateBannerRequest> = {
  encode(message: UpdateBannerRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.groupName !== "") {
      writer.uint32(26).string(message.groupName);
    }
    if (message.bannerType !== 0) {
      writer.uint32(32).int32(message.bannerType);
    }
    if (message.content !== "") {
      writer.uint32(42).string(message.content);
    }
    if (message.entityIdentifier !== "") {
      writer.uint32(50).string(message.entityIdentifier);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateBannerRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateBannerRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.groupName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.bannerType = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.content = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.entityIdentifier = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateBannerRequest {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      groupName: isSet(object.groupName) ? globalThis.String(object.groupName) : "",
      bannerType: isSet(object.bannerType) ? bannerTypeFromJSON(object.bannerType) : 0,
      content: isSet(object.content) ? globalThis.String(object.content) : "",
      entityIdentifier: isSet(object.entityIdentifier) ? globalThis.String(object.entityIdentifier) : "",
    };
  },

  toJSON(message: UpdateBannerRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.groupName !== "") {
      obj.groupName = message.groupName;
    }
    if (message.bannerType !== 0) {
      obj.bannerType = bannerTypeToJSON(message.bannerType);
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    if (message.entityIdentifier !== "") {
      obj.entityIdentifier = message.entityIdentifier;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateBannerRequest>, I>>(base?: I): UpdateBannerRequest {
    return UpdateBannerRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBannerRequest>, I>>(object: I): UpdateBannerRequest {
    const message = createBaseUpdateBannerRequest();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.groupName = object.groupName ?? "";
    message.bannerType = object.bannerType ?? 0;
    message.content = object.content ?? "";
    message.entityIdentifier = object.entityIdentifier ?? "";
    return message;
  },
};

function createBaseDeleteBannerResponse(): DeleteBannerResponse {
  return { status: false };
}

export const DeleteBannerResponse: MessageFns<DeleteBannerResponse> = {
  encode(message: DeleteBannerResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteBannerResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteBannerResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteBannerResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: DeleteBannerResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteBannerResponse>, I>>(base?: I): DeleteBannerResponse {
    return DeleteBannerResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBannerResponse>, I>>(object: I): DeleteBannerResponse {
    const message = createBaseDeleteBannerResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseRuleSummary(): RuleSummary {
  return {
    page: "",
    name: "",
    id: "",
    featureFlag: "",
    expression: "",
    priority: 0,
    outputA: "",
    outputB: "",
    createdAt: 0,
    updatedAt: 0,
    isActive: false,
  };
}

export const RuleSummary: MessageFns<RuleSummary> = {
  encode(message: RuleSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.page !== "") {
      writer.uint32(10).string(message.page);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(26).string(message.id);
    }
    if (message.featureFlag !== "") {
      writer.uint32(34).string(message.featureFlag);
    }
    if (message.expression !== "") {
      writer.uint32(42).string(message.expression);
    }
    if (message.priority !== 0) {
      writer.uint32(48).int32(message.priority);
    }
    if (message.outputA !== "") {
      writer.uint32(58).string(message.outputA);
    }
    if (message.outputB !== "") {
      writer.uint32(66).string(message.outputB);
    }
    if (message.createdAt !== 0) {
      writer.uint32(73).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(81).double(message.updatedAt);
    }
    if (message.isActive !== false) {
      writer.uint32(88).bool(message.isActive);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RuleSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRuleSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.page = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.expression = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.outputA = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.outputB = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 73) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 10: {
          if (tag !== 81) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.isActive = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RuleSummary {
    return {
      page: isSet(object.page) ? globalThis.String(object.page) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      expression: isSet(object.expression) ? globalThis.String(object.expression) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      outputA: isSet(object.outputA) ? globalThis.String(object.outputA) : "",
      outputB: isSet(object.outputB) ? globalThis.String(object.outputB) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      isActive: isSet(object.isActive) ? globalThis.Boolean(object.isActive) : false,
    };
  },

  toJSON(message: RuleSummary): unknown {
    const obj: any = {};
    if (message.page !== "") {
      obj.page = message.page;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.expression !== "") {
      obj.expression = message.expression;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.outputA !== "") {
      obj.outputA = message.outputA;
    }
    if (message.outputB !== "") {
      obj.outputB = message.outputB;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.isActive !== false) {
      obj.isActive = message.isActive;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RuleSummary>, I>>(base?: I): RuleSummary {
    return RuleSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RuleSummary>, I>>(object: I): RuleSummary {
    const message = createBaseRuleSummary();
    message.page = object.page ?? "";
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.expression = object.expression ?? "";
    message.priority = object.priority ?? 0;
    message.outputA = object.outputA ?? "";
    message.outputB = object.outputB ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.isActive = object.isActive ?? false;
    return message;
  },
};

function createBaseRulesRequest(): RulesRequest {
  return { pagination: undefined, includeDeleted: false, q: undefined };
}

export const RulesRequest: MessageFns<RulesRequest> = {
  encode(message: RulesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pagination !== undefined) {
      PaginationRequest.encode(message.pagination, writer.uint32(10).fork()).join();
    }
    if (message.includeDeleted !== false) {
      writer.uint32(16).bool(message.includeDeleted);
    }
    if (message.q !== undefined) {
      writer.uint32(26).string(message.q);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RulesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRulesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pagination = PaginationRequest.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.includeDeleted = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.q = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RulesRequest {
    return {
      pagination: isSet(object.pagination) ? PaginationRequest.fromJSON(object.pagination) : undefined,
      includeDeleted: isSet(object.includeDeleted) ? globalThis.Boolean(object.includeDeleted) : false,
      q: isSet(object.q) ? globalThis.String(object.q) : undefined,
    };
  },

  toJSON(message: RulesRequest): unknown {
    const obj: any = {};
    if (message.pagination !== undefined) {
      obj.pagination = PaginationRequest.toJSON(message.pagination);
    }
    if (message.includeDeleted !== false) {
      obj.includeDeleted = message.includeDeleted;
    }
    if (message.q !== undefined) {
      obj.q = message.q;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RulesRequest>, I>>(base?: I): RulesRequest {
    return RulesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RulesRequest>, I>>(object: I): RulesRequest {
    const message = createBaseRulesRequest();
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationRequest.fromPartial(object.pagination)
      : undefined;
    message.includeDeleted = object.includeDeleted ?? false;
    message.q = object.q ?? undefined;
    return message;
  },
};

function createBaseRulesResponse(): RulesResponse {
  return { data: [], pagination: undefined };
}

export const RulesResponse: MessageFns<RulesResponse> = {
  encode(message: RulesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      RuleSummary.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.pagination !== undefined) {
      PaginationResponse.encode(message.pagination, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RulesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRulesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(RuleSummary.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pagination = PaginationResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RulesResponse {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => RuleSummary.fromJSON(e)) : [],
      pagination: isSet(object.pagination) ? PaginationResponse.fromJSON(object.pagination) : undefined,
    };
  },

  toJSON(message: RulesResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => RuleSummary.toJSON(e));
    }
    if (message.pagination !== undefined) {
      obj.pagination = PaginationResponse.toJSON(message.pagination);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RulesResponse>, I>>(base?: I): RulesResponse {
    return RulesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RulesResponse>, I>>(object: I): RulesResponse {
    const message = createBaseRulesResponse();
    message.data = object.data?.map((e) => RuleSummary.fromPartial(e)) || [];
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationResponse.fromPartial(object.pagination)
      : undefined;
    return message;
  },
};

function createBaseCreateRuleRequest(): CreateRuleRequest {
  return { page: "", name: "", featureFlag: "", expression: "", priority: 0, outputA: "", outputB: "" };
}

export const CreateRuleRequest: MessageFns<CreateRuleRequest> = {
  encode(message: CreateRuleRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.page !== "") {
      writer.uint32(10).string(message.page);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.featureFlag !== "") {
      writer.uint32(26).string(message.featureFlag);
    }
    if (message.expression !== "") {
      writer.uint32(34).string(message.expression);
    }
    if (message.priority !== 0) {
      writer.uint32(40).int32(message.priority);
    }
    if (message.outputA !== "") {
      writer.uint32(50).string(message.outputA);
    }
    if (message.outputB !== "") {
      writer.uint32(58).string(message.outputB);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateRuleRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateRuleRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.page = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.expression = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.outputA = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.outputB = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateRuleRequest {
    return {
      page: isSet(object.page) ? globalThis.String(object.page) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      expression: isSet(object.expression) ? globalThis.String(object.expression) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      outputA: isSet(object.outputA) ? globalThis.String(object.outputA) : "",
      outputB: isSet(object.outputB) ? globalThis.String(object.outputB) : "",
    };
  },

  toJSON(message: CreateRuleRequest): unknown {
    const obj: any = {};
    if (message.page !== "") {
      obj.page = message.page;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.expression !== "") {
      obj.expression = message.expression;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.outputA !== "") {
      obj.outputA = message.outputA;
    }
    if (message.outputB !== "") {
      obj.outputB = message.outputB;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateRuleRequest>, I>>(base?: I): CreateRuleRequest {
    return CreateRuleRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRuleRequest>, I>>(object: I): CreateRuleRequest {
    const message = createBaseCreateRuleRequest();
    message.page = object.page ?? "";
    message.name = object.name ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.expression = object.expression ?? "";
    message.priority = object.priority ?? 0;
    message.outputA = object.outputA ?? "";
    message.outputB = object.outputB ?? "";
    return message;
  },
};

function createBaseRuleResponse(): RuleResponse {
  return {
    page: "",
    name: "",
    id: "",
    featureFlag: "",
    expression: "",
    priority: 0,
    outputA: "",
    outputB: "",
    createdAt: 0,
    updatedAt: 0,
  };
}

export const RuleResponse: MessageFns<RuleResponse> = {
  encode(message: RuleResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.page !== "") {
      writer.uint32(10).string(message.page);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(26).string(message.id);
    }
    if (message.featureFlag !== "") {
      writer.uint32(34).string(message.featureFlag);
    }
    if (message.expression !== "") {
      writer.uint32(42).string(message.expression);
    }
    if (message.priority !== 0) {
      writer.uint32(48).int32(message.priority);
    }
    if (message.outputA !== "") {
      writer.uint32(58).string(message.outputA);
    }
    if (message.outputB !== "") {
      writer.uint32(66).string(message.outputB);
    }
    if (message.createdAt !== 0) {
      writer.uint32(73).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(81).double(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RuleResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRuleResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.page = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.expression = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.outputA = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.outputB = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 73) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 10: {
          if (tag !== 81) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RuleResponse {
    return {
      page: isSet(object.page) ? globalThis.String(object.page) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      expression: isSet(object.expression) ? globalThis.String(object.expression) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      outputA: isSet(object.outputA) ? globalThis.String(object.outputA) : "",
      outputB: isSet(object.outputB) ? globalThis.String(object.outputB) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: RuleResponse): unknown {
    const obj: any = {};
    if (message.page !== "") {
      obj.page = message.page;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.expression !== "") {
      obj.expression = message.expression;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.outputA !== "") {
      obj.outputA = message.outputA;
    }
    if (message.outputB !== "") {
      obj.outputB = message.outputB;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RuleResponse>, I>>(base?: I): RuleResponse {
    return RuleResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RuleResponse>, I>>(object: I): RuleResponse {
    const message = createBaseRuleResponse();
    message.page = object.page ?? "";
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.expression = object.expression ?? "";
    message.priority = object.priority ?? 0;
    message.outputA = object.outputA ?? "";
    message.outputB = object.outputB ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseUpdateRuleRequest(): UpdateRuleRequest {
  return { page: "", name: "", id: "", featureFlag: "", expression: "", priority: 0, outputA: "", outputB: "" };
}

export const UpdateRuleRequest: MessageFns<UpdateRuleRequest> = {
  encode(message: UpdateRuleRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.page !== "") {
      writer.uint32(10).string(message.page);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.id !== "") {
      writer.uint32(26).string(message.id);
    }
    if (message.featureFlag !== "") {
      writer.uint32(34).string(message.featureFlag);
    }
    if (message.expression !== "") {
      writer.uint32(42).string(message.expression);
    }
    if (message.priority !== 0) {
      writer.uint32(48).int32(message.priority);
    }
    if (message.outputA !== "") {
      writer.uint32(58).string(message.outputA);
    }
    if (message.outputB !== "") {
      writer.uint32(66).string(message.outputB);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateRuleRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateRuleRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.page = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.expression = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.outputA = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.outputB = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateRuleRequest {
    return {
      page: isSet(object.page) ? globalThis.String(object.page) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      expression: isSet(object.expression) ? globalThis.String(object.expression) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      outputA: isSet(object.outputA) ? globalThis.String(object.outputA) : "",
      outputB: isSet(object.outputB) ? globalThis.String(object.outputB) : "",
    };
  },

  toJSON(message: UpdateRuleRequest): unknown {
    const obj: any = {};
    if (message.page !== "") {
      obj.page = message.page;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.expression !== "") {
      obj.expression = message.expression;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.outputA !== "") {
      obj.outputA = message.outputA;
    }
    if (message.outputB !== "") {
      obj.outputB = message.outputB;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateRuleRequest>, I>>(base?: I): UpdateRuleRequest {
    return UpdateRuleRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRuleRequest>, I>>(object: I): UpdateRuleRequest {
    const message = createBaseUpdateRuleRequest();
    message.page = object.page ?? "";
    message.name = object.name ?? "";
    message.id = object.id ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.expression = object.expression ?? "";
    message.priority = object.priority ?? 0;
    message.outputA = object.outputA ?? "";
    message.outputB = object.outputB ?? "";
    return message;
  },
};

function createBaseDeleteRuleResponse(): DeleteRuleResponse {
  return { status: false };
}

export const DeleteRuleResponse: MessageFns<DeleteRuleResponse> = {
  encode(message: DeleteRuleResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteRuleResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteRuleResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteRuleResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: DeleteRuleResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteRuleResponse>, I>>(base?: I): DeleteRuleResponse {
    return DeleteRuleResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRuleResponse>, I>>(object: I): DeleteRuleResponse {
    const message = createBaseDeleteRuleResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseBannersHeadingRequest(): BannersHeadingRequest {
  return {};
}

export const BannersHeadingRequest: MessageFns<BannersHeadingRequest> = {
  encode(_: BannersHeadingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BannersHeadingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBannersHeadingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): BannersHeadingRequest {
    return {};
  },

  toJSON(_: BannersHeadingRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<BannersHeadingRequest>, I>>(base?: I): BannersHeadingRequest {
    return BannersHeadingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BannersHeadingRequest>, I>>(_: I): BannersHeadingRequest {
    const message = createBaseBannersHeadingRequest();
    return message;
  },
};

function createBaseBannersHeadingResponse(): BannersHeadingResponse {
  return { banners: [] };
}

export const BannersHeadingResponse: MessageFns<BannersHeadingResponse> = {
  encode(message: BannersHeadingResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.banners) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BannersHeadingResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBannersHeadingResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.banners.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BannersHeadingResponse {
    return {
      banners: globalThis.Array.isArray(object?.banners) ? object.banners.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: BannersHeadingResponse): unknown {
    const obj: any = {};
    if (message.banners?.length) {
      obj.banners = message.banners;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BannersHeadingResponse>, I>>(base?: I): BannersHeadingResponse {
    return BannersHeadingResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BannersHeadingResponse>, I>>(object: I): BannersHeadingResponse {
    const message = createBaseBannersHeadingResponse();
    message.banners = object.banners?.map((e) => e) || [];
    return message;
  },
};

function createBaseStorySummary(): StorySummary {
  return {
    name: "",
    featureFlag: "",
    variant: "",
    page: "",
    isShareable: false,
    isLikeable: false,
    priority: 0,
    createdAt: 0,
    updatedAt: 0,
    id: "",
    startTime: "",
    endTime: "",
    content: "",
  };
}

export const StorySummary: MessageFns<StorySummary> = {
  encode(message: StorySummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.featureFlag !== "") {
      writer.uint32(18).string(message.featureFlag);
    }
    if (message.variant !== "") {
      writer.uint32(26).string(message.variant);
    }
    if (message.page !== "") {
      writer.uint32(34).string(message.page);
    }
    if (message.isShareable !== false) {
      writer.uint32(40).bool(message.isShareable);
    }
    if (message.isLikeable !== false) {
      writer.uint32(48).bool(message.isLikeable);
    }
    if (message.priority !== 0) {
      writer.uint32(56).int32(message.priority);
    }
    if (message.createdAt !== 0) {
      writer.uint32(65).double(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(73).double(message.updatedAt);
    }
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.startTime !== "") {
      writer.uint32(90).string(message.startTime);
    }
    if (message.endTime !== "") {
      writer.uint32(98).string(message.endTime);
    }
    if (message.content !== "") {
      writer.uint32(106).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StorySummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStorySummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.page = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isShareable = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isLikeable = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.createdAt = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 73) {
            break;
          }

          message.updatedAt = reader.double();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.startTime = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.endTime = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StorySummary {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      variant: isSet(object.variant) ? globalThis.String(object.variant) : "",
      page: isSet(object.page) ? globalThis.String(object.page) : "",
      isShareable: isSet(object.isShareable) ? globalThis.Boolean(object.isShareable) : false,
      isLikeable: isSet(object.isLikeable) ? globalThis.Boolean(object.isLikeable) : false,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      startTime: isSet(object.startTime) ? globalThis.String(object.startTime) : "",
      endTime: isSet(object.endTime) ? globalThis.String(object.endTime) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
    };
  },

  toJSON(message: StorySummary): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.variant !== "") {
      obj.variant = message.variant;
    }
    if (message.page !== "") {
      obj.page = message.page;
    }
    if (message.isShareable !== false) {
      obj.isShareable = message.isShareable;
    }
    if (message.isLikeable !== false) {
      obj.isLikeable = message.isLikeable;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = message.createdAt;
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = message.updatedAt;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.startTime !== "") {
      obj.startTime = message.startTime;
    }
    if (message.endTime !== "") {
      obj.endTime = message.endTime;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StorySummary>, I>>(base?: I): StorySummary {
    return StorySummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StorySummary>, I>>(object: I): StorySummary {
    const message = createBaseStorySummary();
    message.name = object.name ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.variant = object.variant ?? "";
    message.page = object.page ?? "";
    message.isShareable = object.isShareable ?? false;
    message.isLikeable = object.isLikeable ?? false;
    message.priority = object.priority ?? 0;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.id = object.id ?? "";
    message.startTime = object.startTime ?? "";
    message.endTime = object.endTime ?? "";
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseStoriesRequest(): StoriesRequest {
  return { pagination: undefined, q: undefined, includeDeleted: false };
}

export const StoriesRequest: MessageFns<StoriesRequest> = {
  encode(message: StoriesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pagination !== undefined) {
      PaginationRequest.encode(message.pagination, writer.uint32(10).fork()).join();
    }
    if (message.q !== undefined) {
      writer.uint32(18).string(message.q);
    }
    if (message.includeDeleted !== false) {
      writer.uint32(24).bool(message.includeDeleted);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StoriesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStoriesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pagination = PaginationRequest.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.q = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.includeDeleted = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StoriesRequest {
    return {
      pagination: isSet(object.pagination) ? PaginationRequest.fromJSON(object.pagination) : undefined,
      q: isSet(object.q) ? globalThis.String(object.q) : undefined,
      includeDeleted: isSet(object.includeDeleted) ? globalThis.Boolean(object.includeDeleted) : false,
    };
  },

  toJSON(message: StoriesRequest): unknown {
    const obj: any = {};
    if (message.pagination !== undefined) {
      obj.pagination = PaginationRequest.toJSON(message.pagination);
    }
    if (message.q !== undefined) {
      obj.q = message.q;
    }
    if (message.includeDeleted !== false) {
      obj.includeDeleted = message.includeDeleted;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StoriesRequest>, I>>(base?: I): StoriesRequest {
    return StoriesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StoriesRequest>, I>>(object: I): StoriesRequest {
    const message = createBaseStoriesRequest();
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationRequest.fromPartial(object.pagination)
      : undefined;
    message.q = object.q ?? undefined;
    message.includeDeleted = object.includeDeleted ?? false;
    return message;
  },
};

function createBaseStoriesResponse(): StoriesResponse {
  return { data: [], pagination: undefined };
}

export const StoriesResponse: MessageFns<StoriesResponse> = {
  encode(message: StoriesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.data) {
      StorySummary.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.pagination !== undefined) {
      PaginationResponse.encode(message.pagination, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StoriesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStoriesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data.push(StorySummary.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pagination = PaginationResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StoriesResponse {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => StorySummary.fromJSON(e)) : [],
      pagination: isSet(object.pagination) ? PaginationResponse.fromJSON(object.pagination) : undefined,
    };
  },

  toJSON(message: StoriesResponse): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => StorySummary.toJSON(e));
    }
    if (message.pagination !== undefined) {
      obj.pagination = PaginationResponse.toJSON(message.pagination);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StoriesResponse>, I>>(base?: I): StoriesResponse {
    return StoriesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StoriesResponse>, I>>(object: I): StoriesResponse {
    const message = createBaseStoriesResponse();
    message.data = object.data?.map((e) => StorySummary.fromPartial(e)) || [];
    message.pagination = (object.pagination !== undefined && object.pagination !== null)
      ? PaginationResponse.fromPartial(object.pagination)
      : undefined;
    return message;
  },
};

function createBaseCreateStoryRequest(): CreateStoryRequest {
  return {
    name: "",
    featureFlag: "",
    variant: "",
    page: "",
    isShareable: false,
    isLikeable: false,
    priority: 0,
    startTime: "",
    endTime: "",
    content: "",
  };
}

export const CreateStoryRequest: MessageFns<CreateStoryRequest> = {
  encode(message: CreateStoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.featureFlag !== "") {
      writer.uint32(18).string(message.featureFlag);
    }
    if (message.variant !== "") {
      writer.uint32(26).string(message.variant);
    }
    if (message.page !== "") {
      writer.uint32(34).string(message.page);
    }
    if (message.isShareable !== false) {
      writer.uint32(40).bool(message.isShareable);
    }
    if (message.isLikeable !== false) {
      writer.uint32(48).bool(message.isLikeable);
    }
    if (message.priority !== 0) {
      writer.uint32(56).int32(message.priority);
    }
    if (message.startTime !== "") {
      writer.uint32(66).string(message.startTime);
    }
    if (message.endTime !== "") {
      writer.uint32(74).string(message.endTime);
    }
    if (message.content !== "") {
      writer.uint32(82).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreateStoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateStoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.page = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isShareable = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isLikeable = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.startTime = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.endTime = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateStoryRequest {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      variant: isSet(object.variant) ? globalThis.String(object.variant) : "",
      page: isSet(object.page) ? globalThis.String(object.page) : "",
      isShareable: isSet(object.isShareable) ? globalThis.Boolean(object.isShareable) : false,
      isLikeable: isSet(object.isLikeable) ? globalThis.Boolean(object.isLikeable) : false,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      startTime: isSet(object.startTime) ? globalThis.String(object.startTime) : "",
      endTime: isSet(object.endTime) ? globalThis.String(object.endTime) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
    };
  },

  toJSON(message: CreateStoryRequest): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.variant !== "") {
      obj.variant = message.variant;
    }
    if (message.page !== "") {
      obj.page = message.page;
    }
    if (message.isShareable !== false) {
      obj.isShareable = message.isShareable;
    }
    if (message.isLikeable !== false) {
      obj.isLikeable = message.isLikeable;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.startTime !== "") {
      obj.startTime = message.startTime;
    }
    if (message.endTime !== "") {
      obj.endTime = message.endTime;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateStoryRequest>, I>>(base?: I): CreateStoryRequest {
    return CreateStoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateStoryRequest>, I>>(object: I): CreateStoryRequest {
    const message = createBaseCreateStoryRequest();
    message.name = object.name ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.variant = object.variant ?? "";
    message.page = object.page ?? "";
    message.isShareable = object.isShareable ?? false;
    message.isLikeable = object.isLikeable ?? false;
    message.priority = object.priority ?? 0;
    message.startTime = object.startTime ?? "";
    message.endTime = object.endTime ?? "";
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseUpdateStoryRequest(): UpdateStoryRequest {
  return {
    name: "",
    featureFlag: "",
    variant: "",
    page: "",
    isShareable: false,
    isLikeable: false,
    priority: 0,
    startTime: "",
    endTime: "",
    id: "",
    content: "",
  };
}

export const UpdateStoryRequest: MessageFns<UpdateStoryRequest> = {
  encode(message: UpdateStoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.featureFlag !== "") {
      writer.uint32(18).string(message.featureFlag);
    }
    if (message.variant !== "") {
      writer.uint32(26).string(message.variant);
    }
    if (message.page !== "") {
      writer.uint32(34).string(message.page);
    }
    if (message.isShareable !== false) {
      writer.uint32(40).bool(message.isShareable);
    }
    if (message.isLikeable !== false) {
      writer.uint32(48).bool(message.isLikeable);
    }
    if (message.priority !== 0) {
      writer.uint32(56).int32(message.priority);
    }
    if (message.startTime !== "") {
      writer.uint32(66).string(message.startTime);
    }
    if (message.endTime !== "") {
      writer.uint32(74).string(message.endTime);
    }
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.content !== "") {
      writer.uint32(90).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateStoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateStoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.page = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isShareable = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isLikeable = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.startTime = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.endTime = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateStoryRequest {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      variant: isSet(object.variant) ? globalThis.String(object.variant) : "",
      page: isSet(object.page) ? globalThis.String(object.page) : "",
      isShareable: isSet(object.isShareable) ? globalThis.Boolean(object.isShareable) : false,
      isLikeable: isSet(object.isLikeable) ? globalThis.Boolean(object.isLikeable) : false,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      startTime: isSet(object.startTime) ? globalThis.String(object.startTime) : "",
      endTime: isSet(object.endTime) ? globalThis.String(object.endTime) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
    };
  },

  toJSON(message: UpdateStoryRequest): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.variant !== "") {
      obj.variant = message.variant;
    }
    if (message.page !== "") {
      obj.page = message.page;
    }
    if (message.isShareable !== false) {
      obj.isShareable = message.isShareable;
    }
    if (message.isLikeable !== false) {
      obj.isLikeable = message.isLikeable;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.startTime !== "") {
      obj.startTime = message.startTime;
    }
    if (message.endTime !== "") {
      obj.endTime = message.endTime;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateStoryRequest>, I>>(base?: I): UpdateStoryRequest {
    return UpdateStoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateStoryRequest>, I>>(object: I): UpdateStoryRequest {
    const message = createBaseUpdateStoryRequest();
    message.name = object.name ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.variant = object.variant ?? "";
    message.page = object.page ?? "";
    message.isShareable = object.isShareable ?? false;
    message.isLikeable = object.isLikeable ?? false;
    message.priority = object.priority ?? 0;
    message.startTime = object.startTime ?? "";
    message.endTime = object.endTime ?? "";
    message.id = object.id ?? "";
    message.content = object.content ?? "";
    return message;
  },
};

function createBaseDeleteStoryRequest(): DeleteStoryRequest {
  return { id: "" };
}

export const DeleteStoryRequest: MessageFns<DeleteStoryRequest> = {
  encode(message: DeleteStoryRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteStoryRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteStoryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteStoryRequest {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: DeleteStoryRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteStoryRequest>, I>>(base?: I): DeleteStoryRequest {
    return DeleteStoryRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteStoryRequest>, I>>(object: I): DeleteStoryRequest {
    const message = createBaseDeleteStoryRequest();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseDeleteStoryResponse(): DeleteStoryResponse {
  return { status: false };
}

export const DeleteStoryResponse: MessageFns<DeleteStoryResponse> = {
  encode(message: DeleteStoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteStoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteStoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteStoryResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: DeleteStoryResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteStoryResponse>, I>>(base?: I): DeleteStoryResponse {
    return DeleteStoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteStoryResponse>, I>>(object: I): DeleteStoryResponse {
    const message = createBaseDeleteStoryResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseStoryResponse(): StoryResponse {
  return {
    name: "",
    featureFlag: "",
    variant: "",
    page: "",
    isShareable: false,
    isLikeable: false,
    priority: 0,
    startTime: "",
    endTime: "",
    id: "",
    content: "",
  };
}

export const StoryResponse: MessageFns<StoryResponse> = {
  encode(message: StoryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.featureFlag !== "") {
      writer.uint32(18).string(message.featureFlag);
    }
    if (message.variant !== "") {
      writer.uint32(26).string(message.variant);
    }
    if (message.page !== "") {
      writer.uint32(34).string(message.page);
    }
    if (message.isShareable !== false) {
      writer.uint32(40).bool(message.isShareable);
    }
    if (message.isLikeable !== false) {
      writer.uint32(48).bool(message.isLikeable);
    }
    if (message.priority !== 0) {
      writer.uint32(56).int32(message.priority);
    }
    if (message.startTime !== "") {
      writer.uint32(66).string(message.startTime);
    }
    if (message.endTime !== "") {
      writer.uint32(74).string(message.endTime);
    }
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.content !== "") {
      writer.uint32(90).string(message.content);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StoryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStoryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.featureFlag = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.variant = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.page = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isShareable = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isLikeable = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.startTime = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.endTime = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.content = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StoryResponse {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      featureFlag: isSet(object.featureFlag) ? globalThis.String(object.featureFlag) : "",
      variant: isSet(object.variant) ? globalThis.String(object.variant) : "",
      page: isSet(object.page) ? globalThis.String(object.page) : "",
      isShareable: isSet(object.isShareable) ? globalThis.Boolean(object.isShareable) : false,
      isLikeable: isSet(object.isLikeable) ? globalThis.Boolean(object.isLikeable) : false,
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      startTime: isSet(object.startTime) ? globalThis.String(object.startTime) : "",
      endTime: isSet(object.endTime) ? globalThis.String(object.endTime) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      content: isSet(object.content) ? globalThis.String(object.content) : "",
    };
  },

  toJSON(message: StoryResponse): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.featureFlag !== "") {
      obj.featureFlag = message.featureFlag;
    }
    if (message.variant !== "") {
      obj.variant = message.variant;
    }
    if (message.page !== "") {
      obj.page = message.page;
    }
    if (message.isShareable !== false) {
      obj.isShareable = message.isShareable;
    }
    if (message.isLikeable !== false) {
      obj.isLikeable = message.isLikeable;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.startTime !== "") {
      obj.startTime = message.startTime;
    }
    if (message.endTime !== "") {
      obj.endTime = message.endTime;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.content !== "") {
      obj.content = message.content;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StoryResponse>, I>>(base?: I): StoryResponse {
    return StoryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StoryResponse>, I>>(object: I): StoryResponse {
    const message = createBaseStoryResponse();
    message.name = object.name ?? "";
    message.featureFlag = object.featureFlag ?? "";
    message.variant = object.variant ?? "";
    message.page = object.page ?? "";
    message.isShareable = object.isShareable ?? false;
    message.isLikeable = object.isLikeable ?? false;
    message.priority = object.priority ?? 0;
    message.startTime = object.startTime ?? "";
    message.endTime = object.endTime ?? "";
    message.id = object.id ?? "";
    message.content = object.content ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
