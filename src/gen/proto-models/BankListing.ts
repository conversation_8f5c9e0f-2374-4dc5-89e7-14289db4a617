// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: BankListing.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  BankType,
  bankTypeFromJSON,
  bankTypeToJSON,
  CompoundingFrequencyType,
  compoundingFrequencyTypeFromJSON,
  compoundingFrequencyTypeToJSON,
  InterestPayoutType,
  interestPayoutTypeFromJSON,
  interestPayoutTypeToJSON,
  InvestabilityStatus,
  investabilityStatusFromJSON,
  investabilityStatusToJSON,
  InvestorType,
  investorTypeFromJSON,
  investorTypeToJSON,
  RedirectDeeplink,
  TenureFormatType,
  tenureFormatTypeFromJSON,
  tenureFormatTypeToJSON,
} from "./BusinessCommon";
import {
  BankResponse,
  FixedDepositResponse,
  MaturityInstruction,
  maturityInstructionFromJSON,
  maturityInstructionToJSON,
} from "./Collection";
import { Faq } from "./Faq";

export const protobufPackage = "com.stablemoney.api.identity";

export enum DeviceInvestabilityStatus {
  UNKNOWN = 0,
  ACTIVE = 1,
  INACTIVE = 2,
  COMING_SOON = 3,
  ACTIVE_ON_APP = 4,
  UNRECOGNIZED = -1,
}

export function deviceInvestabilityStatusFromJSON(object: any): DeviceInvestabilityStatus {
  switch (object) {
    case 0:
    case "UNKNOWN":
      return DeviceInvestabilityStatus.UNKNOWN;
    case 1:
    case "ACTIVE":
      return DeviceInvestabilityStatus.ACTIVE;
    case 2:
    case "INACTIVE":
      return DeviceInvestabilityStatus.INACTIVE;
    case 3:
    case "COMING_SOON":
      return DeviceInvestabilityStatus.COMING_SOON;
    case 4:
    case "ACTIVE_ON_APP":
      return DeviceInvestabilityStatus.ACTIVE_ON_APP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DeviceInvestabilityStatus.UNRECOGNIZED;
  }
}

export function deviceInvestabilityStatusToJSON(object: DeviceInvestabilityStatus): string {
  switch (object) {
    case DeviceInvestabilityStatus.UNKNOWN:
      return "UNKNOWN";
    case DeviceInvestabilityStatus.ACTIVE:
      return "ACTIVE";
    case DeviceInvestabilityStatus.INACTIVE:
      return "INACTIVE";
    case DeviceInvestabilityStatus.COMING_SOON:
      return "COMING_SOON";
    case DeviceInvestabilityStatus.ACTIVE_ON_APP:
      return "ACTIVE_ON_APP";
    case DeviceInvestabilityStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface FDReturnCalculatorResponse {
  extraIncome: number;
  maturedAmount: number;
}

export interface BankListingPageDataResponse {
  bankResponse: BankResponse | undefined;
  fdBookingStats: string;
  assuranceLine: string;
  assuranceLogoUrl: string;
  stableMoneyAnalysis: StableMoneyAnalysis[];
  generalCitizenInterestData: BankListingPageInterestData | undefined;
  seniorCitizenInterestData: BankListingPageInterestData | undefined;
  seniorCitizenRoiMaxDifference: number;
  rateLastUpdatedAt: string;
  ratesPdfUrl: string;
  aboutBankInfo: string;
  bankProminentPersonnel: BankProminentPersonnel[];
  bankAward: BankAward[];
  safeBanks: SafeBank[];
  bankCustomerTestimonial: BankCustomerTestimonial[];
  investNowMessage: InvestNowMessage[];
  bankFaq: Faq[];
  redirectDeeplink: RedirectDeeplink | undefined;
  isFirstTimeOfferApplicable: boolean;
}

export interface BankListingPageInterestData {
  highestInterestFd: FixedDepositResponse | undefined;
  fixedDeposits: FixedDepositResponse[];
  recommendedFds: RecommendedFd[];
  disclaimer: string;
  investorType: InvestorType;
  roiMaxDifferenceFromGeneral: number;
}

export interface InvestNowMessage {
  message: string;
}

export interface StableMoneyAnalysis {
  description: string;
}

export interface SafeBank {
  bankId: string;
  logoUrl: string;
}

export interface BankProminentPersonnel {
  heading: string;
  name: string;
  description: string;
  pictureUrl: string;
}

export interface BankAward {
  title: string;
  iconUrl: string;
  receivedBy: string;
}

export interface BankCustomerTestimonial {
  comment: string;
  name: string;
  designation: string;
  pictureUrl: string;
}

export interface RecommendedFd {
  description: string;
  fixedDepositResponse: FixedDepositResponse | undefined;
  iconUrl: string;
}

export interface BankListingV2 {
  bankResponse: BankResponse | undefined;
  topInfoCard: TopInfoCard[];
  bankTags: BankTag[];
  interestData: BankListingPageInterestData[];
  bankSellingPoints: BankSellingPoint[];
  redirectDeeplink: RedirectDeeplink | undefined;
  fdBookingStats: string;
  bankFaq: Faq[];
  rateLastUpdatedAt: string;
  ratesPdfUrl: string;
  assuranceLine: string;
  assuranceLogoUrl: string;
  isFirstTimeOfferApplicable: boolean;
  isVkycRequired: boolean;
  bankFaqCategoryPriorityList: string[];
}

export interface HighestRateKeyValue {
  investorType: InvestorType;
  fixedDeposit: FixedDepositResponse | undefined;
}

export interface InvestableBankCompare {
  interestRate: number;
  bankResponse: BankResponse | undefined;
}

export interface NonInvestableBankCompare {
  /** @deprecated */
  titles: string[];
  /** @deprecated */
  highestBankDetails: string[];
  /** @deprecated */
  bankDetails: string[];
  highestRateBankResponse: BankResponse | undefined;
  lineItems: NonInvestableBankCompare_CompareLineItem[];
}

export interface NonInvestableBankCompare_CompareLineItem {
  title: string;
  highestBankPoint: string;
  currentBankPoint: string;
  moreDetailsLink: RedirectDeeplink | undefined;
}

export interface FdWithdrawalCalculation {
  tenure: string;
  withdrawalAmount: number;
  rate: number;
  originalRate: number;
}

export interface FdWithdrawalCalculationDetails {
  interestRate: number;
  investmentAmount: number;
  tenure: string;
}

export interface BankListingV3 {
  bankResponse: BankResponse | undefined;
  interestData: BankListingPageInterestData[];
  redirectDeeplink: RedirectDeeplink | undefined;
  bankFaq: Faq[];
  isVkycRequired: boolean;
  highestRateMap: HighestRateKeyValue[];
  investableBankDownTimeMessage: string;
  investableBankCompare: InvestableBankCompare[];
  nonInvestableBankCompare: NonInvestableBankCompare | undefined;
  fdWithdrawalCalculation: FdWithdrawalCalculation[];
  nonInvestableBankMessage: string;
  bankImageUrl: string;
  fdWithdrawalCalculationDetails: FdWithdrawalCalculationDetails | undefined;
  isRepeatUserToPlatform: boolean;
  isRepeatUserToBank: boolean;
  bankFaqCategoryPriorityList: string[];
  deviceInvestabilityStatus: BankListingV3_DeviceInvestabilityStatus;
}

export enum BankListingV3_DeviceInvestabilityStatus {
  UNKNOWN = 0,
  ACTIVE = 1,
  INACTIVE = 2,
  COMING_SOON = 3,
  ACTIVE_ON_APP = 4,
  UNRECOGNIZED = -1,
}

export function bankListingV3_DeviceInvestabilityStatusFromJSON(object: any): BankListingV3_DeviceInvestabilityStatus {
  switch (object) {
    case 0:
    case "UNKNOWN":
      return BankListingV3_DeviceInvestabilityStatus.UNKNOWN;
    case 1:
    case "ACTIVE":
      return BankListingV3_DeviceInvestabilityStatus.ACTIVE;
    case 2:
    case "INACTIVE":
      return BankListingV3_DeviceInvestabilityStatus.INACTIVE;
    case 3:
    case "COMING_SOON":
      return BankListingV3_DeviceInvestabilityStatus.COMING_SOON;
    case 4:
    case "ACTIVE_ON_APP":
      return BankListingV3_DeviceInvestabilityStatus.ACTIVE_ON_APP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BankListingV3_DeviceInvestabilityStatus.UNRECOGNIZED;
  }
}

export function bankListingV3_DeviceInvestabilityStatusToJSON(object: BankListingV3_DeviceInvestabilityStatus): string {
  switch (object) {
    case BankListingV3_DeviceInvestabilityStatus.UNKNOWN:
      return "UNKNOWN";
    case BankListingV3_DeviceInvestabilityStatus.ACTIVE:
      return "ACTIVE";
    case BankListingV3_DeviceInvestabilityStatus.INACTIVE:
      return "INACTIVE";
    case BankListingV3_DeviceInvestabilityStatus.COMING_SOON:
      return "COMING_SOON";
    case BankListingV3_DeviceInvestabilityStatus.ACTIVE_ON_APP:
      return "ACTIVE_ON_APP";
    case BankListingV3_DeviceInvestabilityStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface BankListing {
  id: string;
  slug: string;
  logoUrl: string;
  name: string;
  keywords: string;
  accountRequiredToInvest: boolean;
  bankType: BankType;
  investabilityStatus: InvestabilityStatus;
  faqs: Faq[];
  withdrawalOptions: WithdrawalOption[];
  fixedDeposits: FixedDepositInfo[];
  downtimes: Downtime[];
  investLink: RedirectDeeplink | undefined;
  withdrawalPenaltyRate: number;
  bankFaqCategoryPriorityList: string[];
}

export interface FixedDepositInfo {
  id: string;
  rate: number;
  minDeposit: number;
  maxDeposit: number;
  investorType: InvestorType;
  isPreMatureWithdrawalAllowed: boolean;
  interestPayoutType: InterestPayoutType;
  isLoanAgainstFdAllowed: boolean;
  isPartialWithdrawalAllowed: boolean;
  displayTenure: string;
  minTenureInDays: number;
  maxTenureInDays: number;
}

export interface Downtime {
  startOn: number;
  endOn: number;
}

export interface WithdrawalOption {
  rate: number;
  minTenureInDays: number;
  maxTenureInDays: number;
  minDeposit: number;
  maxDeposit: number;
  investorType: InvestorType;
  compoundingFrequency: CompoundingFrequencyType;
}

export interface TopInfoCard {
  title: string;
  description: string;
  iconUrl: string;
  infoBottomSheetTitle: string;
  infoBottomSheetDescription: string;
}

export interface BankTag {
  tagId: string;
  tagName: string;
  iconUrl: string;
}

export interface BankSellingPoint {
  title: string;
  description: string;
  iconUrl: string;
}

export interface BankFixedDepositStep {
  title: string;
  description: string;
  imageUrl: string;
}

export interface BankFixedDepositStepsResponse {
  bankFixedDepositStep: BankFixedDepositStep[];
}

export interface BankRateNotificationResponse {
  status: boolean;
}

export interface BankRateNotificationRequest {
  bankId: string;
  toNotify?: boolean | undefined;
}

export interface AllBanksResponse {
  banks: BankListing[];
}

export interface BranchLocations {
  branchLocations: BranchLocation[];
  cityName: string;
  totalBranches: number;
  totalCityBranches: number;
}

export interface BranchLocation {
  address: string;
  area: string;
  city: string;
  state: string;
  pincode: string;
  latLong: BranchLocation_LatLong | undefined;
  phoneNumber: string;
}

export interface BranchLocation_LatLong {
  latitude: number;
  longitude: number;
}

export interface MediaItem {
  section: string;
  mediaType: MediaItem_MediaType;
  url: string;
  screenType: MediaItem_ScreenType;
  redirectDeeplink?: RedirectDeeplink | undefined;
}

export enum MediaItem_MediaType {
  UNKNOWN = 0,
  IMAGE = 1,
  VIDEO = 2,
  UNRECOGNIZED = -1,
}

export function mediaItem_MediaTypeFromJSON(object: any): MediaItem_MediaType {
  switch (object) {
    case 0:
    case "UNKNOWN":
      return MediaItem_MediaType.UNKNOWN;
    case 1:
    case "IMAGE":
      return MediaItem_MediaType.IMAGE;
    case 2:
    case "VIDEO":
      return MediaItem_MediaType.VIDEO;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MediaItem_MediaType.UNRECOGNIZED;
  }
}

export function mediaItem_MediaTypeToJSON(object: MediaItem_MediaType): string {
  switch (object) {
    case MediaItem_MediaType.UNKNOWN:
      return "UNKNOWN";
    case MediaItem_MediaType.IMAGE:
      return "IMAGE";
    case MediaItem_MediaType.VIDEO:
      return "VIDEO";
    case MediaItem_MediaType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum MediaItem_ScreenType {
  SCREEN_TYPE_UNKNOWN = 0,
  MOBILE = 1,
  DESKTOP = 2,
  UNRECOGNIZED = -1,
}

export function mediaItem_ScreenTypeFromJSON(object: any): MediaItem_ScreenType {
  switch (object) {
    case 0:
    case "SCREEN_TYPE_UNKNOWN":
      return MediaItem_ScreenType.SCREEN_TYPE_UNKNOWN;
    case 1:
    case "MOBILE":
      return MediaItem_ScreenType.MOBILE;
    case 2:
    case "DESKTOP":
      return MediaItem_ScreenType.DESKTOP;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MediaItem_ScreenType.UNRECOGNIZED;
  }
}

export function mediaItem_ScreenTypeToJSON(object: MediaItem_ScreenType): string {
  switch (object) {
    case MediaItem_ScreenType.SCREEN_TYPE_UNKNOWN:
      return "SCREEN_TYPE_UNKNOWN";
    case MediaItem_ScreenType.MOBILE:
      return "MOBILE";
    case MediaItem_ScreenType.DESKTOP:
      return "DESKTOP";
    case MediaItem_ScreenType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface CompareLineItem {
  title: string;
  highestBankPoint: string;
  currentBankPoint: string;
  moreDetailsLink: RedirectDeeplink | undefined;
}

export interface BankListingV4 {
  bankResponse: BankResponse | undefined;
  marketingHighlights: string[];
  tenures: Tenure[];
  womenRoiDiff?: number | undefined;
  isRateChangeNotificationEnabled: boolean;
  redirectDeeplink: RedirectDeeplink | undefined;
  sipRedirectDeeplink?: RedirectDeeplink | undefined;
  rdRedirectDeeplink?: RedirectDeeplink | undefined;
  investableBankCompare: InvestableBankCompareV4 | undefined;
  nonInvestableBankCompare: NonInvestableBankCompareV4 | undefined;
  fdWithdrawalCalculationDetails: FdWithdrawalCalculationDetails | undefined;
  fdWithdrawalCalculation: FdWithdrawalCalculation[];
  userDeviceInvestabilityStatus: DeviceInvestabilityStatus;
  shareImageUrl: string;
  highlightPointers: string[];
  bankTables: { [key: string]: BankTable };
  bankPointers: { [key: string]: BankPointers };
  mediaItems: MediaItem[];
  /** @deprecated */
  userBankRelationshipType: string;
  userBankRelationship: BankListingV4_UserBankRelationship | undefined;
  seniorRoiDiff?: number | undefined;
}

export interface BankListingV4_UserBankRelationship {
  relationshipType: string;
  numberOfBookings: number;
  totalInvestedAmount: number;
  lastInvestedOn: string;
  vkycCompleted: boolean;
}

export interface BankListingV4_BankTablesEntry {
  key: string;
  value: BankTable | undefined;
}

export interface BankListingV4_BankPointersEntry {
  key: string;
  value: BankPointers | undefined;
}

export interface BankTable {
  items: BankTableItem[];
  footerText: string;
}

export interface BankTableItem {
  key: string;
  value: string;
  subValue?: string | undefined;
  tags: string[];
}

export interface BankPointers {
  items: BankPointerItem[];
}

export interface BankPointerItem {
  pointer: string;
  tags: string[];
}

export interface Tenure {
  rawTenure: string;
  tenureFormatType: TenureFormatType;
  /** will hold investor type to rate map */
  rates: { [key: string]: number };
  minTenureInDays: number;
  maxTenureInDays: number;
  tenure: string;
  tag: string;
  interestPayoutToMaturityInstructionMapping: InterestPayoutToMaturityInstructionMapping[];
  tenureInDays: number;
  tenureInMonths: number;
  tenureInYears: number;
  fdId: string;
  isHighestForInvestorType: { [key: string]: boolean };
  interestRates: { [key: string]: InterestRate };
  defaultInterestPayoutType: InterestPayoutType;
  defaultMaturityInstruction: MaturityInstruction;
  minimumDeposit: number;
  maximumDeposit: number;
}

export interface Tenure_RatesEntry {
  key: string;
  value: number;
}

export interface Tenure_IsHighestForInvestorTypeEntry {
  key: string;
  value: boolean;
}

export interface Tenure_InterestRatesEntry {
  key: string;
  value: InterestRate | undefined;
}

export interface InterestRate {
  fdId: string;
  rate: number;
  xirr: number;
}

export interface InterestPayoutToMaturityInstructionMapping {
  interestPayoutType: InterestPayoutType;
  instructions: MaturityInstruction[];
}

export interface BankSummaryResponse {
  id: string;
  name: string;
  logoUrl: string;
  shortName: string;
  highestInterestRate?: number | undefined;
}

export interface InvestableBankCompareV4 {
  comparedBanks: BankSummaryResponse[];
}

export interface NonInvestableBankCompareV4 {
  currentBank: BankSummaryResponse | undefined;
  highestBank: BankSummaryResponse | undefined;
  lineItems: CompareLineItem[];
}

export interface SearchBankResponse {
  banks: SearchBankResponse_Bank[];
}

export interface SearchBankResponse_Bank {
  bankName: string;
  bankLogo: string;
  bankId: string;
  isPartner: boolean;
}

function createBaseFDReturnCalculatorResponse(): FDReturnCalculatorResponse {
  return { extraIncome: 0, maturedAmount: 0 };
}

export const FDReturnCalculatorResponse: MessageFns<FDReturnCalculatorResponse> = {
  encode(message: FDReturnCalculatorResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.extraIncome !== 0) {
      writer.uint32(9).double(message.extraIncome);
    }
    if (message.maturedAmount !== 0) {
      writer.uint32(17).double(message.maturedAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FDReturnCalculatorResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFDReturnCalculatorResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.extraIncome = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.maturedAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FDReturnCalculatorResponse {
    return {
      extraIncome: isSet(object.extraIncome) ? globalThis.Number(object.extraIncome) : 0,
      maturedAmount: isSet(object.maturedAmount) ? globalThis.Number(object.maturedAmount) : 0,
    };
  },

  toJSON(message: FDReturnCalculatorResponse): unknown {
    const obj: any = {};
    if (message.extraIncome !== 0) {
      obj.extraIncome = message.extraIncome;
    }
    if (message.maturedAmount !== 0) {
      obj.maturedAmount = message.maturedAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FDReturnCalculatorResponse>, I>>(base?: I): FDReturnCalculatorResponse {
    return FDReturnCalculatorResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FDReturnCalculatorResponse>, I>>(object: I): FDReturnCalculatorResponse {
    const message = createBaseFDReturnCalculatorResponse();
    message.extraIncome = object.extraIncome ?? 0;
    message.maturedAmount = object.maturedAmount ?? 0;
    return message;
  },
};

function createBaseBankListingPageDataResponse(): BankListingPageDataResponse {
  return {
    bankResponse: undefined,
    fdBookingStats: "",
    assuranceLine: "",
    assuranceLogoUrl: "",
    stableMoneyAnalysis: [],
    generalCitizenInterestData: undefined,
    seniorCitizenInterestData: undefined,
    seniorCitizenRoiMaxDifference: 0,
    rateLastUpdatedAt: "",
    ratesPdfUrl: "",
    aboutBankInfo: "",
    bankProminentPersonnel: [],
    bankAward: [],
    safeBanks: [],
    bankCustomerTestimonial: [],
    investNowMessage: [],
    bankFaq: [],
    redirectDeeplink: undefined,
    isFirstTimeOfferApplicable: false,
  };
}

export const BankListingPageDataResponse: MessageFns<BankListingPageDataResponse> = {
  encode(message: BankListingPageDataResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankResponse !== undefined) {
      BankResponse.encode(message.bankResponse, writer.uint32(10).fork()).join();
    }
    if (message.fdBookingStats !== "") {
      writer.uint32(18).string(message.fdBookingStats);
    }
    if (message.assuranceLine !== "") {
      writer.uint32(26).string(message.assuranceLine);
    }
    if (message.assuranceLogoUrl !== "") {
      writer.uint32(34).string(message.assuranceLogoUrl);
    }
    for (const v of message.stableMoneyAnalysis) {
      StableMoneyAnalysis.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.generalCitizenInterestData !== undefined) {
      BankListingPageInterestData.encode(message.generalCitizenInterestData, writer.uint32(50).fork()).join();
    }
    if (message.seniorCitizenInterestData !== undefined) {
      BankListingPageInterestData.encode(message.seniorCitizenInterestData, writer.uint32(58).fork()).join();
    }
    if (message.seniorCitizenRoiMaxDifference !== 0) {
      writer.uint32(81).double(message.seniorCitizenRoiMaxDifference);
    }
    if (message.rateLastUpdatedAt !== "") {
      writer.uint32(90).string(message.rateLastUpdatedAt);
    }
    if (message.ratesPdfUrl !== "") {
      writer.uint32(98).string(message.ratesPdfUrl);
    }
    if (message.aboutBankInfo !== "") {
      writer.uint32(106).string(message.aboutBankInfo);
    }
    for (const v of message.bankProminentPersonnel) {
      BankProminentPersonnel.encode(v!, writer.uint32(114).fork()).join();
    }
    for (const v of message.bankAward) {
      BankAward.encode(v!, writer.uint32(122).fork()).join();
    }
    for (const v of message.safeBanks) {
      SafeBank.encode(v!, writer.uint32(130).fork()).join();
    }
    for (const v of message.bankCustomerTestimonial) {
      BankCustomerTestimonial.encode(v!, writer.uint32(138).fork()).join();
    }
    for (const v of message.investNowMessage) {
      InvestNowMessage.encode(v!, writer.uint32(146).fork()).join();
    }
    for (const v of message.bankFaq) {
      Faq.encode(v!, writer.uint32(154).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(162).fork()).join();
    }
    if (message.isFirstTimeOfferApplicable !== false) {
      writer.uint32(168).bool(message.isFirstTimeOfferApplicable);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListingPageDataResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListingPageDataResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankResponse = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fdBookingStats = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.assuranceLine = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.assuranceLogoUrl = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.stableMoneyAnalysis.push(StableMoneyAnalysis.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.generalCitizenInterestData = BankListingPageInterestData.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.seniorCitizenInterestData = BankListingPageInterestData.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 81) {
            break;
          }

          message.seniorCitizenRoiMaxDifference = reader.double();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.rateLastUpdatedAt = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.ratesPdfUrl = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.aboutBankInfo = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.bankProminentPersonnel.push(BankProminentPersonnel.decode(reader, reader.uint32()));
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.bankAward.push(BankAward.decode(reader, reader.uint32()));
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.safeBanks.push(SafeBank.decode(reader, reader.uint32()));
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.bankCustomerTestimonial.push(BankCustomerTestimonial.decode(reader, reader.uint32()));
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.investNowMessage.push(InvestNowMessage.decode(reader, reader.uint32()));
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.bankFaq.push(Faq.decode(reader, reader.uint32()));
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 21: {
          if (tag !== 168) {
            break;
          }

          message.isFirstTimeOfferApplicable = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListingPageDataResponse {
    return {
      bankResponse: isSet(object.bankResponse) ? BankResponse.fromJSON(object.bankResponse) : undefined,
      fdBookingStats: isSet(object.fdBookingStats) ? globalThis.String(object.fdBookingStats) : "",
      assuranceLine: isSet(object.assuranceLine) ? globalThis.String(object.assuranceLine) : "",
      assuranceLogoUrl: isSet(object.assuranceLogoUrl) ? globalThis.String(object.assuranceLogoUrl) : "",
      stableMoneyAnalysis: globalThis.Array.isArray(object?.stableMoneyAnalysis)
        ? object.stableMoneyAnalysis.map((e: any) => StableMoneyAnalysis.fromJSON(e))
        : [],
      generalCitizenInterestData: isSet(object.generalCitizenInterestData)
        ? BankListingPageInterestData.fromJSON(object.generalCitizenInterestData)
        : undefined,
      seniorCitizenInterestData: isSet(object.seniorCitizenInterestData)
        ? BankListingPageInterestData.fromJSON(object.seniorCitizenInterestData)
        : undefined,
      seniorCitizenRoiMaxDifference: isSet(object.seniorCitizenRoiMaxDifference)
        ? globalThis.Number(object.seniorCitizenRoiMaxDifference)
        : 0,
      rateLastUpdatedAt: isSet(object.rateLastUpdatedAt) ? globalThis.String(object.rateLastUpdatedAt) : "",
      ratesPdfUrl: isSet(object.ratesPdfUrl) ? globalThis.String(object.ratesPdfUrl) : "",
      aboutBankInfo: isSet(object.aboutBankInfo) ? globalThis.String(object.aboutBankInfo) : "",
      bankProminentPersonnel: globalThis.Array.isArray(object?.bankProminentPersonnel)
        ? object.bankProminentPersonnel.map((e: any) => BankProminentPersonnel.fromJSON(e))
        : [],
      bankAward: globalThis.Array.isArray(object?.bankAward)
        ? object.bankAward.map((e: any) => BankAward.fromJSON(e))
        : [],
      safeBanks: globalThis.Array.isArray(object?.safeBanks)
        ? object.safeBanks.map((e: any) => SafeBank.fromJSON(e))
        : [],
      bankCustomerTestimonial: globalThis.Array.isArray(object?.bankCustomerTestimonial)
        ? object.bankCustomerTestimonial.map((e: any) => BankCustomerTestimonial.fromJSON(e))
        : [],
      investNowMessage: globalThis.Array.isArray(object?.investNowMessage)
        ? object.investNowMessage.map((e: any) => InvestNowMessage.fromJSON(e))
        : [],
      bankFaq: globalThis.Array.isArray(object?.bankFaq) ? object.bankFaq.map((e: any) => Faq.fromJSON(e)) : [],
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      isFirstTimeOfferApplicable: isSet(object.isFirstTimeOfferApplicable)
        ? globalThis.Boolean(object.isFirstTimeOfferApplicable)
        : false,
    };
  },

  toJSON(message: BankListingPageDataResponse): unknown {
    const obj: any = {};
    if (message.bankResponse !== undefined) {
      obj.bankResponse = BankResponse.toJSON(message.bankResponse);
    }
    if (message.fdBookingStats !== "") {
      obj.fdBookingStats = message.fdBookingStats;
    }
    if (message.assuranceLine !== "") {
      obj.assuranceLine = message.assuranceLine;
    }
    if (message.assuranceLogoUrl !== "") {
      obj.assuranceLogoUrl = message.assuranceLogoUrl;
    }
    if (message.stableMoneyAnalysis?.length) {
      obj.stableMoneyAnalysis = message.stableMoneyAnalysis.map((e) => StableMoneyAnalysis.toJSON(e));
    }
    if (message.generalCitizenInterestData !== undefined) {
      obj.generalCitizenInterestData = BankListingPageInterestData.toJSON(message.generalCitizenInterestData);
    }
    if (message.seniorCitizenInterestData !== undefined) {
      obj.seniorCitizenInterestData = BankListingPageInterestData.toJSON(message.seniorCitizenInterestData);
    }
    if (message.seniorCitizenRoiMaxDifference !== 0) {
      obj.seniorCitizenRoiMaxDifference = message.seniorCitizenRoiMaxDifference;
    }
    if (message.rateLastUpdatedAt !== "") {
      obj.rateLastUpdatedAt = message.rateLastUpdatedAt;
    }
    if (message.ratesPdfUrl !== "") {
      obj.ratesPdfUrl = message.ratesPdfUrl;
    }
    if (message.aboutBankInfo !== "") {
      obj.aboutBankInfo = message.aboutBankInfo;
    }
    if (message.bankProminentPersonnel?.length) {
      obj.bankProminentPersonnel = message.bankProminentPersonnel.map((e) => BankProminentPersonnel.toJSON(e));
    }
    if (message.bankAward?.length) {
      obj.bankAward = message.bankAward.map((e) => BankAward.toJSON(e));
    }
    if (message.safeBanks?.length) {
      obj.safeBanks = message.safeBanks.map((e) => SafeBank.toJSON(e));
    }
    if (message.bankCustomerTestimonial?.length) {
      obj.bankCustomerTestimonial = message.bankCustomerTestimonial.map((e) => BankCustomerTestimonial.toJSON(e));
    }
    if (message.investNowMessage?.length) {
      obj.investNowMessage = message.investNowMessage.map((e) => InvestNowMessage.toJSON(e));
    }
    if (message.bankFaq?.length) {
      obj.bankFaq = message.bankFaq.map((e) => Faq.toJSON(e));
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.isFirstTimeOfferApplicable !== false) {
      obj.isFirstTimeOfferApplicable = message.isFirstTimeOfferApplicable;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListingPageDataResponse>, I>>(base?: I): BankListingPageDataResponse {
    return BankListingPageDataResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListingPageDataResponse>, I>>(object: I): BankListingPageDataResponse {
    const message = createBaseBankListingPageDataResponse();
    message.bankResponse = (object.bankResponse !== undefined && object.bankResponse !== null)
      ? BankResponse.fromPartial(object.bankResponse)
      : undefined;
    message.fdBookingStats = object.fdBookingStats ?? "";
    message.assuranceLine = object.assuranceLine ?? "";
    message.assuranceLogoUrl = object.assuranceLogoUrl ?? "";
    message.stableMoneyAnalysis = object.stableMoneyAnalysis?.map((e) => StableMoneyAnalysis.fromPartial(e)) || [];
    message.generalCitizenInterestData =
      (object.generalCitizenInterestData !== undefined && object.generalCitizenInterestData !== null)
        ? BankListingPageInterestData.fromPartial(object.generalCitizenInterestData)
        : undefined;
    message.seniorCitizenInterestData =
      (object.seniorCitizenInterestData !== undefined && object.seniorCitizenInterestData !== null)
        ? BankListingPageInterestData.fromPartial(object.seniorCitizenInterestData)
        : undefined;
    message.seniorCitizenRoiMaxDifference = object.seniorCitizenRoiMaxDifference ?? 0;
    message.rateLastUpdatedAt = object.rateLastUpdatedAt ?? "";
    message.ratesPdfUrl = object.ratesPdfUrl ?? "";
    message.aboutBankInfo = object.aboutBankInfo ?? "";
    message.bankProminentPersonnel = object.bankProminentPersonnel?.map((e) => BankProminentPersonnel.fromPartial(e)) ||
      [];
    message.bankAward = object.bankAward?.map((e) => BankAward.fromPartial(e)) || [];
    message.safeBanks = object.safeBanks?.map((e) => SafeBank.fromPartial(e)) || [];
    message.bankCustomerTestimonial =
      object.bankCustomerTestimonial?.map((e) => BankCustomerTestimonial.fromPartial(e)) || [];
    message.investNowMessage = object.investNowMessage?.map((e) => InvestNowMessage.fromPartial(e)) || [];
    message.bankFaq = object.bankFaq?.map((e) => Faq.fromPartial(e)) || [];
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.isFirstTimeOfferApplicable = object.isFirstTimeOfferApplicable ?? false;
    return message;
  },
};

function createBaseBankListingPageInterestData(): BankListingPageInterestData {
  return {
    highestInterestFd: undefined,
    fixedDeposits: [],
    recommendedFds: [],
    disclaimer: "",
    investorType: 0,
    roiMaxDifferenceFromGeneral: 0,
  };
}

export const BankListingPageInterestData: MessageFns<BankListingPageInterestData> = {
  encode(message: BankListingPageInterestData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.highestInterestFd !== undefined) {
      FixedDepositResponse.encode(message.highestInterestFd, writer.uint32(10).fork()).join();
    }
    for (const v of message.fixedDeposits) {
      FixedDepositResponse.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.recommendedFds) {
      RecommendedFd.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.disclaimer !== "") {
      writer.uint32(34).string(message.disclaimer);
    }
    if (message.investorType !== 0) {
      writer.uint32(40).int32(message.investorType);
    }
    if (message.roiMaxDifferenceFromGeneral !== 0) {
      writer.uint32(49).double(message.roiMaxDifferenceFromGeneral);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListingPageInterestData {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListingPageInterestData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.highestInterestFd = FixedDepositResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fixedDeposits.push(FixedDepositResponse.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.recommendedFds.push(RecommendedFd.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.disclaimer = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.investorType = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.roiMaxDifferenceFromGeneral = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListingPageInterestData {
    return {
      highestInterestFd: isSet(object.highestInterestFd)
        ? FixedDepositResponse.fromJSON(object.highestInterestFd)
        : undefined,
      fixedDeposits: globalThis.Array.isArray(object?.fixedDeposits)
        ? object.fixedDeposits.map((e: any) => FixedDepositResponse.fromJSON(e))
        : [],
      recommendedFds: globalThis.Array.isArray(object?.recommendedFds)
        ? object.recommendedFds.map((e: any) => RecommendedFd.fromJSON(e))
        : [],
      disclaimer: isSet(object.disclaimer) ? globalThis.String(object.disclaimer) : "",
      investorType: isSet(object.investorType) ? investorTypeFromJSON(object.investorType) : 0,
      roiMaxDifferenceFromGeneral: isSet(object.roiMaxDifferenceFromGeneral)
        ? globalThis.Number(object.roiMaxDifferenceFromGeneral)
        : 0,
    };
  },

  toJSON(message: BankListingPageInterestData): unknown {
    const obj: any = {};
    if (message.highestInterestFd !== undefined) {
      obj.highestInterestFd = FixedDepositResponse.toJSON(message.highestInterestFd);
    }
    if (message.fixedDeposits?.length) {
      obj.fixedDeposits = message.fixedDeposits.map((e) => FixedDepositResponse.toJSON(e));
    }
    if (message.recommendedFds?.length) {
      obj.recommendedFds = message.recommendedFds.map((e) => RecommendedFd.toJSON(e));
    }
    if (message.disclaimer !== "") {
      obj.disclaimer = message.disclaimer;
    }
    if (message.investorType !== 0) {
      obj.investorType = investorTypeToJSON(message.investorType);
    }
    if (message.roiMaxDifferenceFromGeneral !== 0) {
      obj.roiMaxDifferenceFromGeneral = message.roiMaxDifferenceFromGeneral;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListingPageInterestData>, I>>(base?: I): BankListingPageInterestData {
    return BankListingPageInterestData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListingPageInterestData>, I>>(object: I): BankListingPageInterestData {
    const message = createBaseBankListingPageInterestData();
    message.highestInterestFd = (object.highestInterestFd !== undefined && object.highestInterestFd !== null)
      ? FixedDepositResponse.fromPartial(object.highestInterestFd)
      : undefined;
    message.fixedDeposits = object.fixedDeposits?.map((e) => FixedDepositResponse.fromPartial(e)) || [];
    message.recommendedFds = object.recommendedFds?.map((e) => RecommendedFd.fromPartial(e)) || [];
    message.disclaimer = object.disclaimer ?? "";
    message.investorType = object.investorType ?? 0;
    message.roiMaxDifferenceFromGeneral = object.roiMaxDifferenceFromGeneral ?? 0;
    return message;
  },
};

function createBaseInvestNowMessage(): InvestNowMessage {
  return { message: "" };
}

export const InvestNowMessage: MessageFns<InvestNowMessage> = {
  encode(message: InvestNowMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.message !== "") {
      writer.uint32(10).string(message.message);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestNowMessage {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestNowMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.message = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestNowMessage {
    return { message: isSet(object.message) ? globalThis.String(object.message) : "" };
  },

  toJSON(message: InvestNowMessage): unknown {
    const obj: any = {};
    if (message.message !== "") {
      obj.message = message.message;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestNowMessage>, I>>(base?: I): InvestNowMessage {
    return InvestNowMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestNowMessage>, I>>(object: I): InvestNowMessage {
    const message = createBaseInvestNowMessage();
    message.message = object.message ?? "";
    return message;
  },
};

function createBaseStableMoneyAnalysis(): StableMoneyAnalysis {
  return { description: "" };
}

export const StableMoneyAnalysis: MessageFns<StableMoneyAnalysis> = {
  encode(message: StableMoneyAnalysis, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.description !== "") {
      writer.uint32(10).string(message.description);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StableMoneyAnalysis {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStableMoneyAnalysis();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.description = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StableMoneyAnalysis {
    return { description: isSet(object.description) ? globalThis.String(object.description) : "" };
  },

  toJSON(message: StableMoneyAnalysis): unknown {
    const obj: any = {};
    if (message.description !== "") {
      obj.description = message.description;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StableMoneyAnalysis>, I>>(base?: I): StableMoneyAnalysis {
    return StableMoneyAnalysis.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StableMoneyAnalysis>, I>>(object: I): StableMoneyAnalysis {
    const message = createBaseStableMoneyAnalysis();
    message.description = object.description ?? "";
    return message;
  },
};

function createBaseSafeBank(): SafeBank {
  return { bankId: "", logoUrl: "" };
}

export const SafeBank: MessageFns<SafeBank> = {
  encode(message: SafeBank, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    if (message.logoUrl !== "") {
      writer.uint32(18).string(message.logoUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SafeBank {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSafeBank();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SafeBank {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
    };
  },

  toJSON(message: SafeBank): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SafeBank>, I>>(base?: I): SafeBank {
    return SafeBank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SafeBank>, I>>(object: I): SafeBank {
    const message = createBaseSafeBank();
    message.bankId = object.bankId ?? "";
    message.logoUrl = object.logoUrl ?? "";
    return message;
  },
};

function createBaseBankProminentPersonnel(): BankProminentPersonnel {
  return { heading: "", name: "", description: "", pictureUrl: "" };
}

export const BankProminentPersonnel: MessageFns<BankProminentPersonnel> = {
  encode(message: BankProminentPersonnel, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.heading !== "") {
      writer.uint32(10).string(message.heading);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.description !== "") {
      writer.uint32(26).string(message.description);
    }
    if (message.pictureUrl !== "") {
      writer.uint32(34).string(message.pictureUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankProminentPersonnel {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankProminentPersonnel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.heading = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.pictureUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankProminentPersonnel {
    return {
      heading: isSet(object.heading) ? globalThis.String(object.heading) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      pictureUrl: isSet(object.pictureUrl) ? globalThis.String(object.pictureUrl) : "",
    };
  },

  toJSON(message: BankProminentPersonnel): unknown {
    const obj: any = {};
    if (message.heading !== "") {
      obj.heading = message.heading;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.pictureUrl !== "") {
      obj.pictureUrl = message.pictureUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankProminentPersonnel>, I>>(base?: I): BankProminentPersonnel {
    return BankProminentPersonnel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankProminentPersonnel>, I>>(object: I): BankProminentPersonnel {
    const message = createBaseBankProminentPersonnel();
    message.heading = object.heading ?? "";
    message.name = object.name ?? "";
    message.description = object.description ?? "";
    message.pictureUrl = object.pictureUrl ?? "";
    return message;
  },
};

function createBaseBankAward(): BankAward {
  return { title: "", iconUrl: "", receivedBy: "" };
}

export const BankAward: MessageFns<BankAward> = {
  encode(message: BankAward, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.iconUrl !== "") {
      writer.uint32(18).string(message.iconUrl);
    }
    if (message.receivedBy !== "") {
      writer.uint32(26).string(message.receivedBy);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankAward {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankAward();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.receivedBy = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankAward {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      receivedBy: isSet(object.receivedBy) ? globalThis.String(object.receivedBy) : "",
    };
  },

  toJSON(message: BankAward): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.receivedBy !== "") {
      obj.receivedBy = message.receivedBy;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankAward>, I>>(base?: I): BankAward {
    return BankAward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankAward>, I>>(object: I): BankAward {
    const message = createBaseBankAward();
    message.title = object.title ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.receivedBy = object.receivedBy ?? "";
    return message;
  },
};

function createBaseBankCustomerTestimonial(): BankCustomerTestimonial {
  return { comment: "", name: "", designation: "", pictureUrl: "" };
}

export const BankCustomerTestimonial: MessageFns<BankCustomerTestimonial> = {
  encode(message: BankCustomerTestimonial, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.comment !== "") {
      writer.uint32(10).string(message.comment);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.designation !== "") {
      writer.uint32(26).string(message.designation);
    }
    if (message.pictureUrl !== "") {
      writer.uint32(34).string(message.pictureUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankCustomerTestimonial {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankCustomerTestimonial();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.comment = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.designation = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.pictureUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankCustomerTestimonial {
    return {
      comment: isSet(object.comment) ? globalThis.String(object.comment) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      designation: isSet(object.designation) ? globalThis.String(object.designation) : "",
      pictureUrl: isSet(object.pictureUrl) ? globalThis.String(object.pictureUrl) : "",
    };
  },

  toJSON(message: BankCustomerTestimonial): unknown {
    const obj: any = {};
    if (message.comment !== "") {
      obj.comment = message.comment;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.designation !== "") {
      obj.designation = message.designation;
    }
    if (message.pictureUrl !== "") {
      obj.pictureUrl = message.pictureUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankCustomerTestimonial>, I>>(base?: I): BankCustomerTestimonial {
    return BankCustomerTestimonial.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankCustomerTestimonial>, I>>(object: I): BankCustomerTestimonial {
    const message = createBaseBankCustomerTestimonial();
    message.comment = object.comment ?? "";
    message.name = object.name ?? "";
    message.designation = object.designation ?? "";
    message.pictureUrl = object.pictureUrl ?? "";
    return message;
  },
};

function createBaseRecommendedFd(): RecommendedFd {
  return { description: "", fixedDepositResponse: undefined, iconUrl: "" };
}

export const RecommendedFd: MessageFns<RecommendedFd> = {
  encode(message: RecommendedFd, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.description !== "") {
      writer.uint32(10).string(message.description);
    }
    if (message.fixedDepositResponse !== undefined) {
      FixedDepositResponse.encode(message.fixedDepositResponse, writer.uint32(18).fork()).join();
    }
    if (message.iconUrl !== "") {
      writer.uint32(26).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendedFd {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendedFd();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fixedDepositResponse = FixedDepositResponse.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecommendedFd {
    return {
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      fixedDepositResponse: isSet(object.fixedDepositResponse)
        ? FixedDepositResponse.fromJSON(object.fixedDepositResponse)
        : undefined,
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: RecommendedFd): unknown {
    const obj: any = {};
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.fixedDepositResponse !== undefined) {
      obj.fixedDepositResponse = FixedDepositResponse.toJSON(message.fixedDepositResponse);
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendedFd>, I>>(base?: I): RecommendedFd {
    return RecommendedFd.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendedFd>, I>>(object: I): RecommendedFd {
    const message = createBaseRecommendedFd();
    message.description = object.description ?? "";
    message.fixedDepositResponse = (object.fixedDepositResponse !== undefined && object.fixedDepositResponse !== null)
      ? FixedDepositResponse.fromPartial(object.fixedDepositResponse)
      : undefined;
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBaseBankListingV2(): BankListingV2 {
  return {
    bankResponse: undefined,
    topInfoCard: [],
    bankTags: [],
    interestData: [],
    bankSellingPoints: [],
    redirectDeeplink: undefined,
    fdBookingStats: "",
    bankFaq: [],
    rateLastUpdatedAt: "",
    ratesPdfUrl: "",
    assuranceLine: "",
    assuranceLogoUrl: "",
    isFirstTimeOfferApplicable: false,
    isVkycRequired: false,
    bankFaqCategoryPriorityList: [],
  };
}

export const BankListingV2: MessageFns<BankListingV2> = {
  encode(message: BankListingV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankResponse !== undefined) {
      BankResponse.encode(message.bankResponse, writer.uint32(10).fork()).join();
    }
    for (const v of message.topInfoCard) {
      TopInfoCard.encode(v!, writer.uint32(18).fork()).join();
    }
    for (const v of message.bankTags) {
      BankTag.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.interestData) {
      BankListingPageInterestData.encode(v!, writer.uint32(34).fork()).join();
    }
    for (const v of message.bankSellingPoints) {
      BankSellingPoint.encode(v!, writer.uint32(42).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(50).fork()).join();
    }
    if (message.fdBookingStats !== "") {
      writer.uint32(58).string(message.fdBookingStats);
    }
    for (const v of message.bankFaq) {
      Faq.encode(v!, writer.uint32(66).fork()).join();
    }
    if (message.rateLastUpdatedAt !== "") {
      writer.uint32(82).string(message.rateLastUpdatedAt);
    }
    if (message.ratesPdfUrl !== "") {
      writer.uint32(90).string(message.ratesPdfUrl);
    }
    if (message.assuranceLine !== "") {
      writer.uint32(98).string(message.assuranceLine);
    }
    if (message.assuranceLogoUrl !== "") {
      writer.uint32(106).string(message.assuranceLogoUrl);
    }
    if (message.isFirstTimeOfferApplicable !== false) {
      writer.uint32(112).bool(message.isFirstTimeOfferApplicable);
    }
    if (message.isVkycRequired !== false) {
      writer.uint32(120).bool(message.isVkycRequired);
    }
    for (const v of message.bankFaqCategoryPriorityList) {
      writer.uint32(226).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListingV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListingV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankResponse = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.topInfoCard.push(TopInfoCard.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankTags.push(BankTag.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.interestData.push(BankListingPageInterestData.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bankSellingPoints.push(BankSellingPoint.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.fdBookingStats = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.bankFaq.push(Faq.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.rateLastUpdatedAt = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.ratesPdfUrl = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.assuranceLine = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.assuranceLogoUrl = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.isFirstTimeOfferApplicable = reader.bool();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.isVkycRequired = reader.bool();
          continue;
        }
        case 28: {
          if (tag !== 226) {
            break;
          }

          message.bankFaqCategoryPriorityList.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListingV2 {
    return {
      bankResponse: isSet(object.bankResponse) ? BankResponse.fromJSON(object.bankResponse) : undefined,
      topInfoCard: globalThis.Array.isArray(object?.topInfoCard)
        ? object.topInfoCard.map((e: any) => TopInfoCard.fromJSON(e))
        : [],
      bankTags: globalThis.Array.isArray(object?.bankTags) ? object.bankTags.map((e: any) => BankTag.fromJSON(e)) : [],
      interestData: globalThis.Array.isArray(object?.interestData)
        ? object.interestData.map((e: any) => BankListingPageInterestData.fromJSON(e))
        : [],
      bankSellingPoints: globalThis.Array.isArray(object?.bankSellingPoints)
        ? object.bankSellingPoints.map((e: any) => BankSellingPoint.fromJSON(e))
        : [],
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      fdBookingStats: isSet(object.fdBookingStats) ? globalThis.String(object.fdBookingStats) : "",
      bankFaq: globalThis.Array.isArray(object?.bankFaq) ? object.bankFaq.map((e: any) => Faq.fromJSON(e)) : [],
      rateLastUpdatedAt: isSet(object.rateLastUpdatedAt) ? globalThis.String(object.rateLastUpdatedAt) : "",
      ratesPdfUrl: isSet(object.ratesPdfUrl) ? globalThis.String(object.ratesPdfUrl) : "",
      assuranceLine: isSet(object.assuranceLine) ? globalThis.String(object.assuranceLine) : "",
      assuranceLogoUrl: isSet(object.assuranceLogoUrl) ? globalThis.String(object.assuranceLogoUrl) : "",
      isFirstTimeOfferApplicable: isSet(object.isFirstTimeOfferApplicable)
        ? globalThis.Boolean(object.isFirstTimeOfferApplicable)
        : false,
      isVkycRequired: isSet(object.isVkycRequired) ? globalThis.Boolean(object.isVkycRequired) : false,
      bankFaqCategoryPriorityList: globalThis.Array.isArray(object?.bankFaqCategoryPriorityList)
        ? object.bankFaqCategoryPriorityList.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: BankListingV2): unknown {
    const obj: any = {};
    if (message.bankResponse !== undefined) {
      obj.bankResponse = BankResponse.toJSON(message.bankResponse);
    }
    if (message.topInfoCard?.length) {
      obj.topInfoCard = message.topInfoCard.map((e) => TopInfoCard.toJSON(e));
    }
    if (message.bankTags?.length) {
      obj.bankTags = message.bankTags.map((e) => BankTag.toJSON(e));
    }
    if (message.interestData?.length) {
      obj.interestData = message.interestData.map((e) => BankListingPageInterestData.toJSON(e));
    }
    if (message.bankSellingPoints?.length) {
      obj.bankSellingPoints = message.bankSellingPoints.map((e) => BankSellingPoint.toJSON(e));
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.fdBookingStats !== "") {
      obj.fdBookingStats = message.fdBookingStats;
    }
    if (message.bankFaq?.length) {
      obj.bankFaq = message.bankFaq.map((e) => Faq.toJSON(e));
    }
    if (message.rateLastUpdatedAt !== "") {
      obj.rateLastUpdatedAt = message.rateLastUpdatedAt;
    }
    if (message.ratesPdfUrl !== "") {
      obj.ratesPdfUrl = message.ratesPdfUrl;
    }
    if (message.assuranceLine !== "") {
      obj.assuranceLine = message.assuranceLine;
    }
    if (message.assuranceLogoUrl !== "") {
      obj.assuranceLogoUrl = message.assuranceLogoUrl;
    }
    if (message.isFirstTimeOfferApplicable !== false) {
      obj.isFirstTimeOfferApplicable = message.isFirstTimeOfferApplicable;
    }
    if (message.isVkycRequired !== false) {
      obj.isVkycRequired = message.isVkycRequired;
    }
    if (message.bankFaqCategoryPriorityList?.length) {
      obj.bankFaqCategoryPriorityList = message.bankFaqCategoryPriorityList;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListingV2>, I>>(base?: I): BankListingV2 {
    return BankListingV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListingV2>, I>>(object: I): BankListingV2 {
    const message = createBaseBankListingV2();
    message.bankResponse = (object.bankResponse !== undefined && object.bankResponse !== null)
      ? BankResponse.fromPartial(object.bankResponse)
      : undefined;
    message.topInfoCard = object.topInfoCard?.map((e) => TopInfoCard.fromPartial(e)) || [];
    message.bankTags = object.bankTags?.map((e) => BankTag.fromPartial(e)) || [];
    message.interestData = object.interestData?.map((e) => BankListingPageInterestData.fromPartial(e)) || [];
    message.bankSellingPoints = object.bankSellingPoints?.map((e) => BankSellingPoint.fromPartial(e)) || [];
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.fdBookingStats = object.fdBookingStats ?? "";
    message.bankFaq = object.bankFaq?.map((e) => Faq.fromPartial(e)) || [];
    message.rateLastUpdatedAt = object.rateLastUpdatedAt ?? "";
    message.ratesPdfUrl = object.ratesPdfUrl ?? "";
    message.assuranceLine = object.assuranceLine ?? "";
    message.assuranceLogoUrl = object.assuranceLogoUrl ?? "";
    message.isFirstTimeOfferApplicable = object.isFirstTimeOfferApplicable ?? false;
    message.isVkycRequired = object.isVkycRequired ?? false;
    message.bankFaqCategoryPriorityList = object.bankFaqCategoryPriorityList?.map((e) => e) || [];
    return message;
  },
};

function createBaseHighestRateKeyValue(): HighestRateKeyValue {
  return { investorType: 0, fixedDeposit: undefined };
}

export const HighestRateKeyValue: MessageFns<HighestRateKeyValue> = {
  encode(message: HighestRateKeyValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.investorType !== 0) {
      writer.uint32(8).int32(message.investorType);
    }
    if (message.fixedDeposit !== undefined) {
      FixedDepositResponse.encode(message.fixedDeposit, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HighestRateKeyValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHighestRateKeyValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.investorType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fixedDeposit = FixedDepositResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HighestRateKeyValue {
    return {
      investorType: isSet(object.investorType) ? investorTypeFromJSON(object.investorType) : 0,
      fixedDeposit: isSet(object.fixedDeposit) ? FixedDepositResponse.fromJSON(object.fixedDeposit) : undefined,
    };
  },

  toJSON(message: HighestRateKeyValue): unknown {
    const obj: any = {};
    if (message.investorType !== 0) {
      obj.investorType = investorTypeToJSON(message.investorType);
    }
    if (message.fixedDeposit !== undefined) {
      obj.fixedDeposit = FixedDepositResponse.toJSON(message.fixedDeposit);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HighestRateKeyValue>, I>>(base?: I): HighestRateKeyValue {
    return HighestRateKeyValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HighestRateKeyValue>, I>>(object: I): HighestRateKeyValue {
    const message = createBaseHighestRateKeyValue();
    message.investorType = object.investorType ?? 0;
    message.fixedDeposit = (object.fixedDeposit !== undefined && object.fixedDeposit !== null)
      ? FixedDepositResponse.fromPartial(object.fixedDeposit)
      : undefined;
    return message;
  },
};

function createBaseInvestableBankCompare(): InvestableBankCompare {
  return { interestRate: 0, bankResponse: undefined };
}

export const InvestableBankCompare: MessageFns<InvestableBankCompare> = {
  encode(message: InvestableBankCompare, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.interestRate !== 0) {
      writer.uint32(9).double(message.interestRate);
    }
    if (message.bankResponse !== undefined) {
      BankResponse.encode(message.bankResponse, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestableBankCompare {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestableBankCompare();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.interestRate = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankResponse = BankResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestableBankCompare {
    return {
      interestRate: isSet(object.interestRate) ? globalThis.Number(object.interestRate) : 0,
      bankResponse: isSet(object.bankResponse) ? BankResponse.fromJSON(object.bankResponse) : undefined,
    };
  },

  toJSON(message: InvestableBankCompare): unknown {
    const obj: any = {};
    if (message.interestRate !== 0) {
      obj.interestRate = message.interestRate;
    }
    if (message.bankResponse !== undefined) {
      obj.bankResponse = BankResponse.toJSON(message.bankResponse);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestableBankCompare>, I>>(base?: I): InvestableBankCompare {
    return InvestableBankCompare.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestableBankCompare>, I>>(object: I): InvestableBankCompare {
    const message = createBaseInvestableBankCompare();
    message.interestRate = object.interestRate ?? 0;
    message.bankResponse = (object.bankResponse !== undefined && object.bankResponse !== null)
      ? BankResponse.fromPartial(object.bankResponse)
      : undefined;
    return message;
  },
};

function createBaseNonInvestableBankCompare(): NonInvestableBankCompare {
  return { titles: [], highestBankDetails: [], bankDetails: [], highestRateBankResponse: undefined, lineItems: [] };
}

export const NonInvestableBankCompare: MessageFns<NonInvestableBankCompare> = {
  encode(message: NonInvestableBankCompare, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.titles) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.highestBankDetails) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.bankDetails) {
      writer.uint32(26).string(v!);
    }
    if (message.highestRateBankResponse !== undefined) {
      BankResponse.encode(message.highestRateBankResponse, writer.uint32(34).fork()).join();
    }
    for (const v of message.lineItems) {
      NonInvestableBankCompare_CompareLineItem.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NonInvestableBankCompare {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNonInvestableBankCompare();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.titles.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.highestBankDetails.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankDetails.push(reader.string());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.highestRateBankResponse = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.lineItems.push(NonInvestableBankCompare_CompareLineItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NonInvestableBankCompare {
    return {
      titles: globalThis.Array.isArray(object?.titles) ? object.titles.map((e: any) => globalThis.String(e)) : [],
      highestBankDetails: globalThis.Array.isArray(object?.highestBankDetails)
        ? object.highestBankDetails.map((e: any) => globalThis.String(e))
        : [],
      bankDetails: globalThis.Array.isArray(object?.bankDetails)
        ? object.bankDetails.map((e: any) => globalThis.String(e))
        : [],
      highestRateBankResponse: isSet(object.highestRateBankResponse)
        ? BankResponse.fromJSON(object.highestRateBankResponse)
        : undefined,
      lineItems: globalThis.Array.isArray(object?.lineItems)
        ? object.lineItems.map((e: any) => NonInvestableBankCompare_CompareLineItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: NonInvestableBankCompare): unknown {
    const obj: any = {};
    if (message.titles?.length) {
      obj.titles = message.titles;
    }
    if (message.highestBankDetails?.length) {
      obj.highestBankDetails = message.highestBankDetails;
    }
    if (message.bankDetails?.length) {
      obj.bankDetails = message.bankDetails;
    }
    if (message.highestRateBankResponse !== undefined) {
      obj.highestRateBankResponse = BankResponse.toJSON(message.highestRateBankResponse);
    }
    if (message.lineItems?.length) {
      obj.lineItems = message.lineItems.map((e) => NonInvestableBankCompare_CompareLineItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NonInvestableBankCompare>, I>>(base?: I): NonInvestableBankCompare {
    return NonInvestableBankCompare.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NonInvestableBankCompare>, I>>(object: I): NonInvestableBankCompare {
    const message = createBaseNonInvestableBankCompare();
    message.titles = object.titles?.map((e) => e) || [];
    message.highestBankDetails = object.highestBankDetails?.map((e) => e) || [];
    message.bankDetails = object.bankDetails?.map((e) => e) || [];
    message.highestRateBankResponse =
      (object.highestRateBankResponse !== undefined && object.highestRateBankResponse !== null)
        ? BankResponse.fromPartial(object.highestRateBankResponse)
        : undefined;
    message.lineItems = object.lineItems?.map((e) => NonInvestableBankCompare_CompareLineItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseNonInvestableBankCompare_CompareLineItem(): NonInvestableBankCompare_CompareLineItem {
  return { title: "", highestBankPoint: "", currentBankPoint: "", moreDetailsLink: undefined };
}

export const NonInvestableBankCompare_CompareLineItem: MessageFns<NonInvestableBankCompare_CompareLineItem> = {
  encode(message: NonInvestableBankCompare_CompareLineItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.highestBankPoint !== "") {
      writer.uint32(18).string(message.highestBankPoint);
    }
    if (message.currentBankPoint !== "") {
      writer.uint32(26).string(message.currentBankPoint);
    }
    if (message.moreDetailsLink !== undefined) {
      RedirectDeeplink.encode(message.moreDetailsLink, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NonInvestableBankCompare_CompareLineItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNonInvestableBankCompare_CompareLineItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.highestBankPoint = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.currentBankPoint = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.moreDetailsLink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NonInvestableBankCompare_CompareLineItem {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      highestBankPoint: isSet(object.highestBankPoint) ? globalThis.String(object.highestBankPoint) : "",
      currentBankPoint: isSet(object.currentBankPoint) ? globalThis.String(object.currentBankPoint) : "",
      moreDetailsLink: isSet(object.moreDetailsLink) ? RedirectDeeplink.fromJSON(object.moreDetailsLink) : undefined,
    };
  },

  toJSON(message: NonInvestableBankCompare_CompareLineItem): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.highestBankPoint !== "") {
      obj.highestBankPoint = message.highestBankPoint;
    }
    if (message.currentBankPoint !== "") {
      obj.currentBankPoint = message.currentBankPoint;
    }
    if (message.moreDetailsLink !== undefined) {
      obj.moreDetailsLink = RedirectDeeplink.toJSON(message.moreDetailsLink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NonInvestableBankCompare_CompareLineItem>, I>>(
    base?: I,
  ): NonInvestableBankCompare_CompareLineItem {
    return NonInvestableBankCompare_CompareLineItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NonInvestableBankCompare_CompareLineItem>, I>>(
    object: I,
  ): NonInvestableBankCompare_CompareLineItem {
    const message = createBaseNonInvestableBankCompare_CompareLineItem();
    message.title = object.title ?? "";
    message.highestBankPoint = object.highestBankPoint ?? "";
    message.currentBankPoint = object.currentBankPoint ?? "";
    message.moreDetailsLink = (object.moreDetailsLink !== undefined && object.moreDetailsLink !== null)
      ? RedirectDeeplink.fromPartial(object.moreDetailsLink)
      : undefined;
    return message;
  },
};

function createBaseFdWithdrawalCalculation(): FdWithdrawalCalculation {
  return { tenure: "", withdrawalAmount: 0, rate: 0, originalRate: 0 };
}

export const FdWithdrawalCalculation: MessageFns<FdWithdrawalCalculation> = {
  encode(message: FdWithdrawalCalculation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tenure !== "") {
      writer.uint32(10).string(message.tenure);
    }
    if (message.withdrawalAmount !== 0) {
      writer.uint32(17).double(message.withdrawalAmount);
    }
    if (message.rate !== 0) {
      writer.uint32(25).double(message.rate);
    }
    if (message.originalRate !== 0) {
      writer.uint32(33).double(message.originalRate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FdWithdrawalCalculation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFdWithdrawalCalculation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.withdrawalAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.originalRate = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FdWithdrawalCalculation {
    return {
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      withdrawalAmount: isSet(object.withdrawalAmount) ? globalThis.Number(object.withdrawalAmount) : 0,
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      originalRate: isSet(object.originalRate) ? globalThis.Number(object.originalRate) : 0,
    };
  },

  toJSON(message: FdWithdrawalCalculation): unknown {
    const obj: any = {};
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.withdrawalAmount !== 0) {
      obj.withdrawalAmount = message.withdrawalAmount;
    }
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.originalRate !== 0) {
      obj.originalRate = message.originalRate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FdWithdrawalCalculation>, I>>(base?: I): FdWithdrawalCalculation {
    return FdWithdrawalCalculation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FdWithdrawalCalculation>, I>>(object: I): FdWithdrawalCalculation {
    const message = createBaseFdWithdrawalCalculation();
    message.tenure = object.tenure ?? "";
    message.withdrawalAmount = object.withdrawalAmount ?? 0;
    message.rate = object.rate ?? 0;
    message.originalRate = object.originalRate ?? 0;
    return message;
  },
};

function createBaseFdWithdrawalCalculationDetails(): FdWithdrawalCalculationDetails {
  return { interestRate: 0, investmentAmount: 0, tenure: "" };
}

export const FdWithdrawalCalculationDetails: MessageFns<FdWithdrawalCalculationDetails> = {
  encode(message: FdWithdrawalCalculationDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.interestRate !== 0) {
      writer.uint32(9).double(message.interestRate);
    }
    if (message.investmentAmount !== 0) {
      writer.uint32(17).double(message.investmentAmount);
    }
    if (message.tenure !== "") {
      writer.uint32(26).string(message.tenure);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FdWithdrawalCalculationDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFdWithdrawalCalculationDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.interestRate = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FdWithdrawalCalculationDetails {
    return {
      interestRate: isSet(object.interestRate) ? globalThis.Number(object.interestRate) : 0,
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
    };
  },

  toJSON(message: FdWithdrawalCalculationDetails): unknown {
    const obj: any = {};
    if (message.interestRate !== 0) {
      obj.interestRate = message.interestRate;
    }
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FdWithdrawalCalculationDetails>, I>>(base?: I): FdWithdrawalCalculationDetails {
    return FdWithdrawalCalculationDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FdWithdrawalCalculationDetails>, I>>(
    object: I,
  ): FdWithdrawalCalculationDetails {
    const message = createBaseFdWithdrawalCalculationDetails();
    message.interestRate = object.interestRate ?? 0;
    message.investmentAmount = object.investmentAmount ?? 0;
    message.tenure = object.tenure ?? "";
    return message;
  },
};

function createBaseBankListingV3(): BankListingV3 {
  return {
    bankResponse: undefined,
    interestData: [],
    redirectDeeplink: undefined,
    bankFaq: [],
    isVkycRequired: false,
    highestRateMap: [],
    investableBankDownTimeMessage: "",
    investableBankCompare: [],
    nonInvestableBankCompare: undefined,
    fdWithdrawalCalculation: [],
    nonInvestableBankMessage: "",
    bankImageUrl: "",
    fdWithdrawalCalculationDetails: undefined,
    isRepeatUserToPlatform: false,
    isRepeatUserToBank: false,
    bankFaqCategoryPriorityList: [],
    deviceInvestabilityStatus: 0,
  };
}

export const BankListingV3: MessageFns<BankListingV3> = {
  encode(message: BankListingV3, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankResponse !== undefined) {
      BankResponse.encode(message.bankResponse, writer.uint32(10).fork()).join();
    }
    for (const v of message.interestData) {
      BankListingPageInterestData.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(50).fork()).join();
    }
    for (const v of message.bankFaq) {
      Faq.encode(v!, writer.uint32(66).fork()).join();
    }
    if (message.isVkycRequired !== false) {
      writer.uint32(120).bool(message.isVkycRequired);
    }
    for (const v of message.highestRateMap) {
      HighestRateKeyValue.encode(v!, writer.uint32(138).fork()).join();
    }
    if (message.investableBankDownTimeMessage !== "") {
      writer.uint32(146).string(message.investableBankDownTimeMessage);
    }
    for (const v of message.investableBankCompare) {
      InvestableBankCompare.encode(v!, writer.uint32(154).fork()).join();
    }
    if (message.nonInvestableBankCompare !== undefined) {
      NonInvestableBankCompare.encode(message.nonInvestableBankCompare, writer.uint32(162).fork()).join();
    }
    for (const v of message.fdWithdrawalCalculation) {
      FdWithdrawalCalculation.encode(v!, writer.uint32(170).fork()).join();
    }
    if (message.nonInvestableBankMessage !== "") {
      writer.uint32(178).string(message.nonInvestableBankMessage);
    }
    if (message.bankImageUrl !== "") {
      writer.uint32(186).string(message.bankImageUrl);
    }
    if (message.fdWithdrawalCalculationDetails !== undefined) {
      FdWithdrawalCalculationDetails.encode(message.fdWithdrawalCalculationDetails, writer.uint32(194).fork()).join();
    }
    if (message.isRepeatUserToPlatform !== false) {
      writer.uint32(200).bool(message.isRepeatUserToPlatform);
    }
    if (message.isRepeatUserToBank !== false) {
      writer.uint32(208).bool(message.isRepeatUserToBank);
    }
    for (const v of message.bankFaqCategoryPriorityList) {
      writer.uint32(226).string(v!);
    }
    if (message.deviceInvestabilityStatus !== 0) {
      writer.uint32(240).int32(message.deviceInvestabilityStatus);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListingV3 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListingV3();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankResponse = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.interestData.push(BankListingPageInterestData.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.bankFaq.push(Faq.decode(reader, reader.uint32()));
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.isVkycRequired = reader.bool();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.highestRateMap.push(HighestRateKeyValue.decode(reader, reader.uint32()));
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.investableBankDownTimeMessage = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.investableBankCompare.push(InvestableBankCompare.decode(reader, reader.uint32()));
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.nonInvestableBankCompare = NonInvestableBankCompare.decode(reader, reader.uint32());
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.fdWithdrawalCalculation.push(FdWithdrawalCalculation.decode(reader, reader.uint32()));
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.nonInvestableBankMessage = reader.string();
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.bankImageUrl = reader.string();
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.fdWithdrawalCalculationDetails = FdWithdrawalCalculationDetails.decode(reader, reader.uint32());
          continue;
        }
        case 25: {
          if (tag !== 200) {
            break;
          }

          message.isRepeatUserToPlatform = reader.bool();
          continue;
        }
        case 26: {
          if (tag !== 208) {
            break;
          }

          message.isRepeatUserToBank = reader.bool();
          continue;
        }
        case 28: {
          if (tag !== 226) {
            break;
          }

          message.bankFaqCategoryPriorityList.push(reader.string());
          continue;
        }
        case 30: {
          if (tag !== 240) {
            break;
          }

          message.deviceInvestabilityStatus = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListingV3 {
    return {
      bankResponse: isSet(object.bankResponse) ? BankResponse.fromJSON(object.bankResponse) : undefined,
      interestData: globalThis.Array.isArray(object?.interestData)
        ? object.interestData.map((e: any) => BankListingPageInterestData.fromJSON(e))
        : [],
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      bankFaq: globalThis.Array.isArray(object?.bankFaq) ? object.bankFaq.map((e: any) => Faq.fromJSON(e)) : [],
      isVkycRequired: isSet(object.isVkycRequired) ? globalThis.Boolean(object.isVkycRequired) : false,
      highestRateMap: globalThis.Array.isArray(object?.highestRateMap)
        ? object.highestRateMap.map((e: any) => HighestRateKeyValue.fromJSON(e))
        : [],
      investableBankDownTimeMessage: isSet(object.investableBankDownTimeMessage)
        ? globalThis.String(object.investableBankDownTimeMessage)
        : "",
      investableBankCompare: globalThis.Array.isArray(object?.investableBankCompare)
        ? object.investableBankCompare.map((e: any) => InvestableBankCompare.fromJSON(e))
        : [],
      nonInvestableBankCompare: isSet(object.nonInvestableBankCompare)
        ? NonInvestableBankCompare.fromJSON(object.nonInvestableBankCompare)
        : undefined,
      fdWithdrawalCalculation: globalThis.Array.isArray(object?.fdWithdrawalCalculation)
        ? object.fdWithdrawalCalculation.map((e: any) => FdWithdrawalCalculation.fromJSON(e))
        : [],
      nonInvestableBankMessage: isSet(object.nonInvestableBankMessage)
        ? globalThis.String(object.nonInvestableBankMessage)
        : "",
      bankImageUrl: isSet(object.bankImageUrl) ? globalThis.String(object.bankImageUrl) : "",
      fdWithdrawalCalculationDetails: isSet(object.fdWithdrawalCalculationDetails)
        ? FdWithdrawalCalculationDetails.fromJSON(object.fdWithdrawalCalculationDetails)
        : undefined,
      isRepeatUserToPlatform: isSet(object.isRepeatUserToPlatform)
        ? globalThis.Boolean(object.isRepeatUserToPlatform)
        : false,
      isRepeatUserToBank: isSet(object.isRepeatUserToBank) ? globalThis.Boolean(object.isRepeatUserToBank) : false,
      bankFaqCategoryPriorityList: globalThis.Array.isArray(object?.bankFaqCategoryPriorityList)
        ? object.bankFaqCategoryPriorityList.map((e: any) => globalThis.String(e))
        : [],
      deviceInvestabilityStatus: isSet(object.deviceInvestabilityStatus)
        ? bankListingV3_DeviceInvestabilityStatusFromJSON(object.deviceInvestabilityStatus)
        : 0,
    };
  },

  toJSON(message: BankListingV3): unknown {
    const obj: any = {};
    if (message.bankResponse !== undefined) {
      obj.bankResponse = BankResponse.toJSON(message.bankResponse);
    }
    if (message.interestData?.length) {
      obj.interestData = message.interestData.map((e) => BankListingPageInterestData.toJSON(e));
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.bankFaq?.length) {
      obj.bankFaq = message.bankFaq.map((e) => Faq.toJSON(e));
    }
    if (message.isVkycRequired !== false) {
      obj.isVkycRequired = message.isVkycRequired;
    }
    if (message.highestRateMap?.length) {
      obj.highestRateMap = message.highestRateMap.map((e) => HighestRateKeyValue.toJSON(e));
    }
    if (message.investableBankDownTimeMessage !== "") {
      obj.investableBankDownTimeMessage = message.investableBankDownTimeMessage;
    }
    if (message.investableBankCompare?.length) {
      obj.investableBankCompare = message.investableBankCompare.map((e) => InvestableBankCompare.toJSON(e));
    }
    if (message.nonInvestableBankCompare !== undefined) {
      obj.nonInvestableBankCompare = NonInvestableBankCompare.toJSON(message.nonInvestableBankCompare);
    }
    if (message.fdWithdrawalCalculation?.length) {
      obj.fdWithdrawalCalculation = message.fdWithdrawalCalculation.map((e) => FdWithdrawalCalculation.toJSON(e));
    }
    if (message.nonInvestableBankMessage !== "") {
      obj.nonInvestableBankMessage = message.nonInvestableBankMessage;
    }
    if (message.bankImageUrl !== "") {
      obj.bankImageUrl = message.bankImageUrl;
    }
    if (message.fdWithdrawalCalculationDetails !== undefined) {
      obj.fdWithdrawalCalculationDetails = FdWithdrawalCalculationDetails.toJSON(
        message.fdWithdrawalCalculationDetails,
      );
    }
    if (message.isRepeatUserToPlatform !== false) {
      obj.isRepeatUserToPlatform = message.isRepeatUserToPlatform;
    }
    if (message.isRepeatUserToBank !== false) {
      obj.isRepeatUserToBank = message.isRepeatUserToBank;
    }
    if (message.bankFaqCategoryPriorityList?.length) {
      obj.bankFaqCategoryPriorityList = message.bankFaqCategoryPriorityList;
    }
    if (message.deviceInvestabilityStatus !== 0) {
      obj.deviceInvestabilityStatus = bankListingV3_DeviceInvestabilityStatusToJSON(message.deviceInvestabilityStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListingV3>, I>>(base?: I): BankListingV3 {
    return BankListingV3.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListingV3>, I>>(object: I): BankListingV3 {
    const message = createBaseBankListingV3();
    message.bankResponse = (object.bankResponse !== undefined && object.bankResponse !== null)
      ? BankResponse.fromPartial(object.bankResponse)
      : undefined;
    message.interestData = object.interestData?.map((e) => BankListingPageInterestData.fromPartial(e)) || [];
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.bankFaq = object.bankFaq?.map((e) => Faq.fromPartial(e)) || [];
    message.isVkycRequired = object.isVkycRequired ?? false;
    message.highestRateMap = object.highestRateMap?.map((e) => HighestRateKeyValue.fromPartial(e)) || [];
    message.investableBankDownTimeMessage = object.investableBankDownTimeMessage ?? "";
    message.investableBankCompare = object.investableBankCompare?.map((e) => InvestableBankCompare.fromPartial(e)) ||
      [];
    message.nonInvestableBankCompare =
      (object.nonInvestableBankCompare !== undefined && object.nonInvestableBankCompare !== null)
        ? NonInvestableBankCompare.fromPartial(object.nonInvestableBankCompare)
        : undefined;
    message.fdWithdrawalCalculation =
      object.fdWithdrawalCalculation?.map((e) => FdWithdrawalCalculation.fromPartial(e)) || [];
    message.nonInvestableBankMessage = object.nonInvestableBankMessage ?? "";
    message.bankImageUrl = object.bankImageUrl ?? "";
    message.fdWithdrawalCalculationDetails =
      (object.fdWithdrawalCalculationDetails !== undefined && object.fdWithdrawalCalculationDetails !== null)
        ? FdWithdrawalCalculationDetails.fromPartial(object.fdWithdrawalCalculationDetails)
        : undefined;
    message.isRepeatUserToPlatform = object.isRepeatUserToPlatform ?? false;
    message.isRepeatUserToBank = object.isRepeatUserToBank ?? false;
    message.bankFaqCategoryPriorityList = object.bankFaqCategoryPriorityList?.map((e) => e) || [];
    message.deviceInvestabilityStatus = object.deviceInvestabilityStatus ?? 0;
    return message;
  },
};

function createBaseBankListing(): BankListing {
  return {
    id: "",
    slug: "",
    logoUrl: "",
    name: "",
    keywords: "",
    accountRequiredToInvest: false,
    bankType: 0,
    investabilityStatus: 0,
    faqs: [],
    withdrawalOptions: [],
    fixedDeposits: [],
    downtimes: [],
    investLink: undefined,
    withdrawalPenaltyRate: 0,
    bankFaqCategoryPriorityList: [],
  };
}

export const BankListing: MessageFns<BankListing> = {
  encode(message: BankListing, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.slug !== "") {
      writer.uint32(18).string(message.slug);
    }
    if (message.logoUrl !== "") {
      writer.uint32(26).string(message.logoUrl);
    }
    if (message.name !== "") {
      writer.uint32(34).string(message.name);
    }
    if (message.keywords !== "") {
      writer.uint32(42).string(message.keywords);
    }
    if (message.accountRequiredToInvest !== false) {
      writer.uint32(48).bool(message.accountRequiredToInvest);
    }
    if (message.bankType !== 0) {
      writer.uint32(56).int32(message.bankType);
    }
    if (message.investabilityStatus !== 0) {
      writer.uint32(64).int32(message.investabilityStatus);
    }
    for (const v of message.faqs) {
      Faq.encode(v!, writer.uint32(74).fork()).join();
    }
    for (const v of message.withdrawalOptions) {
      WithdrawalOption.encode(v!, writer.uint32(82).fork()).join();
    }
    for (const v of message.fixedDeposits) {
      FixedDepositInfo.encode(v!, writer.uint32(90).fork()).join();
    }
    for (const v of message.downtimes) {
      Downtime.encode(v!, writer.uint32(98).fork()).join();
    }
    if (message.investLink !== undefined) {
      RedirectDeeplink.encode(message.investLink, writer.uint32(106).fork()).join();
    }
    if (message.withdrawalPenaltyRate !== 0) {
      writer.uint32(113).double(message.withdrawalPenaltyRate);
    }
    for (const v of message.bankFaqCategoryPriorityList) {
      writer.uint32(122).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListing {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListing();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.slug = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.keywords = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.accountRequiredToInvest = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.bankType = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.investabilityStatus = reader.int32() as any;
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.faqs.push(Faq.decode(reader, reader.uint32()));
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.withdrawalOptions.push(WithdrawalOption.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.fixedDeposits.push(FixedDepositInfo.decode(reader, reader.uint32()));
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.downtimes.push(Downtime.decode(reader, reader.uint32()));
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.investLink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 14: {
          if (tag !== 113) {
            break;
          }

          message.withdrawalPenaltyRate = reader.double();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.bankFaqCategoryPriorityList.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListing {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      slug: isSet(object.slug) ? globalThis.String(object.slug) : "",
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      keywords: isSet(object.keywords) ? globalThis.String(object.keywords) : "",
      accountRequiredToInvest: isSet(object.accountRequiredToInvest)
        ? globalThis.Boolean(object.accountRequiredToInvest)
        : false,
      bankType: isSet(object.bankType) ? bankTypeFromJSON(object.bankType) : 0,
      investabilityStatus: isSet(object.investabilityStatus)
        ? investabilityStatusFromJSON(object.investabilityStatus)
        : 0,
      faqs: globalThis.Array.isArray(object?.faqs) ? object.faqs.map((e: any) => Faq.fromJSON(e)) : [],
      withdrawalOptions: globalThis.Array.isArray(object?.withdrawalOptions)
        ? object.withdrawalOptions.map((e: any) => WithdrawalOption.fromJSON(e))
        : [],
      fixedDeposits: globalThis.Array.isArray(object?.fixedDeposits)
        ? object.fixedDeposits.map((e: any) => FixedDepositInfo.fromJSON(e))
        : [],
      downtimes: globalThis.Array.isArray(object?.downtimes)
        ? object.downtimes.map((e: any) => Downtime.fromJSON(e))
        : [],
      investLink: isSet(object.investLink) ? RedirectDeeplink.fromJSON(object.investLink) : undefined,
      withdrawalPenaltyRate: isSet(object.withdrawalPenaltyRate) ? globalThis.Number(object.withdrawalPenaltyRate) : 0,
      bankFaqCategoryPriorityList: globalThis.Array.isArray(object?.bankFaqCategoryPriorityList)
        ? object.bankFaqCategoryPriorityList.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: BankListing): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.slug !== "") {
      obj.slug = message.slug;
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.keywords !== "") {
      obj.keywords = message.keywords;
    }
    if (message.accountRequiredToInvest !== false) {
      obj.accountRequiredToInvest = message.accountRequiredToInvest;
    }
    if (message.bankType !== 0) {
      obj.bankType = bankTypeToJSON(message.bankType);
    }
    if (message.investabilityStatus !== 0) {
      obj.investabilityStatus = investabilityStatusToJSON(message.investabilityStatus);
    }
    if (message.faqs?.length) {
      obj.faqs = message.faqs.map((e) => Faq.toJSON(e));
    }
    if (message.withdrawalOptions?.length) {
      obj.withdrawalOptions = message.withdrawalOptions.map((e) => WithdrawalOption.toJSON(e));
    }
    if (message.fixedDeposits?.length) {
      obj.fixedDeposits = message.fixedDeposits.map((e) => FixedDepositInfo.toJSON(e));
    }
    if (message.downtimes?.length) {
      obj.downtimes = message.downtimes.map((e) => Downtime.toJSON(e));
    }
    if (message.investLink !== undefined) {
      obj.investLink = RedirectDeeplink.toJSON(message.investLink);
    }
    if (message.withdrawalPenaltyRate !== 0) {
      obj.withdrawalPenaltyRate = message.withdrawalPenaltyRate;
    }
    if (message.bankFaqCategoryPriorityList?.length) {
      obj.bankFaqCategoryPriorityList = message.bankFaqCategoryPriorityList;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListing>, I>>(base?: I): BankListing {
    return BankListing.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListing>, I>>(object: I): BankListing {
    const message = createBaseBankListing();
    message.id = object.id ?? "";
    message.slug = object.slug ?? "";
    message.logoUrl = object.logoUrl ?? "";
    message.name = object.name ?? "";
    message.keywords = object.keywords ?? "";
    message.accountRequiredToInvest = object.accountRequiredToInvest ?? false;
    message.bankType = object.bankType ?? 0;
    message.investabilityStatus = object.investabilityStatus ?? 0;
    message.faqs = object.faqs?.map((e) => Faq.fromPartial(e)) || [];
    message.withdrawalOptions = object.withdrawalOptions?.map((e) => WithdrawalOption.fromPartial(e)) || [];
    message.fixedDeposits = object.fixedDeposits?.map((e) => FixedDepositInfo.fromPartial(e)) || [];
    message.downtimes = object.downtimes?.map((e) => Downtime.fromPartial(e)) || [];
    message.investLink = (object.investLink !== undefined && object.investLink !== null)
      ? RedirectDeeplink.fromPartial(object.investLink)
      : undefined;
    message.withdrawalPenaltyRate = object.withdrawalPenaltyRate ?? 0;
    message.bankFaqCategoryPriorityList = object.bankFaqCategoryPriorityList?.map((e) => e) || [];
    return message;
  },
};

function createBaseFixedDepositInfo(): FixedDepositInfo {
  return {
    id: "",
    rate: 0,
    minDeposit: 0,
    maxDeposit: 0,
    investorType: 0,
    isPreMatureWithdrawalAllowed: false,
    interestPayoutType: 0,
    isLoanAgainstFdAllowed: false,
    isPartialWithdrawalAllowed: false,
    displayTenure: "",
    minTenureInDays: 0,
    maxTenureInDays: 0,
  };
}

export const FixedDepositInfo: MessageFns<FixedDepositInfo> = {
  encode(message: FixedDepositInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.rate !== 0) {
      writer.uint32(17).double(message.rate);
    }
    if (message.minDeposit !== 0) {
      writer.uint32(25).double(message.minDeposit);
    }
    if (message.maxDeposit !== 0) {
      writer.uint32(33).double(message.maxDeposit);
    }
    if (message.investorType !== 0) {
      writer.uint32(40).int32(message.investorType);
    }
    if (message.isPreMatureWithdrawalAllowed !== false) {
      writer.uint32(48).bool(message.isPreMatureWithdrawalAllowed);
    }
    if (message.interestPayoutType !== 0) {
      writer.uint32(56).int32(message.interestPayoutType);
    }
    if (message.isLoanAgainstFdAllowed !== false) {
      writer.uint32(64).bool(message.isLoanAgainstFdAllowed);
    }
    if (message.isPartialWithdrawalAllowed !== false) {
      writer.uint32(72).bool(message.isPartialWithdrawalAllowed);
    }
    if (message.displayTenure !== "") {
      writer.uint32(82).string(message.displayTenure);
    }
    if (message.minTenureInDays !== 0) {
      writer.uint32(88).uint32(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      writer.uint32(96).uint32(message.maxTenureInDays);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FixedDepositInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFixedDepositInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.minDeposit = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.maxDeposit = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.investorType = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isPreMatureWithdrawalAllowed = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.interestPayoutType = reader.int32() as any;
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isLoanAgainstFdAllowed = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.isPartialWithdrawalAllowed = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.displayTenure = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.minTenureInDays = reader.uint32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.maxTenureInDays = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FixedDepositInfo {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      minDeposit: isSet(object.minDeposit) ? globalThis.Number(object.minDeposit) : 0,
      maxDeposit: isSet(object.maxDeposit) ? globalThis.Number(object.maxDeposit) : 0,
      investorType: isSet(object.investorType) ? investorTypeFromJSON(object.investorType) : 0,
      isPreMatureWithdrawalAllowed: isSet(object.isPreMatureWithdrawalAllowed)
        ? globalThis.Boolean(object.isPreMatureWithdrawalAllowed)
        : false,
      interestPayoutType: isSet(object.interestPayoutType) ? interestPayoutTypeFromJSON(object.interestPayoutType) : 0,
      isLoanAgainstFdAllowed: isSet(object.isLoanAgainstFdAllowed)
        ? globalThis.Boolean(object.isLoanAgainstFdAllowed)
        : false,
      isPartialWithdrawalAllowed: isSet(object.isPartialWithdrawalAllowed)
        ? globalThis.Boolean(object.isPartialWithdrawalAllowed)
        : false,
      displayTenure: isSet(object.displayTenure) ? globalThis.String(object.displayTenure) : "",
      minTenureInDays: isSet(object.minTenureInDays) ? globalThis.Number(object.minTenureInDays) : 0,
      maxTenureInDays: isSet(object.maxTenureInDays) ? globalThis.Number(object.maxTenureInDays) : 0,
    };
  },

  toJSON(message: FixedDepositInfo): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.minDeposit !== 0) {
      obj.minDeposit = message.minDeposit;
    }
    if (message.maxDeposit !== 0) {
      obj.maxDeposit = message.maxDeposit;
    }
    if (message.investorType !== 0) {
      obj.investorType = investorTypeToJSON(message.investorType);
    }
    if (message.isPreMatureWithdrawalAllowed !== false) {
      obj.isPreMatureWithdrawalAllowed = message.isPreMatureWithdrawalAllowed;
    }
    if (message.interestPayoutType !== 0) {
      obj.interestPayoutType = interestPayoutTypeToJSON(message.interestPayoutType);
    }
    if (message.isLoanAgainstFdAllowed !== false) {
      obj.isLoanAgainstFdAllowed = message.isLoanAgainstFdAllowed;
    }
    if (message.isPartialWithdrawalAllowed !== false) {
      obj.isPartialWithdrawalAllowed = message.isPartialWithdrawalAllowed;
    }
    if (message.displayTenure !== "") {
      obj.displayTenure = message.displayTenure;
    }
    if (message.minTenureInDays !== 0) {
      obj.minTenureInDays = Math.round(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      obj.maxTenureInDays = Math.round(message.maxTenureInDays);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FixedDepositInfo>, I>>(base?: I): FixedDepositInfo {
    return FixedDepositInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FixedDepositInfo>, I>>(object: I): FixedDepositInfo {
    const message = createBaseFixedDepositInfo();
    message.id = object.id ?? "";
    message.rate = object.rate ?? 0;
    message.minDeposit = object.minDeposit ?? 0;
    message.maxDeposit = object.maxDeposit ?? 0;
    message.investorType = object.investorType ?? 0;
    message.isPreMatureWithdrawalAllowed = object.isPreMatureWithdrawalAllowed ?? false;
    message.interestPayoutType = object.interestPayoutType ?? 0;
    message.isLoanAgainstFdAllowed = object.isLoanAgainstFdAllowed ?? false;
    message.isPartialWithdrawalAllowed = object.isPartialWithdrawalAllowed ?? false;
    message.displayTenure = object.displayTenure ?? "";
    message.minTenureInDays = object.minTenureInDays ?? 0;
    message.maxTenureInDays = object.maxTenureInDays ?? 0;
    return message;
  },
};

function createBaseDowntime(): Downtime {
  return { startOn: 0, endOn: 0 };
}

export const Downtime: MessageFns<Downtime> = {
  encode(message: Downtime, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.startOn !== 0) {
      writer.uint32(8).uint64(message.startOn);
    }
    if (message.endOn !== 0) {
      writer.uint32(16).uint64(message.endOn);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Downtime {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDowntime();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.startOn = longToNumber(reader.uint64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.endOn = longToNumber(reader.uint64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Downtime {
    return {
      startOn: isSet(object.startOn) ? globalThis.Number(object.startOn) : 0,
      endOn: isSet(object.endOn) ? globalThis.Number(object.endOn) : 0,
    };
  },

  toJSON(message: Downtime): unknown {
    const obj: any = {};
    if (message.startOn !== 0) {
      obj.startOn = Math.round(message.startOn);
    }
    if (message.endOn !== 0) {
      obj.endOn = Math.round(message.endOn);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Downtime>, I>>(base?: I): Downtime {
    return Downtime.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Downtime>, I>>(object: I): Downtime {
    const message = createBaseDowntime();
    message.startOn = object.startOn ?? 0;
    message.endOn = object.endOn ?? 0;
    return message;
  },
};

function createBaseWithdrawalOption(): WithdrawalOption {
  return {
    rate: 0,
    minTenureInDays: 0,
    maxTenureInDays: 0,
    minDeposit: 0,
    maxDeposit: 0,
    investorType: 0,
    compoundingFrequency: 0,
  };
}

export const WithdrawalOption: MessageFns<WithdrawalOption> = {
  encode(message: WithdrawalOption, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rate !== 0) {
      writer.uint32(9).double(message.rate);
    }
    if (message.minTenureInDays !== 0) {
      writer.uint32(16).uint32(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      writer.uint32(24).uint32(message.maxTenureInDays);
    }
    if (message.minDeposit !== 0) {
      writer.uint32(33).double(message.minDeposit);
    }
    if (message.maxDeposit !== 0) {
      writer.uint32(41).double(message.maxDeposit);
    }
    if (message.investorType !== 0) {
      writer.uint32(48).int32(message.investorType);
    }
    if (message.compoundingFrequency !== 0) {
      writer.uint32(56).int32(message.compoundingFrequency);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalOption {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalOption();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.minTenureInDays = reader.uint32();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.maxTenureInDays = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.minDeposit = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.maxDeposit = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.investorType = reader.int32() as any;
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.compoundingFrequency = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalOption {
    return {
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      minTenureInDays: isSet(object.minTenureInDays) ? globalThis.Number(object.minTenureInDays) : 0,
      maxTenureInDays: isSet(object.maxTenureInDays) ? globalThis.Number(object.maxTenureInDays) : 0,
      minDeposit: isSet(object.minDeposit) ? globalThis.Number(object.minDeposit) : 0,
      maxDeposit: isSet(object.maxDeposit) ? globalThis.Number(object.maxDeposit) : 0,
      investorType: isSet(object.investorType) ? investorTypeFromJSON(object.investorType) : 0,
      compoundingFrequency: isSet(object.compoundingFrequency)
        ? compoundingFrequencyTypeFromJSON(object.compoundingFrequency)
        : 0,
    };
  },

  toJSON(message: WithdrawalOption): unknown {
    const obj: any = {};
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.minTenureInDays !== 0) {
      obj.minTenureInDays = Math.round(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      obj.maxTenureInDays = Math.round(message.maxTenureInDays);
    }
    if (message.minDeposit !== 0) {
      obj.minDeposit = message.minDeposit;
    }
    if (message.maxDeposit !== 0) {
      obj.maxDeposit = message.maxDeposit;
    }
    if (message.investorType !== 0) {
      obj.investorType = investorTypeToJSON(message.investorType);
    }
    if (message.compoundingFrequency !== 0) {
      obj.compoundingFrequency = compoundingFrequencyTypeToJSON(message.compoundingFrequency);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalOption>, I>>(base?: I): WithdrawalOption {
    return WithdrawalOption.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalOption>, I>>(object: I): WithdrawalOption {
    const message = createBaseWithdrawalOption();
    message.rate = object.rate ?? 0;
    message.minTenureInDays = object.minTenureInDays ?? 0;
    message.maxTenureInDays = object.maxTenureInDays ?? 0;
    message.minDeposit = object.minDeposit ?? 0;
    message.maxDeposit = object.maxDeposit ?? 0;
    message.investorType = object.investorType ?? 0;
    message.compoundingFrequency = object.compoundingFrequency ?? 0;
    return message;
  },
};

function createBaseTopInfoCard(): TopInfoCard {
  return { title: "", description: "", iconUrl: "", infoBottomSheetTitle: "", infoBottomSheetDescription: "" };
}

export const TopInfoCard: MessageFns<TopInfoCard> = {
  encode(message: TopInfoCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.iconUrl !== "") {
      writer.uint32(26).string(message.iconUrl);
    }
    if (message.infoBottomSheetTitle !== "") {
      writer.uint32(34).string(message.infoBottomSheetTitle);
    }
    if (message.infoBottomSheetDescription !== "") {
      writer.uint32(42).string(message.infoBottomSheetDescription);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TopInfoCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTopInfoCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.infoBottomSheetTitle = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.infoBottomSheetDescription = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TopInfoCard {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      infoBottomSheetTitle: isSet(object.infoBottomSheetTitle) ? globalThis.String(object.infoBottomSheetTitle) : "",
      infoBottomSheetDescription: isSet(object.infoBottomSheetDescription)
        ? globalThis.String(object.infoBottomSheetDescription)
        : "",
    };
  },

  toJSON(message: TopInfoCard): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.infoBottomSheetTitle !== "") {
      obj.infoBottomSheetTitle = message.infoBottomSheetTitle;
    }
    if (message.infoBottomSheetDescription !== "") {
      obj.infoBottomSheetDescription = message.infoBottomSheetDescription;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TopInfoCard>, I>>(base?: I): TopInfoCard {
    return TopInfoCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TopInfoCard>, I>>(object: I): TopInfoCard {
    const message = createBaseTopInfoCard();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.infoBottomSheetTitle = object.infoBottomSheetTitle ?? "";
    message.infoBottomSheetDescription = object.infoBottomSheetDescription ?? "";
    return message;
  },
};

function createBaseBankTag(): BankTag {
  return { tagId: "", tagName: "", iconUrl: "" };
}

export const BankTag: MessageFns<BankTag> = {
  encode(message: BankTag, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tagId !== "") {
      writer.uint32(10).string(message.tagId);
    }
    if (message.tagName !== "") {
      writer.uint32(18).string(message.tagName);
    }
    if (message.iconUrl !== "") {
      writer.uint32(26).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankTag {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankTag();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tagId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tagName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankTag {
    return {
      tagId: isSet(object.tagId) ? globalThis.String(object.tagId) : "",
      tagName: isSet(object.tagName) ? globalThis.String(object.tagName) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: BankTag): unknown {
    const obj: any = {};
    if (message.tagId !== "") {
      obj.tagId = message.tagId;
    }
    if (message.tagName !== "") {
      obj.tagName = message.tagName;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankTag>, I>>(base?: I): BankTag {
    return BankTag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankTag>, I>>(object: I): BankTag {
    const message = createBaseBankTag();
    message.tagId = object.tagId ?? "";
    message.tagName = object.tagName ?? "";
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBaseBankSellingPoint(): BankSellingPoint {
  return { title: "", description: "", iconUrl: "" };
}

export const BankSellingPoint: MessageFns<BankSellingPoint> = {
  encode(message: BankSellingPoint, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.iconUrl !== "") {
      writer.uint32(26).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankSellingPoint {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankSellingPoint();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankSellingPoint {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: BankSellingPoint): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankSellingPoint>, I>>(base?: I): BankSellingPoint {
    return BankSellingPoint.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankSellingPoint>, I>>(object: I): BankSellingPoint {
    const message = createBaseBankSellingPoint();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBaseBankFixedDepositStep(): BankFixedDepositStep {
  return { title: "", description: "", imageUrl: "" };
}

export const BankFixedDepositStep: MessageFns<BankFixedDepositStep> = {
  encode(message: BankFixedDepositStep, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.imageUrl !== "") {
      writer.uint32(26).string(message.imageUrl);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankFixedDepositStep {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankFixedDepositStep();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.imageUrl = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankFixedDepositStep {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      imageUrl: isSet(object.imageUrl) ? globalThis.String(object.imageUrl) : "",
    };
  },

  toJSON(message: BankFixedDepositStep): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.imageUrl !== "") {
      obj.imageUrl = message.imageUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankFixedDepositStep>, I>>(base?: I): BankFixedDepositStep {
    return BankFixedDepositStep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankFixedDepositStep>, I>>(object: I): BankFixedDepositStep {
    const message = createBaseBankFixedDepositStep();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.imageUrl = object.imageUrl ?? "";
    return message;
  },
};

function createBaseBankFixedDepositStepsResponse(): BankFixedDepositStepsResponse {
  return { bankFixedDepositStep: [] };
}

export const BankFixedDepositStepsResponse: MessageFns<BankFixedDepositStepsResponse> = {
  encode(message: BankFixedDepositStepsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bankFixedDepositStep) {
      BankFixedDepositStep.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankFixedDepositStepsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankFixedDepositStepsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankFixedDepositStep.push(BankFixedDepositStep.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankFixedDepositStepsResponse {
    return {
      bankFixedDepositStep: globalThis.Array.isArray(object?.bankFixedDepositStep)
        ? object.bankFixedDepositStep.map((e: any) => BankFixedDepositStep.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BankFixedDepositStepsResponse): unknown {
    const obj: any = {};
    if (message.bankFixedDepositStep?.length) {
      obj.bankFixedDepositStep = message.bankFixedDepositStep.map((e) => BankFixedDepositStep.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankFixedDepositStepsResponse>, I>>(base?: I): BankFixedDepositStepsResponse {
    return BankFixedDepositStepsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankFixedDepositStepsResponse>, I>>(
    object: I,
  ): BankFixedDepositStepsResponse {
    const message = createBaseBankFixedDepositStepsResponse();
    message.bankFixedDepositStep = object.bankFixedDepositStep?.map((e) => BankFixedDepositStep.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBankRateNotificationResponse(): BankRateNotificationResponse {
  return { status: false };
}

export const BankRateNotificationResponse: MessageFns<BankRateNotificationResponse> = {
  encode(message: BankRateNotificationResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.status !== false) {
      writer.uint32(8).bool(message.status);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankRateNotificationResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankRateNotificationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.status = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankRateNotificationResponse {
    return { status: isSet(object.status) ? globalThis.Boolean(object.status) : false };
  },

  toJSON(message: BankRateNotificationResponse): unknown {
    const obj: any = {};
    if (message.status !== false) {
      obj.status = message.status;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankRateNotificationResponse>, I>>(base?: I): BankRateNotificationResponse {
    return BankRateNotificationResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankRateNotificationResponse>, I>>(object: I): BankRateNotificationResponse {
    const message = createBaseBankRateNotificationResponse();
    message.status = object.status ?? false;
    return message;
  },
};

function createBaseBankRateNotificationRequest(): BankRateNotificationRequest {
  return { bankId: "", toNotify: undefined };
}

export const BankRateNotificationRequest: MessageFns<BankRateNotificationRequest> = {
  encode(message: BankRateNotificationRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankId !== "") {
      writer.uint32(10).string(message.bankId);
    }
    if (message.toNotify !== undefined) {
      writer.uint32(16).bool(message.toNotify);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankRateNotificationRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankRateNotificationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.toNotify = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankRateNotificationRequest {
    return {
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      toNotify: isSet(object.toNotify) ? globalThis.Boolean(object.toNotify) : undefined,
    };
  },

  toJSON(message: BankRateNotificationRequest): unknown {
    const obj: any = {};
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.toNotify !== undefined) {
      obj.toNotify = message.toNotify;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankRateNotificationRequest>, I>>(base?: I): BankRateNotificationRequest {
    return BankRateNotificationRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankRateNotificationRequest>, I>>(object: I): BankRateNotificationRequest {
    const message = createBaseBankRateNotificationRequest();
    message.bankId = object.bankId ?? "";
    message.toNotify = object.toNotify ?? undefined;
    return message;
  },
};

function createBaseAllBanksResponse(): AllBanksResponse {
  return { banks: [] };
}

export const AllBanksResponse: MessageFns<AllBanksResponse> = {
  encode(message: AllBanksResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.banks) {
      BankListing.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AllBanksResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAllBanksResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.banks.push(BankListing.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AllBanksResponse {
    return {
      banks: globalThis.Array.isArray(object?.banks) ? object.banks.map((e: any) => BankListing.fromJSON(e)) : [],
    };
  },

  toJSON(message: AllBanksResponse): unknown {
    const obj: any = {};
    if (message.banks?.length) {
      obj.banks = message.banks.map((e) => BankListing.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AllBanksResponse>, I>>(base?: I): AllBanksResponse {
    return AllBanksResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AllBanksResponse>, I>>(object: I): AllBanksResponse {
    const message = createBaseAllBanksResponse();
    message.banks = object.banks?.map((e) => BankListing.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBranchLocations(): BranchLocations {
  return { branchLocations: [], cityName: "", totalBranches: 0, totalCityBranches: 0 };
}

export const BranchLocations: MessageFns<BranchLocations> = {
  encode(message: BranchLocations, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.branchLocations) {
      BranchLocation.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.cityName !== "") {
      writer.uint32(26).string(message.cityName);
    }
    if (message.totalBranches !== 0) {
      writer.uint32(40).int32(message.totalBranches);
    }
    if (message.totalCityBranches !== 0) {
      writer.uint32(56).int32(message.totalCityBranches);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BranchLocations {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBranchLocations();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.branchLocations.push(BranchLocation.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.cityName = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.totalBranches = reader.int32();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.totalCityBranches = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BranchLocations {
    return {
      branchLocations: globalThis.Array.isArray(object?.branchLocations)
        ? object.branchLocations.map((e: any) => BranchLocation.fromJSON(e))
        : [],
      cityName: isSet(object.cityName) ? globalThis.String(object.cityName) : "",
      totalBranches: isSet(object.totalBranches) ? globalThis.Number(object.totalBranches) : 0,
      totalCityBranches: isSet(object.totalCityBranches) ? globalThis.Number(object.totalCityBranches) : 0,
    };
  },

  toJSON(message: BranchLocations): unknown {
    const obj: any = {};
    if (message.branchLocations?.length) {
      obj.branchLocations = message.branchLocations.map((e) => BranchLocation.toJSON(e));
    }
    if (message.cityName !== "") {
      obj.cityName = message.cityName;
    }
    if (message.totalBranches !== 0) {
      obj.totalBranches = Math.round(message.totalBranches);
    }
    if (message.totalCityBranches !== 0) {
      obj.totalCityBranches = Math.round(message.totalCityBranches);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BranchLocations>, I>>(base?: I): BranchLocations {
    return BranchLocations.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BranchLocations>, I>>(object: I): BranchLocations {
    const message = createBaseBranchLocations();
    message.branchLocations = object.branchLocations?.map((e) => BranchLocation.fromPartial(e)) || [];
    message.cityName = object.cityName ?? "";
    message.totalBranches = object.totalBranches ?? 0;
    message.totalCityBranches = object.totalCityBranches ?? 0;
    return message;
  },
};

function createBaseBranchLocation(): BranchLocation {
  return { address: "", area: "", city: "", state: "", pincode: "", latLong: undefined, phoneNumber: "" };
}

export const BranchLocation: MessageFns<BranchLocation> = {
  encode(message: BranchLocation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.address !== "") {
      writer.uint32(10).string(message.address);
    }
    if (message.area !== "") {
      writer.uint32(18).string(message.area);
    }
    if (message.city !== "") {
      writer.uint32(26).string(message.city);
    }
    if (message.state !== "") {
      writer.uint32(34).string(message.state);
    }
    if (message.pincode !== "") {
      writer.uint32(42).string(message.pincode);
    }
    if (message.latLong !== undefined) {
      BranchLocation_LatLong.encode(message.latLong, writer.uint32(50).fork()).join();
    }
    if (message.phoneNumber !== "") {
      writer.uint32(58).string(message.phoneNumber);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BranchLocation {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBranchLocation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.address = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.area = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.city = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.state = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.pincode = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.latLong = BranchLocation_LatLong.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.phoneNumber = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BranchLocation {
    return {
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      area: isSet(object.area) ? globalThis.String(object.area) : "",
      city: isSet(object.city) ? globalThis.String(object.city) : "",
      state: isSet(object.state) ? globalThis.String(object.state) : "",
      pincode: isSet(object.pincode) ? globalThis.String(object.pincode) : "",
      latLong: isSet(object.latLong) ? BranchLocation_LatLong.fromJSON(object.latLong) : undefined,
      phoneNumber: isSet(object.phoneNumber) ? globalThis.String(object.phoneNumber) : "",
    };
  },

  toJSON(message: BranchLocation): unknown {
    const obj: any = {};
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.area !== "") {
      obj.area = message.area;
    }
    if (message.city !== "") {
      obj.city = message.city;
    }
    if (message.state !== "") {
      obj.state = message.state;
    }
    if (message.pincode !== "") {
      obj.pincode = message.pincode;
    }
    if (message.latLong !== undefined) {
      obj.latLong = BranchLocation_LatLong.toJSON(message.latLong);
    }
    if (message.phoneNumber !== "") {
      obj.phoneNumber = message.phoneNumber;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BranchLocation>, I>>(base?: I): BranchLocation {
    return BranchLocation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BranchLocation>, I>>(object: I): BranchLocation {
    const message = createBaseBranchLocation();
    message.address = object.address ?? "";
    message.area = object.area ?? "";
    message.city = object.city ?? "";
    message.state = object.state ?? "";
    message.pincode = object.pincode ?? "";
    message.latLong = (object.latLong !== undefined && object.latLong !== null)
      ? BranchLocation_LatLong.fromPartial(object.latLong)
      : undefined;
    message.phoneNumber = object.phoneNumber ?? "";
    return message;
  },
};

function createBaseBranchLocation_LatLong(): BranchLocation_LatLong {
  return { latitude: 0, longitude: 0 };
}

export const BranchLocation_LatLong: MessageFns<BranchLocation_LatLong> = {
  encode(message: BranchLocation_LatLong, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.latitude !== 0) {
      writer.uint32(9).double(message.latitude);
    }
    if (message.longitude !== 0) {
      writer.uint32(17).double(message.longitude);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BranchLocation_LatLong {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBranchLocation_LatLong();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.latitude = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.longitude = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BranchLocation_LatLong {
    return {
      latitude: isSet(object.latitude) ? globalThis.Number(object.latitude) : 0,
      longitude: isSet(object.longitude) ? globalThis.Number(object.longitude) : 0,
    };
  },

  toJSON(message: BranchLocation_LatLong): unknown {
    const obj: any = {};
    if (message.latitude !== 0) {
      obj.latitude = message.latitude;
    }
    if (message.longitude !== 0) {
      obj.longitude = message.longitude;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BranchLocation_LatLong>, I>>(base?: I): BranchLocation_LatLong {
    return BranchLocation_LatLong.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BranchLocation_LatLong>, I>>(object: I): BranchLocation_LatLong {
    const message = createBaseBranchLocation_LatLong();
    message.latitude = object.latitude ?? 0;
    message.longitude = object.longitude ?? 0;
    return message;
  },
};

function createBaseMediaItem(): MediaItem {
  return { section: "", mediaType: 0, url: "", screenType: 0, redirectDeeplink: undefined };
}

export const MediaItem: MessageFns<MediaItem> = {
  encode(message: MediaItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.section !== "") {
      writer.uint32(10).string(message.section);
    }
    if (message.mediaType !== 0) {
      writer.uint32(16).int32(message.mediaType);
    }
    if (message.url !== "") {
      writer.uint32(26).string(message.url);
    }
    if (message.screenType !== 0) {
      writer.uint32(32).int32(message.screenType);
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MediaItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMediaItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.section = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.mediaType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.url = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.screenType = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MediaItem {
    return {
      section: isSet(object.section) ? globalThis.String(object.section) : "",
      mediaType: isSet(object.mediaType) ? mediaItem_MediaTypeFromJSON(object.mediaType) : 0,
      url: isSet(object.url) ? globalThis.String(object.url) : "",
      screenType: isSet(object.screenType) ? mediaItem_ScreenTypeFromJSON(object.screenType) : 0,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
    };
  },

  toJSON(message: MediaItem): unknown {
    const obj: any = {};
    if (message.section !== "") {
      obj.section = message.section;
    }
    if (message.mediaType !== 0) {
      obj.mediaType = mediaItem_MediaTypeToJSON(message.mediaType);
    }
    if (message.url !== "") {
      obj.url = message.url;
    }
    if (message.screenType !== 0) {
      obj.screenType = mediaItem_ScreenTypeToJSON(message.screenType);
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MediaItem>, I>>(base?: I): MediaItem {
    return MediaItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MediaItem>, I>>(object: I): MediaItem {
    const message = createBaseMediaItem();
    message.section = object.section ?? "";
    message.mediaType = object.mediaType ?? 0;
    message.url = object.url ?? "";
    message.screenType = object.screenType ?? 0;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    return message;
  },
};

function createBaseCompareLineItem(): CompareLineItem {
  return { title: "", highestBankPoint: "", currentBankPoint: "", moreDetailsLink: undefined };
}

export const CompareLineItem: MessageFns<CompareLineItem> = {
  encode(message: CompareLineItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.highestBankPoint !== "") {
      writer.uint32(18).string(message.highestBankPoint);
    }
    if (message.currentBankPoint !== "") {
      writer.uint32(26).string(message.currentBankPoint);
    }
    if (message.moreDetailsLink !== undefined) {
      RedirectDeeplink.encode(message.moreDetailsLink, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompareLineItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompareLineItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.highestBankPoint = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.currentBankPoint = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.moreDetailsLink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CompareLineItem {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      highestBankPoint: isSet(object.highestBankPoint) ? globalThis.String(object.highestBankPoint) : "",
      currentBankPoint: isSet(object.currentBankPoint) ? globalThis.String(object.currentBankPoint) : "",
      moreDetailsLink: isSet(object.moreDetailsLink) ? RedirectDeeplink.fromJSON(object.moreDetailsLink) : undefined,
    };
  },

  toJSON(message: CompareLineItem): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.highestBankPoint !== "") {
      obj.highestBankPoint = message.highestBankPoint;
    }
    if (message.currentBankPoint !== "") {
      obj.currentBankPoint = message.currentBankPoint;
    }
    if (message.moreDetailsLink !== undefined) {
      obj.moreDetailsLink = RedirectDeeplink.toJSON(message.moreDetailsLink);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompareLineItem>, I>>(base?: I): CompareLineItem {
    return CompareLineItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompareLineItem>, I>>(object: I): CompareLineItem {
    const message = createBaseCompareLineItem();
    message.title = object.title ?? "";
    message.highestBankPoint = object.highestBankPoint ?? "";
    message.currentBankPoint = object.currentBankPoint ?? "";
    message.moreDetailsLink = (object.moreDetailsLink !== undefined && object.moreDetailsLink !== null)
      ? RedirectDeeplink.fromPartial(object.moreDetailsLink)
      : undefined;
    return message;
  },
};

function createBaseBankListingV4(): BankListingV4 {
  return {
    bankResponse: undefined,
    marketingHighlights: [],
    tenures: [],
    womenRoiDiff: undefined,
    isRateChangeNotificationEnabled: false,
    redirectDeeplink: undefined,
    sipRedirectDeeplink: undefined,
    rdRedirectDeeplink: undefined,
    investableBankCompare: undefined,
    nonInvestableBankCompare: undefined,
    fdWithdrawalCalculationDetails: undefined,
    fdWithdrawalCalculation: [],
    userDeviceInvestabilityStatus: 0,
    shareImageUrl: "",
    highlightPointers: [],
    bankTables: {},
    bankPointers: {},
    mediaItems: [],
    userBankRelationshipType: "",
    userBankRelationship: undefined,
    seniorRoiDiff: undefined,
  };
}

export const BankListingV4: MessageFns<BankListingV4> = {
  encode(message: BankListingV4, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankResponse !== undefined) {
      BankResponse.encode(message.bankResponse, writer.uint32(10).fork()).join();
    }
    for (const v of message.marketingHighlights) {
      writer.uint32(146).string(v!);
    }
    for (const v of message.tenures) {
      Tenure.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.womenRoiDiff !== undefined) {
      writer.uint32(29).float(message.womenRoiDiff);
    }
    if (message.isRateChangeNotificationEnabled !== false) {
      writer.uint32(136).bool(message.isRateChangeNotificationEnabled);
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(34).fork()).join();
    }
    if (message.sipRedirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.sipRedirectDeeplink, writer.uint32(42).fork()).join();
    }
    if (message.rdRedirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.rdRedirectDeeplink, writer.uint32(50).fork()).join();
    }
    if (message.investableBankCompare !== undefined) {
      InvestableBankCompareV4.encode(message.investableBankCompare, writer.uint32(58).fork()).join();
    }
    if (message.nonInvestableBankCompare !== undefined) {
      NonInvestableBankCompareV4.encode(message.nonInvestableBankCompare, writer.uint32(66).fork()).join();
    }
    if (message.fdWithdrawalCalculationDetails !== undefined) {
      FdWithdrawalCalculationDetails.encode(message.fdWithdrawalCalculationDetails, writer.uint32(74).fork()).join();
    }
    for (const v of message.fdWithdrawalCalculation) {
      FdWithdrawalCalculation.encode(v!, writer.uint32(82).fork()).join();
    }
    if (message.userDeviceInvestabilityStatus !== 0) {
      writer.uint32(88).int32(message.userDeviceInvestabilityStatus);
    }
    if (message.shareImageUrl !== "") {
      writer.uint32(98).string(message.shareImageUrl);
    }
    for (const v of message.highlightPointers) {
      writer.uint32(106).string(v!);
    }
    Object.entries(message.bankTables).forEach(([key, value]) => {
      BankListingV4_BankTablesEntry.encode({ key: key as any, value }, writer.uint32(114).fork()).join();
    });
    Object.entries(message.bankPointers).forEach(([key, value]) => {
      BankListingV4_BankPointersEntry.encode({ key: key as any, value }, writer.uint32(122).fork()).join();
    });
    for (const v of message.mediaItems) {
      MediaItem.encode(v!, writer.uint32(130).fork()).join();
    }
    if (message.userBankRelationshipType !== "") {
      writer.uint32(154).string(message.userBankRelationshipType);
    }
    if (message.userBankRelationship !== undefined) {
      BankListingV4_UserBankRelationship.encode(message.userBankRelationship, writer.uint32(162).fork()).join();
    }
    if (message.seniorRoiDiff !== undefined) {
      writer.uint32(173).float(message.seniorRoiDiff);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListingV4 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListingV4();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankResponse = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.marketingHighlights.push(reader.string());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tenures.push(Tenure.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 29) {
            break;
          }

          message.womenRoiDiff = reader.float();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.isRateChangeNotificationEnabled = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.sipRedirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.rdRedirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.investableBankCompare = InvestableBankCompareV4.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.nonInvestableBankCompare = NonInvestableBankCompareV4.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.fdWithdrawalCalculationDetails = FdWithdrawalCalculationDetails.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.fdWithdrawalCalculation.push(FdWithdrawalCalculation.decode(reader, reader.uint32()));
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.userDeviceInvestabilityStatus = reader.int32() as any;
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.shareImageUrl = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.highlightPointers.push(reader.string());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          const entry14 = BankListingV4_BankTablesEntry.decode(reader, reader.uint32());
          if (entry14.value !== undefined) {
            message.bankTables[entry14.key] = entry14.value;
          }
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          const entry15 = BankListingV4_BankPointersEntry.decode(reader, reader.uint32());
          if (entry15.value !== undefined) {
            message.bankPointers[entry15.key] = entry15.value;
          }
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.mediaItems.push(MediaItem.decode(reader, reader.uint32()));
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.userBankRelationshipType = reader.string();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.userBankRelationship = BankListingV4_UserBankRelationship.decode(reader, reader.uint32());
          continue;
        }
        case 21: {
          if (tag !== 173) {
            break;
          }

          message.seniorRoiDiff = reader.float();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListingV4 {
    return {
      bankResponse: isSet(object.bankResponse) ? BankResponse.fromJSON(object.bankResponse) : undefined,
      marketingHighlights: globalThis.Array.isArray(object?.marketingHighlights)
        ? object.marketingHighlights.map((e: any) => globalThis.String(e))
        : [],
      tenures: globalThis.Array.isArray(object?.tenures) ? object.tenures.map((e: any) => Tenure.fromJSON(e)) : [],
      womenRoiDiff: isSet(object.womenRoiDiff) ? globalThis.Number(object.womenRoiDiff) : undefined,
      isRateChangeNotificationEnabled: isSet(object.isRateChangeNotificationEnabled)
        ? globalThis.Boolean(object.isRateChangeNotificationEnabled)
        : false,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      sipRedirectDeeplink: isSet(object.sipRedirectDeeplink)
        ? RedirectDeeplink.fromJSON(object.sipRedirectDeeplink)
        : undefined,
      rdRedirectDeeplink: isSet(object.rdRedirectDeeplink)
        ? RedirectDeeplink.fromJSON(object.rdRedirectDeeplink)
        : undefined,
      investableBankCompare: isSet(object.investableBankCompare)
        ? InvestableBankCompareV4.fromJSON(object.investableBankCompare)
        : undefined,
      nonInvestableBankCompare: isSet(object.nonInvestableBankCompare)
        ? NonInvestableBankCompareV4.fromJSON(object.nonInvestableBankCompare)
        : undefined,
      fdWithdrawalCalculationDetails: isSet(object.fdWithdrawalCalculationDetails)
        ? FdWithdrawalCalculationDetails.fromJSON(object.fdWithdrawalCalculationDetails)
        : undefined,
      fdWithdrawalCalculation: globalThis.Array.isArray(object?.fdWithdrawalCalculation)
        ? object.fdWithdrawalCalculation.map((e: any) => FdWithdrawalCalculation.fromJSON(e))
        : [],
      userDeviceInvestabilityStatus: isSet(object.userDeviceInvestabilityStatus)
        ? deviceInvestabilityStatusFromJSON(object.userDeviceInvestabilityStatus)
        : 0,
      shareImageUrl: isSet(object.shareImageUrl) ? globalThis.String(object.shareImageUrl) : "",
      highlightPointers: globalThis.Array.isArray(object?.highlightPointers)
        ? object.highlightPointers.map((e: any) => globalThis.String(e))
        : [],
      bankTables: isObject(object.bankTables)
        ? Object.entries(object.bankTables).reduce<{ [key: string]: BankTable }>((acc, [key, value]) => {
          acc[key] = BankTable.fromJSON(value);
          return acc;
        }, {})
        : {},
      bankPointers: isObject(object.bankPointers)
        ? Object.entries(object.bankPointers).reduce<{ [key: string]: BankPointers }>((acc, [key, value]) => {
          acc[key] = BankPointers.fromJSON(value);
          return acc;
        }, {})
        : {},
      mediaItems: globalThis.Array.isArray(object?.mediaItems)
        ? object.mediaItems.map((e: any) => MediaItem.fromJSON(e))
        : [],
      userBankRelationshipType: isSet(object.userBankRelationshipType)
        ? globalThis.String(object.userBankRelationshipType)
        : "",
      userBankRelationship: isSet(object.userBankRelationship)
        ? BankListingV4_UserBankRelationship.fromJSON(object.userBankRelationship)
        : undefined,
      seniorRoiDiff: isSet(object.seniorRoiDiff) ? globalThis.Number(object.seniorRoiDiff) : undefined,
    };
  },

  toJSON(message: BankListingV4): unknown {
    const obj: any = {};
    if (message.bankResponse !== undefined) {
      obj.bankResponse = BankResponse.toJSON(message.bankResponse);
    }
    if (message.marketingHighlights?.length) {
      obj.marketingHighlights = message.marketingHighlights;
    }
    if (message.tenures?.length) {
      obj.tenures = message.tenures.map((e) => Tenure.toJSON(e));
    }
    if (message.womenRoiDiff !== undefined) {
      obj.womenRoiDiff = message.womenRoiDiff;
    }
    if (message.isRateChangeNotificationEnabled !== false) {
      obj.isRateChangeNotificationEnabled = message.isRateChangeNotificationEnabled;
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.sipRedirectDeeplink !== undefined) {
      obj.sipRedirectDeeplink = RedirectDeeplink.toJSON(message.sipRedirectDeeplink);
    }
    if (message.rdRedirectDeeplink !== undefined) {
      obj.rdRedirectDeeplink = RedirectDeeplink.toJSON(message.rdRedirectDeeplink);
    }
    if (message.investableBankCompare !== undefined) {
      obj.investableBankCompare = InvestableBankCompareV4.toJSON(message.investableBankCompare);
    }
    if (message.nonInvestableBankCompare !== undefined) {
      obj.nonInvestableBankCompare = NonInvestableBankCompareV4.toJSON(message.nonInvestableBankCompare);
    }
    if (message.fdWithdrawalCalculationDetails !== undefined) {
      obj.fdWithdrawalCalculationDetails = FdWithdrawalCalculationDetails.toJSON(
        message.fdWithdrawalCalculationDetails,
      );
    }
    if (message.fdWithdrawalCalculation?.length) {
      obj.fdWithdrawalCalculation = message.fdWithdrawalCalculation.map((e) => FdWithdrawalCalculation.toJSON(e));
    }
    if (message.userDeviceInvestabilityStatus !== 0) {
      obj.userDeviceInvestabilityStatus = deviceInvestabilityStatusToJSON(message.userDeviceInvestabilityStatus);
    }
    if (message.shareImageUrl !== "") {
      obj.shareImageUrl = message.shareImageUrl;
    }
    if (message.highlightPointers?.length) {
      obj.highlightPointers = message.highlightPointers;
    }
    if (message.bankTables) {
      const entries = Object.entries(message.bankTables);
      if (entries.length > 0) {
        obj.bankTables = {};
        entries.forEach(([k, v]) => {
          obj.bankTables[k] = BankTable.toJSON(v);
        });
      }
    }
    if (message.bankPointers) {
      const entries = Object.entries(message.bankPointers);
      if (entries.length > 0) {
        obj.bankPointers = {};
        entries.forEach(([k, v]) => {
          obj.bankPointers[k] = BankPointers.toJSON(v);
        });
      }
    }
    if (message.mediaItems?.length) {
      obj.mediaItems = message.mediaItems.map((e) => MediaItem.toJSON(e));
    }
    if (message.userBankRelationshipType !== "") {
      obj.userBankRelationshipType = message.userBankRelationshipType;
    }
    if (message.userBankRelationship !== undefined) {
      obj.userBankRelationship = BankListingV4_UserBankRelationship.toJSON(message.userBankRelationship);
    }
    if (message.seniorRoiDiff !== undefined) {
      obj.seniorRoiDiff = message.seniorRoiDiff;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListingV4>, I>>(base?: I): BankListingV4 {
    return BankListingV4.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListingV4>, I>>(object: I): BankListingV4 {
    const message = createBaseBankListingV4();
    message.bankResponse = (object.bankResponse !== undefined && object.bankResponse !== null)
      ? BankResponse.fromPartial(object.bankResponse)
      : undefined;
    message.marketingHighlights = object.marketingHighlights?.map((e) => e) || [];
    message.tenures = object.tenures?.map((e) => Tenure.fromPartial(e)) || [];
    message.womenRoiDiff = object.womenRoiDiff ?? undefined;
    message.isRateChangeNotificationEnabled = object.isRateChangeNotificationEnabled ?? false;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.sipRedirectDeeplink = (object.sipRedirectDeeplink !== undefined && object.sipRedirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.sipRedirectDeeplink)
      : undefined;
    message.rdRedirectDeeplink = (object.rdRedirectDeeplink !== undefined && object.rdRedirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.rdRedirectDeeplink)
      : undefined;
    message.investableBankCompare =
      (object.investableBankCompare !== undefined && object.investableBankCompare !== null)
        ? InvestableBankCompareV4.fromPartial(object.investableBankCompare)
        : undefined;
    message.nonInvestableBankCompare =
      (object.nonInvestableBankCompare !== undefined && object.nonInvestableBankCompare !== null)
        ? NonInvestableBankCompareV4.fromPartial(object.nonInvestableBankCompare)
        : undefined;
    message.fdWithdrawalCalculationDetails =
      (object.fdWithdrawalCalculationDetails !== undefined && object.fdWithdrawalCalculationDetails !== null)
        ? FdWithdrawalCalculationDetails.fromPartial(object.fdWithdrawalCalculationDetails)
        : undefined;
    message.fdWithdrawalCalculation =
      object.fdWithdrawalCalculation?.map((e) => FdWithdrawalCalculation.fromPartial(e)) || [];
    message.userDeviceInvestabilityStatus = object.userDeviceInvestabilityStatus ?? 0;
    message.shareImageUrl = object.shareImageUrl ?? "";
    message.highlightPointers = object.highlightPointers?.map((e) => e) || [];
    message.bankTables = Object.entries(object.bankTables ?? {}).reduce<{ [key: string]: BankTable }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = BankTable.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.bankPointers = Object.entries(object.bankPointers ?? {}).reduce<{ [key: string]: BankPointers }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = BankPointers.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.mediaItems = object.mediaItems?.map((e) => MediaItem.fromPartial(e)) || [];
    message.userBankRelationshipType = object.userBankRelationshipType ?? "";
    message.userBankRelationship = (object.userBankRelationship !== undefined && object.userBankRelationship !== null)
      ? BankListingV4_UserBankRelationship.fromPartial(object.userBankRelationship)
      : undefined;
    message.seniorRoiDiff = object.seniorRoiDiff ?? undefined;
    return message;
  },
};

function createBaseBankListingV4_UserBankRelationship(): BankListingV4_UserBankRelationship {
  return {
    relationshipType: "",
    numberOfBookings: 0,
    totalInvestedAmount: 0,
    lastInvestedOn: "",
    vkycCompleted: false,
  };
}

export const BankListingV4_UserBankRelationship: MessageFns<BankListingV4_UserBankRelationship> = {
  encode(message: BankListingV4_UserBankRelationship, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.relationshipType !== "") {
      writer.uint32(10).string(message.relationshipType);
    }
    if (message.numberOfBookings !== 0) {
      writer.uint32(16).int32(message.numberOfBookings);
    }
    if (message.totalInvestedAmount !== 0) {
      writer.uint32(25).double(message.totalInvestedAmount);
    }
    if (message.lastInvestedOn !== "") {
      writer.uint32(34).string(message.lastInvestedOn);
    }
    if (message.vkycCompleted !== false) {
      writer.uint32(40).bool(message.vkycCompleted);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListingV4_UserBankRelationship {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListingV4_UserBankRelationship();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.relationshipType = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.numberOfBookings = reader.int32();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.totalInvestedAmount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.lastInvestedOn = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.vkycCompleted = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListingV4_UserBankRelationship {
    return {
      relationshipType: isSet(object.relationshipType) ? globalThis.String(object.relationshipType) : "",
      numberOfBookings: isSet(object.numberOfBookings) ? globalThis.Number(object.numberOfBookings) : 0,
      totalInvestedAmount: isSet(object.totalInvestedAmount) ? globalThis.Number(object.totalInvestedAmount) : 0,
      lastInvestedOn: isSet(object.lastInvestedOn) ? globalThis.String(object.lastInvestedOn) : "",
      vkycCompleted: isSet(object.vkycCompleted) ? globalThis.Boolean(object.vkycCompleted) : false,
    };
  },

  toJSON(message: BankListingV4_UserBankRelationship): unknown {
    const obj: any = {};
    if (message.relationshipType !== "") {
      obj.relationshipType = message.relationshipType;
    }
    if (message.numberOfBookings !== 0) {
      obj.numberOfBookings = Math.round(message.numberOfBookings);
    }
    if (message.totalInvestedAmount !== 0) {
      obj.totalInvestedAmount = message.totalInvestedAmount;
    }
    if (message.lastInvestedOn !== "") {
      obj.lastInvestedOn = message.lastInvestedOn;
    }
    if (message.vkycCompleted !== false) {
      obj.vkycCompleted = message.vkycCompleted;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListingV4_UserBankRelationship>, I>>(
    base?: I,
  ): BankListingV4_UserBankRelationship {
    return BankListingV4_UserBankRelationship.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListingV4_UserBankRelationship>, I>>(
    object: I,
  ): BankListingV4_UserBankRelationship {
    const message = createBaseBankListingV4_UserBankRelationship();
    message.relationshipType = object.relationshipType ?? "";
    message.numberOfBookings = object.numberOfBookings ?? 0;
    message.totalInvestedAmount = object.totalInvestedAmount ?? 0;
    message.lastInvestedOn = object.lastInvestedOn ?? "";
    message.vkycCompleted = object.vkycCompleted ?? false;
    return message;
  },
};

function createBaseBankListingV4_BankTablesEntry(): BankListingV4_BankTablesEntry {
  return { key: "", value: undefined };
}

export const BankListingV4_BankTablesEntry: MessageFns<BankListingV4_BankTablesEntry> = {
  encode(message: BankListingV4_BankTablesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      BankTable.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListingV4_BankTablesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListingV4_BankTablesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = BankTable.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListingV4_BankTablesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? BankTable.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: BankListingV4_BankTablesEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = BankTable.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListingV4_BankTablesEntry>, I>>(base?: I): BankListingV4_BankTablesEntry {
    return BankListingV4_BankTablesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListingV4_BankTablesEntry>, I>>(
    object: I,
  ): BankListingV4_BankTablesEntry {
    const message = createBaseBankListingV4_BankTablesEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? BankTable.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseBankListingV4_BankPointersEntry(): BankListingV4_BankPointersEntry {
  return { key: "", value: undefined };
}

export const BankListingV4_BankPointersEntry: MessageFns<BankListingV4_BankPointersEntry> = {
  encode(message: BankListingV4_BankPointersEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      BankPointers.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankListingV4_BankPointersEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankListingV4_BankPointersEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = BankPointers.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankListingV4_BankPointersEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? BankPointers.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: BankListingV4_BankPointersEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = BankPointers.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankListingV4_BankPointersEntry>, I>>(base?: I): BankListingV4_BankPointersEntry {
    return BankListingV4_BankPointersEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankListingV4_BankPointersEntry>, I>>(
    object: I,
  ): BankListingV4_BankPointersEntry {
    const message = createBaseBankListingV4_BankPointersEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? BankPointers.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseBankTable(): BankTable {
  return { items: [], footerText: "" };
}

export const BankTable: MessageFns<BankTable> = {
  encode(message: BankTable, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.items) {
      BankTableItem.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.footerText !== "") {
      writer.uint32(18).string(message.footerText);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankTable {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankTable();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.items.push(BankTableItem.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.footerText = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankTable {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => BankTableItem.fromJSON(e)) : [],
      footerText: isSet(object.footerText) ? globalThis.String(object.footerText) : "",
    };
  },

  toJSON(message: BankTable): unknown {
    const obj: any = {};
    if (message.items?.length) {
      obj.items = message.items.map((e) => BankTableItem.toJSON(e));
    }
    if (message.footerText !== "") {
      obj.footerText = message.footerText;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankTable>, I>>(base?: I): BankTable {
    return BankTable.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankTable>, I>>(object: I): BankTable {
    const message = createBaseBankTable();
    message.items = object.items?.map((e) => BankTableItem.fromPartial(e)) || [];
    message.footerText = object.footerText ?? "";
    return message;
  },
};

function createBaseBankTableItem(): BankTableItem {
  return { key: "", value: "", subValue: undefined, tags: [] };
}

export const BankTableItem: MessageFns<BankTableItem> = {
  encode(message: BankTableItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    if (message.subValue !== undefined) {
      writer.uint32(26).string(message.subValue);
    }
    for (const v of message.tags) {
      writer.uint32(34).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankTableItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankTableItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subValue = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.tags.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankTableItem {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
      subValue: isSet(object.subValue) ? globalThis.String(object.subValue) : undefined,
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: BankTableItem): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    if (message.subValue !== undefined) {
      obj.subValue = message.subValue;
    }
    if (message.tags?.length) {
      obj.tags = message.tags;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankTableItem>, I>>(base?: I): BankTableItem {
    return BankTableItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankTableItem>, I>>(object: I): BankTableItem {
    const message = createBaseBankTableItem();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    message.subValue = object.subValue ?? undefined;
    message.tags = object.tags?.map((e) => e) || [];
    return message;
  },
};

function createBaseBankPointers(): BankPointers {
  return { items: [] };
}

export const BankPointers: MessageFns<BankPointers> = {
  encode(message: BankPointers, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.items) {
      BankPointerItem.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankPointers {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankPointers();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.items.push(BankPointerItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankPointers {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => BankPointerItem.fromJSON(e)) : [],
    };
  },

  toJSON(message: BankPointers): unknown {
    const obj: any = {};
    if (message.items?.length) {
      obj.items = message.items.map((e) => BankPointerItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankPointers>, I>>(base?: I): BankPointers {
    return BankPointers.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankPointers>, I>>(object: I): BankPointers {
    const message = createBaseBankPointers();
    message.items = object.items?.map((e) => BankPointerItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBankPointerItem(): BankPointerItem {
  return { pointer: "", tags: [] };
}

export const BankPointerItem: MessageFns<BankPointerItem> = {
  encode(message: BankPointerItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pointer !== "") {
      writer.uint32(10).string(message.pointer);
    }
    for (const v of message.tags) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankPointerItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankPointerItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pointer = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tags.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankPointerItem {
    return {
      pointer: isSet(object.pointer) ? globalThis.String(object.pointer) : "",
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: BankPointerItem): unknown {
    const obj: any = {};
    if (message.pointer !== "") {
      obj.pointer = message.pointer;
    }
    if (message.tags?.length) {
      obj.tags = message.tags;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankPointerItem>, I>>(base?: I): BankPointerItem {
    return BankPointerItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankPointerItem>, I>>(object: I): BankPointerItem {
    const message = createBaseBankPointerItem();
    message.pointer = object.pointer ?? "";
    message.tags = object.tags?.map((e) => e) || [];
    return message;
  },
};

function createBaseTenure(): Tenure {
  return {
    rawTenure: "",
    tenureFormatType: 0,
    rates: {},
    minTenureInDays: 0,
    maxTenureInDays: 0,
    tenure: "",
    tag: "",
    interestPayoutToMaturityInstructionMapping: [],
    tenureInDays: 0,
    tenureInMonths: 0,
    tenureInYears: 0,
    fdId: "",
    isHighestForInvestorType: {},
    interestRates: {},
    defaultInterestPayoutType: 0,
    defaultMaturityInstruction: 0,
    minimumDeposit: 0,
    maximumDeposit: 0,
  };
}

export const Tenure: MessageFns<Tenure> = {
  encode(message: Tenure, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rawTenure !== "") {
      writer.uint32(10).string(message.rawTenure);
    }
    if (message.tenureFormatType !== 0) {
      writer.uint32(16).int32(message.tenureFormatType);
    }
    Object.entries(message.rates).forEach(([key, value]) => {
      Tenure_RatesEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).join();
    });
    if (message.minTenureInDays !== 0) {
      writer.uint32(32).int32(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      writer.uint32(40).int32(message.maxTenureInDays);
    }
    if (message.tenure !== "") {
      writer.uint32(50).string(message.tenure);
    }
    if (message.tag !== "") {
      writer.uint32(66).string(message.tag);
    }
    for (const v of message.interestPayoutToMaturityInstructionMapping) {
      InterestPayoutToMaturityInstructionMapping.encode(v!, writer.uint32(74).fork()).join();
    }
    if (message.tenureInDays !== 0) {
      writer.uint32(80).int32(message.tenureInDays);
    }
    if (message.tenureInMonths !== 0) {
      writer.uint32(88).int32(message.tenureInMonths);
    }
    if (message.tenureInYears !== 0) {
      writer.uint32(96).int32(message.tenureInYears);
    }
    if (message.fdId !== "") {
      writer.uint32(106).string(message.fdId);
    }
    Object.entries(message.isHighestForInvestorType).forEach(([key, value]) => {
      Tenure_IsHighestForInvestorTypeEntry.encode({ key: key as any, value }, writer.uint32(114).fork()).join();
    });
    Object.entries(message.interestRates).forEach(([key, value]) => {
      Tenure_InterestRatesEntry.encode({ key: key as any, value }, writer.uint32(122).fork()).join();
    });
    if (message.defaultInterestPayoutType !== 0) {
      writer.uint32(128).int32(message.defaultInterestPayoutType);
    }
    if (message.defaultMaturityInstruction !== 0) {
      writer.uint32(136).int32(message.defaultMaturityInstruction);
    }
    if (message.minimumDeposit !== 0) {
      writer.uint32(145).double(message.minimumDeposit);
    }
    if (message.maximumDeposit !== 0) {
      writer.uint32(153).double(message.maximumDeposit);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tenure {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenure();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rawTenure = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.tenureFormatType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          const entry3 = Tenure_RatesEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.rates[entry3.key] = entry3.value;
          }
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.minTenureInDays = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.maxTenureInDays = reader.int32();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.tag = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.interestPayoutToMaturityInstructionMapping.push(
            InterestPayoutToMaturityInstructionMapping.decode(reader, reader.uint32()),
          );
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.tenureInDays = reader.int32();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.tenureInMonths = reader.int32();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.tenureInYears = reader.int32();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.fdId = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          const entry14 = Tenure_IsHighestForInvestorTypeEntry.decode(reader, reader.uint32());
          if (entry14.value !== undefined) {
            message.isHighestForInvestorType[entry14.key] = entry14.value;
          }
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          const entry15 = Tenure_InterestRatesEntry.decode(reader, reader.uint32());
          if (entry15.value !== undefined) {
            message.interestRates[entry15.key] = entry15.value;
          }
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.defaultInterestPayoutType = reader.int32() as any;
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.defaultMaturityInstruction = reader.int32() as any;
          continue;
        }
        case 18: {
          if (tag !== 145) {
            break;
          }

          message.minimumDeposit = reader.double();
          continue;
        }
        case 19: {
          if (tag !== 153) {
            break;
          }

          message.maximumDeposit = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Tenure {
    return {
      rawTenure: isSet(object.rawTenure) ? globalThis.String(object.rawTenure) : "",
      tenureFormatType: isSet(object.tenureFormatType) ? tenureFormatTypeFromJSON(object.tenureFormatType) : 0,
      rates: isObject(object.rates)
        ? Object.entries(object.rates).reduce<{ [key: string]: number }>((acc, [key, value]) => {
          acc[key] = Number(value);
          return acc;
        }, {})
        : {},
      minTenureInDays: isSet(object.minTenureInDays) ? globalThis.Number(object.minTenureInDays) : 0,
      maxTenureInDays: isSet(object.maxTenureInDays) ? globalThis.Number(object.maxTenureInDays) : 0,
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      tag: isSet(object.tag) ? globalThis.String(object.tag) : "",
      interestPayoutToMaturityInstructionMapping:
        globalThis.Array.isArray(object?.interestPayoutToMaturityInstructionMapping)
          ? object.interestPayoutToMaturityInstructionMapping.map((e: any) =>
            InterestPayoutToMaturityInstructionMapping.fromJSON(e)
          )
          : [],
      tenureInDays: isSet(object.tenureInDays) ? globalThis.Number(object.tenureInDays) : 0,
      tenureInMonths: isSet(object.tenureInMonths) ? globalThis.Number(object.tenureInMonths) : 0,
      tenureInYears: isSet(object.tenureInYears) ? globalThis.Number(object.tenureInYears) : 0,
      fdId: isSet(object.fdId) ? globalThis.String(object.fdId) : "",
      isHighestForInvestorType: isObject(object.isHighestForInvestorType)
        ? Object.entries(object.isHighestForInvestorType).reduce<{ [key: string]: boolean }>((acc, [key, value]) => {
          acc[key] = Boolean(value);
          return acc;
        }, {})
        : {},
      interestRates: isObject(object.interestRates)
        ? Object.entries(object.interestRates).reduce<{ [key: string]: InterestRate }>((acc, [key, value]) => {
          acc[key] = InterestRate.fromJSON(value);
          return acc;
        }, {})
        : {},
      defaultInterestPayoutType: isSet(object.defaultInterestPayoutType)
        ? interestPayoutTypeFromJSON(object.defaultInterestPayoutType)
        : 0,
      defaultMaturityInstruction: isSet(object.defaultMaturityInstruction)
        ? maturityInstructionFromJSON(object.defaultMaturityInstruction)
        : 0,
      minimumDeposit: isSet(object.minimumDeposit) ? globalThis.Number(object.minimumDeposit) : 0,
      maximumDeposit: isSet(object.maximumDeposit) ? globalThis.Number(object.maximumDeposit) : 0,
    };
  },

  toJSON(message: Tenure): unknown {
    const obj: any = {};
    if (message.rawTenure !== "") {
      obj.rawTenure = message.rawTenure;
    }
    if (message.tenureFormatType !== 0) {
      obj.tenureFormatType = tenureFormatTypeToJSON(message.tenureFormatType);
    }
    if (message.rates) {
      const entries = Object.entries(message.rates);
      if (entries.length > 0) {
        obj.rates = {};
        entries.forEach(([k, v]) => {
          obj.rates[k] = v;
        });
      }
    }
    if (message.minTenureInDays !== 0) {
      obj.minTenureInDays = Math.round(message.minTenureInDays);
    }
    if (message.maxTenureInDays !== 0) {
      obj.maxTenureInDays = Math.round(message.maxTenureInDays);
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.tag !== "") {
      obj.tag = message.tag;
    }
    if (message.interestPayoutToMaturityInstructionMapping?.length) {
      obj.interestPayoutToMaturityInstructionMapping = message.interestPayoutToMaturityInstructionMapping.map((e) =>
        InterestPayoutToMaturityInstructionMapping.toJSON(e)
      );
    }
    if (message.tenureInDays !== 0) {
      obj.tenureInDays = Math.round(message.tenureInDays);
    }
    if (message.tenureInMonths !== 0) {
      obj.tenureInMonths = Math.round(message.tenureInMonths);
    }
    if (message.tenureInYears !== 0) {
      obj.tenureInYears = Math.round(message.tenureInYears);
    }
    if (message.fdId !== "") {
      obj.fdId = message.fdId;
    }
    if (message.isHighestForInvestorType) {
      const entries = Object.entries(message.isHighestForInvestorType);
      if (entries.length > 0) {
        obj.isHighestForInvestorType = {};
        entries.forEach(([k, v]) => {
          obj.isHighestForInvestorType[k] = v;
        });
      }
    }
    if (message.interestRates) {
      const entries = Object.entries(message.interestRates);
      if (entries.length > 0) {
        obj.interestRates = {};
        entries.forEach(([k, v]) => {
          obj.interestRates[k] = InterestRate.toJSON(v);
        });
      }
    }
    if (message.defaultInterestPayoutType !== 0) {
      obj.defaultInterestPayoutType = interestPayoutTypeToJSON(message.defaultInterestPayoutType);
    }
    if (message.defaultMaturityInstruction !== 0) {
      obj.defaultMaturityInstruction = maturityInstructionToJSON(message.defaultMaturityInstruction);
    }
    if (message.minimumDeposit !== 0) {
      obj.minimumDeposit = message.minimumDeposit;
    }
    if (message.maximumDeposit !== 0) {
      obj.maximumDeposit = message.maximumDeposit;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Tenure>, I>>(base?: I): Tenure {
    return Tenure.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tenure>, I>>(object: I): Tenure {
    const message = createBaseTenure();
    message.rawTenure = object.rawTenure ?? "";
    message.tenureFormatType = object.tenureFormatType ?? 0;
    message.rates = Object.entries(object.rates ?? {}).reduce<{ [key: string]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.minTenureInDays = object.minTenureInDays ?? 0;
    message.maxTenureInDays = object.maxTenureInDays ?? 0;
    message.tenure = object.tenure ?? "";
    message.tag = object.tag ?? "";
    message.interestPayoutToMaturityInstructionMapping =
      object.interestPayoutToMaturityInstructionMapping?.map((e) =>
        InterestPayoutToMaturityInstructionMapping.fromPartial(e)
      ) || [];
    message.tenureInDays = object.tenureInDays ?? 0;
    message.tenureInMonths = object.tenureInMonths ?? 0;
    message.tenureInYears = object.tenureInYears ?? 0;
    message.fdId = object.fdId ?? "";
    message.isHighestForInvestorType = Object.entries(object.isHighestForInvestorType ?? {}).reduce<
      { [key: string]: boolean }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.Boolean(value);
      }
      return acc;
    }, {});
    message.interestRates = Object.entries(object.interestRates ?? {}).reduce<{ [key: string]: InterestRate }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = InterestRate.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    message.defaultInterestPayoutType = object.defaultInterestPayoutType ?? 0;
    message.defaultMaturityInstruction = object.defaultMaturityInstruction ?? 0;
    message.minimumDeposit = object.minimumDeposit ?? 0;
    message.maximumDeposit = object.maximumDeposit ?? 0;
    return message;
  },
};

function createBaseTenure_RatesEntry(): Tenure_RatesEntry {
  return { key: "", value: 0 };
}

export const Tenure_RatesEntry: MessageFns<Tenure_RatesEntry> = {
  encode(message: Tenure_RatesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(17).double(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tenure_RatesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenure_RatesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.value = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Tenure_RatesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
    };
  },

  toJSON(message: Tenure_RatesEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== 0) {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Tenure_RatesEntry>, I>>(base?: I): Tenure_RatesEntry {
    return Tenure_RatesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tenure_RatesEntry>, I>>(object: I): Tenure_RatesEntry {
    const message = createBaseTenure_RatesEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTenure_IsHighestForInvestorTypeEntry(): Tenure_IsHighestForInvestorTypeEntry {
  return { key: "", value: false };
}

export const Tenure_IsHighestForInvestorTypeEntry: MessageFns<Tenure_IsHighestForInvestorTypeEntry> = {
  encode(message: Tenure_IsHighestForInvestorTypeEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== false) {
      writer.uint32(16).bool(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tenure_IsHighestForInvestorTypeEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenure_IsHighestForInvestorTypeEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.value = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Tenure_IsHighestForInvestorTypeEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.Boolean(object.value) : false,
    };
  },

  toJSON(message: Tenure_IsHighestForInvestorTypeEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== false) {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Tenure_IsHighestForInvestorTypeEntry>, I>>(
    base?: I,
  ): Tenure_IsHighestForInvestorTypeEntry {
    return Tenure_IsHighestForInvestorTypeEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tenure_IsHighestForInvestorTypeEntry>, I>>(
    object: I,
  ): Tenure_IsHighestForInvestorTypeEntry {
    const message = createBaseTenure_IsHighestForInvestorTypeEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? false;
    return message;
  },
};

function createBaseTenure_InterestRatesEntry(): Tenure_InterestRatesEntry {
  return { key: "", value: undefined };
}

export const Tenure_InterestRatesEntry: MessageFns<Tenure_InterestRatesEntry> = {
  encode(message: Tenure_InterestRatesEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      InterestRate.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Tenure_InterestRatesEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenure_InterestRatesEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = InterestRate.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Tenure_InterestRatesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? InterestRate.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: Tenure_InterestRatesEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = InterestRate.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Tenure_InterestRatesEntry>, I>>(base?: I): Tenure_InterestRatesEntry {
    return Tenure_InterestRatesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tenure_InterestRatesEntry>, I>>(object: I): Tenure_InterestRatesEntry {
    const message = createBaseTenure_InterestRatesEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? InterestRate.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseInterestRate(): InterestRate {
  return { fdId: "", rate: 0, xirr: 0 };
}

export const InterestRate: MessageFns<InterestRate> = {
  encode(message: InterestRate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdId !== "") {
      writer.uint32(26).string(message.fdId);
    }
    if (message.rate !== 0) {
      writer.uint32(9).double(message.rate);
    }
    if (message.xirr !== 0) {
      writer.uint32(17).double(message.xirr);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InterestRate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInterestRate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fdId = reader.string();
          continue;
        }
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.rate = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.xirr = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InterestRate {
    return {
      fdId: isSet(object.fdId) ? globalThis.String(object.fdId) : "",
      rate: isSet(object.rate) ? globalThis.Number(object.rate) : 0,
      xirr: isSet(object.xirr) ? globalThis.Number(object.xirr) : 0,
    };
  },

  toJSON(message: InterestRate): unknown {
    const obj: any = {};
    if (message.fdId !== "") {
      obj.fdId = message.fdId;
    }
    if (message.rate !== 0) {
      obj.rate = message.rate;
    }
    if (message.xirr !== 0) {
      obj.xirr = message.xirr;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InterestRate>, I>>(base?: I): InterestRate {
    return InterestRate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InterestRate>, I>>(object: I): InterestRate {
    const message = createBaseInterestRate();
    message.fdId = object.fdId ?? "";
    message.rate = object.rate ?? 0;
    message.xirr = object.xirr ?? 0;
    return message;
  },
};

function createBaseInterestPayoutToMaturityInstructionMapping(): InterestPayoutToMaturityInstructionMapping {
  return { interestPayoutType: 0, instructions: [] };
}

export const InterestPayoutToMaturityInstructionMapping: MessageFns<InterestPayoutToMaturityInstructionMapping> = {
  encode(message: InterestPayoutToMaturityInstructionMapping, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.interestPayoutType !== 0) {
      writer.uint32(8).int32(message.interestPayoutType);
    }
    writer.uint32(18).fork();
    for (const v of message.instructions) {
      writer.int32(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InterestPayoutToMaturityInstructionMapping {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInterestPayoutToMaturityInstructionMapping();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.interestPayoutType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag === 16) {
            message.instructions.push(reader.int32() as any);

            continue;
          }

          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.instructions.push(reader.int32() as any);
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InterestPayoutToMaturityInstructionMapping {
    return {
      interestPayoutType: isSet(object.interestPayoutType) ? interestPayoutTypeFromJSON(object.interestPayoutType) : 0,
      instructions: globalThis.Array.isArray(object?.instructions)
        ? object.instructions.map((e: any) => maturityInstructionFromJSON(e))
        : [],
    };
  },

  toJSON(message: InterestPayoutToMaturityInstructionMapping): unknown {
    const obj: any = {};
    if (message.interestPayoutType !== 0) {
      obj.interestPayoutType = interestPayoutTypeToJSON(message.interestPayoutType);
    }
    if (message.instructions?.length) {
      obj.instructions = message.instructions.map((e) => maturityInstructionToJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InterestPayoutToMaturityInstructionMapping>, I>>(
    base?: I,
  ): InterestPayoutToMaturityInstructionMapping {
    return InterestPayoutToMaturityInstructionMapping.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InterestPayoutToMaturityInstructionMapping>, I>>(
    object: I,
  ): InterestPayoutToMaturityInstructionMapping {
    const message = createBaseInterestPayoutToMaturityInstructionMapping();
    message.interestPayoutType = object.interestPayoutType ?? 0;
    message.instructions = object.instructions?.map((e) => e) || [];
    return message;
  },
};

function createBaseBankSummaryResponse(): BankSummaryResponse {
  return { id: "", name: "", logoUrl: "", shortName: "", highestInterestRate: undefined };
}

export const BankSummaryResponse: MessageFns<BankSummaryResponse> = {
  encode(message: BankSummaryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.logoUrl !== "") {
      writer.uint32(26).string(message.logoUrl);
    }
    if (message.shortName !== "") {
      writer.uint32(34).string(message.shortName);
    }
    if (message.highestInterestRate !== undefined) {
      writer.uint32(41).double(message.highestInterestRate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankSummaryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankSummaryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.logoUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.shortName = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.highestInterestRate = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankSummaryResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      logoUrl: isSet(object.logoUrl) ? globalThis.String(object.logoUrl) : "",
      shortName: isSet(object.shortName) ? globalThis.String(object.shortName) : "",
      highestInterestRate: isSet(object.highestInterestRate)
        ? globalThis.Number(object.highestInterestRate)
        : undefined,
    };
  },

  toJSON(message: BankSummaryResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.logoUrl !== "") {
      obj.logoUrl = message.logoUrl;
    }
    if (message.shortName !== "") {
      obj.shortName = message.shortName;
    }
    if (message.highestInterestRate !== undefined) {
      obj.highestInterestRate = message.highestInterestRate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankSummaryResponse>, I>>(base?: I): BankSummaryResponse {
    return BankSummaryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankSummaryResponse>, I>>(object: I): BankSummaryResponse {
    const message = createBaseBankSummaryResponse();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.logoUrl = object.logoUrl ?? "";
    message.shortName = object.shortName ?? "";
    message.highestInterestRate = object.highestInterestRate ?? undefined;
    return message;
  },
};

function createBaseInvestableBankCompareV4(): InvestableBankCompareV4 {
  return { comparedBanks: [] };
}

export const InvestableBankCompareV4: MessageFns<InvestableBankCompareV4> = {
  encode(message: InvestableBankCompareV4, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.comparedBanks) {
      BankSummaryResponse.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestableBankCompareV4 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestableBankCompareV4();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.comparedBanks.push(BankSummaryResponse.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestableBankCompareV4 {
    return {
      comparedBanks: globalThis.Array.isArray(object?.comparedBanks)
        ? object.comparedBanks.map((e: any) => BankSummaryResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: InvestableBankCompareV4): unknown {
    const obj: any = {};
    if (message.comparedBanks?.length) {
      obj.comparedBanks = message.comparedBanks.map((e) => BankSummaryResponse.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestableBankCompareV4>, I>>(base?: I): InvestableBankCompareV4 {
    return InvestableBankCompareV4.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestableBankCompareV4>, I>>(object: I): InvestableBankCompareV4 {
    const message = createBaseInvestableBankCompareV4();
    message.comparedBanks = object.comparedBanks?.map((e) => BankSummaryResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseNonInvestableBankCompareV4(): NonInvestableBankCompareV4 {
  return { currentBank: undefined, highestBank: undefined, lineItems: [] };
}

export const NonInvestableBankCompareV4: MessageFns<NonInvestableBankCompareV4> = {
  encode(message: NonInvestableBankCompareV4, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.currentBank !== undefined) {
      BankSummaryResponse.encode(message.currentBank, writer.uint32(10).fork()).join();
    }
    if (message.highestBank !== undefined) {
      BankSummaryResponse.encode(message.highestBank, writer.uint32(18).fork()).join();
    }
    for (const v of message.lineItems) {
      CompareLineItem.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NonInvestableBankCompareV4 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNonInvestableBankCompareV4();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.currentBank = BankSummaryResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.highestBank = BankSummaryResponse.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.lineItems.push(CompareLineItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NonInvestableBankCompareV4 {
    return {
      currentBank: isSet(object.currentBank) ? BankSummaryResponse.fromJSON(object.currentBank) : undefined,
      highestBank: isSet(object.highestBank) ? BankSummaryResponse.fromJSON(object.highestBank) : undefined,
      lineItems: globalThis.Array.isArray(object?.lineItems)
        ? object.lineItems.map((e: any) => CompareLineItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: NonInvestableBankCompareV4): unknown {
    const obj: any = {};
    if (message.currentBank !== undefined) {
      obj.currentBank = BankSummaryResponse.toJSON(message.currentBank);
    }
    if (message.highestBank !== undefined) {
      obj.highestBank = BankSummaryResponse.toJSON(message.highestBank);
    }
    if (message.lineItems?.length) {
      obj.lineItems = message.lineItems.map((e) => CompareLineItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NonInvestableBankCompareV4>, I>>(base?: I): NonInvestableBankCompareV4 {
    return NonInvestableBankCompareV4.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NonInvestableBankCompareV4>, I>>(object: I): NonInvestableBankCompareV4 {
    const message = createBaseNonInvestableBankCompareV4();
    message.currentBank = (object.currentBank !== undefined && object.currentBank !== null)
      ? BankSummaryResponse.fromPartial(object.currentBank)
      : undefined;
    message.highestBank = (object.highestBank !== undefined && object.highestBank !== null)
      ? BankSummaryResponse.fromPartial(object.highestBank)
      : undefined;
    message.lineItems = object.lineItems?.map((e) => CompareLineItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSearchBankResponse(): SearchBankResponse {
  return { banks: [] };
}

export const SearchBankResponse: MessageFns<SearchBankResponse> = {
  encode(message: SearchBankResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.banks) {
      SearchBankResponse_Bank.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchBankResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchBankResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.banks.push(SearchBankResponse_Bank.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchBankResponse {
    return {
      banks: globalThis.Array.isArray(object?.banks)
        ? object.banks.map((e: any) => SearchBankResponse_Bank.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SearchBankResponse): unknown {
    const obj: any = {};
    if (message.banks?.length) {
      obj.banks = message.banks.map((e) => SearchBankResponse_Bank.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchBankResponse>, I>>(base?: I): SearchBankResponse {
    return SearchBankResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchBankResponse>, I>>(object: I): SearchBankResponse {
    const message = createBaseSearchBankResponse();
    message.banks = object.banks?.map((e) => SearchBankResponse_Bank.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSearchBankResponse_Bank(): SearchBankResponse_Bank {
  return { bankName: "", bankLogo: "", bankId: "", isPartner: false };
}

export const SearchBankResponse_Bank: MessageFns<SearchBankResponse_Bank> = {
  encode(message: SearchBankResponse_Bank, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankName !== "") {
      writer.uint32(10).string(message.bankName);
    }
    if (message.bankLogo !== "") {
      writer.uint32(18).string(message.bankLogo);
    }
    if (message.bankId !== "") {
      writer.uint32(26).string(message.bankId);
    }
    if (message.isPartner !== false) {
      writer.uint32(32).bool(message.isPartner);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SearchBankResponse_Bank {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchBankResponse_Bank();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankLogo = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.isPartner = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SearchBankResponse_Bank {
    return {
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogo: isSet(object.bankLogo) ? globalThis.String(object.bankLogo) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      isPartner: isSet(object.isPartner) ? globalThis.Boolean(object.isPartner) : false,
    };
  },

  toJSON(message: SearchBankResponse_Bank): unknown {
    const obj: any = {};
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogo !== "") {
      obj.bankLogo = message.bankLogo;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.isPartner !== false) {
      obj.isPartner = message.isPartner;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SearchBankResponse_Bank>, I>>(base?: I): SearchBankResponse_Bank {
    return SearchBankResponse_Bank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchBankResponse_Bank>, I>>(object: I): SearchBankResponse_Bank {
    const message = createBaseSearchBankResponse_Bank();
    message.bankName = object.bankName ?? "";
    message.bankLogo = object.bankLogo ?? "";
    message.bankId = object.bankId ?? "";
    message.isPartner = object.isPartner ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
