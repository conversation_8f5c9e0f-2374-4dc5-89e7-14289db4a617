// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: WidgetCards.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum WidgetType {
  WIDGET_TYPE_UNKNOWN = 0,
  GLOBAL_WIDGET = 1,
  HOME_PAGE_WIDGET = 2,
  UNRECOGNIZED = -1,
}

export function widgetTypeFromJSON(object: any): WidgetType {
  switch (object) {
    case 0:
    case "WIDGET_TYPE_UNKNOWN":
      return WidgetType.WIDGET_TYPE_UNKNOWN;
    case 1:
    case "GLOBAL_WIDGET":
      return WidgetType.GLOBAL_WIDGET;
    case 2:
    case "HOME_PAGE_WIDGET":
      return WidgetType.HOME_PAGE_WIDGET;
    case -1:
    case "UNRECOGNIZED":
    default:
      return WidgetType.UNRECOGNIZED;
  }
}

export function widgetTypeToJSON(object: WidgetType): string {
  switch (object) {
    case WidgetType.WIDGET_TYPE_UNKNOWN:
      return "WIDGET_TYPE_UNKNOWN";
    case WidgetType.GLOBAL_WIDGET:
      return "GLOBAL_WIDGET";
    case WidgetType.HOME_PAGE_WIDGET:
      return "HOME_PAGE_WIDGET";
    case WidgetType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface HomeMessagingWidgetItem {
  title: string;
  description: string;
  iconUrl: string;
  iconType: string;
  iconWidth: number;
  iconHeight: number;
}

export interface HomeMessagingWidgetResponse {
  homeMessagingWidgetItem: HomeMessagingWidgetItem[];
}

export interface MessageWidgetMappingRequest {
  messageWidgetId: string;
  userId: string[];
}

export interface MessageWidgetMappingResponse {
  mappingsAdded: number;
  invalidUsers: number;
}

function createBaseHomeMessagingWidgetItem(): HomeMessagingWidgetItem {
  return { title: "", description: "", iconUrl: "", iconType: "", iconWidth: 0, iconHeight: 0 };
}

export const HomeMessagingWidgetItem: MessageFns<HomeMessagingWidgetItem> = {
  encode(message: HomeMessagingWidgetItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.title !== "") {
      writer.uint32(10).string(message.title);
    }
    if (message.description !== "") {
      writer.uint32(18).string(message.description);
    }
    if (message.iconUrl !== "") {
      writer.uint32(26).string(message.iconUrl);
    }
    if (message.iconType !== "") {
      writer.uint32(34).string(message.iconType);
    }
    if (message.iconWidth !== 0) {
      writer.uint32(41).double(message.iconWidth);
    }
    if (message.iconHeight !== 0) {
      writer.uint32(49).double(message.iconHeight);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HomeMessagingWidgetItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHomeMessagingWidgetItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.iconType = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.iconWidth = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.iconHeight = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HomeMessagingWidgetItem {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : "",
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      iconType: isSet(object.iconType) ? globalThis.String(object.iconType) : "",
      iconWidth: isSet(object.iconWidth) ? globalThis.Number(object.iconWidth) : 0,
      iconHeight: isSet(object.iconHeight) ? globalThis.Number(object.iconHeight) : 0,
    };
  },

  toJSON(message: HomeMessagingWidgetItem): unknown {
    const obj: any = {};
    if (message.title !== "") {
      obj.title = message.title;
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.iconType !== "") {
      obj.iconType = message.iconType;
    }
    if (message.iconWidth !== 0) {
      obj.iconWidth = message.iconWidth;
    }
    if (message.iconHeight !== 0) {
      obj.iconHeight = message.iconHeight;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HomeMessagingWidgetItem>, I>>(base?: I): HomeMessagingWidgetItem {
    return HomeMessagingWidgetItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HomeMessagingWidgetItem>, I>>(object: I): HomeMessagingWidgetItem {
    const message = createBaseHomeMessagingWidgetItem();
    message.title = object.title ?? "";
    message.description = object.description ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.iconType = object.iconType ?? "";
    message.iconWidth = object.iconWidth ?? 0;
    message.iconHeight = object.iconHeight ?? 0;
    return message;
  },
};

function createBaseHomeMessagingWidgetResponse(): HomeMessagingWidgetResponse {
  return { homeMessagingWidgetItem: [] };
}

export const HomeMessagingWidgetResponse: MessageFns<HomeMessagingWidgetResponse> = {
  encode(message: HomeMessagingWidgetResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.homeMessagingWidgetItem) {
      HomeMessagingWidgetItem.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): HomeMessagingWidgetResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHomeMessagingWidgetResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.homeMessagingWidgetItem.push(HomeMessagingWidgetItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HomeMessagingWidgetResponse {
    return {
      homeMessagingWidgetItem: globalThis.Array.isArray(object?.homeMessagingWidgetItem)
        ? object.homeMessagingWidgetItem.map((e: any) => HomeMessagingWidgetItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: HomeMessagingWidgetResponse): unknown {
    const obj: any = {};
    if (message.homeMessagingWidgetItem?.length) {
      obj.homeMessagingWidgetItem = message.homeMessagingWidgetItem.map((e) => HomeMessagingWidgetItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HomeMessagingWidgetResponse>, I>>(base?: I): HomeMessagingWidgetResponse {
    return HomeMessagingWidgetResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HomeMessagingWidgetResponse>, I>>(object: I): HomeMessagingWidgetResponse {
    const message = createBaseHomeMessagingWidgetResponse();
    message.homeMessagingWidgetItem =
      object.homeMessagingWidgetItem?.map((e) => HomeMessagingWidgetItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMessageWidgetMappingRequest(): MessageWidgetMappingRequest {
  return { messageWidgetId: "", userId: [] };
}

export const MessageWidgetMappingRequest: MessageFns<MessageWidgetMappingRequest> = {
  encode(message: MessageWidgetMappingRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.messageWidgetId !== "") {
      writer.uint32(10).string(message.messageWidgetId);
    }
    for (const v of message.userId) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageWidgetMappingRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageWidgetMappingRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.messageWidgetId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MessageWidgetMappingRequest {
    return {
      messageWidgetId: isSet(object.messageWidgetId) ? globalThis.String(object.messageWidgetId) : "",
      userId: globalThis.Array.isArray(object?.userId) ? object.userId.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: MessageWidgetMappingRequest): unknown {
    const obj: any = {};
    if (message.messageWidgetId !== "") {
      obj.messageWidgetId = message.messageWidgetId;
    }
    if (message.userId?.length) {
      obj.userId = message.userId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageWidgetMappingRequest>, I>>(base?: I): MessageWidgetMappingRequest {
    return MessageWidgetMappingRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageWidgetMappingRequest>, I>>(object: I): MessageWidgetMappingRequest {
    const message = createBaseMessageWidgetMappingRequest();
    message.messageWidgetId = object.messageWidgetId ?? "";
    message.userId = object.userId?.map((e) => e) || [];
    return message;
  },
};

function createBaseMessageWidgetMappingResponse(): MessageWidgetMappingResponse {
  return { mappingsAdded: 0, invalidUsers: 0 };
}

export const MessageWidgetMappingResponse: MessageFns<MessageWidgetMappingResponse> = {
  encode(message: MessageWidgetMappingResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.mappingsAdded !== 0) {
      writer.uint32(8).int32(message.mappingsAdded);
    }
    if (message.invalidUsers !== 0) {
      writer.uint32(16).int32(message.invalidUsers);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MessageWidgetMappingResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMessageWidgetMappingResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.mappingsAdded = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.invalidUsers = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MessageWidgetMappingResponse {
    return {
      mappingsAdded: isSet(object.mappingsAdded) ? globalThis.Number(object.mappingsAdded) : 0,
      invalidUsers: isSet(object.invalidUsers) ? globalThis.Number(object.invalidUsers) : 0,
    };
  },

  toJSON(message: MessageWidgetMappingResponse): unknown {
    const obj: any = {};
    if (message.mappingsAdded !== 0) {
      obj.mappingsAdded = Math.round(message.mappingsAdded);
    }
    if (message.invalidUsers !== 0) {
      obj.invalidUsers = Math.round(message.invalidUsers);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MessageWidgetMappingResponse>, I>>(base?: I): MessageWidgetMappingResponse {
    return MessageWidgetMappingResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MessageWidgetMappingResponse>, I>>(object: I): MessageWidgetMappingResponse {
    const message = createBaseMessageWidgetMappingResponse();
    message.mappingsAdded = object.mappingsAdded ?? 0;
    message.invalidUsers = object.invalidUsers ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
