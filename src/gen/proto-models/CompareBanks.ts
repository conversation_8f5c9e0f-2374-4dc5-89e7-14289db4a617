// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: CompareBanks.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { BankResponse, FixedDepositResponse, Tag } from "./Collection";

export const protobufPackage = "com.stablemoney.api.identity";

export interface RecommendedBankItem {
  bankResponse: BankResponse | undefined;
  lowestFixedDepositRate: number;
  highestFixedDepositRate: number;
  highestFixedDepositResponse: FixedDepositResponse | undefined;
  isRecommended: boolean;
  tag: Tag | undefined;
}

export interface RecommendedBankResponse {
  recommendedBankList: RecommendedBankItem[];
}

export interface CompareBankCell {
  stringValue?: string | undefined;
  boolValue?: boolean | undefined;
  stringColor?: string | undefined;
  caption?: string | undefined;
  captionColor?: string | undefined;
}

export interface CompareBankItem {
  compareBankList: CompareBankCell[];
}

export interface CompareBankResponse {
  recommendedBankList: RecommendedBankItem[];
  titles: string[];
  compareBankItemList: CompareBankItem[];
  addBankList: RecommendedBankItem[];
}

export interface CompareBankResponseV2 {
  compareBankItemListV2: CompareBankItemV2[];
}

export interface CompareBankItemV2 {
  bankName: string;
  bankLogo: string;
  roi: string;
  bankId: string;
}

function createBaseRecommendedBankItem(): RecommendedBankItem {
  return {
    bankResponse: undefined,
    lowestFixedDepositRate: 0,
    highestFixedDepositRate: 0,
    highestFixedDepositResponse: undefined,
    isRecommended: false,
    tag: undefined,
  };
}

export const RecommendedBankItem: MessageFns<RecommendedBankItem> = {
  encode(message: RecommendedBankItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankResponse !== undefined) {
      BankResponse.encode(message.bankResponse, writer.uint32(10).fork()).join();
    }
    if (message.lowestFixedDepositRate !== 0) {
      writer.uint32(17).double(message.lowestFixedDepositRate);
    }
    if (message.highestFixedDepositRate !== 0) {
      writer.uint32(25).double(message.highestFixedDepositRate);
    }
    if (message.highestFixedDepositResponse !== undefined) {
      FixedDepositResponse.encode(message.highestFixedDepositResponse, writer.uint32(34).fork()).join();
    }
    if (message.isRecommended !== false) {
      writer.uint32(40).bool(message.isRecommended);
    }
    if (message.tag !== undefined) {
      Tag.encode(message.tag, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendedBankItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendedBankItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankResponse = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.lowestFixedDepositRate = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.highestFixedDepositRate = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.highestFixedDepositResponse = FixedDepositResponse.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.isRecommended = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.tag = Tag.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecommendedBankItem {
    return {
      bankResponse: isSet(object.bankResponse) ? BankResponse.fromJSON(object.bankResponse) : undefined,
      lowestFixedDepositRate: isSet(object.lowestFixedDepositRate)
        ? globalThis.Number(object.lowestFixedDepositRate)
        : 0,
      highestFixedDepositRate: isSet(object.highestFixedDepositRate)
        ? globalThis.Number(object.highestFixedDepositRate)
        : 0,
      highestFixedDepositResponse: isSet(object.highestFixedDepositResponse)
        ? FixedDepositResponse.fromJSON(object.highestFixedDepositResponse)
        : undefined,
      isRecommended: isSet(object.isRecommended) ? globalThis.Boolean(object.isRecommended) : false,
      tag: isSet(object.tag) ? Tag.fromJSON(object.tag) : undefined,
    };
  },

  toJSON(message: RecommendedBankItem): unknown {
    const obj: any = {};
    if (message.bankResponse !== undefined) {
      obj.bankResponse = BankResponse.toJSON(message.bankResponse);
    }
    if (message.lowestFixedDepositRate !== 0) {
      obj.lowestFixedDepositRate = message.lowestFixedDepositRate;
    }
    if (message.highestFixedDepositRate !== 0) {
      obj.highestFixedDepositRate = message.highestFixedDepositRate;
    }
    if (message.highestFixedDepositResponse !== undefined) {
      obj.highestFixedDepositResponse = FixedDepositResponse.toJSON(message.highestFixedDepositResponse);
    }
    if (message.isRecommended !== false) {
      obj.isRecommended = message.isRecommended;
    }
    if (message.tag !== undefined) {
      obj.tag = Tag.toJSON(message.tag);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendedBankItem>, I>>(base?: I): RecommendedBankItem {
    return RecommendedBankItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendedBankItem>, I>>(object: I): RecommendedBankItem {
    const message = createBaseRecommendedBankItem();
    message.bankResponse = (object.bankResponse !== undefined && object.bankResponse !== null)
      ? BankResponse.fromPartial(object.bankResponse)
      : undefined;
    message.lowestFixedDepositRate = object.lowestFixedDepositRate ?? 0;
    message.highestFixedDepositRate = object.highestFixedDepositRate ?? 0;
    message.highestFixedDepositResponse =
      (object.highestFixedDepositResponse !== undefined && object.highestFixedDepositResponse !== null)
        ? FixedDepositResponse.fromPartial(object.highestFixedDepositResponse)
        : undefined;
    message.isRecommended = object.isRecommended ?? false;
    message.tag = (object.tag !== undefined && object.tag !== null) ? Tag.fromPartial(object.tag) : undefined;
    return message;
  },
};

function createBaseRecommendedBankResponse(): RecommendedBankResponse {
  return { recommendedBankList: [] };
}

export const RecommendedBankResponse: MessageFns<RecommendedBankResponse> = {
  encode(message: RecommendedBankResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.recommendedBankList) {
      RecommendedBankItem.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecommendedBankResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecommendedBankResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recommendedBankList.push(RecommendedBankItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecommendedBankResponse {
    return {
      recommendedBankList: globalThis.Array.isArray(object?.recommendedBankList)
        ? object.recommendedBankList.map((e: any) => RecommendedBankItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: RecommendedBankResponse): unknown {
    const obj: any = {};
    if (message.recommendedBankList?.length) {
      obj.recommendedBankList = message.recommendedBankList.map((e) => RecommendedBankItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecommendedBankResponse>, I>>(base?: I): RecommendedBankResponse {
    return RecommendedBankResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecommendedBankResponse>, I>>(object: I): RecommendedBankResponse {
    const message = createBaseRecommendedBankResponse();
    message.recommendedBankList = object.recommendedBankList?.map((e) => RecommendedBankItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCompareBankCell(): CompareBankCell {
  return {
    stringValue: undefined,
    boolValue: undefined,
    stringColor: undefined,
    caption: undefined,
    captionColor: undefined,
  };
}

export const CompareBankCell: MessageFns<CompareBankCell> = {
  encode(message: CompareBankCell, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.stringValue !== undefined) {
      writer.uint32(18).string(message.stringValue);
    }
    if (message.boolValue !== undefined) {
      writer.uint32(24).bool(message.boolValue);
    }
    if (message.stringColor !== undefined) {
      writer.uint32(34).string(message.stringColor);
    }
    if (message.caption !== undefined) {
      writer.uint32(42).string(message.caption);
    }
    if (message.captionColor !== undefined) {
      writer.uint32(50).string(message.captionColor);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompareBankCell {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompareBankCell();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.stringValue = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.boolValue = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.stringColor = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.caption = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.captionColor = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CompareBankCell {
    return {
      stringValue: isSet(object.stringValue) ? globalThis.String(object.stringValue) : undefined,
      boolValue: isSet(object.boolValue) ? globalThis.Boolean(object.boolValue) : undefined,
      stringColor: isSet(object.stringColor) ? globalThis.String(object.stringColor) : undefined,
      caption: isSet(object.caption) ? globalThis.String(object.caption) : undefined,
      captionColor: isSet(object.captionColor) ? globalThis.String(object.captionColor) : undefined,
    };
  },

  toJSON(message: CompareBankCell): unknown {
    const obj: any = {};
    if (message.stringValue !== undefined) {
      obj.stringValue = message.stringValue;
    }
    if (message.boolValue !== undefined) {
      obj.boolValue = message.boolValue;
    }
    if (message.stringColor !== undefined) {
      obj.stringColor = message.stringColor;
    }
    if (message.caption !== undefined) {
      obj.caption = message.caption;
    }
    if (message.captionColor !== undefined) {
      obj.captionColor = message.captionColor;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompareBankCell>, I>>(base?: I): CompareBankCell {
    return CompareBankCell.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompareBankCell>, I>>(object: I): CompareBankCell {
    const message = createBaseCompareBankCell();
    message.stringValue = object.stringValue ?? undefined;
    message.boolValue = object.boolValue ?? undefined;
    message.stringColor = object.stringColor ?? undefined;
    message.caption = object.caption ?? undefined;
    message.captionColor = object.captionColor ?? undefined;
    return message;
  },
};

function createBaseCompareBankItem(): CompareBankItem {
  return { compareBankList: [] };
}

export const CompareBankItem: MessageFns<CompareBankItem> = {
  encode(message: CompareBankItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.compareBankList) {
      CompareBankCell.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompareBankItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompareBankItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.compareBankList.push(CompareBankCell.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CompareBankItem {
    return {
      compareBankList: globalThis.Array.isArray(object?.compareBankList)
        ? object.compareBankList.map((e: any) => CompareBankCell.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CompareBankItem): unknown {
    const obj: any = {};
    if (message.compareBankList?.length) {
      obj.compareBankList = message.compareBankList.map((e) => CompareBankCell.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompareBankItem>, I>>(base?: I): CompareBankItem {
    return CompareBankItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompareBankItem>, I>>(object: I): CompareBankItem {
    const message = createBaseCompareBankItem();
    message.compareBankList = object.compareBankList?.map((e) => CompareBankCell.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCompareBankResponse(): CompareBankResponse {
  return { recommendedBankList: [], titles: [], compareBankItemList: [], addBankList: [] };
}

export const CompareBankResponse: MessageFns<CompareBankResponse> = {
  encode(message: CompareBankResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.recommendedBankList) {
      RecommendedBankItem.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.titles) {
      writer.uint32(18).string(v!);
    }
    for (const v of message.compareBankItemList) {
      CompareBankItem.encode(v!, writer.uint32(26).fork()).join();
    }
    for (const v of message.addBankList) {
      RecommendedBankItem.encode(v!, writer.uint32(34).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompareBankResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompareBankResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recommendedBankList.push(RecommendedBankItem.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.titles.push(reader.string());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.compareBankItemList.push(CompareBankItem.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.addBankList.push(RecommendedBankItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CompareBankResponse {
    return {
      recommendedBankList: globalThis.Array.isArray(object?.recommendedBankList)
        ? object.recommendedBankList.map((e: any) => RecommendedBankItem.fromJSON(e))
        : [],
      titles: globalThis.Array.isArray(object?.titles) ? object.titles.map((e: any) => globalThis.String(e)) : [],
      compareBankItemList: globalThis.Array.isArray(object?.compareBankItemList)
        ? object.compareBankItemList.map((e: any) => CompareBankItem.fromJSON(e))
        : [],
      addBankList: globalThis.Array.isArray(object?.addBankList)
        ? object.addBankList.map((e: any) => RecommendedBankItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CompareBankResponse): unknown {
    const obj: any = {};
    if (message.recommendedBankList?.length) {
      obj.recommendedBankList = message.recommendedBankList.map((e) => RecommendedBankItem.toJSON(e));
    }
    if (message.titles?.length) {
      obj.titles = message.titles;
    }
    if (message.compareBankItemList?.length) {
      obj.compareBankItemList = message.compareBankItemList.map((e) => CompareBankItem.toJSON(e));
    }
    if (message.addBankList?.length) {
      obj.addBankList = message.addBankList.map((e) => RecommendedBankItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompareBankResponse>, I>>(base?: I): CompareBankResponse {
    return CompareBankResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompareBankResponse>, I>>(object: I): CompareBankResponse {
    const message = createBaseCompareBankResponse();
    message.recommendedBankList = object.recommendedBankList?.map((e) => RecommendedBankItem.fromPartial(e)) || [];
    message.titles = object.titles?.map((e) => e) || [];
    message.compareBankItemList = object.compareBankItemList?.map((e) => CompareBankItem.fromPartial(e)) || [];
    message.addBankList = object.addBankList?.map((e) => RecommendedBankItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCompareBankResponseV2(): CompareBankResponseV2 {
  return { compareBankItemListV2: [] };
}

export const CompareBankResponseV2: MessageFns<CompareBankResponseV2> = {
  encode(message: CompareBankResponseV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.compareBankItemListV2) {
      CompareBankItemV2.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompareBankResponseV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompareBankResponseV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.compareBankItemListV2.push(CompareBankItemV2.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CompareBankResponseV2 {
    return {
      compareBankItemListV2: globalThis.Array.isArray(object?.compareBankItemListV2)
        ? object.compareBankItemListV2.map((e: any) => CompareBankItemV2.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CompareBankResponseV2): unknown {
    const obj: any = {};
    if (message.compareBankItemListV2?.length) {
      obj.compareBankItemListV2 = message.compareBankItemListV2.map((e) => CompareBankItemV2.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompareBankResponseV2>, I>>(base?: I): CompareBankResponseV2 {
    return CompareBankResponseV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompareBankResponseV2>, I>>(object: I): CompareBankResponseV2 {
    const message = createBaseCompareBankResponseV2();
    message.compareBankItemListV2 = object.compareBankItemListV2?.map((e) => CompareBankItemV2.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCompareBankItemV2(): CompareBankItemV2 {
  return { bankName: "", bankLogo: "", roi: "", bankId: "" };
}

export const CompareBankItemV2: MessageFns<CompareBankItemV2> = {
  encode(message: CompareBankItemV2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankName !== "") {
      writer.uint32(10).string(message.bankName);
    }
    if (message.bankLogo !== "") {
      writer.uint32(18).string(message.bankLogo);
    }
    if (message.roi !== "") {
      writer.uint32(26).string(message.roi);
    }
    if (message.bankId !== "") {
      writer.uint32(34).string(message.bankId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CompareBankItemV2 {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCompareBankItemV2();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.bankLogo = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.roi = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CompareBankItemV2 {
    return {
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogo: isSet(object.bankLogo) ? globalThis.String(object.bankLogo) : "",
      roi: isSet(object.roi) ? globalThis.String(object.roi) : "",
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
    };
  },

  toJSON(message: CompareBankItemV2): unknown {
    const obj: any = {};
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogo !== "") {
      obj.bankLogo = message.bankLogo;
    }
    if (message.roi !== "") {
      obj.roi = message.roi;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CompareBankItemV2>, I>>(base?: I): CompareBankItemV2 {
    return CompareBankItemV2.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CompareBankItemV2>, I>>(object: I): CompareBankItemV2 {
    const message = createBaseCompareBankItemV2();
    message.bankName = object.bankName ?? "";
    message.bankLogo = object.bankLogo ?? "";
    message.roi = object.roi ?? "";
    message.bankId = object.bankId ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
