// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Investment.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { RedirectDeeplink } from "./BusinessCommon";
import {
  BankResponse,
  FixedDepositResponse,
  MaturityInstruction,
  maturityInstructionFromJSON,
  maturityInstructionToJSON,
} from "./Collection";
import { DataKey } from "./Common";

export const protobufPackage = "com.stablemoney.api.identity";

export enum BookingStatus {
  UNKNOWN_INVESTMENT_STATUS = 0,
  ACTIVE_INVESTMENT_STATUS = 1,
  MATURED_INVESTMENT_STATUS = 2,
  WITHDRAWN_INVESTMENT_STATUS = 3,
  IN_PROGRESS_INVESTMENT_STATUS = 4,
  FAILED_INVESTMENT_STATUS = 5,
  RENEWED_INVESTMENT_STATUS = 6,
  CANCELLED_INVESTMENT_STATUS = 7,
  UNRECOGNIZED = -1,
}

export function bookingStatusFromJSON(object: any): BookingStatus {
  switch (object) {
    case 0:
    case "UNKNOWN_INVESTMENT_STATUS":
      return BookingStatus.UNKNOWN_INVESTMENT_STATUS;
    case 1:
    case "ACTIVE_INVESTMENT_STATUS":
      return BookingStatus.ACTIVE_INVESTMENT_STATUS;
    case 2:
    case "MATURED_INVESTMENT_STATUS":
      return BookingStatus.MATURED_INVESTMENT_STATUS;
    case 3:
    case "WITHDRAWN_INVESTMENT_STATUS":
      return BookingStatus.WITHDRAWN_INVESTMENT_STATUS;
    case 4:
    case "IN_PROGRESS_INVESTMENT_STATUS":
      return BookingStatus.IN_PROGRESS_INVESTMENT_STATUS;
    case 5:
    case "FAILED_INVESTMENT_STATUS":
      return BookingStatus.FAILED_INVESTMENT_STATUS;
    case 6:
    case "RENEWED_INVESTMENT_STATUS":
      return BookingStatus.RENEWED_INVESTMENT_STATUS;
    case 7:
    case "CANCELLED_INVESTMENT_STATUS":
      return BookingStatus.CANCELLED_INVESTMENT_STATUS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BookingStatus.UNRECOGNIZED;
  }
}

export function bookingStatusToJSON(object: BookingStatus): string {
  switch (object) {
    case BookingStatus.UNKNOWN_INVESTMENT_STATUS:
      return "UNKNOWN_INVESTMENT_STATUS";
    case BookingStatus.ACTIVE_INVESTMENT_STATUS:
      return "ACTIVE_INVESTMENT_STATUS";
    case BookingStatus.MATURED_INVESTMENT_STATUS:
      return "MATURED_INVESTMENT_STATUS";
    case BookingStatus.WITHDRAWN_INVESTMENT_STATUS:
      return "WITHDRAWN_INVESTMENT_STATUS";
    case BookingStatus.IN_PROGRESS_INVESTMENT_STATUS:
      return "IN_PROGRESS_INVESTMENT_STATUS";
    case BookingStatus.FAILED_INVESTMENT_STATUS:
      return "FAILED_INVESTMENT_STATUS";
    case BookingStatus.RENEWED_INVESTMENT_STATUS:
      return "RENEWED_INVESTMENT_STATUS";
    case BookingStatus.CANCELLED_INVESTMENT_STATUS:
      return "CANCELLED_INVESTMENT_STATUS";
    case BookingStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface BanksVkycDoneOrNbfcsWithAnInvestment {
  bankInvestmentInfo: BankInvestmentInfo[];
}

export interface BankInvestmentInfo {
  fdResponse: FixedDepositResponse | undefined;
  redirectDeeplink: RedirectDeeplink | undefined;
  isCompleted: boolean;
  totalInvestment: number;
  targetInvestmentAmount: number;
  tags: string[];
  progressPercentage: number;
  progressTitle: string;
  progressSubtitle: string;
}

export interface UserInvestments {
  userInvestmentSummary: UserInvestmentSummary | undefined;
  investmentsGroupedByStatus: InvestmentsGroupedByStatus[];
  showInvestments: boolean;
  redirectDeeplink: RedirectDeeplink | undefined;
  redirectCta: string;
}

export interface UserInvestmentSummary {
  totalInvestment: number;
  totalGain: number;
  maturityAmount: number;
  currentValue: number;
  totalInvestmentText: string;
  totalGainText: string;
  maturityAmountText: string;
  currentValueText: string;
  totalInvestmentColor: string;
  totalGainColor: string;
  maturityAmountColor: string;
  currentValueColor: string;
  iconUrl: string;
  totalInvestmentTextColor: string;
  totalGainTextColor: string;
  maturityAmountTextColor: string;
  currentValueTextColor: string;
}

export interface InvestmentsGroupedByStatus {
  investmentStatus: BookingStatus;
  investmentStatusLabel: string;
  investmentInfo: InvestmentInfo[];
  redirectDeeplink: RedirectDeeplink | undefined;
  redirectCta: string;
  showViewAll: boolean;
}

export interface InvestmentInfo {
  bankResponse: BankResponse | undefined;
  leftTopValue: string;
  leftBottomValue: string;
  rightTopValue: string;
  rightBottomValue: string;
  subtitle: string;
  subtitleColor: string;
  message: string;
  messageTextColor: string;
  messageBgColor: string;
  messageIconColor: string;
  redirectDeeplink: RedirectDeeplink | undefined;
  ctaText: string;
  id: string;
  bookingStatus: BookingStatus;
  leftTopText: string;
  leftBottomText: string;
  rightTopText: string;
  rightBottomText: string;
  leftTopColor: string;
  leftBottomColor: string;
  rightTopColor: string;
  rightBottomColor: string;
  leftTopTextColor: string;
  leftBottomTextColor: string;
  rightTopTextColor: string;
  rightBottomTextColor: string;
  maturityDate: string;
  tenure: string;
  investmentDate: string;
  withdrawDeeplink?: RedirectDeeplink | undefined;
  isTaxSaving: boolean;
  bookingDate: string;
  maturityInstruction: MaturityInstruction;
  isWithdrawalConfirmationRequired: boolean;
  withdrawalMetadata: { [key: string]: string };
}

export interface InvestmentInfo_WithdrawalMetadataEntry {
  key: string;
  value: string;
}

export interface InvestmentNomineeDetails {
  nomineeNameText: string;
  nomineeNameValue: string;
  nomineeNameColor: string;
  nomineeRelationText: string;
  nomineeRelationValue: string;
  nomineeRelationColor: string;
  nomineeDobText: string;
  nomineeDobValue: string;
  nomineeDobColor: string;
  heading: string;
}

export interface WithdrawalBankAccountDetails {
  accountHolderNameText: string;
  accountHolderNameValue: string;
  accountHolderNameColor: string;
  accountNumberText: string;
  accountNumberValue: string;
  accountNumberColor: string;
  ifscCodeText: string;
  ifscCodeValue: string;
  ifscCodeColor: string;
  heading: string;
}

export interface SavingsBankAccountDetails {
  investmentKeyValueDetails: InvestmentKeyValue[];
  heading: string;
}

export interface InvestmentKeyValue {
  labelText: string;
  labelValue: string;
  labelValueColor: string;
}

export interface InvestmentDetailsResponse {
  investmentInfo: InvestmentInfo | undefined;
  nomineeDetails?: InvestmentNomineeDetails | undefined;
  withdrawalBankAccountDetails?: WithdrawalBankAccountDetails | undefined;
  interestPayoutDetails?: InterestPayoutDetails | undefined;
  savingsBankAcountDetails?: SavingsBankAccountDetails | undefined;
}

export interface InterestPayoutDetails {
  heading: string;
  payoutTypeText: string;
  payoutTypeValue: string;
  gainsTillDateText: string;
  gainsTillDateValue: string;
  interestPaidTillDateText: string;
  interestPaidTillDateValue: string;
  gainsThisMonthText: string;
  gainsThisMonthValue: string;
}

export interface PrematureWithdrawalDetailsResponse {
  interestGainedTillDate: number;
  interestGainedIfWithdrawnToday: number;
  interestGainedIfWithdrawnAfterSomeDays: number;
  daysToIncreaseInterest: number;
  redirectDeeplink: RedirectDeeplink | undefined;
  isWithdrawalPossible: boolean;
  lockInPeriodInDays: number;
  lockInPeriod: string;
  lockInPeriodEndDate: string;
}

export interface VkycExpiryResponse {
  expiryDate: string;
}

export interface PostWithdrawalDetail {
  userFixedDeposit: PostWithdrawalDetail_UserFixedDeposit | undefined;
  surveySubmitted: boolean;
  surveyResponse: string;
  withdrawalTimestamp: number;
  estimatedRefundTime: number;
  refundProcessed: boolean;
  withdrawalTimeline: PostWithdrawalDetail_WithdrawalTimeline | undefined;
}

export interface PostWithdrawalDetail_UserFixedDeposit {
  id: string;
  fdIdentifier: string;
  journeyId: string;
  bankName: string;
  bankLogoUrl: string;
  tenure: string;
  investmentAmount: number;
}

export interface PostWithdrawalDetail_WithdrawalTimeline {
  items: PostWithdrawalDetail_WithdrawalTimeline_TimelineItem[];
}

export interface PostWithdrawalDetail_WithdrawalTimeline_TimelineItem {
  title: DataKey | undefined;
  description: DataKey | undefined;
  complete: boolean;
}

export interface WithdrawalReasonSubmitRequest {
  userFixedDepositId: string;
  reasonCode: string;
}

export interface WithdrawalReasonSubmitResponse {
}

function createBaseBanksVkycDoneOrNbfcsWithAnInvestment(): BanksVkycDoneOrNbfcsWithAnInvestment {
  return { bankInvestmentInfo: [] };
}

export const BanksVkycDoneOrNbfcsWithAnInvestment: MessageFns<BanksVkycDoneOrNbfcsWithAnInvestment> = {
  encode(message: BanksVkycDoneOrNbfcsWithAnInvestment, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bankInvestmentInfo) {
      BankInvestmentInfo.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BanksVkycDoneOrNbfcsWithAnInvestment {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBanksVkycDoneOrNbfcsWithAnInvestment();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankInvestmentInfo.push(BankInvestmentInfo.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BanksVkycDoneOrNbfcsWithAnInvestment {
    return {
      bankInvestmentInfo: globalThis.Array.isArray(object?.bankInvestmentInfo)
        ? object.bankInvestmentInfo.map((e: any) => BankInvestmentInfo.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BanksVkycDoneOrNbfcsWithAnInvestment): unknown {
    const obj: any = {};
    if (message.bankInvestmentInfo?.length) {
      obj.bankInvestmentInfo = message.bankInvestmentInfo.map((e) => BankInvestmentInfo.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BanksVkycDoneOrNbfcsWithAnInvestment>, I>>(
    base?: I,
  ): BanksVkycDoneOrNbfcsWithAnInvestment {
    return BanksVkycDoneOrNbfcsWithAnInvestment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BanksVkycDoneOrNbfcsWithAnInvestment>, I>>(
    object: I,
  ): BanksVkycDoneOrNbfcsWithAnInvestment {
    const message = createBaseBanksVkycDoneOrNbfcsWithAnInvestment();
    message.bankInvestmentInfo = object.bankInvestmentInfo?.map((e) => BankInvestmentInfo.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBankInvestmentInfo(): BankInvestmentInfo {
  return {
    fdResponse: undefined,
    redirectDeeplink: undefined,
    isCompleted: false,
    totalInvestment: 0,
    targetInvestmentAmount: 0,
    tags: [],
    progressPercentage: 0,
    progressTitle: "",
    progressSubtitle: "",
  };
}

export const BankInvestmentInfo: MessageFns<BankInvestmentInfo> = {
  encode(message: BankInvestmentInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fdResponse !== undefined) {
      FixedDepositResponse.encode(message.fdResponse, writer.uint32(10).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(18).fork()).join();
    }
    if (message.isCompleted !== false) {
      writer.uint32(24).bool(message.isCompleted);
    }
    if (message.totalInvestment !== 0) {
      writer.uint32(33).double(message.totalInvestment);
    }
    if (message.targetInvestmentAmount !== 0) {
      writer.uint32(41).double(message.targetInvestmentAmount);
    }
    for (const v of message.tags) {
      writer.uint32(50).string(v!);
    }
    if (message.progressPercentage !== 0) {
      writer.uint32(57).double(message.progressPercentage);
    }
    if (message.progressTitle !== "") {
      writer.uint32(66).string(message.progressTitle);
    }
    if (message.progressSubtitle !== "") {
      writer.uint32(74).string(message.progressSubtitle);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BankInvestmentInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBankInvestmentInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.fdResponse = FixedDepositResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.isCompleted = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.totalInvestment = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 41) {
            break;
          }

          message.targetInvestmentAmount = reader.double();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.tags.push(reader.string());
          continue;
        }
        case 7: {
          if (tag !== 57) {
            break;
          }

          message.progressPercentage = reader.double();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.progressTitle = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.progressSubtitle = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BankInvestmentInfo {
    return {
      fdResponse: isSet(object.fdResponse) ? FixedDepositResponse.fromJSON(object.fdResponse) : undefined,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      isCompleted: isSet(object.isCompleted) ? globalThis.Boolean(object.isCompleted) : false,
      totalInvestment: isSet(object.totalInvestment) ? globalThis.Number(object.totalInvestment) : 0,
      targetInvestmentAmount: isSet(object.targetInvestmentAmount)
        ? globalThis.Number(object.targetInvestmentAmount)
        : 0,
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => globalThis.String(e)) : [],
      progressPercentage: isSet(object.progressPercentage) ? globalThis.Number(object.progressPercentage) : 0,
      progressTitle: isSet(object.progressTitle) ? globalThis.String(object.progressTitle) : "",
      progressSubtitle: isSet(object.progressSubtitle) ? globalThis.String(object.progressSubtitle) : "",
    };
  },

  toJSON(message: BankInvestmentInfo): unknown {
    const obj: any = {};
    if (message.fdResponse !== undefined) {
      obj.fdResponse = FixedDepositResponse.toJSON(message.fdResponse);
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.isCompleted !== false) {
      obj.isCompleted = message.isCompleted;
    }
    if (message.totalInvestment !== 0) {
      obj.totalInvestment = message.totalInvestment;
    }
    if (message.targetInvestmentAmount !== 0) {
      obj.targetInvestmentAmount = message.targetInvestmentAmount;
    }
    if (message.tags?.length) {
      obj.tags = message.tags;
    }
    if (message.progressPercentage !== 0) {
      obj.progressPercentage = message.progressPercentage;
    }
    if (message.progressTitle !== "") {
      obj.progressTitle = message.progressTitle;
    }
    if (message.progressSubtitle !== "") {
      obj.progressSubtitle = message.progressSubtitle;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BankInvestmentInfo>, I>>(base?: I): BankInvestmentInfo {
    return BankInvestmentInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BankInvestmentInfo>, I>>(object: I): BankInvestmentInfo {
    const message = createBaseBankInvestmentInfo();
    message.fdResponse = (object.fdResponse !== undefined && object.fdResponse !== null)
      ? FixedDepositResponse.fromPartial(object.fdResponse)
      : undefined;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.isCompleted = object.isCompleted ?? false;
    message.totalInvestment = object.totalInvestment ?? 0;
    message.targetInvestmentAmount = object.targetInvestmentAmount ?? 0;
    message.tags = object.tags?.map((e) => e) || [];
    message.progressPercentage = object.progressPercentage ?? 0;
    message.progressTitle = object.progressTitle ?? "";
    message.progressSubtitle = object.progressSubtitle ?? "";
    return message;
  },
};

function createBaseUserInvestments(): UserInvestments {
  return {
    userInvestmentSummary: undefined,
    investmentsGroupedByStatus: [],
    showInvestments: false,
    redirectDeeplink: undefined,
    redirectCta: "",
  };
}

export const UserInvestments: MessageFns<UserInvestments> = {
  encode(message: UserInvestments, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userInvestmentSummary !== undefined) {
      UserInvestmentSummary.encode(message.userInvestmentSummary, writer.uint32(10).fork()).join();
    }
    for (const v of message.investmentsGroupedByStatus) {
      InvestmentsGroupedByStatus.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.showInvestments !== false) {
      writer.uint32(24).bool(message.showInvestments);
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(34).fork()).join();
    }
    if (message.redirectCta !== "") {
      writer.uint32(42).string(message.redirectCta);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserInvestments {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserInvestments();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userInvestmentSummary = UserInvestmentSummary.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.investmentsGroupedByStatus.push(InvestmentsGroupedByStatus.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.showInvestments = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.redirectCta = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserInvestments {
    return {
      userInvestmentSummary: isSet(object.userInvestmentSummary)
        ? UserInvestmentSummary.fromJSON(object.userInvestmentSummary)
        : undefined,
      investmentsGroupedByStatus: globalThis.Array.isArray(object?.investmentsGroupedByStatus)
        ? object.investmentsGroupedByStatus.map((e: any) => InvestmentsGroupedByStatus.fromJSON(e))
        : [],
      showInvestments: isSet(object.showInvestments) ? globalThis.Boolean(object.showInvestments) : false,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      redirectCta: isSet(object.redirectCta) ? globalThis.String(object.redirectCta) : "",
    };
  },

  toJSON(message: UserInvestments): unknown {
    const obj: any = {};
    if (message.userInvestmentSummary !== undefined) {
      obj.userInvestmentSummary = UserInvestmentSummary.toJSON(message.userInvestmentSummary);
    }
    if (message.investmentsGroupedByStatus?.length) {
      obj.investmentsGroupedByStatus = message.investmentsGroupedByStatus.map((e) =>
        InvestmentsGroupedByStatus.toJSON(e)
      );
    }
    if (message.showInvestments !== false) {
      obj.showInvestments = message.showInvestments;
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.redirectCta !== "") {
      obj.redirectCta = message.redirectCta;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserInvestments>, I>>(base?: I): UserInvestments {
    return UserInvestments.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInvestments>, I>>(object: I): UserInvestments {
    const message = createBaseUserInvestments();
    message.userInvestmentSummary =
      (object.userInvestmentSummary !== undefined && object.userInvestmentSummary !== null)
        ? UserInvestmentSummary.fromPartial(object.userInvestmentSummary)
        : undefined;
    message.investmentsGroupedByStatus =
      object.investmentsGroupedByStatus?.map((e) => InvestmentsGroupedByStatus.fromPartial(e)) || [];
    message.showInvestments = object.showInvestments ?? false;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.redirectCta = object.redirectCta ?? "";
    return message;
  },
};

function createBaseUserInvestmentSummary(): UserInvestmentSummary {
  return {
    totalInvestment: 0,
    totalGain: 0,
    maturityAmount: 0,
    currentValue: 0,
    totalInvestmentText: "",
    totalGainText: "",
    maturityAmountText: "",
    currentValueText: "",
    totalInvestmentColor: "",
    totalGainColor: "",
    maturityAmountColor: "",
    currentValueColor: "",
    iconUrl: "",
    totalInvestmentTextColor: "",
    totalGainTextColor: "",
    maturityAmountTextColor: "",
    currentValueTextColor: "",
  };
}

export const UserInvestmentSummary: MessageFns<UserInvestmentSummary> = {
  encode(message: UserInvestmentSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalInvestment !== 0) {
      writer.uint32(9).double(message.totalInvestment);
    }
    if (message.totalGain !== 0) {
      writer.uint32(17).double(message.totalGain);
    }
    if (message.maturityAmount !== 0) {
      writer.uint32(25).double(message.maturityAmount);
    }
    if (message.currentValue !== 0) {
      writer.uint32(33).double(message.currentValue);
    }
    if (message.totalInvestmentText !== "") {
      writer.uint32(42).string(message.totalInvestmentText);
    }
    if (message.totalGainText !== "") {
      writer.uint32(50).string(message.totalGainText);
    }
    if (message.maturityAmountText !== "") {
      writer.uint32(58).string(message.maturityAmountText);
    }
    if (message.currentValueText !== "") {
      writer.uint32(66).string(message.currentValueText);
    }
    if (message.totalInvestmentColor !== "") {
      writer.uint32(74).string(message.totalInvestmentColor);
    }
    if (message.totalGainColor !== "") {
      writer.uint32(82).string(message.totalGainColor);
    }
    if (message.maturityAmountColor !== "") {
      writer.uint32(90).string(message.maturityAmountColor);
    }
    if (message.currentValueColor !== "") {
      writer.uint32(98).string(message.currentValueColor);
    }
    if (message.iconUrl !== "") {
      writer.uint32(106).string(message.iconUrl);
    }
    if (message.totalInvestmentTextColor !== "") {
      writer.uint32(114).string(message.totalInvestmentTextColor);
    }
    if (message.totalGainTextColor !== "") {
      writer.uint32(122).string(message.totalGainTextColor);
    }
    if (message.maturityAmountTextColor !== "") {
      writer.uint32(130).string(message.maturityAmountTextColor);
    }
    if (message.currentValueTextColor !== "") {
      writer.uint32(138).string(message.currentValueTextColor);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UserInvestmentSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserInvestmentSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.totalInvestment = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.totalGain = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.maturityAmount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.currentValue = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.totalInvestmentText = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.totalGainText = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.maturityAmountText = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.currentValueText = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.totalInvestmentColor = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.totalGainColor = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.maturityAmountColor = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.currentValueColor = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.totalInvestmentTextColor = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.totalGainTextColor = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.maturityAmountTextColor = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.currentValueTextColor = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserInvestmentSummary {
    return {
      totalInvestment: isSet(object.totalInvestment) ? globalThis.Number(object.totalInvestment) : 0,
      totalGain: isSet(object.totalGain) ? globalThis.Number(object.totalGain) : 0,
      maturityAmount: isSet(object.maturityAmount) ? globalThis.Number(object.maturityAmount) : 0,
      currentValue: isSet(object.currentValue) ? globalThis.Number(object.currentValue) : 0,
      totalInvestmentText: isSet(object.totalInvestmentText) ? globalThis.String(object.totalInvestmentText) : "",
      totalGainText: isSet(object.totalGainText) ? globalThis.String(object.totalGainText) : "",
      maturityAmountText: isSet(object.maturityAmountText) ? globalThis.String(object.maturityAmountText) : "",
      currentValueText: isSet(object.currentValueText) ? globalThis.String(object.currentValueText) : "",
      totalInvestmentColor: isSet(object.totalInvestmentColor) ? globalThis.String(object.totalInvestmentColor) : "",
      totalGainColor: isSet(object.totalGainColor) ? globalThis.String(object.totalGainColor) : "",
      maturityAmountColor: isSet(object.maturityAmountColor) ? globalThis.String(object.maturityAmountColor) : "",
      currentValueColor: isSet(object.currentValueColor) ? globalThis.String(object.currentValueColor) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
      totalInvestmentTextColor: isSet(object.totalInvestmentTextColor)
        ? globalThis.String(object.totalInvestmentTextColor)
        : "",
      totalGainTextColor: isSet(object.totalGainTextColor) ? globalThis.String(object.totalGainTextColor) : "",
      maturityAmountTextColor: isSet(object.maturityAmountTextColor)
        ? globalThis.String(object.maturityAmountTextColor)
        : "",
      currentValueTextColor: isSet(object.currentValueTextColor) ? globalThis.String(object.currentValueTextColor) : "",
    };
  },

  toJSON(message: UserInvestmentSummary): unknown {
    const obj: any = {};
    if (message.totalInvestment !== 0) {
      obj.totalInvestment = message.totalInvestment;
    }
    if (message.totalGain !== 0) {
      obj.totalGain = message.totalGain;
    }
    if (message.maturityAmount !== 0) {
      obj.maturityAmount = message.maturityAmount;
    }
    if (message.currentValue !== 0) {
      obj.currentValue = message.currentValue;
    }
    if (message.totalInvestmentText !== "") {
      obj.totalInvestmentText = message.totalInvestmentText;
    }
    if (message.totalGainText !== "") {
      obj.totalGainText = message.totalGainText;
    }
    if (message.maturityAmountText !== "") {
      obj.maturityAmountText = message.maturityAmountText;
    }
    if (message.currentValueText !== "") {
      obj.currentValueText = message.currentValueText;
    }
    if (message.totalInvestmentColor !== "") {
      obj.totalInvestmentColor = message.totalInvestmentColor;
    }
    if (message.totalGainColor !== "") {
      obj.totalGainColor = message.totalGainColor;
    }
    if (message.maturityAmountColor !== "") {
      obj.maturityAmountColor = message.maturityAmountColor;
    }
    if (message.currentValueColor !== "") {
      obj.currentValueColor = message.currentValueColor;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    if (message.totalInvestmentTextColor !== "") {
      obj.totalInvestmentTextColor = message.totalInvestmentTextColor;
    }
    if (message.totalGainTextColor !== "") {
      obj.totalGainTextColor = message.totalGainTextColor;
    }
    if (message.maturityAmountTextColor !== "") {
      obj.maturityAmountTextColor = message.maturityAmountTextColor;
    }
    if (message.currentValueTextColor !== "") {
      obj.currentValueTextColor = message.currentValueTextColor;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserInvestmentSummary>, I>>(base?: I): UserInvestmentSummary {
    return UserInvestmentSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInvestmentSummary>, I>>(object: I): UserInvestmentSummary {
    const message = createBaseUserInvestmentSummary();
    message.totalInvestment = object.totalInvestment ?? 0;
    message.totalGain = object.totalGain ?? 0;
    message.maturityAmount = object.maturityAmount ?? 0;
    message.currentValue = object.currentValue ?? 0;
    message.totalInvestmentText = object.totalInvestmentText ?? "";
    message.totalGainText = object.totalGainText ?? "";
    message.maturityAmountText = object.maturityAmountText ?? "";
    message.currentValueText = object.currentValueText ?? "";
    message.totalInvestmentColor = object.totalInvestmentColor ?? "";
    message.totalGainColor = object.totalGainColor ?? "";
    message.maturityAmountColor = object.maturityAmountColor ?? "";
    message.currentValueColor = object.currentValueColor ?? "";
    message.iconUrl = object.iconUrl ?? "";
    message.totalInvestmentTextColor = object.totalInvestmentTextColor ?? "";
    message.totalGainTextColor = object.totalGainTextColor ?? "";
    message.maturityAmountTextColor = object.maturityAmountTextColor ?? "";
    message.currentValueTextColor = object.currentValueTextColor ?? "";
    return message;
  },
};

function createBaseInvestmentsGroupedByStatus(): InvestmentsGroupedByStatus {
  return {
    investmentStatus: 0,
    investmentStatusLabel: "",
    investmentInfo: [],
    redirectDeeplink: undefined,
    redirectCta: "",
    showViewAll: false,
  };
}

export const InvestmentsGroupedByStatus: MessageFns<InvestmentsGroupedByStatus> = {
  encode(message: InvestmentsGroupedByStatus, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.investmentStatus !== 0) {
      writer.uint32(8).int32(message.investmentStatus);
    }
    if (message.investmentStatusLabel !== "") {
      writer.uint32(18).string(message.investmentStatusLabel);
    }
    for (const v of message.investmentInfo) {
      InvestmentInfo.encode(v!, writer.uint32(26).fork()).join();
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(34).fork()).join();
    }
    if (message.redirectCta !== "") {
      writer.uint32(42).string(message.redirectCta);
    }
    if (message.showViewAll !== false) {
      writer.uint32(48).bool(message.showViewAll);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentsGroupedByStatus {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentsGroupedByStatus();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.investmentStatus = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.investmentStatusLabel = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.investmentInfo.push(InvestmentInfo.decode(reader, reader.uint32()));
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.redirectCta = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.showViewAll = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentsGroupedByStatus {
    return {
      investmentStatus: isSet(object.investmentStatus) ? bookingStatusFromJSON(object.investmentStatus) : 0,
      investmentStatusLabel: isSet(object.investmentStatusLabel) ? globalThis.String(object.investmentStatusLabel) : "",
      investmentInfo: globalThis.Array.isArray(object?.investmentInfo)
        ? object.investmentInfo.map((e: any) => InvestmentInfo.fromJSON(e))
        : [],
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      redirectCta: isSet(object.redirectCta) ? globalThis.String(object.redirectCta) : "",
      showViewAll: isSet(object.showViewAll) ? globalThis.Boolean(object.showViewAll) : false,
    };
  },

  toJSON(message: InvestmentsGroupedByStatus): unknown {
    const obj: any = {};
    if (message.investmentStatus !== 0) {
      obj.investmentStatus = bookingStatusToJSON(message.investmentStatus);
    }
    if (message.investmentStatusLabel !== "") {
      obj.investmentStatusLabel = message.investmentStatusLabel;
    }
    if (message.investmentInfo?.length) {
      obj.investmentInfo = message.investmentInfo.map((e) => InvestmentInfo.toJSON(e));
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.redirectCta !== "") {
      obj.redirectCta = message.redirectCta;
    }
    if (message.showViewAll !== false) {
      obj.showViewAll = message.showViewAll;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentsGroupedByStatus>, I>>(base?: I): InvestmentsGroupedByStatus {
    return InvestmentsGroupedByStatus.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentsGroupedByStatus>, I>>(object: I): InvestmentsGroupedByStatus {
    const message = createBaseInvestmentsGroupedByStatus();
    message.investmentStatus = object.investmentStatus ?? 0;
    message.investmentStatusLabel = object.investmentStatusLabel ?? "";
    message.investmentInfo = object.investmentInfo?.map((e) => InvestmentInfo.fromPartial(e)) || [];
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.redirectCta = object.redirectCta ?? "";
    message.showViewAll = object.showViewAll ?? false;
    return message;
  },
};

function createBaseInvestmentInfo(): InvestmentInfo {
  return {
    bankResponse: undefined,
    leftTopValue: "",
    leftBottomValue: "",
    rightTopValue: "",
    rightBottomValue: "",
    subtitle: "",
    subtitleColor: "",
    message: "",
    messageTextColor: "",
    messageBgColor: "",
    messageIconColor: "",
    redirectDeeplink: undefined,
    ctaText: "",
    id: "",
    bookingStatus: 0,
    leftTopText: "",
    leftBottomText: "",
    rightTopText: "",
    rightBottomText: "",
    leftTopColor: "",
    leftBottomColor: "",
    rightTopColor: "",
    rightBottomColor: "",
    leftTopTextColor: "",
    leftBottomTextColor: "",
    rightTopTextColor: "",
    rightBottomTextColor: "",
    maturityDate: "",
    tenure: "",
    investmentDate: "",
    withdrawDeeplink: undefined,
    isTaxSaving: false,
    bookingDate: "",
    maturityInstruction: 0,
    isWithdrawalConfirmationRequired: false,
    withdrawalMetadata: {},
  };
}

export const InvestmentInfo: MessageFns<InvestmentInfo> = {
  encode(message: InvestmentInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bankResponse !== undefined) {
      BankResponse.encode(message.bankResponse, writer.uint32(10).fork()).join();
    }
    if (message.leftTopValue !== "") {
      writer.uint32(18).string(message.leftTopValue);
    }
    if (message.leftBottomValue !== "") {
      writer.uint32(26).string(message.leftBottomValue);
    }
    if (message.rightTopValue !== "") {
      writer.uint32(34).string(message.rightTopValue);
    }
    if (message.rightBottomValue !== "") {
      writer.uint32(42).string(message.rightBottomValue);
    }
    if (message.subtitle !== "") {
      writer.uint32(50).string(message.subtitle);
    }
    if (message.subtitleColor !== "") {
      writer.uint32(58).string(message.subtitleColor);
    }
    if (message.message !== "") {
      writer.uint32(66).string(message.message);
    }
    if (message.messageTextColor !== "") {
      writer.uint32(74).string(message.messageTextColor);
    }
    if (message.messageBgColor !== "") {
      writer.uint32(82).string(message.messageBgColor);
    }
    if (message.messageIconColor !== "") {
      writer.uint32(90).string(message.messageIconColor);
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(98).fork()).join();
    }
    if (message.ctaText !== "") {
      writer.uint32(106).string(message.ctaText);
    }
    if (message.id !== "") {
      writer.uint32(114).string(message.id);
    }
    if (message.bookingStatus !== 0) {
      writer.uint32(120).int32(message.bookingStatus);
    }
    if (message.leftTopText !== "") {
      writer.uint32(130).string(message.leftTopText);
    }
    if (message.leftBottomText !== "") {
      writer.uint32(138).string(message.leftBottomText);
    }
    if (message.rightTopText !== "") {
      writer.uint32(146).string(message.rightTopText);
    }
    if (message.rightBottomText !== "") {
      writer.uint32(154).string(message.rightBottomText);
    }
    if (message.leftTopColor !== "") {
      writer.uint32(162).string(message.leftTopColor);
    }
    if (message.leftBottomColor !== "") {
      writer.uint32(170).string(message.leftBottomColor);
    }
    if (message.rightTopColor !== "") {
      writer.uint32(178).string(message.rightTopColor);
    }
    if (message.rightBottomColor !== "") {
      writer.uint32(186).string(message.rightBottomColor);
    }
    if (message.leftTopTextColor !== "") {
      writer.uint32(194).string(message.leftTopTextColor);
    }
    if (message.leftBottomTextColor !== "") {
      writer.uint32(202).string(message.leftBottomTextColor);
    }
    if (message.rightTopTextColor !== "") {
      writer.uint32(210).string(message.rightTopTextColor);
    }
    if (message.rightBottomTextColor !== "") {
      writer.uint32(218).string(message.rightBottomTextColor);
    }
    if (message.maturityDate !== "") {
      writer.uint32(226).string(message.maturityDate);
    }
    if (message.tenure !== "") {
      writer.uint32(234).string(message.tenure);
    }
    if (message.investmentDate !== "") {
      writer.uint32(242).string(message.investmentDate);
    }
    if (message.withdrawDeeplink !== undefined) {
      RedirectDeeplink.encode(message.withdrawDeeplink, writer.uint32(250).fork()).join();
    }
    if (message.isTaxSaving !== false) {
      writer.uint32(256).bool(message.isTaxSaving);
    }
    if (message.bookingDate !== "") {
      writer.uint32(266).string(message.bookingDate);
    }
    if (message.maturityInstruction !== 0) {
      writer.uint32(280).int32(message.maturityInstruction);
    }
    if (message.isWithdrawalConfirmationRequired !== false) {
      writer.uint32(288).bool(message.isWithdrawalConfirmationRequired);
    }
    Object.entries(message.withdrawalMetadata).forEach(([key, value]) => {
      InvestmentInfo_WithdrawalMetadataEntry.encode({ key: key as any, value }, writer.uint32(298).fork()).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentInfo {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bankResponse = BankResponse.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.leftTopValue = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.leftBottomValue = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.rightTopValue = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rightBottomValue = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.subtitle = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.subtitleColor = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.message = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.messageTextColor = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.messageBgColor = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.messageIconColor = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.ctaText = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.bookingStatus = reader.int32() as any;
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }

          message.leftTopText = reader.string();
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }

          message.leftBottomText = reader.string();
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }

          message.rightTopText = reader.string();
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.rightBottomText = reader.string();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.leftTopColor = reader.string();
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.leftBottomColor = reader.string();
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.rightTopColor = reader.string();
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.rightBottomColor = reader.string();
          continue;
        }
        case 24: {
          if (tag !== 194) {
            break;
          }

          message.leftTopTextColor = reader.string();
          continue;
        }
        case 25: {
          if (tag !== 202) {
            break;
          }

          message.leftBottomTextColor = reader.string();
          continue;
        }
        case 26: {
          if (tag !== 210) {
            break;
          }

          message.rightTopTextColor = reader.string();
          continue;
        }
        case 27: {
          if (tag !== 218) {
            break;
          }

          message.rightBottomTextColor = reader.string();
          continue;
        }
        case 28: {
          if (tag !== 226) {
            break;
          }

          message.maturityDate = reader.string();
          continue;
        }
        case 29: {
          if (tag !== 234) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 30: {
          if (tag !== 242) {
            break;
          }

          message.investmentDate = reader.string();
          continue;
        }
        case 31: {
          if (tag !== 250) {
            break;
          }

          message.withdrawDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 32: {
          if (tag !== 256) {
            break;
          }

          message.isTaxSaving = reader.bool();
          continue;
        }
        case 33: {
          if (tag !== 266) {
            break;
          }

          message.bookingDate = reader.string();
          continue;
        }
        case 35: {
          if (tag !== 280) {
            break;
          }

          message.maturityInstruction = reader.int32() as any;
          continue;
        }
        case 36: {
          if (tag !== 288) {
            break;
          }

          message.isWithdrawalConfirmationRequired = reader.bool();
          continue;
        }
        case 37: {
          if (tag !== 298) {
            break;
          }

          const entry37 = InvestmentInfo_WithdrawalMetadataEntry.decode(reader, reader.uint32());
          if (entry37.value !== undefined) {
            message.withdrawalMetadata[entry37.key] = entry37.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentInfo {
    return {
      bankResponse: isSet(object.bankResponse) ? BankResponse.fromJSON(object.bankResponse) : undefined,
      leftTopValue: isSet(object.leftTopValue) ? globalThis.String(object.leftTopValue) : "",
      leftBottomValue: isSet(object.leftBottomValue) ? globalThis.String(object.leftBottomValue) : "",
      rightTopValue: isSet(object.rightTopValue) ? globalThis.String(object.rightTopValue) : "",
      rightBottomValue: isSet(object.rightBottomValue) ? globalThis.String(object.rightBottomValue) : "",
      subtitle: isSet(object.subtitle) ? globalThis.String(object.subtitle) : "",
      subtitleColor: isSet(object.subtitleColor) ? globalThis.String(object.subtitleColor) : "",
      message: isSet(object.message) ? globalThis.String(object.message) : "",
      messageTextColor: isSet(object.messageTextColor) ? globalThis.String(object.messageTextColor) : "",
      messageBgColor: isSet(object.messageBgColor) ? globalThis.String(object.messageBgColor) : "",
      messageIconColor: isSet(object.messageIconColor) ? globalThis.String(object.messageIconColor) : "",
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      ctaText: isSet(object.ctaText) ? globalThis.String(object.ctaText) : "",
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      bookingStatus: isSet(object.bookingStatus) ? bookingStatusFromJSON(object.bookingStatus) : 0,
      leftTopText: isSet(object.leftTopText) ? globalThis.String(object.leftTopText) : "",
      leftBottomText: isSet(object.leftBottomText) ? globalThis.String(object.leftBottomText) : "",
      rightTopText: isSet(object.rightTopText) ? globalThis.String(object.rightTopText) : "",
      rightBottomText: isSet(object.rightBottomText) ? globalThis.String(object.rightBottomText) : "",
      leftTopColor: isSet(object.leftTopColor) ? globalThis.String(object.leftTopColor) : "",
      leftBottomColor: isSet(object.leftBottomColor) ? globalThis.String(object.leftBottomColor) : "",
      rightTopColor: isSet(object.rightTopColor) ? globalThis.String(object.rightTopColor) : "",
      rightBottomColor: isSet(object.rightBottomColor) ? globalThis.String(object.rightBottomColor) : "",
      leftTopTextColor: isSet(object.leftTopTextColor) ? globalThis.String(object.leftTopTextColor) : "",
      leftBottomTextColor: isSet(object.leftBottomTextColor) ? globalThis.String(object.leftBottomTextColor) : "",
      rightTopTextColor: isSet(object.rightTopTextColor) ? globalThis.String(object.rightTopTextColor) : "",
      rightBottomTextColor: isSet(object.rightBottomTextColor) ? globalThis.String(object.rightBottomTextColor) : "",
      maturityDate: isSet(object.maturityDate) ? globalThis.String(object.maturityDate) : "",
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      investmentDate: isSet(object.investmentDate) ? globalThis.String(object.investmentDate) : "",
      withdrawDeeplink: isSet(object.withdrawDeeplink) ? RedirectDeeplink.fromJSON(object.withdrawDeeplink) : undefined,
      isTaxSaving: isSet(object.isTaxSaving) ? globalThis.Boolean(object.isTaxSaving) : false,
      bookingDate: isSet(object.bookingDate) ? globalThis.String(object.bookingDate) : "",
      maturityInstruction: isSet(object.maturityInstruction)
        ? maturityInstructionFromJSON(object.maturityInstruction)
        : 0,
      isWithdrawalConfirmationRequired: isSet(object.isWithdrawalConfirmationRequired)
        ? globalThis.Boolean(object.isWithdrawalConfirmationRequired)
        : false,
      withdrawalMetadata: isObject(object.withdrawalMetadata)
        ? Object.entries(object.withdrawalMetadata).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: InvestmentInfo): unknown {
    const obj: any = {};
    if (message.bankResponse !== undefined) {
      obj.bankResponse = BankResponse.toJSON(message.bankResponse);
    }
    if (message.leftTopValue !== "") {
      obj.leftTopValue = message.leftTopValue;
    }
    if (message.leftBottomValue !== "") {
      obj.leftBottomValue = message.leftBottomValue;
    }
    if (message.rightTopValue !== "") {
      obj.rightTopValue = message.rightTopValue;
    }
    if (message.rightBottomValue !== "") {
      obj.rightBottomValue = message.rightBottomValue;
    }
    if (message.subtitle !== "") {
      obj.subtitle = message.subtitle;
    }
    if (message.subtitleColor !== "") {
      obj.subtitleColor = message.subtitleColor;
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    if (message.messageTextColor !== "") {
      obj.messageTextColor = message.messageTextColor;
    }
    if (message.messageBgColor !== "") {
      obj.messageBgColor = message.messageBgColor;
    }
    if (message.messageIconColor !== "") {
      obj.messageIconColor = message.messageIconColor;
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.ctaText !== "") {
      obj.ctaText = message.ctaText;
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.bookingStatus !== 0) {
      obj.bookingStatus = bookingStatusToJSON(message.bookingStatus);
    }
    if (message.leftTopText !== "") {
      obj.leftTopText = message.leftTopText;
    }
    if (message.leftBottomText !== "") {
      obj.leftBottomText = message.leftBottomText;
    }
    if (message.rightTopText !== "") {
      obj.rightTopText = message.rightTopText;
    }
    if (message.rightBottomText !== "") {
      obj.rightBottomText = message.rightBottomText;
    }
    if (message.leftTopColor !== "") {
      obj.leftTopColor = message.leftTopColor;
    }
    if (message.leftBottomColor !== "") {
      obj.leftBottomColor = message.leftBottomColor;
    }
    if (message.rightTopColor !== "") {
      obj.rightTopColor = message.rightTopColor;
    }
    if (message.rightBottomColor !== "") {
      obj.rightBottomColor = message.rightBottomColor;
    }
    if (message.leftTopTextColor !== "") {
      obj.leftTopTextColor = message.leftTopTextColor;
    }
    if (message.leftBottomTextColor !== "") {
      obj.leftBottomTextColor = message.leftBottomTextColor;
    }
    if (message.rightTopTextColor !== "") {
      obj.rightTopTextColor = message.rightTopTextColor;
    }
    if (message.rightBottomTextColor !== "") {
      obj.rightBottomTextColor = message.rightBottomTextColor;
    }
    if (message.maturityDate !== "") {
      obj.maturityDate = message.maturityDate;
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.investmentDate !== "") {
      obj.investmentDate = message.investmentDate;
    }
    if (message.withdrawDeeplink !== undefined) {
      obj.withdrawDeeplink = RedirectDeeplink.toJSON(message.withdrawDeeplink);
    }
    if (message.isTaxSaving !== false) {
      obj.isTaxSaving = message.isTaxSaving;
    }
    if (message.bookingDate !== "") {
      obj.bookingDate = message.bookingDate;
    }
    if (message.maturityInstruction !== 0) {
      obj.maturityInstruction = maturityInstructionToJSON(message.maturityInstruction);
    }
    if (message.isWithdrawalConfirmationRequired !== false) {
      obj.isWithdrawalConfirmationRequired = message.isWithdrawalConfirmationRequired;
    }
    if (message.withdrawalMetadata) {
      const entries = Object.entries(message.withdrawalMetadata);
      if (entries.length > 0) {
        obj.withdrawalMetadata = {};
        entries.forEach(([k, v]) => {
          obj.withdrawalMetadata[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentInfo>, I>>(base?: I): InvestmentInfo {
    return InvestmentInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentInfo>, I>>(object: I): InvestmentInfo {
    const message = createBaseInvestmentInfo();
    message.bankResponse = (object.bankResponse !== undefined && object.bankResponse !== null)
      ? BankResponse.fromPartial(object.bankResponse)
      : undefined;
    message.leftTopValue = object.leftTopValue ?? "";
    message.leftBottomValue = object.leftBottomValue ?? "";
    message.rightTopValue = object.rightTopValue ?? "";
    message.rightBottomValue = object.rightBottomValue ?? "";
    message.subtitle = object.subtitle ?? "";
    message.subtitleColor = object.subtitleColor ?? "";
    message.message = object.message ?? "";
    message.messageTextColor = object.messageTextColor ?? "";
    message.messageBgColor = object.messageBgColor ?? "";
    message.messageIconColor = object.messageIconColor ?? "";
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.ctaText = object.ctaText ?? "";
    message.id = object.id ?? "";
    message.bookingStatus = object.bookingStatus ?? 0;
    message.leftTopText = object.leftTopText ?? "";
    message.leftBottomText = object.leftBottomText ?? "";
    message.rightTopText = object.rightTopText ?? "";
    message.rightBottomText = object.rightBottomText ?? "";
    message.leftTopColor = object.leftTopColor ?? "";
    message.leftBottomColor = object.leftBottomColor ?? "";
    message.rightTopColor = object.rightTopColor ?? "";
    message.rightBottomColor = object.rightBottomColor ?? "";
    message.leftTopTextColor = object.leftTopTextColor ?? "";
    message.leftBottomTextColor = object.leftBottomTextColor ?? "";
    message.rightTopTextColor = object.rightTopTextColor ?? "";
    message.rightBottomTextColor = object.rightBottomTextColor ?? "";
    message.maturityDate = object.maturityDate ?? "";
    message.tenure = object.tenure ?? "";
    message.investmentDate = object.investmentDate ?? "";
    message.withdrawDeeplink = (object.withdrawDeeplink !== undefined && object.withdrawDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.withdrawDeeplink)
      : undefined;
    message.isTaxSaving = object.isTaxSaving ?? false;
    message.bookingDate = object.bookingDate ?? "";
    message.maturityInstruction = object.maturityInstruction ?? 0;
    message.isWithdrawalConfirmationRequired = object.isWithdrawalConfirmationRequired ?? false;
    message.withdrawalMetadata = Object.entries(object.withdrawalMetadata ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseInvestmentInfo_WithdrawalMetadataEntry(): InvestmentInfo_WithdrawalMetadataEntry {
  return { key: "", value: "" };
}

export const InvestmentInfo_WithdrawalMetadataEntry: MessageFns<InvestmentInfo_WithdrawalMetadataEntry> = {
  encode(message: InvestmentInfo_WithdrawalMetadataEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentInfo_WithdrawalMetadataEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentInfo_WithdrawalMetadataEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentInfo_WithdrawalMetadataEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: InvestmentInfo_WithdrawalMetadataEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentInfo_WithdrawalMetadataEntry>, I>>(
    base?: I,
  ): InvestmentInfo_WithdrawalMetadataEntry {
    return InvestmentInfo_WithdrawalMetadataEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentInfo_WithdrawalMetadataEntry>, I>>(
    object: I,
  ): InvestmentInfo_WithdrawalMetadataEntry {
    const message = createBaseInvestmentInfo_WithdrawalMetadataEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseInvestmentNomineeDetails(): InvestmentNomineeDetails {
  return {
    nomineeNameText: "",
    nomineeNameValue: "",
    nomineeNameColor: "",
    nomineeRelationText: "",
    nomineeRelationValue: "",
    nomineeRelationColor: "",
    nomineeDobText: "",
    nomineeDobValue: "",
    nomineeDobColor: "",
    heading: "",
  };
}

export const InvestmentNomineeDetails: MessageFns<InvestmentNomineeDetails> = {
  encode(message: InvestmentNomineeDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nomineeNameText !== "") {
      writer.uint32(10).string(message.nomineeNameText);
    }
    if (message.nomineeNameValue !== "") {
      writer.uint32(18).string(message.nomineeNameValue);
    }
    if (message.nomineeNameColor !== "") {
      writer.uint32(26).string(message.nomineeNameColor);
    }
    if (message.nomineeRelationText !== "") {
      writer.uint32(34).string(message.nomineeRelationText);
    }
    if (message.nomineeRelationValue !== "") {
      writer.uint32(42).string(message.nomineeRelationValue);
    }
    if (message.nomineeRelationColor !== "") {
      writer.uint32(50).string(message.nomineeRelationColor);
    }
    if (message.nomineeDobText !== "") {
      writer.uint32(58).string(message.nomineeDobText);
    }
    if (message.nomineeDobValue !== "") {
      writer.uint32(66).string(message.nomineeDobValue);
    }
    if (message.nomineeDobColor !== "") {
      writer.uint32(74).string(message.nomineeDobColor);
    }
    if (message.heading !== "") {
      writer.uint32(82).string(message.heading);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentNomineeDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentNomineeDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.nomineeNameText = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nomineeNameValue = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.nomineeNameColor = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.nomineeRelationText = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.nomineeRelationValue = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.nomineeRelationColor = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.nomineeDobText = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.nomineeDobValue = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.nomineeDobColor = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.heading = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentNomineeDetails {
    return {
      nomineeNameText: isSet(object.nomineeNameText) ? globalThis.String(object.nomineeNameText) : "",
      nomineeNameValue: isSet(object.nomineeNameValue) ? globalThis.String(object.nomineeNameValue) : "",
      nomineeNameColor: isSet(object.nomineeNameColor) ? globalThis.String(object.nomineeNameColor) : "",
      nomineeRelationText: isSet(object.nomineeRelationText) ? globalThis.String(object.nomineeRelationText) : "",
      nomineeRelationValue: isSet(object.nomineeRelationValue) ? globalThis.String(object.nomineeRelationValue) : "",
      nomineeRelationColor: isSet(object.nomineeRelationColor) ? globalThis.String(object.nomineeRelationColor) : "",
      nomineeDobText: isSet(object.nomineeDobText) ? globalThis.String(object.nomineeDobText) : "",
      nomineeDobValue: isSet(object.nomineeDobValue) ? globalThis.String(object.nomineeDobValue) : "",
      nomineeDobColor: isSet(object.nomineeDobColor) ? globalThis.String(object.nomineeDobColor) : "",
      heading: isSet(object.heading) ? globalThis.String(object.heading) : "",
    };
  },

  toJSON(message: InvestmentNomineeDetails): unknown {
    const obj: any = {};
    if (message.nomineeNameText !== "") {
      obj.nomineeNameText = message.nomineeNameText;
    }
    if (message.nomineeNameValue !== "") {
      obj.nomineeNameValue = message.nomineeNameValue;
    }
    if (message.nomineeNameColor !== "") {
      obj.nomineeNameColor = message.nomineeNameColor;
    }
    if (message.nomineeRelationText !== "") {
      obj.nomineeRelationText = message.nomineeRelationText;
    }
    if (message.nomineeRelationValue !== "") {
      obj.nomineeRelationValue = message.nomineeRelationValue;
    }
    if (message.nomineeRelationColor !== "") {
      obj.nomineeRelationColor = message.nomineeRelationColor;
    }
    if (message.nomineeDobText !== "") {
      obj.nomineeDobText = message.nomineeDobText;
    }
    if (message.nomineeDobValue !== "") {
      obj.nomineeDobValue = message.nomineeDobValue;
    }
    if (message.nomineeDobColor !== "") {
      obj.nomineeDobColor = message.nomineeDobColor;
    }
    if (message.heading !== "") {
      obj.heading = message.heading;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentNomineeDetails>, I>>(base?: I): InvestmentNomineeDetails {
    return InvestmentNomineeDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentNomineeDetails>, I>>(object: I): InvestmentNomineeDetails {
    const message = createBaseInvestmentNomineeDetails();
    message.nomineeNameText = object.nomineeNameText ?? "";
    message.nomineeNameValue = object.nomineeNameValue ?? "";
    message.nomineeNameColor = object.nomineeNameColor ?? "";
    message.nomineeRelationText = object.nomineeRelationText ?? "";
    message.nomineeRelationValue = object.nomineeRelationValue ?? "";
    message.nomineeRelationColor = object.nomineeRelationColor ?? "";
    message.nomineeDobText = object.nomineeDobText ?? "";
    message.nomineeDobValue = object.nomineeDobValue ?? "";
    message.nomineeDobColor = object.nomineeDobColor ?? "";
    message.heading = object.heading ?? "";
    return message;
  },
};

function createBaseWithdrawalBankAccountDetails(): WithdrawalBankAccountDetails {
  return {
    accountHolderNameText: "",
    accountHolderNameValue: "",
    accountHolderNameColor: "",
    accountNumberText: "",
    accountNumberValue: "",
    accountNumberColor: "",
    ifscCodeText: "",
    ifscCodeValue: "",
    ifscCodeColor: "",
    heading: "",
  };
}

export const WithdrawalBankAccountDetails: MessageFns<WithdrawalBankAccountDetails> = {
  encode(message: WithdrawalBankAccountDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accountHolderNameText !== "") {
      writer.uint32(10).string(message.accountHolderNameText);
    }
    if (message.accountHolderNameValue !== "") {
      writer.uint32(18).string(message.accountHolderNameValue);
    }
    if (message.accountHolderNameColor !== "") {
      writer.uint32(26).string(message.accountHolderNameColor);
    }
    if (message.accountNumberText !== "") {
      writer.uint32(34).string(message.accountNumberText);
    }
    if (message.accountNumberValue !== "") {
      writer.uint32(42).string(message.accountNumberValue);
    }
    if (message.accountNumberColor !== "") {
      writer.uint32(50).string(message.accountNumberColor);
    }
    if (message.ifscCodeText !== "") {
      writer.uint32(58).string(message.ifscCodeText);
    }
    if (message.ifscCodeValue !== "") {
      writer.uint32(66).string(message.ifscCodeValue);
    }
    if (message.ifscCodeColor !== "") {
      writer.uint32(74).string(message.ifscCodeColor);
    }
    if (message.heading !== "") {
      writer.uint32(82).string(message.heading);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalBankAccountDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalBankAccountDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accountHolderNameText = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountHolderNameValue = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accountHolderNameColor = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.accountNumberText = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.accountNumberValue = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.accountNumberColor = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.ifscCodeText = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.ifscCodeValue = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.ifscCodeColor = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.heading = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalBankAccountDetails {
    return {
      accountHolderNameText: isSet(object.accountHolderNameText) ? globalThis.String(object.accountHolderNameText) : "",
      accountHolderNameValue: isSet(object.accountHolderNameValue)
        ? globalThis.String(object.accountHolderNameValue)
        : "",
      accountHolderNameColor: isSet(object.accountHolderNameColor)
        ? globalThis.String(object.accountHolderNameColor)
        : "",
      accountNumberText: isSet(object.accountNumberText) ? globalThis.String(object.accountNumberText) : "",
      accountNumberValue: isSet(object.accountNumberValue) ? globalThis.String(object.accountNumberValue) : "",
      accountNumberColor: isSet(object.accountNumberColor) ? globalThis.String(object.accountNumberColor) : "",
      ifscCodeText: isSet(object.ifscCodeText) ? globalThis.String(object.ifscCodeText) : "",
      ifscCodeValue: isSet(object.ifscCodeValue) ? globalThis.String(object.ifscCodeValue) : "",
      ifscCodeColor: isSet(object.ifscCodeColor) ? globalThis.String(object.ifscCodeColor) : "",
      heading: isSet(object.heading) ? globalThis.String(object.heading) : "",
    };
  },

  toJSON(message: WithdrawalBankAccountDetails): unknown {
    const obj: any = {};
    if (message.accountHolderNameText !== "") {
      obj.accountHolderNameText = message.accountHolderNameText;
    }
    if (message.accountHolderNameValue !== "") {
      obj.accountHolderNameValue = message.accountHolderNameValue;
    }
    if (message.accountHolderNameColor !== "") {
      obj.accountHolderNameColor = message.accountHolderNameColor;
    }
    if (message.accountNumberText !== "") {
      obj.accountNumberText = message.accountNumberText;
    }
    if (message.accountNumberValue !== "") {
      obj.accountNumberValue = message.accountNumberValue;
    }
    if (message.accountNumberColor !== "") {
      obj.accountNumberColor = message.accountNumberColor;
    }
    if (message.ifscCodeText !== "") {
      obj.ifscCodeText = message.ifscCodeText;
    }
    if (message.ifscCodeValue !== "") {
      obj.ifscCodeValue = message.ifscCodeValue;
    }
    if (message.ifscCodeColor !== "") {
      obj.ifscCodeColor = message.ifscCodeColor;
    }
    if (message.heading !== "") {
      obj.heading = message.heading;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalBankAccountDetails>, I>>(base?: I): WithdrawalBankAccountDetails {
    return WithdrawalBankAccountDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalBankAccountDetails>, I>>(object: I): WithdrawalBankAccountDetails {
    const message = createBaseWithdrawalBankAccountDetails();
    message.accountHolderNameText = object.accountHolderNameText ?? "";
    message.accountHolderNameValue = object.accountHolderNameValue ?? "";
    message.accountHolderNameColor = object.accountHolderNameColor ?? "";
    message.accountNumberText = object.accountNumberText ?? "";
    message.accountNumberValue = object.accountNumberValue ?? "";
    message.accountNumberColor = object.accountNumberColor ?? "";
    message.ifscCodeText = object.ifscCodeText ?? "";
    message.ifscCodeValue = object.ifscCodeValue ?? "";
    message.ifscCodeColor = object.ifscCodeColor ?? "";
    message.heading = object.heading ?? "";
    return message;
  },
};

function createBaseSavingsBankAccountDetails(): SavingsBankAccountDetails {
  return { investmentKeyValueDetails: [], heading: "" };
}

export const SavingsBankAccountDetails: MessageFns<SavingsBankAccountDetails> = {
  encode(message: SavingsBankAccountDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.investmentKeyValueDetails) {
      InvestmentKeyValue.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.heading !== "") {
      writer.uint32(18).string(message.heading);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SavingsBankAccountDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSavingsBankAccountDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.investmentKeyValueDetails.push(InvestmentKeyValue.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.heading = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SavingsBankAccountDetails {
    return {
      investmentKeyValueDetails: globalThis.Array.isArray(object?.investmentKeyValueDetails)
        ? object.investmentKeyValueDetails.map((e: any) => InvestmentKeyValue.fromJSON(e))
        : [],
      heading: isSet(object.heading) ? globalThis.String(object.heading) : "",
    };
  },

  toJSON(message: SavingsBankAccountDetails): unknown {
    const obj: any = {};
    if (message.investmentKeyValueDetails?.length) {
      obj.investmentKeyValueDetails = message.investmentKeyValueDetails.map((e) => InvestmentKeyValue.toJSON(e));
    }
    if (message.heading !== "") {
      obj.heading = message.heading;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SavingsBankAccountDetails>, I>>(base?: I): SavingsBankAccountDetails {
    return SavingsBankAccountDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavingsBankAccountDetails>, I>>(object: I): SavingsBankAccountDetails {
    const message = createBaseSavingsBankAccountDetails();
    message.investmentKeyValueDetails =
      object.investmentKeyValueDetails?.map((e) => InvestmentKeyValue.fromPartial(e)) || [];
    message.heading = object.heading ?? "";
    return message;
  },
};

function createBaseInvestmentKeyValue(): InvestmentKeyValue {
  return { labelText: "", labelValue: "", labelValueColor: "" };
}

export const InvestmentKeyValue: MessageFns<InvestmentKeyValue> = {
  encode(message: InvestmentKeyValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.labelText !== "") {
      writer.uint32(10).string(message.labelText);
    }
    if (message.labelValue !== "") {
      writer.uint32(18).string(message.labelValue);
    }
    if (message.labelValueColor !== "") {
      writer.uint32(26).string(message.labelValueColor);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentKeyValue {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentKeyValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.labelText = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.labelValue = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.labelValueColor = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentKeyValue {
    return {
      labelText: isSet(object.labelText) ? globalThis.String(object.labelText) : "",
      labelValue: isSet(object.labelValue) ? globalThis.String(object.labelValue) : "",
      labelValueColor: isSet(object.labelValueColor) ? globalThis.String(object.labelValueColor) : "",
    };
  },

  toJSON(message: InvestmentKeyValue): unknown {
    const obj: any = {};
    if (message.labelText !== "") {
      obj.labelText = message.labelText;
    }
    if (message.labelValue !== "") {
      obj.labelValue = message.labelValue;
    }
    if (message.labelValueColor !== "") {
      obj.labelValueColor = message.labelValueColor;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentKeyValue>, I>>(base?: I): InvestmentKeyValue {
    return InvestmentKeyValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentKeyValue>, I>>(object: I): InvestmentKeyValue {
    const message = createBaseInvestmentKeyValue();
    message.labelText = object.labelText ?? "";
    message.labelValue = object.labelValue ?? "";
    message.labelValueColor = object.labelValueColor ?? "";
    return message;
  },
};

function createBaseInvestmentDetailsResponse(): InvestmentDetailsResponse {
  return {
    investmentInfo: undefined,
    nomineeDetails: undefined,
    withdrawalBankAccountDetails: undefined,
    interestPayoutDetails: undefined,
    savingsBankAcountDetails: undefined,
  };
}

export const InvestmentDetailsResponse: MessageFns<InvestmentDetailsResponse> = {
  encode(message: InvestmentDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.investmentInfo !== undefined) {
      InvestmentInfo.encode(message.investmentInfo, writer.uint32(10).fork()).join();
    }
    if (message.nomineeDetails !== undefined) {
      InvestmentNomineeDetails.encode(message.nomineeDetails, writer.uint32(18).fork()).join();
    }
    if (message.withdrawalBankAccountDetails !== undefined) {
      WithdrawalBankAccountDetails.encode(message.withdrawalBankAccountDetails, writer.uint32(26).fork()).join();
    }
    if (message.interestPayoutDetails !== undefined) {
      InterestPayoutDetails.encode(message.interestPayoutDetails, writer.uint32(34).fork()).join();
    }
    if (message.savingsBankAcountDetails !== undefined) {
      SavingsBankAccountDetails.encode(message.savingsBankAcountDetails, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InvestmentDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInvestmentDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.investmentInfo = InvestmentInfo.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nomineeDetails = InvestmentNomineeDetails.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.withdrawalBankAccountDetails = WithdrawalBankAccountDetails.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.interestPayoutDetails = InterestPayoutDetails.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.savingsBankAcountDetails = SavingsBankAccountDetails.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InvestmentDetailsResponse {
    return {
      investmentInfo: isSet(object.investmentInfo) ? InvestmentInfo.fromJSON(object.investmentInfo) : undefined,
      nomineeDetails: isSet(object.nomineeDetails)
        ? InvestmentNomineeDetails.fromJSON(object.nomineeDetails)
        : undefined,
      withdrawalBankAccountDetails: isSet(object.withdrawalBankAccountDetails)
        ? WithdrawalBankAccountDetails.fromJSON(object.withdrawalBankAccountDetails)
        : undefined,
      interestPayoutDetails: isSet(object.interestPayoutDetails)
        ? InterestPayoutDetails.fromJSON(object.interestPayoutDetails)
        : undefined,
      savingsBankAcountDetails: isSet(object.savingsBankAcountDetails)
        ? SavingsBankAccountDetails.fromJSON(object.savingsBankAcountDetails)
        : undefined,
    };
  },

  toJSON(message: InvestmentDetailsResponse): unknown {
    const obj: any = {};
    if (message.investmentInfo !== undefined) {
      obj.investmentInfo = InvestmentInfo.toJSON(message.investmentInfo);
    }
    if (message.nomineeDetails !== undefined) {
      obj.nomineeDetails = InvestmentNomineeDetails.toJSON(message.nomineeDetails);
    }
    if (message.withdrawalBankAccountDetails !== undefined) {
      obj.withdrawalBankAccountDetails = WithdrawalBankAccountDetails.toJSON(message.withdrawalBankAccountDetails);
    }
    if (message.interestPayoutDetails !== undefined) {
      obj.interestPayoutDetails = InterestPayoutDetails.toJSON(message.interestPayoutDetails);
    }
    if (message.savingsBankAcountDetails !== undefined) {
      obj.savingsBankAcountDetails = SavingsBankAccountDetails.toJSON(message.savingsBankAcountDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InvestmentDetailsResponse>, I>>(base?: I): InvestmentDetailsResponse {
    return InvestmentDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InvestmentDetailsResponse>, I>>(object: I): InvestmentDetailsResponse {
    const message = createBaseInvestmentDetailsResponse();
    message.investmentInfo = (object.investmentInfo !== undefined && object.investmentInfo !== null)
      ? InvestmentInfo.fromPartial(object.investmentInfo)
      : undefined;
    message.nomineeDetails = (object.nomineeDetails !== undefined && object.nomineeDetails !== null)
      ? InvestmentNomineeDetails.fromPartial(object.nomineeDetails)
      : undefined;
    message.withdrawalBankAccountDetails =
      (object.withdrawalBankAccountDetails !== undefined && object.withdrawalBankAccountDetails !== null)
        ? WithdrawalBankAccountDetails.fromPartial(object.withdrawalBankAccountDetails)
        : undefined;
    message.interestPayoutDetails =
      (object.interestPayoutDetails !== undefined && object.interestPayoutDetails !== null)
        ? InterestPayoutDetails.fromPartial(object.interestPayoutDetails)
        : undefined;
    message.savingsBankAcountDetails =
      (object.savingsBankAcountDetails !== undefined && object.savingsBankAcountDetails !== null)
        ? SavingsBankAccountDetails.fromPartial(object.savingsBankAcountDetails)
        : undefined;
    return message;
  },
};

function createBaseInterestPayoutDetails(): InterestPayoutDetails {
  return {
    heading: "",
    payoutTypeText: "",
    payoutTypeValue: "",
    gainsTillDateText: "",
    gainsTillDateValue: "",
    interestPaidTillDateText: "",
    interestPaidTillDateValue: "",
    gainsThisMonthText: "",
    gainsThisMonthValue: "",
  };
}

export const InterestPayoutDetails: MessageFns<InterestPayoutDetails> = {
  encode(message: InterestPayoutDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.heading !== "") {
      writer.uint32(10).string(message.heading);
    }
    if (message.payoutTypeText !== "") {
      writer.uint32(18).string(message.payoutTypeText);
    }
    if (message.payoutTypeValue !== "") {
      writer.uint32(26).string(message.payoutTypeValue);
    }
    if (message.gainsTillDateText !== "") {
      writer.uint32(34).string(message.gainsTillDateText);
    }
    if (message.gainsTillDateValue !== "") {
      writer.uint32(42).string(message.gainsTillDateValue);
    }
    if (message.interestPaidTillDateText !== "") {
      writer.uint32(50).string(message.interestPaidTillDateText);
    }
    if (message.interestPaidTillDateValue !== "") {
      writer.uint32(58).string(message.interestPaidTillDateValue);
    }
    if (message.gainsThisMonthText !== "") {
      writer.uint32(66).string(message.gainsThisMonthText);
    }
    if (message.gainsThisMonthValue !== "") {
      writer.uint32(74).string(message.gainsThisMonthValue);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InterestPayoutDetails {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInterestPayoutDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.heading = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.payoutTypeText = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.payoutTypeValue = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.gainsTillDateText = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.gainsTillDateValue = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.interestPaidTillDateText = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.interestPaidTillDateValue = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.gainsThisMonthText = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.gainsThisMonthValue = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InterestPayoutDetails {
    return {
      heading: isSet(object.heading) ? globalThis.String(object.heading) : "",
      payoutTypeText: isSet(object.payoutTypeText) ? globalThis.String(object.payoutTypeText) : "",
      payoutTypeValue: isSet(object.payoutTypeValue) ? globalThis.String(object.payoutTypeValue) : "",
      gainsTillDateText: isSet(object.gainsTillDateText) ? globalThis.String(object.gainsTillDateText) : "",
      gainsTillDateValue: isSet(object.gainsTillDateValue) ? globalThis.String(object.gainsTillDateValue) : "",
      interestPaidTillDateText: isSet(object.interestPaidTillDateText)
        ? globalThis.String(object.interestPaidTillDateText)
        : "",
      interestPaidTillDateValue: isSet(object.interestPaidTillDateValue)
        ? globalThis.String(object.interestPaidTillDateValue)
        : "",
      gainsThisMonthText: isSet(object.gainsThisMonthText) ? globalThis.String(object.gainsThisMonthText) : "",
      gainsThisMonthValue: isSet(object.gainsThisMonthValue) ? globalThis.String(object.gainsThisMonthValue) : "",
    };
  },

  toJSON(message: InterestPayoutDetails): unknown {
    const obj: any = {};
    if (message.heading !== "") {
      obj.heading = message.heading;
    }
    if (message.payoutTypeText !== "") {
      obj.payoutTypeText = message.payoutTypeText;
    }
    if (message.payoutTypeValue !== "") {
      obj.payoutTypeValue = message.payoutTypeValue;
    }
    if (message.gainsTillDateText !== "") {
      obj.gainsTillDateText = message.gainsTillDateText;
    }
    if (message.gainsTillDateValue !== "") {
      obj.gainsTillDateValue = message.gainsTillDateValue;
    }
    if (message.interestPaidTillDateText !== "") {
      obj.interestPaidTillDateText = message.interestPaidTillDateText;
    }
    if (message.interestPaidTillDateValue !== "") {
      obj.interestPaidTillDateValue = message.interestPaidTillDateValue;
    }
    if (message.gainsThisMonthText !== "") {
      obj.gainsThisMonthText = message.gainsThisMonthText;
    }
    if (message.gainsThisMonthValue !== "") {
      obj.gainsThisMonthValue = message.gainsThisMonthValue;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InterestPayoutDetails>, I>>(base?: I): InterestPayoutDetails {
    return InterestPayoutDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InterestPayoutDetails>, I>>(object: I): InterestPayoutDetails {
    const message = createBaseInterestPayoutDetails();
    message.heading = object.heading ?? "";
    message.payoutTypeText = object.payoutTypeText ?? "";
    message.payoutTypeValue = object.payoutTypeValue ?? "";
    message.gainsTillDateText = object.gainsTillDateText ?? "";
    message.gainsTillDateValue = object.gainsTillDateValue ?? "";
    message.interestPaidTillDateText = object.interestPaidTillDateText ?? "";
    message.interestPaidTillDateValue = object.interestPaidTillDateValue ?? "";
    message.gainsThisMonthText = object.gainsThisMonthText ?? "";
    message.gainsThisMonthValue = object.gainsThisMonthValue ?? "";
    return message;
  },
};

function createBasePrematureWithdrawalDetailsResponse(): PrematureWithdrawalDetailsResponse {
  return {
    interestGainedTillDate: 0,
    interestGainedIfWithdrawnToday: 0,
    interestGainedIfWithdrawnAfterSomeDays: 0,
    daysToIncreaseInterest: 0,
    redirectDeeplink: undefined,
    isWithdrawalPossible: false,
    lockInPeriodInDays: 0,
    lockInPeriod: "",
    lockInPeriodEndDate: "",
  };
}

export const PrematureWithdrawalDetailsResponse: MessageFns<PrematureWithdrawalDetailsResponse> = {
  encode(message: PrematureWithdrawalDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.interestGainedTillDate !== 0) {
      writer.uint32(9).double(message.interestGainedTillDate);
    }
    if (message.interestGainedIfWithdrawnToday !== 0) {
      writer.uint32(17).double(message.interestGainedIfWithdrawnToday);
    }
    if (message.interestGainedIfWithdrawnAfterSomeDays !== 0) {
      writer.uint32(25).double(message.interestGainedIfWithdrawnAfterSomeDays);
    }
    if (message.daysToIncreaseInterest !== 0) {
      writer.uint32(32).int32(message.daysToIncreaseInterest);
    }
    if (message.redirectDeeplink !== undefined) {
      RedirectDeeplink.encode(message.redirectDeeplink, writer.uint32(42).fork()).join();
    }
    if (message.isWithdrawalPossible !== false) {
      writer.uint32(48).bool(message.isWithdrawalPossible);
    }
    if (message.lockInPeriodInDays !== 0) {
      writer.uint32(56).int32(message.lockInPeriodInDays);
    }
    if (message.lockInPeriod !== "") {
      writer.uint32(66).string(message.lockInPeriod);
    }
    if (message.lockInPeriodEndDate !== "") {
      writer.uint32(74).string(message.lockInPeriodEndDate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PrematureWithdrawalDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePrematureWithdrawalDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.interestGainedTillDate = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.interestGainedIfWithdrawnToday = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.interestGainedIfWithdrawnAfterSomeDays = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.daysToIncreaseInterest = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.redirectDeeplink = RedirectDeeplink.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.isWithdrawalPossible = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.lockInPeriodInDays = reader.int32();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.lockInPeriod = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.lockInPeriodEndDate = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PrematureWithdrawalDetailsResponse {
    return {
      interestGainedTillDate: isSet(object.interestGainedTillDate)
        ? globalThis.Number(object.interestGainedTillDate)
        : 0,
      interestGainedIfWithdrawnToday: isSet(object.interestGainedIfWithdrawnToday)
        ? globalThis.Number(object.interestGainedIfWithdrawnToday)
        : 0,
      interestGainedIfWithdrawnAfterSomeDays: isSet(object.interestGainedIfWithdrawnAfterSomeDays)
        ? globalThis.Number(object.interestGainedIfWithdrawnAfterSomeDays)
        : 0,
      daysToIncreaseInterest: isSet(object.daysToIncreaseInterest)
        ? globalThis.Number(object.daysToIncreaseInterest)
        : 0,
      redirectDeeplink: isSet(object.redirectDeeplink) ? RedirectDeeplink.fromJSON(object.redirectDeeplink) : undefined,
      isWithdrawalPossible: isSet(object.isWithdrawalPossible)
        ? globalThis.Boolean(object.isWithdrawalPossible)
        : false,
      lockInPeriodInDays: isSet(object.lockInPeriodInDays) ? globalThis.Number(object.lockInPeriodInDays) : 0,
      lockInPeriod: isSet(object.lockInPeriod) ? globalThis.String(object.lockInPeriod) : "",
      lockInPeriodEndDate: isSet(object.lockInPeriodEndDate) ? globalThis.String(object.lockInPeriodEndDate) : "",
    };
  },

  toJSON(message: PrematureWithdrawalDetailsResponse): unknown {
    const obj: any = {};
    if (message.interestGainedTillDate !== 0) {
      obj.interestGainedTillDate = message.interestGainedTillDate;
    }
    if (message.interestGainedIfWithdrawnToday !== 0) {
      obj.interestGainedIfWithdrawnToday = message.interestGainedIfWithdrawnToday;
    }
    if (message.interestGainedIfWithdrawnAfterSomeDays !== 0) {
      obj.interestGainedIfWithdrawnAfterSomeDays = message.interestGainedIfWithdrawnAfterSomeDays;
    }
    if (message.daysToIncreaseInterest !== 0) {
      obj.daysToIncreaseInterest = Math.round(message.daysToIncreaseInterest);
    }
    if (message.redirectDeeplink !== undefined) {
      obj.redirectDeeplink = RedirectDeeplink.toJSON(message.redirectDeeplink);
    }
    if (message.isWithdrawalPossible !== false) {
      obj.isWithdrawalPossible = message.isWithdrawalPossible;
    }
    if (message.lockInPeriodInDays !== 0) {
      obj.lockInPeriodInDays = Math.round(message.lockInPeriodInDays);
    }
    if (message.lockInPeriod !== "") {
      obj.lockInPeriod = message.lockInPeriod;
    }
    if (message.lockInPeriodEndDate !== "") {
      obj.lockInPeriodEndDate = message.lockInPeriodEndDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PrematureWithdrawalDetailsResponse>, I>>(
    base?: I,
  ): PrematureWithdrawalDetailsResponse {
    return PrematureWithdrawalDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrematureWithdrawalDetailsResponse>, I>>(
    object: I,
  ): PrematureWithdrawalDetailsResponse {
    const message = createBasePrematureWithdrawalDetailsResponse();
    message.interestGainedTillDate = object.interestGainedTillDate ?? 0;
    message.interestGainedIfWithdrawnToday = object.interestGainedIfWithdrawnToday ?? 0;
    message.interestGainedIfWithdrawnAfterSomeDays = object.interestGainedIfWithdrawnAfterSomeDays ?? 0;
    message.daysToIncreaseInterest = object.daysToIncreaseInterest ?? 0;
    message.redirectDeeplink = (object.redirectDeeplink !== undefined && object.redirectDeeplink !== null)
      ? RedirectDeeplink.fromPartial(object.redirectDeeplink)
      : undefined;
    message.isWithdrawalPossible = object.isWithdrawalPossible ?? false;
    message.lockInPeriodInDays = object.lockInPeriodInDays ?? 0;
    message.lockInPeriod = object.lockInPeriod ?? "";
    message.lockInPeriodEndDate = object.lockInPeriodEndDate ?? "";
    return message;
  },
};

function createBaseVkycExpiryResponse(): VkycExpiryResponse {
  return { expiryDate: "" };
}

export const VkycExpiryResponse: MessageFns<VkycExpiryResponse> = {
  encode(message: VkycExpiryResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.expiryDate !== "") {
      writer.uint32(10).string(message.expiryDate);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VkycExpiryResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVkycExpiryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.expiryDate = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VkycExpiryResponse {
    return { expiryDate: isSet(object.expiryDate) ? globalThis.String(object.expiryDate) : "" };
  },

  toJSON(message: VkycExpiryResponse): unknown {
    const obj: any = {};
    if (message.expiryDate !== "") {
      obj.expiryDate = message.expiryDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VkycExpiryResponse>, I>>(base?: I): VkycExpiryResponse {
    return VkycExpiryResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VkycExpiryResponse>, I>>(object: I): VkycExpiryResponse {
    const message = createBaseVkycExpiryResponse();
    message.expiryDate = object.expiryDate ?? "";
    return message;
  },
};

function createBasePostWithdrawalDetail(): PostWithdrawalDetail {
  return {
    userFixedDeposit: undefined,
    surveySubmitted: false,
    surveyResponse: "",
    withdrawalTimestamp: 0,
    estimatedRefundTime: 0,
    refundProcessed: false,
    withdrawalTimeline: undefined,
  };
}

export const PostWithdrawalDetail: MessageFns<PostWithdrawalDetail> = {
  encode(message: PostWithdrawalDetail, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userFixedDeposit !== undefined) {
      PostWithdrawalDetail_UserFixedDeposit.encode(message.userFixedDeposit, writer.uint32(10).fork()).join();
    }
    if (message.surveySubmitted !== false) {
      writer.uint32(16).bool(message.surveySubmitted);
    }
    if (message.surveyResponse !== "") {
      writer.uint32(26).string(message.surveyResponse);
    }
    if (message.withdrawalTimestamp !== 0) {
      writer.uint32(32).int64(message.withdrawalTimestamp);
    }
    if (message.estimatedRefundTime !== 0) {
      writer.uint32(40).int64(message.estimatedRefundTime);
    }
    if (message.refundProcessed !== false) {
      writer.uint32(64).bool(message.refundProcessed);
    }
    if (message.withdrawalTimeline !== undefined) {
      PostWithdrawalDetail_WithdrawalTimeline.encode(message.withdrawalTimeline, writer.uint32(82).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PostWithdrawalDetail {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePostWithdrawalDetail();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userFixedDeposit = PostWithdrawalDetail_UserFixedDeposit.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.surveySubmitted = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.surveyResponse = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.withdrawalTimestamp = longToNumber(reader.int64());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.estimatedRefundTime = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.refundProcessed = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.withdrawalTimeline = PostWithdrawalDetail_WithdrawalTimeline.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PostWithdrawalDetail {
    return {
      userFixedDeposit: isSet(object.userFixedDeposit)
        ? PostWithdrawalDetail_UserFixedDeposit.fromJSON(object.userFixedDeposit)
        : undefined,
      surveySubmitted: isSet(object.surveySubmitted) ? globalThis.Boolean(object.surveySubmitted) : false,
      surveyResponse: isSet(object.surveyResponse) ? globalThis.String(object.surveyResponse) : "",
      withdrawalTimestamp: isSet(object.withdrawalTimestamp) ? globalThis.Number(object.withdrawalTimestamp) : 0,
      estimatedRefundTime: isSet(object.estimatedRefundTime) ? globalThis.Number(object.estimatedRefundTime) : 0,
      refundProcessed: isSet(object.refundProcessed) ? globalThis.Boolean(object.refundProcessed) : false,
      withdrawalTimeline: isSet(object.withdrawalTimeline)
        ? PostWithdrawalDetail_WithdrawalTimeline.fromJSON(object.withdrawalTimeline)
        : undefined,
    };
  },

  toJSON(message: PostWithdrawalDetail): unknown {
    const obj: any = {};
    if (message.userFixedDeposit !== undefined) {
      obj.userFixedDeposit = PostWithdrawalDetail_UserFixedDeposit.toJSON(message.userFixedDeposit);
    }
    if (message.surveySubmitted !== false) {
      obj.surveySubmitted = message.surveySubmitted;
    }
    if (message.surveyResponse !== "") {
      obj.surveyResponse = message.surveyResponse;
    }
    if (message.withdrawalTimestamp !== 0) {
      obj.withdrawalTimestamp = Math.round(message.withdrawalTimestamp);
    }
    if (message.estimatedRefundTime !== 0) {
      obj.estimatedRefundTime = Math.round(message.estimatedRefundTime);
    }
    if (message.refundProcessed !== false) {
      obj.refundProcessed = message.refundProcessed;
    }
    if (message.withdrawalTimeline !== undefined) {
      obj.withdrawalTimeline = PostWithdrawalDetail_WithdrawalTimeline.toJSON(message.withdrawalTimeline);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PostWithdrawalDetail>, I>>(base?: I): PostWithdrawalDetail {
    return PostWithdrawalDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PostWithdrawalDetail>, I>>(object: I): PostWithdrawalDetail {
    const message = createBasePostWithdrawalDetail();
    message.userFixedDeposit = (object.userFixedDeposit !== undefined && object.userFixedDeposit !== null)
      ? PostWithdrawalDetail_UserFixedDeposit.fromPartial(object.userFixedDeposit)
      : undefined;
    message.surveySubmitted = object.surveySubmitted ?? false;
    message.surveyResponse = object.surveyResponse ?? "";
    message.withdrawalTimestamp = object.withdrawalTimestamp ?? 0;
    message.estimatedRefundTime = object.estimatedRefundTime ?? 0;
    message.refundProcessed = object.refundProcessed ?? false;
    message.withdrawalTimeline = (object.withdrawalTimeline !== undefined && object.withdrawalTimeline !== null)
      ? PostWithdrawalDetail_WithdrawalTimeline.fromPartial(object.withdrawalTimeline)
      : undefined;
    return message;
  },
};

function createBasePostWithdrawalDetail_UserFixedDeposit(): PostWithdrawalDetail_UserFixedDeposit {
  return { id: "", fdIdentifier: "", journeyId: "", bankName: "", bankLogoUrl: "", tenure: "", investmentAmount: 0 };
}

export const PostWithdrawalDetail_UserFixedDeposit: MessageFns<PostWithdrawalDetail_UserFixedDeposit> = {
  encode(message: PostWithdrawalDetail_UserFixedDeposit, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.fdIdentifier !== "") {
      writer.uint32(18).string(message.fdIdentifier);
    }
    if (message.journeyId !== "") {
      writer.uint32(34).string(message.journeyId);
    }
    if (message.bankName !== "") {
      writer.uint32(50).string(message.bankName);
    }
    if (message.bankLogoUrl !== "") {
      writer.uint32(66).string(message.bankLogoUrl);
    }
    if (message.tenure !== "") {
      writer.uint32(82).string(message.tenure);
    }
    if (message.investmentAmount !== 0) {
      writer.uint32(97).double(message.investmentAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PostWithdrawalDetail_UserFixedDeposit {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePostWithdrawalDetail_UserFixedDeposit();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fdIdentifier = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.journeyId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.bankName = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.bankLogoUrl = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.tenure = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.investmentAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PostWithdrawalDetail_UserFixedDeposit {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      fdIdentifier: isSet(object.fdIdentifier) ? globalThis.String(object.fdIdentifier) : "",
      journeyId: isSet(object.journeyId) ? globalThis.String(object.journeyId) : "",
      bankName: isSet(object.bankName) ? globalThis.String(object.bankName) : "",
      bankLogoUrl: isSet(object.bankLogoUrl) ? globalThis.String(object.bankLogoUrl) : "",
      tenure: isSet(object.tenure) ? globalThis.String(object.tenure) : "",
      investmentAmount: isSet(object.investmentAmount) ? globalThis.Number(object.investmentAmount) : 0,
    };
  },

  toJSON(message: PostWithdrawalDetail_UserFixedDeposit): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.fdIdentifier !== "") {
      obj.fdIdentifier = message.fdIdentifier;
    }
    if (message.journeyId !== "") {
      obj.journeyId = message.journeyId;
    }
    if (message.bankName !== "") {
      obj.bankName = message.bankName;
    }
    if (message.bankLogoUrl !== "") {
      obj.bankLogoUrl = message.bankLogoUrl;
    }
    if (message.tenure !== "") {
      obj.tenure = message.tenure;
    }
    if (message.investmentAmount !== 0) {
      obj.investmentAmount = message.investmentAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PostWithdrawalDetail_UserFixedDeposit>, I>>(
    base?: I,
  ): PostWithdrawalDetail_UserFixedDeposit {
    return PostWithdrawalDetail_UserFixedDeposit.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PostWithdrawalDetail_UserFixedDeposit>, I>>(
    object: I,
  ): PostWithdrawalDetail_UserFixedDeposit {
    const message = createBasePostWithdrawalDetail_UserFixedDeposit();
    message.id = object.id ?? "";
    message.fdIdentifier = object.fdIdentifier ?? "";
    message.journeyId = object.journeyId ?? "";
    message.bankName = object.bankName ?? "";
    message.bankLogoUrl = object.bankLogoUrl ?? "";
    message.tenure = object.tenure ?? "";
    message.investmentAmount = object.investmentAmount ?? 0;
    return message;
  },
};

function createBasePostWithdrawalDetail_WithdrawalTimeline(): PostWithdrawalDetail_WithdrawalTimeline {
  return { items: [] };
}

export const PostWithdrawalDetail_WithdrawalTimeline: MessageFns<PostWithdrawalDetail_WithdrawalTimeline> = {
  encode(message: PostWithdrawalDetail_WithdrawalTimeline, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.items) {
      PostWithdrawalDetail_WithdrawalTimeline_TimelineItem.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PostWithdrawalDetail_WithdrawalTimeline {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePostWithdrawalDetail_WithdrawalTimeline();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.items.push(PostWithdrawalDetail_WithdrawalTimeline_TimelineItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PostWithdrawalDetail_WithdrawalTimeline {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => PostWithdrawalDetail_WithdrawalTimeline_TimelineItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: PostWithdrawalDetail_WithdrawalTimeline): unknown {
    const obj: any = {};
    if (message.items?.length) {
      obj.items = message.items.map((e) => PostWithdrawalDetail_WithdrawalTimeline_TimelineItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PostWithdrawalDetail_WithdrawalTimeline>, I>>(
    base?: I,
  ): PostWithdrawalDetail_WithdrawalTimeline {
    return PostWithdrawalDetail_WithdrawalTimeline.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PostWithdrawalDetail_WithdrawalTimeline>, I>>(
    object: I,
  ): PostWithdrawalDetail_WithdrawalTimeline {
    const message = createBasePostWithdrawalDetail_WithdrawalTimeline();
    message.items = object.items?.map((e) => PostWithdrawalDetail_WithdrawalTimeline_TimelineItem.fromPartial(e)) || [];
    return message;
  },
};

function createBasePostWithdrawalDetail_WithdrawalTimeline_TimelineItem(): PostWithdrawalDetail_WithdrawalTimeline_TimelineItem {
  return { title: undefined, description: undefined, complete: false };
}

export const PostWithdrawalDetail_WithdrawalTimeline_TimelineItem: MessageFns<
  PostWithdrawalDetail_WithdrawalTimeline_TimelineItem
> = {
  encode(
    message: PostWithdrawalDetail_WithdrawalTimeline_TimelineItem,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.title !== undefined) {
      DataKey.encode(message.title, writer.uint32(10).fork()).join();
    }
    if (message.description !== undefined) {
      DataKey.encode(message.description, writer.uint32(18).fork()).join();
    }
    if (message.complete !== false) {
      writer.uint32(24).bool(message.complete);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PostWithdrawalDetail_WithdrawalTimeline_TimelineItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePostWithdrawalDetail_WithdrawalTimeline_TimelineItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.title = DataKey.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.description = DataKey.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.complete = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PostWithdrawalDetail_WithdrawalTimeline_TimelineItem {
    return {
      title: isSet(object.title) ? DataKey.fromJSON(object.title) : undefined,
      description: isSet(object.description) ? DataKey.fromJSON(object.description) : undefined,
      complete: isSet(object.complete) ? globalThis.Boolean(object.complete) : false,
    };
  },

  toJSON(message: PostWithdrawalDetail_WithdrawalTimeline_TimelineItem): unknown {
    const obj: any = {};
    if (message.title !== undefined) {
      obj.title = DataKey.toJSON(message.title);
    }
    if (message.description !== undefined) {
      obj.description = DataKey.toJSON(message.description);
    }
    if (message.complete !== false) {
      obj.complete = message.complete;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PostWithdrawalDetail_WithdrawalTimeline_TimelineItem>, I>>(
    base?: I,
  ): PostWithdrawalDetail_WithdrawalTimeline_TimelineItem {
    return PostWithdrawalDetail_WithdrawalTimeline_TimelineItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PostWithdrawalDetail_WithdrawalTimeline_TimelineItem>, I>>(
    object: I,
  ): PostWithdrawalDetail_WithdrawalTimeline_TimelineItem {
    const message = createBasePostWithdrawalDetail_WithdrawalTimeline_TimelineItem();
    message.title = (object.title !== undefined && object.title !== null)
      ? DataKey.fromPartial(object.title)
      : undefined;
    message.description = (object.description !== undefined && object.description !== null)
      ? DataKey.fromPartial(object.description)
      : undefined;
    message.complete = object.complete ?? false;
    return message;
  },
};

function createBaseWithdrawalReasonSubmitRequest(): WithdrawalReasonSubmitRequest {
  return { userFixedDepositId: "", reasonCode: "" };
}

export const WithdrawalReasonSubmitRequest: MessageFns<WithdrawalReasonSubmitRequest> = {
  encode(message: WithdrawalReasonSubmitRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.userFixedDepositId !== "") {
      writer.uint32(10).string(message.userFixedDepositId);
    }
    if (message.reasonCode !== "") {
      writer.uint32(18).string(message.reasonCode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalReasonSubmitRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalReasonSubmitRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userFixedDepositId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.reasonCode = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WithdrawalReasonSubmitRequest {
    return {
      userFixedDepositId: isSet(object.userFixedDepositId) ? globalThis.String(object.userFixedDepositId) : "",
      reasonCode: isSet(object.reasonCode) ? globalThis.String(object.reasonCode) : "",
    };
  },

  toJSON(message: WithdrawalReasonSubmitRequest): unknown {
    const obj: any = {};
    if (message.userFixedDepositId !== "") {
      obj.userFixedDepositId = message.userFixedDepositId;
    }
    if (message.reasonCode !== "") {
      obj.reasonCode = message.reasonCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalReasonSubmitRequest>, I>>(base?: I): WithdrawalReasonSubmitRequest {
    return WithdrawalReasonSubmitRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalReasonSubmitRequest>, I>>(
    object: I,
  ): WithdrawalReasonSubmitRequest {
    const message = createBaseWithdrawalReasonSubmitRequest();
    message.userFixedDepositId = object.userFixedDepositId ?? "";
    message.reasonCode = object.reasonCode ?? "";
    return message;
  },
};

function createBaseWithdrawalReasonSubmitResponse(): WithdrawalReasonSubmitResponse {
  return {};
}

export const WithdrawalReasonSubmitResponse: MessageFns<WithdrawalReasonSubmitResponse> = {
  encode(_: WithdrawalReasonSubmitResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WithdrawalReasonSubmitResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWithdrawalReasonSubmitResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): WithdrawalReasonSubmitResponse {
    return {};
  },

  toJSON(_: WithdrawalReasonSubmitResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<WithdrawalReasonSubmitResponse>, I>>(base?: I): WithdrawalReasonSubmitResponse {
    return WithdrawalReasonSubmitResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawalReasonSubmitResponse>, I>>(_: I): WithdrawalReasonSubmitResponse {
    const message = createBaseWithdrawalReasonSubmitResponse();
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
