// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: SendAppLink.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export interface SendAppLinkRequest {
  phoneNumber: string;
}

export interface SendAppLinkResponse {
}

function createBaseSendAppLinkRequest(): SendAppLinkRequest {
  return { phoneNumber: "" };
}

export const SendAppLinkRequest: MessageFns<SendAppLinkRequest> = {
  encode(message: SendAppLinkRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.phoneNumber !== "") {
      writer.uint32(10).string(message.phoneNumber);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendAppLinkRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendAppLinkRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.phoneNumber = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SendAppLinkRequest {
    return { phoneNumber: isSet(object.phoneNumber) ? globalThis.String(object.phoneNumber) : "" };
  },

  toJSON(message: SendAppLinkRequest): unknown {
    const obj: any = {};
    if (message.phoneNumber !== "") {
      obj.phoneNumber = message.phoneNumber;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendAppLinkRequest>, I>>(base?: I): SendAppLinkRequest {
    return SendAppLinkRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendAppLinkRequest>, I>>(object: I): SendAppLinkRequest {
    const message = createBaseSendAppLinkRequest();
    message.phoneNumber = object.phoneNumber ?? "";
    return message;
  },
};

function createBaseSendAppLinkResponse(): SendAppLinkResponse {
  return {};
}

export const SendAppLinkResponse: MessageFns<SendAppLinkResponse> = {
  encode(_: SendAppLinkResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendAppLinkResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendAppLinkResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): SendAppLinkResponse {
    return {};
  },

  toJSON(_: SendAppLinkResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SendAppLinkResponse>, I>>(base?: I): SendAppLinkResponse {
    return SendAppLinkResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendAppLinkResponse>, I>>(_: I): SendAppLinkResponse {
    const message = createBaseSendAppLinkResponse();
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
