// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Reward.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Timestamp } from "./google/protobuf/timestamp";

export const protobufPackage = "com.stablemoney.api.identity";

export enum VoucherType {
  VOUCHER_TYPE_UNKNOWN = 0,
  AMAZON_PAY_VOUCHER = 1,
  AMAZON_VOUCHER_CODE = 2,
  UNRECOGNIZED = -1,
}

export function voucherTypeFromJSON(object: any): VoucherType {
  switch (object) {
    case 0:
    case "VOUCHER_TYPE_UNKNOWN":
      return VoucherType.VOUCHER_TYPE_UNKNOWN;
    case 1:
    case "AMAZON_PAY_VOUCHER":
      return VoucherType.AMAZON_PAY_VOUCHER;
    case 2:
    case "AMAZON_VOUCHER_CODE":
      return VoucherType.AMAZON_VOUCHER_CODE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return VoucherType.UNRECOGNIZED;
  }
}

export function voucherTypeToJSON(object: VoucherType): string {
  switch (object) {
    case VoucherType.VOUCHER_TYPE_UNKNOWN:
      return "VOUCHER_TYPE_UNKNOWN";
    case VoucherType.AMAZON_PAY_VOUCHER:
      return "AMAZON_PAY_VOUCHER";
    case VoucherType.AMAZON_VOUCHER_CODE:
      return "AMAZON_VOUCHER_CODE";
    case VoucherType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum RewardCode {
  REWARD_CODE_UNKNOWN = 0,
  FIRST_FD_REWARD = 1,
  REFERRAL_REWARD = 2,
  INVESTMENT_BASED_REFERRAL_REWARD = 3,
  REFEREE_FIRST_FD_REWARD = 4,
  EMERGENCY_FUND_REFERRAL_REWARD = 5,
  UNRECOGNIZED = -1,
}

export function rewardCodeFromJSON(object: any): RewardCode {
  switch (object) {
    case 0:
    case "REWARD_CODE_UNKNOWN":
      return RewardCode.REWARD_CODE_UNKNOWN;
    case 1:
    case "FIRST_FD_REWARD":
      return RewardCode.FIRST_FD_REWARD;
    case 2:
    case "REFERRAL_REWARD":
      return RewardCode.REFERRAL_REWARD;
    case 3:
    case "INVESTMENT_BASED_REFERRAL_REWARD":
      return RewardCode.INVESTMENT_BASED_REFERRAL_REWARD;
    case 4:
    case "REFEREE_FIRST_FD_REWARD":
      return RewardCode.REFEREE_FIRST_FD_REWARD;
    case 5:
    case "EMERGENCY_FUND_REFERRAL_REWARD":
      return RewardCode.EMERGENCY_FUND_REFERRAL_REWARD;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RewardCode.UNRECOGNIZED;
  }
}

export function rewardCodeToJSON(object: RewardCode): string {
  switch (object) {
    case RewardCode.REWARD_CODE_UNKNOWN:
      return "REWARD_CODE_UNKNOWN";
    case RewardCode.FIRST_FD_REWARD:
      return "FIRST_FD_REWARD";
    case RewardCode.REFERRAL_REWARD:
      return "REFERRAL_REWARD";
    case RewardCode.INVESTMENT_BASED_REFERRAL_REWARD:
      return "INVESTMENT_BASED_REFERRAL_REWARD";
    case RewardCode.REFEREE_FIRST_FD_REWARD:
      return "REFEREE_FIRST_FD_REWARD";
    case RewardCode.EMERGENCY_FUND_REFERRAL_REWARD:
      return "EMERGENCY_FUND_REFERRAL_REWARD";
    case RewardCode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum VoucherStatus {
  VOUCHER_STATUS_UNKNOWN = 0,
  SENT = 1,
  AVAILABLE = 2,
  EXPIRED = 3,
  UNRECOGNIZED = -1,
}

export function voucherStatusFromJSON(object: any): VoucherStatus {
  switch (object) {
    case 0:
    case "VOUCHER_STATUS_UNKNOWN":
      return VoucherStatus.VOUCHER_STATUS_UNKNOWN;
    case 1:
    case "SENT":
      return VoucherStatus.SENT;
    case 2:
    case "AVAILABLE":
      return VoucherStatus.AVAILABLE;
    case 3:
    case "EXPIRED":
      return VoucherStatus.EXPIRED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return VoucherStatus.UNRECOGNIZED;
  }
}

export function voucherStatusToJSON(object: VoucherStatus): string {
  switch (object) {
    case VoucherStatus.VOUCHER_STATUS_UNKNOWN:
      return "VOUCHER_STATUS_UNKNOWN";
    case VoucherStatus.SENT:
      return "SENT";
    case VoucherStatus.AVAILABLE:
      return "AVAILABLE";
    case VoucherStatus.EXPIRED:
      return "EXPIRED";
    case VoucherStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum RewardDeliveryStatus {
  REWARD_PROCESSING_STATUS_UNKNOWN = 0,
  QUEUED = 1,
  FAILED = 2,
  SENT_FROM_BACKEND = 3,
  DELIVERED = 4,
  OPENED = 5,
  REWARD_TO_BE_SENT = 6,
  REWARD_NOT_ELIGIBLE = 7,
  UNRECOGNIZED = -1,
}

export function rewardDeliveryStatusFromJSON(object: any): RewardDeliveryStatus {
  switch (object) {
    case 0:
    case "REWARD_PROCESSING_STATUS_UNKNOWN":
      return RewardDeliveryStatus.REWARD_PROCESSING_STATUS_UNKNOWN;
    case 1:
    case "QUEUED":
      return RewardDeliveryStatus.QUEUED;
    case 2:
    case "FAILED":
      return RewardDeliveryStatus.FAILED;
    case 3:
    case "SENT_FROM_BACKEND":
      return RewardDeliveryStatus.SENT_FROM_BACKEND;
    case 4:
    case "DELIVERED":
      return RewardDeliveryStatus.DELIVERED;
    case 5:
    case "OPENED":
      return RewardDeliveryStatus.OPENED;
    case 6:
    case "REWARD_TO_BE_SENT":
      return RewardDeliveryStatus.REWARD_TO_BE_SENT;
    case 7:
    case "REWARD_NOT_ELIGIBLE":
      return RewardDeliveryStatus.REWARD_NOT_ELIGIBLE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RewardDeliveryStatus.UNRECOGNIZED;
  }
}

export function rewardDeliveryStatusToJSON(object: RewardDeliveryStatus): string {
  switch (object) {
    case RewardDeliveryStatus.REWARD_PROCESSING_STATUS_UNKNOWN:
      return "REWARD_PROCESSING_STATUS_UNKNOWN";
    case RewardDeliveryStatus.QUEUED:
      return "QUEUED";
    case RewardDeliveryStatus.FAILED:
      return "FAILED";
    case RewardDeliveryStatus.SENT_FROM_BACKEND:
      return "SENT_FROM_BACKEND";
    case RewardDeliveryStatus.DELIVERED:
      return "DELIVERED";
    case RewardDeliveryStatus.OPENED:
      return "OPENED";
    case RewardDeliveryStatus.REWARD_TO_BE_SENT:
      return "REWARD_TO_BE_SENT";
    case RewardDeliveryStatus.REWARD_NOT_ELIGIBLE:
      return "REWARD_NOT_ELIGIBLE";
    case RewardDeliveryStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum BlacklistUserType {
  BLACKLIST_USER_TYPE_UNKNOWN = 0,
  INTERNAL = 1,
  EXTERNAL = 2,
  UNRECOGNIZED = -1,
}

export function blacklistUserTypeFromJSON(object: any): BlacklistUserType {
  switch (object) {
    case 0:
    case "BLACKLIST_USER_TYPE_UNKNOWN":
      return BlacklistUserType.BLACKLIST_USER_TYPE_UNKNOWN;
    case 1:
    case "INTERNAL":
      return BlacklistUserType.INTERNAL;
    case 2:
    case "EXTERNAL":
      return BlacklistUserType.EXTERNAL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return BlacklistUserType.UNRECOGNIZED;
  }
}

export function blacklistUserTypeToJSON(object: BlacklistUserType): string {
  switch (object) {
    case BlacklistUserType.BLACKLIST_USER_TYPE_UNKNOWN:
      return "BLACKLIST_USER_TYPE_UNKNOWN";
    case BlacklistUserType.INTERNAL:
      return "INTERNAL";
    case BlacklistUserType.EXTERNAL:
      return "EXTERNAL";
    case BlacklistUserType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ReferralStatus {
  REFERRAL_STATUS_UNKNOWN = 0,
  BOOKING_NOT_INITIATED = 1,
  BOOKING_DONE = 2,
  REWARD_PROCESSED = 3,
  REWARD_NOT_APPLICABLE = 4,
  UNRECOGNIZED = -1,
}

export function referralStatusFromJSON(object: any): ReferralStatus {
  switch (object) {
    case 0:
    case "REFERRAL_STATUS_UNKNOWN":
      return ReferralStatus.REFERRAL_STATUS_UNKNOWN;
    case 1:
    case "BOOKING_NOT_INITIATED":
      return ReferralStatus.BOOKING_NOT_INITIATED;
    case 2:
    case "BOOKING_DONE":
      return ReferralStatus.BOOKING_DONE;
    case 3:
    case "REWARD_PROCESSED":
      return ReferralStatus.REWARD_PROCESSED;
    case 4:
    case "REWARD_NOT_APPLICABLE":
      return ReferralStatus.REWARD_NOT_APPLICABLE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ReferralStatus.UNRECOGNIZED;
  }
}

export function referralStatusToJSON(object: ReferralStatus): string {
  switch (object) {
    case ReferralStatus.REFERRAL_STATUS_UNKNOWN:
      return "REFERRAL_STATUS_UNKNOWN";
    case ReferralStatus.BOOKING_NOT_INITIATED:
      return "BOOKING_NOT_INITIATED";
    case ReferralStatus.BOOKING_DONE:
      return "BOOKING_DONE";
    case ReferralStatus.REWARD_PROCESSED:
      return "REWARD_PROCESSED";
    case ReferralStatus.REWARD_NOT_APPLICABLE:
      return "REWARD_NOT_APPLICABLE";
    case ReferralStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface VoucherItem {
  voucherCode: string;
  amount: number;
  rewardCode: RewardCode;
  voucherStatus: VoucherStatus;
  expiryDate?: Date | undefined;
  voucherType: VoucherType;
}

export interface BulkVoucherRequest {
  voucherItemsList: VoucherItem[];
}

export interface AddRewardTransactionRequest {
  rewardLink: string;
  phoneNumber: string;
  transactionTimestamp: Date | undefined;
}

export interface BulkAddRewardTransactionsRequest {
  transactions: AddRewardTransactionRequest[];
}

export interface ManualAddRewardRequest {
  rewardCode: RewardCode;
  userId: string;
  referrerUserId?: string | undefined;
  amount: number;
  bankId: string;
  consumeBooking?: boolean | undefined;
  overridePaymentTimeCheck?: boolean | undefined;
}

export interface CheckGoldenTicketRequest {
  userIdList: string[];
}

export interface RewardItem {
  rewardLink: string;
  rewardSentTimestamp: Date | undefined;
  rewardAmount: number;
  rewardCode: RewardCode;
  rewardDeliveryStatus: RewardDeliveryStatus;
}

export interface RewardsDataResponse {
  totalEarnings: number;
  rewards: RewardItem[];
}

export interface RewardTypesResponse {
  rewardTypes: RewardType[];
}

export interface RewardTypesRequest {
  rewardCode?: RewardCode | undefined;
}

export interface RewardType {
  id: string;
  amount: number;
  maxCount: number;
  minTransactionAmount: number;
  rewardCode: RewardCode;
  validFrom?: number | undefined;
  validTill?: number | undefined;
  instantRewardTrAmount?: number | undefined;
  deliveryDelayInDays?: number | undefined;
  referralPercent?: number | undefined;
  referralLevel?: number | undefined;
  refererMinNetWorth?: number | undefined;
  goldenTicketAmount?: number | undefined;
  randomizedRewardPercentage?: number | undefined;
  referralCount?: number | undefined;
}

function createBaseVoucherItem(): VoucherItem {
  return { voucherCode: "", amount: 0, rewardCode: 0, voucherStatus: 0, expiryDate: undefined, voucherType: 0 };
}

export const VoucherItem: MessageFns<VoucherItem> = {
  encode(message: VoucherItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.voucherCode !== "") {
      writer.uint32(10).string(message.voucherCode);
    }
    if (message.amount !== 0) {
      writer.uint32(17).double(message.amount);
    }
    if (message.rewardCode !== 0) {
      writer.uint32(24).int32(message.rewardCode);
    }
    if (message.voucherStatus !== 0) {
      writer.uint32(32).int32(message.voucherStatus);
    }
    if (message.expiryDate !== undefined) {
      Timestamp.encode(toTimestamp(message.expiryDate), writer.uint32(42).fork()).join();
    }
    if (message.voucherType !== 0) {
      writer.uint32(48).int32(message.voucherType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): VoucherItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVoucherItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.voucherCode = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.rewardCode = reader.int32() as any;
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.voucherStatus = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.expiryDate = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.voucherType = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VoucherItem {
    return {
      voucherCode: isSet(object.voucherCode) ? globalThis.String(object.voucherCode) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      rewardCode: isSet(object.rewardCode) ? rewardCodeFromJSON(object.rewardCode) : 0,
      voucherStatus: isSet(object.voucherStatus) ? voucherStatusFromJSON(object.voucherStatus) : 0,
      expiryDate: isSet(object.expiryDate) ? fromJsonTimestamp(object.expiryDate) : undefined,
      voucherType: isSet(object.voucherType) ? voucherTypeFromJSON(object.voucherType) : 0,
    };
  },

  toJSON(message: VoucherItem): unknown {
    const obj: any = {};
    if (message.voucherCode !== "") {
      obj.voucherCode = message.voucherCode;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.rewardCode !== 0) {
      obj.rewardCode = rewardCodeToJSON(message.rewardCode);
    }
    if (message.voucherStatus !== 0) {
      obj.voucherStatus = voucherStatusToJSON(message.voucherStatus);
    }
    if (message.expiryDate !== undefined) {
      obj.expiryDate = message.expiryDate.toISOString();
    }
    if (message.voucherType !== 0) {
      obj.voucherType = voucherTypeToJSON(message.voucherType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<VoucherItem>, I>>(base?: I): VoucherItem {
    return VoucherItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VoucherItem>, I>>(object: I): VoucherItem {
    const message = createBaseVoucherItem();
    message.voucherCode = object.voucherCode ?? "";
    message.amount = object.amount ?? 0;
    message.rewardCode = object.rewardCode ?? 0;
    message.voucherStatus = object.voucherStatus ?? 0;
    message.expiryDate = object.expiryDate ?? undefined;
    message.voucherType = object.voucherType ?? 0;
    return message;
  },
};

function createBaseBulkVoucherRequest(): BulkVoucherRequest {
  return { voucherItemsList: [] };
}

export const BulkVoucherRequest: MessageFns<BulkVoucherRequest> = {
  encode(message: BulkVoucherRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.voucherItemsList) {
      VoucherItem.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkVoucherRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkVoucherRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.voucherItemsList.push(VoucherItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkVoucherRequest {
    return {
      voucherItemsList: globalThis.Array.isArray(object?.voucherItemsList)
        ? object.voucherItemsList.map((e: any) => VoucherItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BulkVoucherRequest): unknown {
    const obj: any = {};
    if (message.voucherItemsList?.length) {
      obj.voucherItemsList = message.voucherItemsList.map((e) => VoucherItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkVoucherRequest>, I>>(base?: I): BulkVoucherRequest {
    return BulkVoucherRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkVoucherRequest>, I>>(object: I): BulkVoucherRequest {
    const message = createBaseBulkVoucherRequest();
    message.voucherItemsList = object.voucherItemsList?.map((e) => VoucherItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseAddRewardTransactionRequest(): AddRewardTransactionRequest {
  return { rewardLink: "", phoneNumber: "", transactionTimestamp: undefined };
}

export const AddRewardTransactionRequest: MessageFns<AddRewardTransactionRequest> = {
  encode(message: AddRewardTransactionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rewardLink !== "") {
      writer.uint32(10).string(message.rewardLink);
    }
    if (message.phoneNumber !== "") {
      writer.uint32(18).string(message.phoneNumber);
    }
    if (message.transactionTimestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.transactionTimestamp), writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddRewardTransactionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddRewardTransactionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rewardLink = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.phoneNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.transactionTimestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddRewardTransactionRequest {
    return {
      rewardLink: isSet(object.rewardLink) ? globalThis.String(object.rewardLink) : "",
      phoneNumber: isSet(object.phoneNumber) ? globalThis.String(object.phoneNumber) : "",
      transactionTimestamp: isSet(object.transactionTimestamp)
        ? fromJsonTimestamp(object.transactionTimestamp)
        : undefined,
    };
  },

  toJSON(message: AddRewardTransactionRequest): unknown {
    const obj: any = {};
    if (message.rewardLink !== "") {
      obj.rewardLink = message.rewardLink;
    }
    if (message.phoneNumber !== "") {
      obj.phoneNumber = message.phoneNumber;
    }
    if (message.transactionTimestamp !== undefined) {
      obj.transactionTimestamp = message.transactionTimestamp.toISOString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddRewardTransactionRequest>, I>>(base?: I): AddRewardTransactionRequest {
    return AddRewardTransactionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddRewardTransactionRequest>, I>>(object: I): AddRewardTransactionRequest {
    const message = createBaseAddRewardTransactionRequest();
    message.rewardLink = object.rewardLink ?? "";
    message.phoneNumber = object.phoneNumber ?? "";
    message.transactionTimestamp = object.transactionTimestamp ?? undefined;
    return message;
  },
};

function createBaseBulkAddRewardTransactionsRequest(): BulkAddRewardTransactionsRequest {
  return { transactions: [] };
}

export const BulkAddRewardTransactionsRequest: MessageFns<BulkAddRewardTransactionsRequest> = {
  encode(message: BulkAddRewardTransactionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.transactions) {
      AddRewardTransactionRequest.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BulkAddRewardTransactionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBulkAddRewardTransactionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactions.push(AddRewardTransactionRequest.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BulkAddRewardTransactionsRequest {
    return {
      transactions: globalThis.Array.isArray(object?.transactions)
        ? object.transactions.map((e: any) => AddRewardTransactionRequest.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BulkAddRewardTransactionsRequest): unknown {
    const obj: any = {};
    if (message.transactions?.length) {
      obj.transactions = message.transactions.map((e) => AddRewardTransactionRequest.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BulkAddRewardTransactionsRequest>, I>>(
    base?: I,
  ): BulkAddRewardTransactionsRequest {
    return BulkAddRewardTransactionsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BulkAddRewardTransactionsRequest>, I>>(
    object: I,
  ): BulkAddRewardTransactionsRequest {
    const message = createBaseBulkAddRewardTransactionsRequest();
    message.transactions = object.transactions?.map((e) => AddRewardTransactionRequest.fromPartial(e)) || [];
    return message;
  },
};

function createBaseManualAddRewardRequest(): ManualAddRewardRequest {
  return {
    rewardCode: 0,
    userId: "",
    referrerUserId: undefined,
    amount: 0,
    bankId: "",
    consumeBooking: undefined,
    overridePaymentTimeCheck: undefined,
  };
}

export const ManualAddRewardRequest: MessageFns<ManualAddRewardRequest> = {
  encode(message: ManualAddRewardRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rewardCode !== 0) {
      writer.uint32(8).int32(message.rewardCode);
    }
    if (message.userId !== "") {
      writer.uint32(18).string(message.userId);
    }
    if (message.referrerUserId !== undefined) {
      writer.uint32(26).string(message.referrerUserId);
    }
    if (message.amount !== 0) {
      writer.uint32(33).double(message.amount);
    }
    if (message.bankId !== "") {
      writer.uint32(42).string(message.bankId);
    }
    if (message.consumeBooking !== undefined) {
      writer.uint32(48).bool(message.consumeBooking);
    }
    if (message.overridePaymentTimeCheck !== undefined) {
      writer.uint32(56).bool(message.overridePaymentTimeCheck);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ManualAddRewardRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseManualAddRewardRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.rewardCode = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.referrerUserId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.bankId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.consumeBooking = reader.bool();
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.overridePaymentTimeCheck = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ManualAddRewardRequest {
    return {
      rewardCode: isSet(object.rewardCode) ? rewardCodeFromJSON(object.rewardCode) : 0,
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      referrerUserId: isSet(object.referrerUserId) ? globalThis.String(object.referrerUserId) : undefined,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      bankId: isSet(object.bankId) ? globalThis.String(object.bankId) : "",
      consumeBooking: isSet(object.consumeBooking) ? globalThis.Boolean(object.consumeBooking) : undefined,
      overridePaymentTimeCheck: isSet(object.overridePaymentTimeCheck)
        ? globalThis.Boolean(object.overridePaymentTimeCheck)
        : undefined,
    };
  },

  toJSON(message: ManualAddRewardRequest): unknown {
    const obj: any = {};
    if (message.rewardCode !== 0) {
      obj.rewardCode = rewardCodeToJSON(message.rewardCode);
    }
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.referrerUserId !== undefined) {
      obj.referrerUserId = message.referrerUserId;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.bankId !== "") {
      obj.bankId = message.bankId;
    }
    if (message.consumeBooking !== undefined) {
      obj.consumeBooking = message.consumeBooking;
    }
    if (message.overridePaymentTimeCheck !== undefined) {
      obj.overridePaymentTimeCheck = message.overridePaymentTimeCheck;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ManualAddRewardRequest>, I>>(base?: I): ManualAddRewardRequest {
    return ManualAddRewardRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ManualAddRewardRequest>, I>>(object: I): ManualAddRewardRequest {
    const message = createBaseManualAddRewardRequest();
    message.rewardCode = object.rewardCode ?? 0;
    message.userId = object.userId ?? "";
    message.referrerUserId = object.referrerUserId ?? undefined;
    message.amount = object.amount ?? 0;
    message.bankId = object.bankId ?? "";
    message.consumeBooking = object.consumeBooking ?? undefined;
    message.overridePaymentTimeCheck = object.overridePaymentTimeCheck ?? undefined;
    return message;
  },
};

function createBaseCheckGoldenTicketRequest(): CheckGoldenTicketRequest {
  return { userIdList: [] };
}

export const CheckGoldenTicketRequest: MessageFns<CheckGoldenTicketRequest> = {
  encode(message: CheckGoldenTicketRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.userIdList) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CheckGoldenTicketRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCheckGoldenTicketRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.userIdList.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CheckGoldenTicketRequest {
    return {
      userIdList: globalThis.Array.isArray(object?.userIdList)
        ? object.userIdList.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: CheckGoldenTicketRequest): unknown {
    const obj: any = {};
    if (message.userIdList?.length) {
      obj.userIdList = message.userIdList;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CheckGoldenTicketRequest>, I>>(base?: I): CheckGoldenTicketRequest {
    return CheckGoldenTicketRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckGoldenTicketRequest>, I>>(object: I): CheckGoldenTicketRequest {
    const message = createBaseCheckGoldenTicketRequest();
    message.userIdList = object.userIdList?.map((e) => e) || [];
    return message;
  },
};

function createBaseRewardItem(): RewardItem {
  return { rewardLink: "", rewardSentTimestamp: undefined, rewardAmount: 0, rewardCode: 0, rewardDeliveryStatus: 0 };
}

export const RewardItem: MessageFns<RewardItem> = {
  encode(message: RewardItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rewardLink !== "") {
      writer.uint32(10).string(message.rewardLink);
    }
    if (message.rewardSentTimestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.rewardSentTimestamp), writer.uint32(18).fork()).join();
    }
    if (message.rewardAmount !== 0) {
      writer.uint32(25).double(message.rewardAmount);
    }
    if (message.rewardCode !== 0) {
      writer.uint32(32).int32(message.rewardCode);
    }
    if (message.rewardDeliveryStatus !== 0) {
      writer.uint32(40).int32(message.rewardDeliveryStatus);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RewardItem {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRewardItem();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rewardLink = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rewardSentTimestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.rewardAmount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.rewardCode = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.rewardDeliveryStatus = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RewardItem {
    return {
      rewardLink: isSet(object.rewardLink) ? globalThis.String(object.rewardLink) : "",
      rewardSentTimestamp: isSet(object.rewardSentTimestamp)
        ? fromJsonTimestamp(object.rewardSentTimestamp)
        : undefined,
      rewardAmount: isSet(object.rewardAmount) ? globalThis.Number(object.rewardAmount) : 0,
      rewardCode: isSet(object.rewardCode) ? rewardCodeFromJSON(object.rewardCode) : 0,
      rewardDeliveryStatus: isSet(object.rewardDeliveryStatus)
        ? rewardDeliveryStatusFromJSON(object.rewardDeliveryStatus)
        : 0,
    };
  },

  toJSON(message: RewardItem): unknown {
    const obj: any = {};
    if (message.rewardLink !== "") {
      obj.rewardLink = message.rewardLink;
    }
    if (message.rewardSentTimestamp !== undefined) {
      obj.rewardSentTimestamp = message.rewardSentTimestamp.toISOString();
    }
    if (message.rewardAmount !== 0) {
      obj.rewardAmount = message.rewardAmount;
    }
    if (message.rewardCode !== 0) {
      obj.rewardCode = rewardCodeToJSON(message.rewardCode);
    }
    if (message.rewardDeliveryStatus !== 0) {
      obj.rewardDeliveryStatus = rewardDeliveryStatusToJSON(message.rewardDeliveryStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RewardItem>, I>>(base?: I): RewardItem {
    return RewardItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardItem>, I>>(object: I): RewardItem {
    const message = createBaseRewardItem();
    message.rewardLink = object.rewardLink ?? "";
    message.rewardSentTimestamp = object.rewardSentTimestamp ?? undefined;
    message.rewardAmount = object.rewardAmount ?? 0;
    message.rewardCode = object.rewardCode ?? 0;
    message.rewardDeliveryStatus = object.rewardDeliveryStatus ?? 0;
    return message;
  },
};

function createBaseRewardsDataResponse(): RewardsDataResponse {
  return { totalEarnings: 0, rewards: [] };
}

export const RewardsDataResponse: MessageFns<RewardsDataResponse> = {
  encode(message: RewardsDataResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.totalEarnings !== 0) {
      writer.uint32(9).double(message.totalEarnings);
    }
    for (const v of message.rewards) {
      RewardItem.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RewardsDataResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRewardsDataResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 9) {
            break;
          }

          message.totalEarnings = reader.double();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.rewards.push(RewardItem.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RewardsDataResponse {
    return {
      totalEarnings: isSet(object.totalEarnings) ? globalThis.Number(object.totalEarnings) : 0,
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => RewardItem.fromJSON(e)) : [],
    };
  },

  toJSON(message: RewardsDataResponse): unknown {
    const obj: any = {};
    if (message.totalEarnings !== 0) {
      obj.totalEarnings = message.totalEarnings;
    }
    if (message.rewards?.length) {
      obj.rewards = message.rewards.map((e) => RewardItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RewardsDataResponse>, I>>(base?: I): RewardsDataResponse {
    return RewardsDataResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardsDataResponse>, I>>(object: I): RewardsDataResponse {
    const message = createBaseRewardsDataResponse();
    message.totalEarnings = object.totalEarnings ?? 0;
    message.rewards = object.rewards?.map((e) => RewardItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRewardTypesResponse(): RewardTypesResponse {
  return { rewardTypes: [] };
}

export const RewardTypesResponse: MessageFns<RewardTypesResponse> = {
  encode(message: RewardTypesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.rewardTypes) {
      RewardType.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RewardTypesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRewardTypesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.rewardTypes.push(RewardType.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RewardTypesResponse {
    return {
      rewardTypes: globalThis.Array.isArray(object?.rewardTypes)
        ? object.rewardTypes.map((e: any) => RewardType.fromJSON(e))
        : [],
    };
  },

  toJSON(message: RewardTypesResponse): unknown {
    const obj: any = {};
    if (message.rewardTypes?.length) {
      obj.rewardTypes = message.rewardTypes.map((e) => RewardType.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RewardTypesResponse>, I>>(base?: I): RewardTypesResponse {
    return RewardTypesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardTypesResponse>, I>>(object: I): RewardTypesResponse {
    const message = createBaseRewardTypesResponse();
    message.rewardTypes = object.rewardTypes?.map((e) => RewardType.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRewardTypesRequest(): RewardTypesRequest {
  return { rewardCode: undefined };
}

export const RewardTypesRequest: MessageFns<RewardTypesRequest> = {
  encode(message: RewardTypesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.rewardCode !== undefined) {
      writer.uint32(8).int32(message.rewardCode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RewardTypesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRewardTypesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.rewardCode = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RewardTypesRequest {
    return { rewardCode: isSet(object.rewardCode) ? rewardCodeFromJSON(object.rewardCode) : undefined };
  },

  toJSON(message: RewardTypesRequest): unknown {
    const obj: any = {};
    if (message.rewardCode !== undefined) {
      obj.rewardCode = rewardCodeToJSON(message.rewardCode);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RewardTypesRequest>, I>>(base?: I): RewardTypesRequest {
    return RewardTypesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardTypesRequest>, I>>(object: I): RewardTypesRequest {
    const message = createBaseRewardTypesRequest();
    message.rewardCode = object.rewardCode ?? undefined;
    return message;
  },
};

function createBaseRewardType(): RewardType {
  return {
    id: "",
    amount: 0,
    maxCount: 0,
    minTransactionAmount: 0,
    rewardCode: 0,
    validFrom: undefined,
    validTill: undefined,
    instantRewardTrAmount: undefined,
    deliveryDelayInDays: undefined,
    referralPercent: undefined,
    referralLevel: undefined,
    refererMinNetWorth: undefined,
    goldenTicketAmount: undefined,
    randomizedRewardPercentage: undefined,
    referralCount: undefined,
  };
}

export const RewardType: MessageFns<RewardType> = {
  encode(message: RewardType, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.amount !== 0) {
      writer.uint32(17).double(message.amount);
    }
    if (message.maxCount !== 0) {
      writer.uint32(24).uint32(message.maxCount);
    }
    if (message.minTransactionAmount !== 0) {
      writer.uint32(33).double(message.minTransactionAmount);
    }
    if (message.rewardCode !== 0) {
      writer.uint32(40).int32(message.rewardCode);
    }
    if (message.validFrom !== undefined) {
      writer.uint32(48).uint64(message.validFrom);
    }
    if (message.validTill !== undefined) {
      writer.uint32(56).uint64(message.validTill);
    }
    if (message.instantRewardTrAmount !== undefined) {
      writer.uint32(65).double(message.instantRewardTrAmount);
    }
    if (message.deliveryDelayInDays !== undefined) {
      writer.uint32(72).uint32(message.deliveryDelayInDays);
    }
    if (message.referralPercent !== undefined) {
      writer.uint32(85).float(message.referralPercent);
    }
    if (message.referralLevel !== undefined) {
      writer.uint32(88).uint32(message.referralLevel);
    }
    if (message.refererMinNetWorth !== undefined) {
      writer.uint32(97).double(message.refererMinNetWorth);
    }
    if (message.goldenTicketAmount !== undefined) {
      writer.uint32(105).double(message.goldenTicketAmount);
    }
    if (message.randomizedRewardPercentage !== undefined) {
      writer.uint32(112).uint32(message.randomizedRewardPercentage);
    }
    if (message.referralCount !== undefined) {
      writer.uint32(120).uint32(message.referralCount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RewardType {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRewardType();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.maxCount = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.minTransactionAmount = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.rewardCode = reader.int32() as any;
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.validFrom = longToNumber(reader.uint64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.validTill = longToNumber(reader.uint64());
          continue;
        }
        case 8: {
          if (tag !== 65) {
            break;
          }

          message.instantRewardTrAmount = reader.double();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.deliveryDelayInDays = reader.uint32();
          continue;
        }
        case 10: {
          if (tag !== 85) {
            break;
          }

          message.referralPercent = reader.float();
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.referralLevel = reader.uint32();
          continue;
        }
        case 12: {
          if (tag !== 97) {
            break;
          }

          message.refererMinNetWorth = reader.double();
          continue;
        }
        case 13: {
          if (tag !== 105) {
            break;
          }

          message.goldenTicketAmount = reader.double();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.randomizedRewardPercentage = reader.uint32();
          continue;
        }
        case 15: {
          if (tag !== 120) {
            break;
          }

          message.referralCount = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RewardType {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      maxCount: isSet(object.maxCount) ? globalThis.Number(object.maxCount) : 0,
      minTransactionAmount: isSet(object.minTransactionAmount) ? globalThis.Number(object.minTransactionAmount) : 0,
      rewardCode: isSet(object.rewardCode) ? rewardCodeFromJSON(object.rewardCode) : 0,
      validFrom: isSet(object.validFrom) ? globalThis.Number(object.validFrom) : undefined,
      validTill: isSet(object.validTill) ? globalThis.Number(object.validTill) : undefined,
      instantRewardTrAmount: isSet(object.instantRewardTrAmount)
        ? globalThis.Number(object.instantRewardTrAmount)
        : undefined,
      deliveryDelayInDays: isSet(object.deliveryDelayInDays)
        ? globalThis.Number(object.deliveryDelayInDays)
        : undefined,
      referralPercent: isSet(object.referralPercent) ? globalThis.Number(object.referralPercent) : undefined,
      referralLevel: isSet(object.referralLevel) ? globalThis.Number(object.referralLevel) : undefined,
      refererMinNetWorth: isSet(object.refererMinNetWorth) ? globalThis.Number(object.refererMinNetWorth) : undefined,
      goldenTicketAmount: isSet(object.goldenTicketAmount) ? globalThis.Number(object.goldenTicketAmount) : undefined,
      randomizedRewardPercentage: isSet(object.randomizedRewardPercentage)
        ? globalThis.Number(object.randomizedRewardPercentage)
        : undefined,
      referralCount: isSet(object.referralCount) ? globalThis.Number(object.referralCount) : undefined,
    };
  },

  toJSON(message: RewardType): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.maxCount !== 0) {
      obj.maxCount = Math.round(message.maxCount);
    }
    if (message.minTransactionAmount !== 0) {
      obj.minTransactionAmount = message.minTransactionAmount;
    }
    if (message.rewardCode !== 0) {
      obj.rewardCode = rewardCodeToJSON(message.rewardCode);
    }
    if (message.validFrom !== undefined) {
      obj.validFrom = Math.round(message.validFrom);
    }
    if (message.validTill !== undefined) {
      obj.validTill = Math.round(message.validTill);
    }
    if (message.instantRewardTrAmount !== undefined) {
      obj.instantRewardTrAmount = message.instantRewardTrAmount;
    }
    if (message.deliveryDelayInDays !== undefined) {
      obj.deliveryDelayInDays = Math.round(message.deliveryDelayInDays);
    }
    if (message.referralPercent !== undefined) {
      obj.referralPercent = message.referralPercent;
    }
    if (message.referralLevel !== undefined) {
      obj.referralLevel = Math.round(message.referralLevel);
    }
    if (message.refererMinNetWorth !== undefined) {
      obj.refererMinNetWorth = message.refererMinNetWorth;
    }
    if (message.goldenTicketAmount !== undefined) {
      obj.goldenTicketAmount = message.goldenTicketAmount;
    }
    if (message.randomizedRewardPercentage !== undefined) {
      obj.randomizedRewardPercentage = Math.round(message.randomizedRewardPercentage);
    }
    if (message.referralCount !== undefined) {
      obj.referralCount = Math.round(message.referralCount);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RewardType>, I>>(base?: I): RewardType {
    return RewardType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardType>, I>>(object: I): RewardType {
    const message = createBaseRewardType();
    message.id = object.id ?? "";
    message.amount = object.amount ?? 0;
    message.maxCount = object.maxCount ?? 0;
    message.minTransactionAmount = object.minTransactionAmount ?? 0;
    message.rewardCode = object.rewardCode ?? 0;
    message.validFrom = object.validFrom ?? undefined;
    message.validTill = object.validTill ?? undefined;
    message.instantRewardTrAmount = object.instantRewardTrAmount ?? undefined;
    message.deliveryDelayInDays = object.deliveryDelayInDays ?? undefined;
    message.referralPercent = object.referralPercent ?? undefined;
    message.referralLevel = object.referralLevel ?? undefined;
    message.refererMinNetWorth = object.refererMinNetWorth ?? undefined;
    message.goldenTicketAmount = object.goldenTicketAmount ?? undefined;
    message.randomizedRewardPercentage = object.randomizedRewardPercentage ?? undefined;
    message.referralCount = object.referralCount ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(date: Date): Timestamp {
  const seconds = Math.trunc(date.getTime() / 1_000);
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (t.seconds || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function fromJsonTimestamp(o: any): Date {
  if (o instanceof globalThis.Date) {
    return o;
  } else if (typeof o === "string") {
    return new globalThis.Date(o);
  } else {
    return fromTimestamp(Timestamp.fromJSON(o));
  }
}

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
