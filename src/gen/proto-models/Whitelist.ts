// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Whitelist.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export interface WhitelistResponse {
  isWhitelisted: boolean;
}

function createBaseWhitelistResponse(): WhitelistResponse {
  return { isWhitelisted: false };
}

export const WhitelistResponse: MessageFns<WhitelistResponse> = {
  encode(message: WhitelistResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.isWhitelisted !== false) {
      writer.uint32(8).bool(message.isWhitelisted);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WhitelistResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWhitelistResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.isWhitelisted = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WhitelistResponse {
    return { isWhitelisted: isSet(object.isWhitelisted) ? globalThis.Boolean(object.isWhitelisted) : false };
  },

  toJSON(message: WhitelistResponse): unknown {
    const obj: any = {};
    if (message.isWhitelisted !== false) {
      obj.isWhitelisted = message.isWhitelisted;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WhitelistResponse>, I>>(base?: I): WhitelistResponse {
    return WhitelistResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WhitelistResponse>, I>>(object: I): WhitelistResponse {
    const message = createBaseWhitelistResponse();
    message.isWhitelisted = object.isWhitelisted ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
