// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v5.29.3
// source: Nudges.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "com.stablemoney.api.identity";

export enum NudgeType {
  UNKNOWN_NUDGE_TYPE = 0,
  REFERRAL_NUDGE = 1,
  REFERRAL_TEST_NUDGE = 2,
  FIRST_FD_REWARD_NUDGE = 3,
  FUNDING_ANNOUNCEMENT_NUDGE = 4,
  PROFILE_COMPLETION_NUDGE = 5,
  BANK_NUDGE = 6,
  MATURED_FD_CARDS_NUDGE = 7,
  MATURING_SOON_FD_CARDS_NUDGE = 8,
  GENERIC_EXPRESSION_NUDGE = 10,
  UNRECOGNIZED = -1,
}

export function nudgeTypeFromJSON(object: any): NudgeType {
  switch (object) {
    case 0:
    case "UNKNOWN_NUDGE_TYPE":
      return NudgeType.UNKNOWN_NUDGE_TYPE;
    case 1:
    case "REFERRAL_NUDGE":
      return NudgeType.REFERRAL_NUDGE;
    case 2:
    case "REFERRAL_TEST_NUDGE":
      return NudgeType.REFERRAL_TEST_NUDGE;
    case 3:
    case "FIRST_FD_REWARD_NUDGE":
      return NudgeType.FIRST_FD_REWARD_NUDGE;
    case 4:
    case "FUNDING_ANNOUNCEMENT_NUDGE":
      return NudgeType.FUNDING_ANNOUNCEMENT_NUDGE;
    case 5:
    case "PROFILE_COMPLETION_NUDGE":
      return NudgeType.PROFILE_COMPLETION_NUDGE;
    case 6:
    case "BANK_NUDGE":
      return NudgeType.BANK_NUDGE;
    case 7:
    case "MATURED_FD_CARDS_NUDGE":
      return NudgeType.MATURED_FD_CARDS_NUDGE;
    case 8:
    case "MATURING_SOON_FD_CARDS_NUDGE":
      return NudgeType.MATURING_SOON_FD_CARDS_NUDGE;
    case 10:
    case "GENERIC_EXPRESSION_NUDGE":
      return NudgeType.GENERIC_EXPRESSION_NUDGE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return NudgeType.UNRECOGNIZED;
  }
}

export function nudgeTypeToJSON(object: NudgeType): string {
  switch (object) {
    case NudgeType.UNKNOWN_NUDGE_TYPE:
      return "UNKNOWN_NUDGE_TYPE";
    case NudgeType.REFERRAL_NUDGE:
      return "REFERRAL_NUDGE";
    case NudgeType.REFERRAL_TEST_NUDGE:
      return "REFERRAL_TEST_NUDGE";
    case NudgeType.FIRST_FD_REWARD_NUDGE:
      return "FIRST_FD_REWARD_NUDGE";
    case NudgeType.FUNDING_ANNOUNCEMENT_NUDGE:
      return "FUNDING_ANNOUNCEMENT_NUDGE";
    case NudgeType.PROFILE_COMPLETION_NUDGE:
      return "PROFILE_COMPLETION_NUDGE";
    case NudgeType.BANK_NUDGE:
      return "BANK_NUDGE";
    case NudgeType.MATURED_FD_CARDS_NUDGE:
      return "MATURED_FD_CARDS_NUDGE";
    case NudgeType.MATURING_SOON_FD_CARDS_NUDGE:
      return "MATURING_SOON_FD_CARDS_NUDGE";
    case NudgeType.GENERIC_EXPRESSION_NUDGE:
      return "GENERIC_EXPRESSION_NUDGE";
    case NudgeType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum NudgeActionType {
  UNKNOWN_NUDGE_ACTION = 0,
  NUDGE_SUCCESS = 1,
  NUDGE_DISMISS = 2,
  UNRECOGNIZED = -1,
}

export function nudgeActionTypeFromJSON(object: any): NudgeActionType {
  switch (object) {
    case 0:
    case "UNKNOWN_NUDGE_ACTION":
      return NudgeActionType.UNKNOWN_NUDGE_ACTION;
    case 1:
    case "NUDGE_SUCCESS":
      return NudgeActionType.NUDGE_SUCCESS;
    case 2:
    case "NUDGE_DISMISS":
      return NudgeActionType.NUDGE_DISMISS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return NudgeActionType.UNRECOGNIZED;
  }
}

export function nudgeActionTypeToJSON(object: NudgeActionType): string {
  switch (object) {
    case NudgeActionType.UNKNOWN_NUDGE_ACTION:
      return "UNKNOWN_NUDGE_ACTION";
    case NudgeActionType.NUDGE_SUCCESS:
      return "NUDGE_SUCCESS";
    case NudgeActionType.NUDGE_DISMISS:
      return "NUDGE_DISMISS";
    case NudgeActionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface NudgeResponse {
  nudge: string;
  nudgeType: string;
  nudgeId: string;
  priority: number;
  name: string;
}

export interface UpdateNudgeActionRequest {
  nudgeType: string;
  nudgeActionType: NudgeActionType;
  nudgeId: string;
}

function createBaseNudgeResponse(): NudgeResponse {
  return { nudge: "", nudgeType: "", nudgeId: "", priority: 0, name: "" };
}

export const NudgeResponse: MessageFns<NudgeResponse> = {
  encode(message: NudgeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nudge !== "") {
      writer.uint32(10).string(message.nudge);
    }
    if (message.nudgeType !== "") {
      writer.uint32(18).string(message.nudgeType);
    }
    if (message.nudgeId !== "") {
      writer.uint32(26).string(message.nudgeId);
    }
    if (message.priority !== 0) {
      writer.uint32(32).int32(message.priority);
    }
    if (message.name !== "") {
      writer.uint32(42).string(message.name);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NudgeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNudgeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.nudge = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.nudgeType = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.nudgeId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.priority = reader.int32();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.name = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): NudgeResponse {
    return {
      nudge: isSet(object.nudge) ? globalThis.String(object.nudge) : "",
      nudgeType: isSet(object.nudgeType) ? globalThis.String(object.nudgeType) : "",
      nudgeId: isSet(object.nudgeId) ? globalThis.String(object.nudgeId) : "",
      priority: isSet(object.priority) ? globalThis.Number(object.priority) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: NudgeResponse): unknown {
    const obj: any = {};
    if (message.nudge !== "") {
      obj.nudge = message.nudge;
    }
    if (message.nudgeType !== "") {
      obj.nudgeType = message.nudgeType;
    }
    if (message.nudgeId !== "") {
      obj.nudgeId = message.nudgeId;
    }
    if (message.priority !== 0) {
      obj.priority = Math.round(message.priority);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NudgeResponse>, I>>(base?: I): NudgeResponse {
    return NudgeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NudgeResponse>, I>>(object: I): NudgeResponse {
    const message = createBaseNudgeResponse();
    message.nudge = object.nudge ?? "";
    message.nudgeType = object.nudgeType ?? "";
    message.nudgeId = object.nudgeId ?? "";
    message.priority = object.priority ?? 0;
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseUpdateNudgeActionRequest(): UpdateNudgeActionRequest {
  return { nudgeType: "", nudgeActionType: 0, nudgeId: "" };
}

export const UpdateNudgeActionRequest: MessageFns<UpdateNudgeActionRequest> = {
  encode(message: UpdateNudgeActionRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.nudgeType !== "") {
      writer.uint32(10).string(message.nudgeType);
    }
    if (message.nudgeActionType !== 0) {
      writer.uint32(16).int32(message.nudgeActionType);
    }
    if (message.nudgeId !== "") {
      writer.uint32(26).string(message.nudgeId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UpdateNudgeActionRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateNudgeActionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.nudgeType = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.nudgeActionType = reader.int32() as any;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.nudgeId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateNudgeActionRequest {
    return {
      nudgeType: isSet(object.nudgeType) ? globalThis.String(object.nudgeType) : "",
      nudgeActionType: isSet(object.nudgeActionType) ? nudgeActionTypeFromJSON(object.nudgeActionType) : 0,
      nudgeId: isSet(object.nudgeId) ? globalThis.String(object.nudgeId) : "",
    };
  },

  toJSON(message: UpdateNudgeActionRequest): unknown {
    const obj: any = {};
    if (message.nudgeType !== "") {
      obj.nudgeType = message.nudgeType;
    }
    if (message.nudgeActionType !== 0) {
      obj.nudgeActionType = nudgeActionTypeToJSON(message.nudgeActionType);
    }
    if (message.nudgeId !== "") {
      obj.nudgeId = message.nudgeId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateNudgeActionRequest>, I>>(base?: I): UpdateNudgeActionRequest {
    return UpdateNudgeActionRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateNudgeActionRequest>, I>>(object: I): UpdateNudgeActionRequest {
    const message = createBaseUpdateNudgeActionRequest();
    message.nudgeType = object.nudgeType ?? "";
    message.nudgeActionType = object.nudgeActionType ?? 0;
    message.nudgeId = object.nudgeId ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
