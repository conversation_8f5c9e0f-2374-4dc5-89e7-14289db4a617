import errorIcon from "~/assets/icons/error-orange.svg";
import { Button } from "antd";
type ErrorPageProps =
  | {
      error: unknown;
      title?: string;
      description?: string;
    }
  | {
      error?: never;
      title: string;
      description?: string;
    };

export default function ErrorPage({ error }: ErrorPageProps) {
  const reload = () => {
    window.location.reload();
  };
  const buttons = (
    <div className="w-full space-y-3 text-center">
      <Button onClick={reload}>Retry</Button>
    </div>
  );
  return (
    <>
      <div className=" flex flex-col justify-center items-center gap-7 h-full">
        <img src={errorIcon} alt="error_icon" className="mx-auto mt-5" />
        <div className="space-y-3 px-5 text-center">
          <p className="text-heading3">Something went wrong</p>
          <p className="text-body text-gray-600">
            {error instanceof Error ? error.message : "Unknown error"}
          </p>
        </div>
        {buttons}
      </div>
    </>
  );
}
