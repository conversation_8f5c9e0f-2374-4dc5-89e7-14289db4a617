import { Input } from 'antd';

interface SearchBarProps {
  searchText: string;
  setSearchText: React.Dispatch<React.SetStateAction<string>>;
}

export function SearchBar({ searchText, setSearchText }: SearchBarProps) {
  return (
    <>
      <div className="w-80">
        <Input.Search
          placeholder="Search"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
        />
      </div>
    </>
  );
}
