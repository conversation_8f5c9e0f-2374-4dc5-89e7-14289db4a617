import { type ReactNode } from "react";
import type { UseQueryResult } from "@tanstack/react-query";
import { Loader } from "../loader";
import ErrorPage from "../error-page";
import { Empty } from "antd";

type QueryRendererProps<T> = {
  query: UseQueryResult<T, Error>;
  children?: (data: T) => ReactNode;
};

export default function QueryRenderer<T>({
  query,
  children,
}: QueryRendererProps<T>) {

  if (query.data) {
    return children?.(query.data);
  }

  if (query.isLoading) {
    return <Loader />;
  }

  if (query.error) {
    return <ErrorPage error={query.error} />;
  }

  return <Empty />;
}