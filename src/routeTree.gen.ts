/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as HomeRouteImport } from './routes/_home'
import { Route as IndexRouteImport } from './routes/index'
import { Route as HomeHomeRouteRouteImport } from './routes/_home/home/<USER>'
import { Route as AuthLoginIndexRouteImport } from './routes/auth/login/index'
import { Route as HomeHomeIndexRouteImport } from './routes/_home/home/<USER>'
import { Route as HomeHomeUsersIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeTagIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeStoriesIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeRolesIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomePagesIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeIssuingInstitutionsIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeFramesIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeFdCollectionsIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeBondCollectionsIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeBondCatalogIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeBannersIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeBankDowntimeIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeAccessResourcesIndexRouteImport } from './routes/_home/home/<USER>/index'
import { Route as HomeHomeTagTagIdIndexRouteImport } from './routes/_home/home/<USER>/$tag-id/index'
import { Route as HomeHomeStoriesCreateIndexRouteImport } from './routes/_home/home/<USER>/create/index'
import { Route as HomeHomeStoriesStoryIdIndexRouteImport } from './routes/_home/home/<USER>/$story-id/index'
import { Route as HomeHomeRolesCreateIndexRouteImport } from './routes/_home/home/<USER>/create/index'
import { Route as HomeHomeRolesRoleIdIndexRouteImport } from './routes/_home/home/<USER>/$role-id/index'
import { Route as HomeHomePagesCreateIndexRouteImport } from './routes/_home/home/<USER>/create/index'
import { Route as HomeHomePagesPageIdIndexRouteImport } from './routes/_home/home/<USER>/$page-id/index'
import { Route as HomeHomeIssuingInstitutionsIsinIdIndexRouteImport } from './routes/_home/home/<USER>/$isin-id/index'
import { Route as HomeHomeFramesCreateIndexRouteImport } from './routes/_home/home/<USER>/create/index'
import { Route as HomeHomeFramesFrameIdIndexRouteImport } from './routes/_home/home/<USER>/$frame-id/index'
import { Route as HomeHomeFdCollectionsCreateIndexRouteImport } from './routes/_home/home/<USER>/create/index'
import { Route as HomeHomeFdCollectionsFdCollectionIdIndexRouteImport } from './routes/_home/home/<USER>/$fd-collection-id/index'
import { Route as HomeHomeBannersRulesIndexRouteImport } from './routes/_home/home/<USER>/rules/index'
import { Route as HomeHomeBannersCreateIndexRouteImport } from './routes/_home/home/<USER>/create/index'
import { Route as HomeHomeBannersBannerIdIndexRouteImport } from './routes/_home/home/<USER>/$banner-id/index'
import { Route as HomeHomeAccessResourcesCreateIndexRouteImport } from './routes/_home/home/<USER>/create/index'
import { Route as HomeHomeAccessResourcesResourceIdIndexRouteImport } from './routes/_home/home/<USER>/$resource-id/index'
import { Route as HomeHomeBondCatalogActiveBondIdRouteRouteImport } from './routes/_home/home/<USER>/active/$bond-id/route'
import { Route as HomeHomePagesPageIdFeatureFlagMappingsIndexRouteImport } from './routes/_home/home/<USER>/$page-id/feature-flag-mappings/index'
import { Route as HomeHomeIssuingInstitutionsIsinIdCreateIndexRouteImport } from './routes/_home/home/<USER>/$isin-id/create/index'
import { Route as HomeHomeFramesFrameIdFeatureFlagMappingsIndexRouteImport } from './routes/_home/home/<USER>/$frame-id/feature-flag-mappings/index'
import { Route as HomeHomeFdCollectionsFdCollectionIdCreateIndexRouteImport } from './routes/_home/home/<USER>/$fd-collection-id/create/index'
import { Route as HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRouteImport } from './routes/_home/home/<USER>/$fd-collection-id/$collection-item-id/index'
import { Route as HomeHomeBondCollectionsEditBondCollectionIdIndexRouteImport } from './routes/_home/home/<USER>/edit/$bond-collection-id/index'
import { Route as HomeHomeBannersRulesCreateIndexRouteImport } from './routes/_home/home/<USER>/rules/create/index'
import { Route as HomeHomeBannersRulesRuleIdIndexRouteImport } from './routes/_home/home/<USER>/rules/$rule-id/index'
import { Route as HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteImport } from './routes/_home/home/<USER>/active/$bond-id/offerings/route'
import { Route as HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRouteImport } from './routes/_home/home/<USER>/$page-id/feature-flag-mappings/create/index'
import { Route as HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRouteImport } from './routes/_home/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id/index'
import { Route as HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRouteImport } from './routes/_home/home/<USER>/$frame-id/feature-flag-mappings/create/index'
import { Route as HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRouteImport } from './routes/_home/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id/index'
import { Route as HomeHomeBondCatalogActiveBondIdMediaItemsIndexRouteImport } from './routes/_home/home/<USER>/active/$bond-id/media-items/index'
import { Route as HomeHomeBondCatalogActiveBondIdBondIndexRouteImport } from './routes/_home/home/<USER>/active/$bond-id/_bond/index'
import { Route as HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRouteImport } from './routes/_home/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id/index'
import { Route as HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRouteImport } from './routes/_home/home/<USER>/active/$bond-id/media-items/create/index'
import { Route as HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRouteImport } from './routes/_home/home/<USER>/active/$bond-id/media-items/$media-item-id/index'

const HomeRoute = HomeRouteImport.update({
  id: '/_home',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const HomeHomeRouteRoute = HomeHomeRouteRouteImport.update({
  id: '/home',
  path: '/home',
  getParentRoute: () => HomeRoute,
} as any)
const AuthLoginIndexRoute = AuthLoginIndexRouteImport.update({
  id: '/auth/login/',
  path: '/auth/login/',
  getParentRoute: () => rootRouteImport,
} as any)
const HomeHomeIndexRoute = HomeHomeIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => HomeHomeRouteRoute,
} as any)
const HomeHomeUsersIndexRoute = HomeHomeUsersIndexRouteImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => HomeHomeRouteRoute,
} as any)
const HomeHomeTagIndexRoute = HomeHomeTagIndexRouteImport.update({
  id: '/tag/',
  path: '/tag/',
  getParentRoute: () => HomeHomeRouteRoute,
} as any)
const HomeHomeStoriesIndexRoute = HomeHomeStoriesIndexRouteImport.update({
  id: '/stories/',
  path: '/stories/',
  getParentRoute: () => HomeHomeRouteRoute,
} as any)
const HomeHomeRolesIndexRoute = HomeHomeRolesIndexRouteImport.update({
  id: '/roles/',
  path: '/roles/',
  getParentRoute: () => HomeHomeRouteRoute,
} as any)
const HomeHomePagesIndexRoute = HomeHomePagesIndexRouteImport.update({
  id: '/pages/',
  path: '/pages/',
  getParentRoute: () => HomeHomeRouteRoute,
} as any)
const HomeHomeIssuingInstitutionsIndexRoute =
  HomeHomeIssuingInstitutionsIndexRouteImport.update({
    id: '/issuing-institutions/',
    path: '/issuing-institutions/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeFramesIndexRoute = HomeHomeFramesIndexRouteImport.update({
  id: '/frames/',
  path: '/frames/',
  getParentRoute: () => HomeHomeRouteRoute,
} as any)
const HomeHomeFdCollectionsIndexRoute =
  HomeHomeFdCollectionsIndexRouteImport.update({
    id: '/fd-collections/',
    path: '/fd-collections/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBondCollectionsIndexRoute =
  HomeHomeBondCollectionsIndexRouteImport.update({
    id: '/bond-collections/',
    path: '/bond-collections/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBondCatalogIndexRoute =
  HomeHomeBondCatalogIndexRouteImport.update({
    id: '/bond-catalog/',
    path: '/bond-catalog/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBannersIndexRoute = HomeHomeBannersIndexRouteImport.update({
  id: '/banners/',
  path: '/banners/',
  getParentRoute: () => HomeHomeRouteRoute,
} as any)
const HomeHomeBankDowntimeIndexRoute =
  HomeHomeBankDowntimeIndexRouteImport.update({
    id: '/bank-downtime/',
    path: '/bank-downtime/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeAccessResourcesIndexRoute =
  HomeHomeAccessResourcesIndexRouteImport.update({
    id: '/access-resources/',
    path: '/access-resources/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeTagTagIdIndexRoute = HomeHomeTagTagIdIndexRouteImport.update({
  id: '/tag/$tag-id/',
  path: '/tag/$tag-id/',
  getParentRoute: () => HomeHomeRouteRoute,
} as any)
const HomeHomeStoriesCreateIndexRoute =
  HomeHomeStoriesCreateIndexRouteImport.update({
    id: '/stories/create/',
    path: '/stories/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeStoriesStoryIdIndexRoute =
  HomeHomeStoriesStoryIdIndexRouteImport.update({
    id: '/stories/$story-id/',
    path: '/stories/$story-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeRolesCreateIndexRoute =
  HomeHomeRolesCreateIndexRouteImport.update({
    id: '/roles/create/',
    path: '/roles/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeRolesRoleIdIndexRoute =
  HomeHomeRolesRoleIdIndexRouteImport.update({
    id: '/roles/$role-id/',
    path: '/roles/$role-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomePagesCreateIndexRoute =
  HomeHomePagesCreateIndexRouteImport.update({
    id: '/pages/create/',
    path: '/pages/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomePagesPageIdIndexRoute =
  HomeHomePagesPageIdIndexRouteImport.update({
    id: '/pages/$page-id/',
    path: '/pages/$page-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeIssuingInstitutionsIsinIdIndexRoute =
  HomeHomeIssuingInstitutionsIsinIdIndexRouteImport.update({
    id: '/issuing-institutions/$isin-id/',
    path: '/issuing-institutions/$isin-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeFramesCreateIndexRoute =
  HomeHomeFramesCreateIndexRouteImport.update({
    id: '/frames/create/',
    path: '/frames/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeFramesFrameIdIndexRoute =
  HomeHomeFramesFrameIdIndexRouteImport.update({
    id: '/frames/$frame-id/',
    path: '/frames/$frame-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeFdCollectionsCreateIndexRoute =
  HomeHomeFdCollectionsCreateIndexRouteImport.update({
    id: '/fd-collections/create/',
    path: '/fd-collections/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeFdCollectionsFdCollectionIdIndexRoute =
  HomeHomeFdCollectionsFdCollectionIdIndexRouteImport.update({
    id: '/fd-collections/$fd-collection-id/',
    path: '/fd-collections/$fd-collection-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBannersRulesIndexRoute =
  HomeHomeBannersRulesIndexRouteImport.update({
    id: '/banners/rules/',
    path: '/banners/rules/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBannersCreateIndexRoute =
  HomeHomeBannersCreateIndexRouteImport.update({
    id: '/banners/create/',
    path: '/banners/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBannersBannerIdIndexRoute =
  HomeHomeBannersBannerIdIndexRouteImport.update({
    id: '/banners/$banner-id/',
    path: '/banners/$banner-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeAccessResourcesCreateIndexRoute =
  HomeHomeAccessResourcesCreateIndexRouteImport.update({
    id: '/access-resources/create/',
    path: '/access-resources/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeAccessResourcesResourceIdIndexRoute =
  HomeHomeAccessResourcesResourceIdIndexRouteImport.update({
    id: '/access-resources/$resource-id/',
    path: '/access-resources/$resource-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBondCatalogActiveBondIdRouteRoute =
  HomeHomeBondCatalogActiveBondIdRouteRouteImport.update({
    id: '/bond-catalog/active/$bond-id',
    path: '/bond-catalog/active/$bond-id',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomePagesPageIdFeatureFlagMappingsIndexRoute =
  HomeHomePagesPageIdFeatureFlagMappingsIndexRouteImport.update({
    id: '/pages/$page-id/feature-flag-mappings/',
    path: '/pages/$page-id/feature-flag-mappings/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeIssuingInstitutionsIsinIdCreateIndexRoute =
  HomeHomeIssuingInstitutionsIsinIdCreateIndexRouteImport.update({
    id: '/issuing-institutions/$isin-id/create/',
    path: '/issuing-institutions/$isin-id/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeFramesFrameIdFeatureFlagMappingsIndexRoute =
  HomeHomeFramesFrameIdFeatureFlagMappingsIndexRouteImport.update({
    id: '/frames/$frame-id/feature-flag-mappings/',
    path: '/frames/$frame-id/feature-flag-mappings/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeFdCollectionsFdCollectionIdCreateIndexRoute =
  HomeHomeFdCollectionsFdCollectionIdCreateIndexRouteImport.update({
    id: '/fd-collections/$fd-collection-id/create/',
    path: '/fd-collections/$fd-collection-id/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRoute =
  HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRouteImport.update({
    id: '/fd-collections/$fd-collection-id/$collection-item-id/',
    path: '/fd-collections/$fd-collection-id/$collection-item-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBondCollectionsEditBondCollectionIdIndexRoute =
  HomeHomeBondCollectionsEditBondCollectionIdIndexRouteImport.update({
    id: '/bond-collections/edit/$bond-collection-id/',
    path: '/bond-collections/edit/$bond-collection-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBannersRulesCreateIndexRoute =
  HomeHomeBannersRulesCreateIndexRouteImport.update({
    id: '/banners/rules/create/',
    path: '/banners/rules/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBannersRulesRuleIdIndexRoute =
  HomeHomeBannersRulesRuleIdIndexRouteImport.update({
    id: '/banners/rules/$rule-id/',
    path: '/banners/rules/$rule-id/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeBondCatalogActiveBondIdOfferingsRouteRoute =
  HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteImport.update({
    id: '/offerings',
    path: '/offerings',
    getParentRoute: () => HomeHomeBondCatalogActiveBondIdRouteRoute,
  } as any)
const HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRoute =
  HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRouteImport.update({
    id: '/pages/$page-id/feature-flag-mappings/create/',
    path: '/pages/$page-id/feature-flag-mappings/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute =
  HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRouteImport.update(
    {
      id: '/pages/$page-id/feature-flag-mappings/$feature-flag-mapping-id/',
      path: '/pages/$page-id/feature-flag-mappings/$feature-flag-mapping-id/',
      getParentRoute: () => HomeHomeRouteRoute,
    } as any,
  )
const HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRoute =
  HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRouteImport.update({
    id: '/frames/$frame-id/feature-flag-mappings/create/',
    path: '/frames/$frame-id/feature-flag-mappings/create/',
    getParentRoute: () => HomeHomeRouteRoute,
  } as any)
const HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute =
  HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRouteImport.update(
    {
      id: '/frames/$frame-id/feature-flag-mappings/$feature-flag-mapping-id/',
      path: '/frames/$frame-id/feature-flag-mappings/$feature-flag-mapping-id/',
      getParentRoute: () => HomeHomeRouteRoute,
    } as any,
  )
const HomeHomeBondCatalogActiveBondIdMediaItemsIndexRoute =
  HomeHomeBondCatalogActiveBondIdMediaItemsIndexRouteImport.update({
    id: '/media-items/',
    path: '/media-items/',
    getParentRoute: () => HomeHomeBondCatalogActiveBondIdRouteRoute,
  } as any)
const HomeHomeBondCatalogActiveBondIdBondIndexRoute =
  HomeHomeBondCatalogActiveBondIdBondIndexRouteImport.update({
    id: '/_bond/',
    path: '/',
    getParentRoute: () => HomeHomeBondCatalogActiveBondIdRouteRoute,
  } as any)
const HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRoute =
  HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRouteImport.update(
    {
      id: '/$bond-offering-detail-id/',
      path: '/$bond-offering-detail-id/',
      getParentRoute: () => HomeHomeBondCatalogActiveBondIdOfferingsRouteRoute,
    } as any,
  )
const HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRoute =
  HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRouteImport.update({
    id: '/media-items/create/',
    path: '/media-items/create/',
    getParentRoute: () => HomeHomeBondCatalogActiveBondIdRouteRoute,
  } as any)
const HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRoute =
  HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRouteImport.update({
    id: '/media-items/$media-item-id/',
    path: '/media-items/$media-item-id/',
    getParentRoute: () => HomeHomeBondCatalogActiveBondIdRouteRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/home': typeof HomeHomeRouteRouteWithChildren
  '/home/': typeof HomeHomeIndexRoute
  '/auth/login': typeof AuthLoginIndexRoute
  '/home/<USER>': typeof HomeHomeAccessResourcesIndexRoute
  '/home/<USER>': typeof HomeHomeBankDowntimeIndexRoute
  '/home/<USER>': typeof HomeHomeBannersIndexRoute
  '/home/<USER>': typeof HomeHomeBondCatalogIndexRoute
  '/home/<USER>': typeof HomeHomeBondCollectionsIndexRoute
  '/home/<USER>': typeof HomeHomeFdCollectionsIndexRoute
  '/home/<USER>': typeof HomeHomeFramesIndexRoute
  '/home/<USER>': typeof HomeHomeIssuingInstitutionsIndexRoute
  '/home/<USER>': typeof HomeHomePagesIndexRoute
  '/home/<USER>': typeof HomeHomeRolesIndexRoute
  '/home/<USER>': typeof HomeHomeStoriesIndexRoute
  '/home/<USER>': typeof HomeHomeTagIndexRoute
  '/home/<USER>': typeof HomeHomeUsersIndexRoute
  '/home/<USER>/active/$bond-id': typeof HomeHomeBondCatalogActiveBondIdRouteRouteWithChildren
  '/home/<USER>/$resource-id': typeof HomeHomeAccessResourcesResourceIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeAccessResourcesCreateIndexRoute
  '/home/<USER>/$banner-id': typeof HomeHomeBannersBannerIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeBannersCreateIndexRoute
  '/home/<USER>/rules': typeof HomeHomeBannersRulesIndexRoute
  '/home/<USER>/$fd-collection-id': typeof HomeHomeFdCollectionsFdCollectionIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeFdCollectionsCreateIndexRoute
  '/home/<USER>/$frame-id': typeof HomeHomeFramesFrameIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeFramesCreateIndexRoute
  '/home/<USER>/$isin-id': typeof HomeHomeIssuingInstitutionsIsinIdIndexRoute
  '/home/<USER>/$page-id': typeof HomeHomePagesPageIdIndexRoute
  '/home/<USER>/create': typeof HomeHomePagesCreateIndexRoute
  '/home/<USER>/$role-id': typeof HomeHomeRolesRoleIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeRolesCreateIndexRoute
  '/home/<USER>/$story-id': typeof HomeHomeStoriesStoryIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeStoriesCreateIndexRoute
  '/home/<USER>/$tag-id': typeof HomeHomeTagTagIdIndexRoute
  '/home/<USER>/active/$bond-id/offerings': typeof HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteWithChildren
  '/home/<USER>/rules/$rule-id': typeof HomeHomeBannersRulesRuleIdIndexRoute
  '/home/<USER>/rules/create': typeof HomeHomeBannersRulesCreateIndexRoute
  '/home/<USER>/edit/$bond-collection-id': typeof HomeHomeBondCollectionsEditBondCollectionIdIndexRoute
  '/home/<USER>/$fd-collection-id/$collection-item-id': typeof HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRoute
  '/home/<USER>/$fd-collection-id/create': typeof HomeHomeFdCollectionsFdCollectionIdCreateIndexRoute
  '/home/<USER>/$frame-id/feature-flag-mappings': typeof HomeHomeFramesFrameIdFeatureFlagMappingsIndexRoute
  '/home/<USER>/$isin-id/create': typeof HomeHomeIssuingInstitutionsIsinIdCreateIndexRoute
  '/home/<USER>/$page-id/feature-flag-mappings': typeof HomeHomePagesPageIdFeatureFlagMappingsIndexRoute
  '/home/<USER>/active/$bond-id/': typeof HomeHomeBondCatalogActiveBondIdBondIndexRoute
  '/home/<USER>/active/$bond-id/media-items': typeof HomeHomeBondCatalogActiveBondIdMediaItemsIndexRoute
  '/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id': typeof HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute
  '/home/<USER>/$frame-id/feature-flag-mappings/create': typeof HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRoute
  '/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id': typeof HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute
  '/home/<USER>/$page-id/feature-flag-mappings/create': typeof HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRoute
  '/home/<USER>/active/$bond-id/media-items/$media-item-id': typeof HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRoute
  '/home/<USER>/active/$bond-id/media-items/create': typeof HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRoute
  '/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id': typeof HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/home': typeof HomeHomeIndexRoute
  '/auth/login': typeof AuthLoginIndexRoute
  '/home/<USER>': typeof HomeHomeAccessResourcesIndexRoute
  '/home/<USER>': typeof HomeHomeBankDowntimeIndexRoute
  '/home/<USER>': typeof HomeHomeBannersIndexRoute
  '/home/<USER>': typeof HomeHomeBondCatalogIndexRoute
  '/home/<USER>': typeof HomeHomeBondCollectionsIndexRoute
  '/home/<USER>': typeof HomeHomeFdCollectionsIndexRoute
  '/home/<USER>': typeof HomeHomeFramesIndexRoute
  '/home/<USER>': typeof HomeHomeIssuingInstitutionsIndexRoute
  '/home/<USER>': typeof HomeHomePagesIndexRoute
  '/home/<USER>': typeof HomeHomeRolesIndexRoute
  '/home/<USER>': typeof HomeHomeStoriesIndexRoute
  '/home/<USER>': typeof HomeHomeTagIndexRoute
  '/home/<USER>': typeof HomeHomeUsersIndexRoute
  '/home/<USER>/$resource-id': typeof HomeHomeAccessResourcesResourceIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeAccessResourcesCreateIndexRoute
  '/home/<USER>/$banner-id': typeof HomeHomeBannersBannerIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeBannersCreateIndexRoute
  '/home/<USER>/rules': typeof HomeHomeBannersRulesIndexRoute
  '/home/<USER>/$fd-collection-id': typeof HomeHomeFdCollectionsFdCollectionIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeFdCollectionsCreateIndexRoute
  '/home/<USER>/$frame-id': typeof HomeHomeFramesFrameIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeFramesCreateIndexRoute
  '/home/<USER>/$isin-id': typeof HomeHomeIssuingInstitutionsIsinIdIndexRoute
  '/home/<USER>/$page-id': typeof HomeHomePagesPageIdIndexRoute
  '/home/<USER>/create': typeof HomeHomePagesCreateIndexRoute
  '/home/<USER>/$role-id': typeof HomeHomeRolesRoleIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeRolesCreateIndexRoute
  '/home/<USER>/$story-id': typeof HomeHomeStoriesStoryIdIndexRoute
  '/home/<USER>/create': typeof HomeHomeStoriesCreateIndexRoute
  '/home/<USER>/$tag-id': typeof HomeHomeTagTagIdIndexRoute
  '/home/<USER>/active/$bond-id/offerings': typeof HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteWithChildren
  '/home/<USER>/rules/$rule-id': typeof HomeHomeBannersRulesRuleIdIndexRoute
  '/home/<USER>/rules/create': typeof HomeHomeBannersRulesCreateIndexRoute
  '/home/<USER>/edit/$bond-collection-id': typeof HomeHomeBondCollectionsEditBondCollectionIdIndexRoute
  '/home/<USER>/$fd-collection-id/$collection-item-id': typeof HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRoute
  '/home/<USER>/$fd-collection-id/create': typeof HomeHomeFdCollectionsFdCollectionIdCreateIndexRoute
  '/home/<USER>/$frame-id/feature-flag-mappings': typeof HomeHomeFramesFrameIdFeatureFlagMappingsIndexRoute
  '/home/<USER>/$isin-id/create': typeof HomeHomeIssuingInstitutionsIsinIdCreateIndexRoute
  '/home/<USER>/$page-id/feature-flag-mappings': typeof HomeHomePagesPageIdFeatureFlagMappingsIndexRoute
  '/home/<USER>/active/$bond-id': typeof HomeHomeBondCatalogActiveBondIdBondIndexRoute
  '/home/<USER>/active/$bond-id/media-items': typeof HomeHomeBondCatalogActiveBondIdMediaItemsIndexRoute
  '/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id': typeof HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute
  '/home/<USER>/$frame-id/feature-flag-mappings/create': typeof HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRoute
  '/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id': typeof HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute
  '/home/<USER>/$page-id/feature-flag-mappings/create': typeof HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRoute
  '/home/<USER>/active/$bond-id/media-items/$media-item-id': typeof HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRoute
  '/home/<USER>/active/$bond-id/media-items/create': typeof HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRoute
  '/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id': typeof HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_home': typeof HomeRouteWithChildren
  '/_home/home': typeof HomeHomeRouteRouteWithChildren
  '/_home/home/': typeof HomeHomeIndexRoute
  '/auth/login/': typeof AuthLoginIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeAccessResourcesIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeBankDowntimeIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeBannersIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeBondCatalogIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeBondCollectionsIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeFdCollectionsIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeFramesIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeIssuingInstitutionsIndexRoute
  '/_home/home/<USER>/': typeof HomeHomePagesIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeRolesIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeStoriesIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeTagIndexRoute
  '/_home/home/<USER>/': typeof HomeHomeUsersIndexRoute
  '/_home/home/<USER>/active/$bond-id': typeof HomeHomeBondCatalogActiveBondIdRouteRouteWithChildren
  '/_home/home/<USER>/$resource-id/': typeof HomeHomeAccessResourcesResourceIdIndexRoute
  '/_home/home/<USER>/create/': typeof HomeHomeAccessResourcesCreateIndexRoute
  '/_home/home/<USER>/$banner-id/': typeof HomeHomeBannersBannerIdIndexRoute
  '/_home/home/<USER>/create/': typeof HomeHomeBannersCreateIndexRoute
  '/_home/home/<USER>/rules/': typeof HomeHomeBannersRulesIndexRoute
  '/_home/home/<USER>/$fd-collection-id/': typeof HomeHomeFdCollectionsFdCollectionIdIndexRoute
  '/_home/home/<USER>/create/': typeof HomeHomeFdCollectionsCreateIndexRoute
  '/_home/home/<USER>/$frame-id/': typeof HomeHomeFramesFrameIdIndexRoute
  '/_home/home/<USER>/create/': typeof HomeHomeFramesCreateIndexRoute
  '/_home/home/<USER>/$isin-id/': typeof HomeHomeIssuingInstitutionsIsinIdIndexRoute
  '/_home/home/<USER>/$page-id/': typeof HomeHomePagesPageIdIndexRoute
  '/_home/home/<USER>/create/': typeof HomeHomePagesCreateIndexRoute
  '/_home/home/<USER>/$role-id/': typeof HomeHomeRolesRoleIdIndexRoute
  '/_home/home/<USER>/create/': typeof HomeHomeRolesCreateIndexRoute
  '/_home/home/<USER>/$story-id/': typeof HomeHomeStoriesStoryIdIndexRoute
  '/_home/home/<USER>/create/': typeof HomeHomeStoriesCreateIndexRoute
  '/_home/home/<USER>/$tag-id/': typeof HomeHomeTagTagIdIndexRoute
  '/_home/home/<USER>/active/$bond-id/offerings': typeof HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteWithChildren
  '/_home/home/<USER>/rules/$rule-id/': typeof HomeHomeBannersRulesRuleIdIndexRoute
  '/_home/home/<USER>/rules/create/': typeof HomeHomeBannersRulesCreateIndexRoute
  '/_home/home/<USER>/edit/$bond-collection-id/': typeof HomeHomeBondCollectionsEditBondCollectionIdIndexRoute
  '/_home/home/<USER>/$fd-collection-id/$collection-item-id/': typeof HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRoute
  '/_home/home/<USER>/$fd-collection-id/create/': typeof HomeHomeFdCollectionsFdCollectionIdCreateIndexRoute
  '/_home/home/<USER>/$frame-id/feature-flag-mappings/': typeof HomeHomeFramesFrameIdFeatureFlagMappingsIndexRoute
  '/_home/home/<USER>/$isin-id/create/': typeof HomeHomeIssuingInstitutionsIsinIdCreateIndexRoute
  '/_home/home/<USER>/$page-id/feature-flag-mappings/': typeof HomeHomePagesPageIdFeatureFlagMappingsIndexRoute
  '/_home/home/<USER>/active/$bond-id/_bond/': typeof HomeHomeBondCatalogActiveBondIdBondIndexRoute
  '/_home/home/<USER>/active/$bond-id/media-items/': typeof HomeHomeBondCatalogActiveBondIdMediaItemsIndexRoute
  '/_home/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id/': typeof HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute
  '/_home/home/<USER>/$frame-id/feature-flag-mappings/create/': typeof HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRoute
  '/_home/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id/': typeof HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute
  '/_home/home/<USER>/$page-id/feature-flag-mappings/create/': typeof HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRoute
  '/_home/home/<USER>/active/$bond-id/media-items/$media-item-id/': typeof HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRoute
  '/_home/home/<USER>/active/$bond-id/media-items/create/': typeof HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRoute
  '/_home/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id/': typeof HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/home'
    | '/home/'
    | '/auth/login'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>/active/$bond-id'
    | '/home/<USER>/$resource-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$banner-id'
    | '/home/<USER>/create'
    | '/home/<USER>/rules'
    | '/home/<USER>/$fd-collection-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$frame-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$isin-id'
    | '/home/<USER>/$page-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$role-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$story-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$tag-id'
    | '/home/<USER>/active/$bond-id/offerings'
    | '/home/<USER>/rules/$rule-id'
    | '/home/<USER>/rules/create'
    | '/home/<USER>/edit/$bond-collection-id'
    | '/home/<USER>/$fd-collection-id/$collection-item-id'
    | '/home/<USER>/$fd-collection-id/create'
    | '/home/<USER>/$frame-id/feature-flag-mappings'
    | '/home/<USER>/$isin-id/create'
    | '/home/<USER>/$page-id/feature-flag-mappings'
    | '/home/<USER>/active/$bond-id/'
    | '/home/<USER>/active/$bond-id/media-items'
    | '/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id'
    | '/home/<USER>/$frame-id/feature-flag-mappings/create'
    | '/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id'
    | '/home/<USER>/$page-id/feature-flag-mappings/create'
    | '/home/<USER>/active/$bond-id/media-items/$media-item-id'
    | '/home/<USER>/active/$bond-id/media-items/create'
    | '/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/home'
    | '/auth/login'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>/$resource-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$banner-id'
    | '/home/<USER>/create'
    | '/home/<USER>/rules'
    | '/home/<USER>/$fd-collection-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$frame-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$isin-id'
    | '/home/<USER>/$page-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$role-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$story-id'
    | '/home/<USER>/create'
    | '/home/<USER>/$tag-id'
    | '/home/<USER>/active/$bond-id/offerings'
    | '/home/<USER>/rules/$rule-id'
    | '/home/<USER>/rules/create'
    | '/home/<USER>/edit/$bond-collection-id'
    | '/home/<USER>/$fd-collection-id/$collection-item-id'
    | '/home/<USER>/$fd-collection-id/create'
    | '/home/<USER>/$frame-id/feature-flag-mappings'
    | '/home/<USER>/$isin-id/create'
    | '/home/<USER>/$page-id/feature-flag-mappings'
    | '/home/<USER>/active/$bond-id'
    | '/home/<USER>/active/$bond-id/media-items'
    | '/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id'
    | '/home/<USER>/$frame-id/feature-flag-mappings/create'
    | '/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id'
    | '/home/<USER>/$page-id/feature-flag-mappings/create'
    | '/home/<USER>/active/$bond-id/media-items/$media-item-id'
    | '/home/<USER>/active/$bond-id/media-items/create'
    | '/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id'
  id:
    | '__root__'
    | '/'
    | '/_home'
    | '/_home/home'
    | '/_home/home/'
    | '/auth/login/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/'
    | '/_home/home/<USER>/active/$bond-id'
    | '/_home/home/<USER>/$resource-id/'
    | '/_home/home/<USER>/create/'
    | '/_home/home/<USER>/$banner-id/'
    | '/_home/home/<USER>/create/'
    | '/_home/home/<USER>/rules/'
    | '/_home/home/<USER>/$fd-collection-id/'
    | '/_home/home/<USER>/create/'
    | '/_home/home/<USER>/$frame-id/'
    | '/_home/home/<USER>/create/'
    | '/_home/home/<USER>/$isin-id/'
    | '/_home/home/<USER>/$page-id/'
    | '/_home/home/<USER>/create/'
    | '/_home/home/<USER>/$role-id/'
    | '/_home/home/<USER>/create/'
    | '/_home/home/<USER>/$story-id/'
    | '/_home/home/<USER>/create/'
    | '/_home/home/<USER>/$tag-id/'
    | '/_home/home/<USER>/active/$bond-id/offerings'
    | '/_home/home/<USER>/rules/$rule-id/'
    | '/_home/home/<USER>/rules/create/'
    | '/_home/home/<USER>/edit/$bond-collection-id/'
    | '/_home/home/<USER>/$fd-collection-id/$collection-item-id/'
    | '/_home/home/<USER>/$fd-collection-id/create/'
    | '/_home/home/<USER>/$frame-id/feature-flag-mappings/'
    | '/_home/home/<USER>/$isin-id/create/'
    | '/_home/home/<USER>/$page-id/feature-flag-mappings/'
    | '/_home/home/<USER>/active/$bond-id/_bond/'
    | '/_home/home/<USER>/active/$bond-id/media-items/'
    | '/_home/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id/'
    | '/_home/home/<USER>/$frame-id/feature-flag-mappings/create/'
    | '/_home/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id/'
    | '/_home/home/<USER>/$page-id/feature-flag-mappings/create/'
    | '/_home/home/<USER>/active/$bond-id/media-items/$media-item-id/'
    | '/_home/home/<USER>/active/$bond-id/media-items/create/'
    | '/_home/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  HomeRoute: typeof HomeRouteWithChildren
  AuthLoginIndexRoute: typeof AuthLoginIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_home': {
      id: '/_home'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof HomeRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_home/home': {
      id: '/_home/home'
      path: '/home'
      fullPath: '/home'
      preLoaderRoute: typeof HomeHomeRouteRouteImport
      parentRoute: typeof HomeRoute
    }
    '/auth/login/': {
      id: '/auth/login/'
      path: '/auth/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_home/home/': {
      id: '/_home/home/'
      path: '/'
      fullPath: '/home/'
      preLoaderRoute: typeof HomeHomeIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/users'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeUsersIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/tag'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeTagIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/stories'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeStoriesIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/roles'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeRolesIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/pages'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomePagesIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/issuing-institutions'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeIssuingInstitutionsIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/frames'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeFramesIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/fd-collections'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeFdCollectionsIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/bond-collections'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeBondCollectionsIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/bond-catalog'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeBondCatalogIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/banners'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeBannersIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/bank-downtime'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeBankDowntimeIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/': {
      id: '/_home/home/<USER>/'
      path: '/access-resources'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeHomeAccessResourcesIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$tag-id/': {
      id: '/_home/home/<USER>/$tag-id/'
      path: '/tag/$tag-id'
      fullPath: '/home/<USER>/$tag-id'
      preLoaderRoute: typeof HomeHomeTagTagIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/create/': {
      id: '/_home/home/<USER>/create/'
      path: '/stories/create'
      fullPath: '/home/<USER>/create'
      preLoaderRoute: typeof HomeHomeStoriesCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$story-id/': {
      id: '/_home/home/<USER>/$story-id/'
      path: '/stories/$story-id'
      fullPath: '/home/<USER>/$story-id'
      preLoaderRoute: typeof HomeHomeStoriesStoryIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/create/': {
      id: '/_home/home/<USER>/create/'
      path: '/roles/create'
      fullPath: '/home/<USER>/create'
      preLoaderRoute: typeof HomeHomeRolesCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$role-id/': {
      id: '/_home/home/<USER>/$role-id/'
      path: '/roles/$role-id'
      fullPath: '/home/<USER>/$role-id'
      preLoaderRoute: typeof HomeHomeRolesRoleIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/create/': {
      id: '/_home/home/<USER>/create/'
      path: '/pages/create'
      fullPath: '/home/<USER>/create'
      preLoaderRoute: typeof HomeHomePagesCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$page-id/': {
      id: '/_home/home/<USER>/$page-id/'
      path: '/pages/$page-id'
      fullPath: '/home/<USER>/$page-id'
      preLoaderRoute: typeof HomeHomePagesPageIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$isin-id/': {
      id: '/_home/home/<USER>/$isin-id/'
      path: '/issuing-institutions/$isin-id'
      fullPath: '/home/<USER>/$isin-id'
      preLoaderRoute: typeof HomeHomeIssuingInstitutionsIsinIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/create/': {
      id: '/_home/home/<USER>/create/'
      path: '/frames/create'
      fullPath: '/home/<USER>/create'
      preLoaderRoute: typeof HomeHomeFramesCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$frame-id/': {
      id: '/_home/home/<USER>/$frame-id/'
      path: '/frames/$frame-id'
      fullPath: '/home/<USER>/$frame-id'
      preLoaderRoute: typeof HomeHomeFramesFrameIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/create/': {
      id: '/_home/home/<USER>/create/'
      path: '/fd-collections/create'
      fullPath: '/home/<USER>/create'
      preLoaderRoute: typeof HomeHomeFdCollectionsCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$fd-collection-id/': {
      id: '/_home/home/<USER>/$fd-collection-id/'
      path: '/fd-collections/$fd-collection-id'
      fullPath: '/home/<USER>/$fd-collection-id'
      preLoaderRoute: typeof HomeHomeFdCollectionsFdCollectionIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/rules/': {
      id: '/_home/home/<USER>/rules/'
      path: '/banners/rules'
      fullPath: '/home/<USER>/rules'
      preLoaderRoute: typeof HomeHomeBannersRulesIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/create/': {
      id: '/_home/home/<USER>/create/'
      path: '/banners/create'
      fullPath: '/home/<USER>/create'
      preLoaderRoute: typeof HomeHomeBannersCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$banner-id/': {
      id: '/_home/home/<USER>/$banner-id/'
      path: '/banners/$banner-id'
      fullPath: '/home/<USER>/$banner-id'
      preLoaderRoute: typeof HomeHomeBannersBannerIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/create/': {
      id: '/_home/home/<USER>/create/'
      path: '/access-resources/create'
      fullPath: '/home/<USER>/create'
      preLoaderRoute: typeof HomeHomeAccessResourcesCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$resource-id/': {
      id: '/_home/home/<USER>/$resource-id/'
      path: '/access-resources/$resource-id'
      fullPath: '/home/<USER>/$resource-id'
      preLoaderRoute: typeof HomeHomeAccessResourcesResourceIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/active/$bond-id': {
      id: '/_home/home/<USER>/active/$bond-id'
      path: '/bond-catalog/active/$bond-id'
      fullPath: '/home/<USER>/active/$bond-id'
      preLoaderRoute: typeof HomeHomeBondCatalogActiveBondIdRouteRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$page-id/feature-flag-mappings/': {
      id: '/_home/home/<USER>/$page-id/feature-flag-mappings/'
      path: '/pages/$page-id/feature-flag-mappings'
      fullPath: '/home/<USER>/$page-id/feature-flag-mappings'
      preLoaderRoute: typeof HomeHomePagesPageIdFeatureFlagMappingsIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$isin-id/create/': {
      id: '/_home/home/<USER>/$isin-id/create/'
      path: '/issuing-institutions/$isin-id/create'
      fullPath: '/home/<USER>/$isin-id/create'
      preLoaderRoute: typeof HomeHomeIssuingInstitutionsIsinIdCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$frame-id/feature-flag-mappings/': {
      id: '/_home/home/<USER>/$frame-id/feature-flag-mappings/'
      path: '/frames/$frame-id/feature-flag-mappings'
      fullPath: '/home/<USER>/$frame-id/feature-flag-mappings'
      preLoaderRoute: typeof HomeHomeFramesFrameIdFeatureFlagMappingsIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$fd-collection-id/create/': {
      id: '/_home/home/<USER>/$fd-collection-id/create/'
      path: '/fd-collections/$fd-collection-id/create'
      fullPath: '/home/<USER>/$fd-collection-id/create'
      preLoaderRoute: typeof HomeHomeFdCollectionsFdCollectionIdCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$fd-collection-id/$collection-item-id/': {
      id: '/_home/home/<USER>/$fd-collection-id/$collection-item-id/'
      path: '/fd-collections/$fd-collection-id/$collection-item-id'
      fullPath: '/home/<USER>/$fd-collection-id/$collection-item-id'
      preLoaderRoute: typeof HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/edit/$bond-collection-id/': {
      id: '/_home/home/<USER>/edit/$bond-collection-id/'
      path: '/bond-collections/edit/$bond-collection-id'
      fullPath: '/home/<USER>/edit/$bond-collection-id'
      preLoaderRoute: typeof HomeHomeBondCollectionsEditBondCollectionIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/rules/create/': {
      id: '/_home/home/<USER>/rules/create/'
      path: '/banners/rules/create'
      fullPath: '/home/<USER>/rules/create'
      preLoaderRoute: typeof HomeHomeBannersRulesCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/rules/$rule-id/': {
      id: '/_home/home/<USER>/rules/$rule-id/'
      path: '/banners/rules/$rule-id'
      fullPath: '/home/<USER>/rules/$rule-id'
      preLoaderRoute: typeof HomeHomeBannersRulesRuleIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/active/$bond-id/offerings': {
      id: '/_home/home/<USER>/active/$bond-id/offerings'
      path: '/offerings'
      fullPath: '/home/<USER>/active/$bond-id/offerings'
      preLoaderRoute: typeof HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteImport
      parentRoute: typeof HomeHomeBondCatalogActiveBondIdRouteRoute
    }
    '/_home/home/<USER>/$page-id/feature-flag-mappings/create/': {
      id: '/_home/home/<USER>/$page-id/feature-flag-mappings/create/'
      path: '/pages/$page-id/feature-flag-mappings/create'
      fullPath: '/home/<USER>/$page-id/feature-flag-mappings/create'
      preLoaderRoute: typeof HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id/': {
      id: '/_home/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id/'
      path: '/pages/$page-id/feature-flag-mappings/$feature-flag-mapping-id'
      fullPath: '/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id'
      preLoaderRoute: typeof HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$frame-id/feature-flag-mappings/create/': {
      id: '/_home/home/<USER>/$frame-id/feature-flag-mappings/create/'
      path: '/frames/$frame-id/feature-flag-mappings/create'
      fullPath: '/home/<USER>/$frame-id/feature-flag-mappings/create'
      preLoaderRoute: typeof HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id/': {
      id: '/_home/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id/'
      path: '/frames/$frame-id/feature-flag-mappings/$feature-flag-mapping-id'
      fullPath: '/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id'
      preLoaderRoute: typeof HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRouteImport
      parentRoute: typeof HomeHomeRouteRoute
    }
    '/_home/home/<USER>/active/$bond-id/media-items/': {
      id: '/_home/home/<USER>/active/$bond-id/media-items/'
      path: '/media-items'
      fullPath: '/home/<USER>/active/$bond-id/media-items'
      preLoaderRoute: typeof HomeHomeBondCatalogActiveBondIdMediaItemsIndexRouteImport
      parentRoute: typeof HomeHomeBondCatalogActiveBondIdRouteRoute
    }
    '/_home/home/<USER>/active/$bond-id/_bond/': {
      id: '/_home/home/<USER>/active/$bond-id/_bond/'
      path: '/'
      fullPath: '/home/<USER>/active/$bond-id/'
      preLoaderRoute: typeof HomeHomeBondCatalogActiveBondIdBondIndexRouteImport
      parentRoute: typeof HomeHomeBondCatalogActiveBondIdRouteRoute
    }
    '/_home/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id/': {
      id: '/_home/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id/'
      path: '/$bond-offering-detail-id'
      fullPath: '/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id'
      preLoaderRoute: typeof HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRouteImport
      parentRoute: typeof HomeHomeBondCatalogActiveBondIdOfferingsRouteRoute
    }
    '/_home/home/<USER>/active/$bond-id/media-items/create/': {
      id: '/_home/home/<USER>/active/$bond-id/media-items/create/'
      path: '/media-items/create'
      fullPath: '/home/<USER>/active/$bond-id/media-items/create'
      preLoaderRoute: typeof HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRouteImport
      parentRoute: typeof HomeHomeBondCatalogActiveBondIdRouteRoute
    }
    '/_home/home/<USER>/active/$bond-id/media-items/$media-item-id/': {
      id: '/_home/home/<USER>/active/$bond-id/media-items/$media-item-id/'
      path: '/media-items/$media-item-id'
      fullPath: '/home/<USER>/active/$bond-id/media-items/$media-item-id'
      preLoaderRoute: typeof HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRouteImport
      parentRoute: typeof HomeHomeBondCatalogActiveBondIdRouteRoute
    }
  }
}

interface HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteChildren {
  HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRoute: typeof HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRoute
}

const HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteChildren: HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteChildren =
  {
    HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRoute:
      HomeHomeBondCatalogActiveBondIdOfferingsBondOfferingDetailIdIndexRoute,
  }

const HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteWithChildren =
  HomeHomeBondCatalogActiveBondIdOfferingsRouteRoute._addFileChildren(
    HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteChildren,
  )

interface HomeHomeBondCatalogActiveBondIdRouteRouteChildren {
  HomeHomeBondCatalogActiveBondIdOfferingsRouteRoute: typeof HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteWithChildren
  HomeHomeBondCatalogActiveBondIdBondIndexRoute: typeof HomeHomeBondCatalogActiveBondIdBondIndexRoute
  HomeHomeBondCatalogActiveBondIdMediaItemsIndexRoute: typeof HomeHomeBondCatalogActiveBondIdMediaItemsIndexRoute
  HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRoute: typeof HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRoute
  HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRoute: typeof HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRoute
}

const HomeHomeBondCatalogActiveBondIdRouteRouteChildren: HomeHomeBondCatalogActiveBondIdRouteRouteChildren =
  {
    HomeHomeBondCatalogActiveBondIdOfferingsRouteRoute:
      HomeHomeBondCatalogActiveBondIdOfferingsRouteRouteWithChildren,
    HomeHomeBondCatalogActiveBondIdBondIndexRoute:
      HomeHomeBondCatalogActiveBondIdBondIndexRoute,
    HomeHomeBondCatalogActiveBondIdMediaItemsIndexRoute:
      HomeHomeBondCatalogActiveBondIdMediaItemsIndexRoute,
    HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRoute:
      HomeHomeBondCatalogActiveBondIdMediaItemsMediaItemIdIndexRoute,
    HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRoute:
      HomeHomeBondCatalogActiveBondIdMediaItemsCreateIndexRoute,
  }

const HomeHomeBondCatalogActiveBondIdRouteRouteWithChildren =
  HomeHomeBondCatalogActiveBondIdRouteRoute._addFileChildren(
    HomeHomeBondCatalogActiveBondIdRouteRouteChildren,
  )

interface HomeHomeRouteRouteChildren {
  HomeHomeIndexRoute: typeof HomeHomeIndexRoute
  HomeHomeAccessResourcesIndexRoute: typeof HomeHomeAccessResourcesIndexRoute
  HomeHomeBankDowntimeIndexRoute: typeof HomeHomeBankDowntimeIndexRoute
  HomeHomeBannersIndexRoute: typeof HomeHomeBannersIndexRoute
  HomeHomeBondCatalogIndexRoute: typeof HomeHomeBondCatalogIndexRoute
  HomeHomeBondCollectionsIndexRoute: typeof HomeHomeBondCollectionsIndexRoute
  HomeHomeFdCollectionsIndexRoute: typeof HomeHomeFdCollectionsIndexRoute
  HomeHomeFramesIndexRoute: typeof HomeHomeFramesIndexRoute
  HomeHomeIssuingInstitutionsIndexRoute: typeof HomeHomeIssuingInstitutionsIndexRoute
  HomeHomePagesIndexRoute: typeof HomeHomePagesIndexRoute
  HomeHomeRolesIndexRoute: typeof HomeHomeRolesIndexRoute
  HomeHomeStoriesIndexRoute: typeof HomeHomeStoriesIndexRoute
  HomeHomeTagIndexRoute: typeof HomeHomeTagIndexRoute
  HomeHomeUsersIndexRoute: typeof HomeHomeUsersIndexRoute
  HomeHomeBondCatalogActiveBondIdRouteRoute: typeof HomeHomeBondCatalogActiveBondIdRouteRouteWithChildren
  HomeHomeAccessResourcesResourceIdIndexRoute: typeof HomeHomeAccessResourcesResourceIdIndexRoute
  HomeHomeAccessResourcesCreateIndexRoute: typeof HomeHomeAccessResourcesCreateIndexRoute
  HomeHomeBannersBannerIdIndexRoute: typeof HomeHomeBannersBannerIdIndexRoute
  HomeHomeBannersCreateIndexRoute: typeof HomeHomeBannersCreateIndexRoute
  HomeHomeBannersRulesIndexRoute: typeof HomeHomeBannersRulesIndexRoute
  HomeHomeFdCollectionsFdCollectionIdIndexRoute: typeof HomeHomeFdCollectionsFdCollectionIdIndexRoute
  HomeHomeFdCollectionsCreateIndexRoute: typeof HomeHomeFdCollectionsCreateIndexRoute
  HomeHomeFramesFrameIdIndexRoute: typeof HomeHomeFramesFrameIdIndexRoute
  HomeHomeFramesCreateIndexRoute: typeof HomeHomeFramesCreateIndexRoute
  HomeHomeIssuingInstitutionsIsinIdIndexRoute: typeof HomeHomeIssuingInstitutionsIsinIdIndexRoute
  HomeHomePagesPageIdIndexRoute: typeof HomeHomePagesPageIdIndexRoute
  HomeHomePagesCreateIndexRoute: typeof HomeHomePagesCreateIndexRoute
  HomeHomeRolesRoleIdIndexRoute: typeof HomeHomeRolesRoleIdIndexRoute
  HomeHomeRolesCreateIndexRoute: typeof HomeHomeRolesCreateIndexRoute
  HomeHomeStoriesStoryIdIndexRoute: typeof HomeHomeStoriesStoryIdIndexRoute
  HomeHomeStoriesCreateIndexRoute: typeof HomeHomeStoriesCreateIndexRoute
  HomeHomeTagTagIdIndexRoute: typeof HomeHomeTagTagIdIndexRoute
  HomeHomeBannersRulesRuleIdIndexRoute: typeof HomeHomeBannersRulesRuleIdIndexRoute
  HomeHomeBannersRulesCreateIndexRoute: typeof HomeHomeBannersRulesCreateIndexRoute
  HomeHomeBondCollectionsEditBondCollectionIdIndexRoute: typeof HomeHomeBondCollectionsEditBondCollectionIdIndexRoute
  HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRoute: typeof HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRoute
  HomeHomeFdCollectionsFdCollectionIdCreateIndexRoute: typeof HomeHomeFdCollectionsFdCollectionIdCreateIndexRoute
  HomeHomeFramesFrameIdFeatureFlagMappingsIndexRoute: typeof HomeHomeFramesFrameIdFeatureFlagMappingsIndexRoute
  HomeHomeIssuingInstitutionsIsinIdCreateIndexRoute: typeof HomeHomeIssuingInstitutionsIsinIdCreateIndexRoute
  HomeHomePagesPageIdFeatureFlagMappingsIndexRoute: typeof HomeHomePagesPageIdFeatureFlagMappingsIndexRoute
  HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute: typeof HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute
  HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRoute: typeof HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRoute
  HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute: typeof HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute
  HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRoute: typeof HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRoute
}

const HomeHomeRouteRouteChildren: HomeHomeRouteRouteChildren = {
  HomeHomeIndexRoute: HomeHomeIndexRoute,
  HomeHomeAccessResourcesIndexRoute: HomeHomeAccessResourcesIndexRoute,
  HomeHomeBankDowntimeIndexRoute: HomeHomeBankDowntimeIndexRoute,
  HomeHomeBannersIndexRoute: HomeHomeBannersIndexRoute,
  HomeHomeBondCatalogIndexRoute: HomeHomeBondCatalogIndexRoute,
  HomeHomeBondCollectionsIndexRoute: HomeHomeBondCollectionsIndexRoute,
  HomeHomeFdCollectionsIndexRoute: HomeHomeFdCollectionsIndexRoute,
  HomeHomeFramesIndexRoute: HomeHomeFramesIndexRoute,
  HomeHomeIssuingInstitutionsIndexRoute: HomeHomeIssuingInstitutionsIndexRoute,
  HomeHomePagesIndexRoute: HomeHomePagesIndexRoute,
  HomeHomeRolesIndexRoute: HomeHomeRolesIndexRoute,
  HomeHomeStoriesIndexRoute: HomeHomeStoriesIndexRoute,
  HomeHomeTagIndexRoute: HomeHomeTagIndexRoute,
  HomeHomeUsersIndexRoute: HomeHomeUsersIndexRoute,
  HomeHomeBondCatalogActiveBondIdRouteRoute:
    HomeHomeBondCatalogActiveBondIdRouteRouteWithChildren,
  HomeHomeAccessResourcesResourceIdIndexRoute:
    HomeHomeAccessResourcesResourceIdIndexRoute,
  HomeHomeAccessResourcesCreateIndexRoute:
    HomeHomeAccessResourcesCreateIndexRoute,
  HomeHomeBannersBannerIdIndexRoute: HomeHomeBannersBannerIdIndexRoute,
  HomeHomeBannersCreateIndexRoute: HomeHomeBannersCreateIndexRoute,
  HomeHomeBannersRulesIndexRoute: HomeHomeBannersRulesIndexRoute,
  HomeHomeFdCollectionsFdCollectionIdIndexRoute:
    HomeHomeFdCollectionsFdCollectionIdIndexRoute,
  HomeHomeFdCollectionsCreateIndexRoute: HomeHomeFdCollectionsCreateIndexRoute,
  HomeHomeFramesFrameIdIndexRoute: HomeHomeFramesFrameIdIndexRoute,
  HomeHomeFramesCreateIndexRoute: HomeHomeFramesCreateIndexRoute,
  HomeHomeIssuingInstitutionsIsinIdIndexRoute:
    HomeHomeIssuingInstitutionsIsinIdIndexRoute,
  HomeHomePagesPageIdIndexRoute: HomeHomePagesPageIdIndexRoute,
  HomeHomePagesCreateIndexRoute: HomeHomePagesCreateIndexRoute,
  HomeHomeRolesRoleIdIndexRoute: HomeHomeRolesRoleIdIndexRoute,
  HomeHomeRolesCreateIndexRoute: HomeHomeRolesCreateIndexRoute,
  HomeHomeStoriesStoryIdIndexRoute: HomeHomeStoriesStoryIdIndexRoute,
  HomeHomeStoriesCreateIndexRoute: HomeHomeStoriesCreateIndexRoute,
  HomeHomeTagTagIdIndexRoute: HomeHomeTagTagIdIndexRoute,
  HomeHomeBannersRulesRuleIdIndexRoute: HomeHomeBannersRulesRuleIdIndexRoute,
  HomeHomeBannersRulesCreateIndexRoute: HomeHomeBannersRulesCreateIndexRoute,
  HomeHomeBondCollectionsEditBondCollectionIdIndexRoute:
    HomeHomeBondCollectionsEditBondCollectionIdIndexRoute,
  HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRoute:
    HomeHomeFdCollectionsFdCollectionIdCollectionItemIdIndexRoute,
  HomeHomeFdCollectionsFdCollectionIdCreateIndexRoute:
    HomeHomeFdCollectionsFdCollectionIdCreateIndexRoute,
  HomeHomeFramesFrameIdFeatureFlagMappingsIndexRoute:
    HomeHomeFramesFrameIdFeatureFlagMappingsIndexRoute,
  HomeHomeIssuingInstitutionsIsinIdCreateIndexRoute:
    HomeHomeIssuingInstitutionsIsinIdCreateIndexRoute,
  HomeHomePagesPageIdFeatureFlagMappingsIndexRoute:
    HomeHomePagesPageIdFeatureFlagMappingsIndexRoute,
  HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute:
    HomeHomeFramesFrameIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute,
  HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRoute:
    HomeHomeFramesFrameIdFeatureFlagMappingsCreateIndexRoute,
  HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute:
    HomeHomePagesPageIdFeatureFlagMappingsFeatureFlagMappingIdIndexRoute,
  HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRoute:
    HomeHomePagesPageIdFeatureFlagMappingsCreateIndexRoute,
}

const HomeHomeRouteRouteWithChildren = HomeHomeRouteRoute._addFileChildren(
  HomeHomeRouteRouteChildren,
)

interface HomeRouteChildren {
  HomeHomeRouteRoute: typeof HomeHomeRouteRouteWithChildren
}

const HomeRouteChildren: HomeRouteChildren = {
  HomeHomeRouteRoute: HomeHomeRouteRouteWithChildren,
}

const HomeRouteWithChildren = HomeRoute._addFileChildren(HomeRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  HomeRoute: HomeRouteWithChildren,
  AuthLoginIndexRoute: AuthLoginIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
