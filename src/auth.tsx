import * as React from "react";
import { getStoredAuthToken, setStoredAuthToken } from "./lib/utils/auth";

export interface AuthContext {
  isAuthenticated: boolean;
  login: (username: string) => Promise<void>;
  logout: () => Promise<void>;
  authToken: string | null;
}

const AuthContext = React.createContext<AuthContext | null>(null);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authToken, setAuthToken] = React.useState<string | null>(
    getStoredAuthToken()
  );
  const isAuthenticated = !!authToken;

  const logout = React.useCallback(async () => {
    setStoredAuthToken(null);
    setAuthToken(null);
  }, []);

  const login = React.useCallback(async (authToken: string) => {
    setStoredAuthToken(authToken);
    setAuthToken(authToken);
  }, []);

  React.useEffect(() => {
    setAuthToken(getStoredAuthToken());
  }, []);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        authToken,
        login,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = React.useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
