import { createFileRoute, Outlet } from "@tanstack/react-router";
import { useNavigate } from "@tanstack/react-router";
import {
  Avatar,
  Button,
  ConfigProvider,
  Layout,
  Menu,
  Row,
  theme,
  type MenuProps,
} from "antd";
import stableMoneyIcon from "~/assets/logos/logo.webp";
import { Dropdown } from "antd";
import { useState } from "react";
import {
  UserOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  FileTextOutlined,
  BankOutlined,
  TeamOutlined,
  SecurityScanOutlined,
} from "@ant-design/icons";
import { Divider } from "antd";
import { useAuth } from "~/auth";
import { Content } from "antd/es/layout/layout";
import { getCurrentUserRolesQueryOptions } from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { useSuspenseQueries } from "@tanstack/react-query";

const { Header, Sider } = Layout;

export const Route = createFileRoute("/_home")({
  component: RouteComponent,
  loader: async () => {
    await queryClient.ensureQueryData(getCurrentUserRolesQueryOptions());
  },
});

function RouteComponent() {
  const auth = useAuth();
  const handleLogout = () => {
    auth.logout();
  };

  const [getCurrentUserRolesQuery] = useSuspenseQueries({
    queries: [getCurrentUserRolesQueryOptions()],
  });

  const currentUserRoles = getCurrentUserRolesQuery.data;

  const hasRole = (roleName: string) => {
    return currentUserRoles.roles.some((role) => role.name === roleName);
  };

  const getFilteredMenuItems = () => {
    const menuItems = [];

    if (hasRole("BOND_ADMIN")) {
      const bondsChildren = [];

      if (hasRole("BOND_ADMIN")) {
        bondsChildren.push({
          key: "1-1",
          label: "Bond Catalog",
          onClick: () => {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (hasRole("BOND_ADMIN")) {
        bondsChildren.push({
          key: "1-2",
          label: "Issuing Institutions",
          onClick: () => {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (hasRole("BOND_ADMIN")) {
        bondsChildren.push({
          key: "1-3",
          label: "Bond Collections",
          onClick: () => {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (hasRole("BOND_ADMIN")) {
        bondsChildren.push({
          key: "1-4",
          label: "Tag",
          onClick: () => {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (bondsChildren.length > 0) {
        menuItems.push({
          key: "1",
          icon: <FileTextOutlined height={30} />,
          label: "Bonds",
          children: bondsChildren,
        });
      }
    }

    if (hasRole("BANK_ADMIN")) {
      const fdChildren = [];

      if (hasRole("BANK_ADMIN")) {
        fdChildren.push({
          key: "2-1",
          label: "Bank Down Time",
          onClick() {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (hasRole("BANK_ADMIN")) {
        fdChildren.push({
          key: "2-2",
          label: "FD Collections",
          onClick() {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (fdChildren.length > 0) {
        menuItems.push({
          key: "2",
          icon: <BankOutlined height={40} />,
          label: "FD",
          children: fdChildren,
        });
      }
    }

    if (hasRole("PERSONALIZATION_ADMIN")) {
      const personalizationChildren = [];

      if (hasRole("PERSONALIZATION_ADMIN")) {
        personalizationChildren.push({
          key: "3-1",
          label: "Pages",
          onClick() {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (hasRole("PERSONALIZATION_ADMIN")) {
        personalizationChildren.push({
          key: "3-2",
          label: "Frames",
          onClick() {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (hasRole("PERSONALIZATION_ADMIN")) {
        personalizationChildren.push({
          key: "3-3",
          label: "Banners",
          onClick() {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (hasRole("PERSONALIZATION_ADMIN")) {
        personalizationChildren.push({
          key: "3-4",
          label: "Stories",
          onClick() {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (personalizationChildren.length > 0) {
        menuItems.push({
          key: "3",
          icon: <TeamOutlined height={40} />,
          label: "Personalization",
          children: personalizationChildren,
        });
      }
    }

    if (hasRole("USER_MANAGEMENT_ADMIN")) {
      const accessControlChildren = [];

      if (hasRole("USER_MANAGEMENT_ADMIN")) {
        accessControlChildren.push({
          key: "4-1",
          label: "Users",
          onClick() {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (hasRole("USER_MANAGEMENT_ADMIN")) {
        accessControlChildren.push({
          key: "4-2",
          label: "Access Resources",
          onClick() {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (hasRole("USER_MANAGEMENT_ADMIN")) {
        accessControlChildren.push({
          key: "4-3",
          label: "Roles",
          onClick() {
            setCollapsed(true);
            navigate({ to: "/home/<USER>" });
          },
        });
      }

      if (accessControlChildren.length > 0) {
        menuItems.push({
          key: "4",
          icon: <SecurityScanOutlined height={40} />,
          label: "Access Control",
          children: accessControlChildren,
        });
      }
    }

    return menuItems;
  };

  const items: MenuProps["items"] = [
    {
      key: "1",
      label: (
        <button onClick={handleLogout} className="cursor-pointer">
          Logout
        </button>
      ),
    },
  ];
  const [collapsed, setCollapsed] = useState(true);
  const {
    token: { colorBgContainer },
  } = theme.useToken();
  const navigate = useNavigate();
  return (
    <Layout>
      <ConfigProvider
        theme={{
          components: {
            Layout: {
              siderBg: colorBgContainer,
            },
          },
        }}
      >
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          className="overflow-auto"
        >
          <div className="flex gap-1 items-center justify-center p-4">
            <div>
              <img src={stableMoneyIcon} alt="StableMoney" className="h-6" />
            </div>
            {!collapsed && <div className="text-xl font-bold">Control Hub</div>}
          </div>
          <div className="m-0">
            <Divider size="small" className="border-gray-300" />
          </div>
          <Menu
            defaultSelectedKeys={["1"]}
            mode="inline"
            items={getFilteredMenuItems()}
            className="h-screen"
          />
        </Sider>
      </ConfigProvider>
      <Layout>
        <ConfigProvider
          theme={{
            components: {
              Layout: {
                headerPadding: "0",
                headerBg: colorBgContainer,
              },
            },
          }}
        >
          <Header>
            <Row justify="space-between" align="middle" className="pr-6">
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                className="text-base w-16 h-16"
              />
              <div className="flex-row-reverse">
                <Dropdown menu={{ items }}>
                  <Avatar
                    size="large"
                    icon={<UserOutlined />}
                    className="cursor-pointer"
                  />
                </Dropdown>
              </div>
            </Row>
          </Header>
        </ConfigProvider>

        <Content>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
}
