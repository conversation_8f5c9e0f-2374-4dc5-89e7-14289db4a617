import { createFile<PERSON>out<PERSON>, <PERSON> } from "@tanstack/react-router";
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { Form, Input, <PERSON><PERSON>, Card, Breadcrumb, Row, Col, message } from "antd";
import { HomeOutlined, SaveOutlined } from "@ant-design/icons";
import { useForm } from "antd/es/form/Form";
import { handleError } from "~/lib/utils/error";
import type { Role } from "~/lib/types/access-control";
import { getRoleQueryOptions } from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { updateRole } from "~/lib/queries/access-control";

export const Route = createFileRoute("/_home/home/<USER>/$role-id/")({
  component: RouteComponent,
  loader: async ({ params }) => {
    await queryClient.ensureQueryData(getRoleQueryOptions(params["role-id"]));
  },
});

function RouteComponent() {
  const { "role-id": roleId } = Route.useParams();
  const [form] = useForm();
  const queryClient = useQueryClient();

  const { data: role } = useSuspenseQuery(getRoleQueryOptions(roleId));

  const { mutate: updateRoleItem } = useMutation({
    mutationFn: updateRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      message.success("Role updated successfully!");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update role");
    },
  });

  const onFinish = (values: Role) => {
    const payload = {
      ...values,
      id: roleId,
    };
    updateRoleItem(payload);
  };

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                Roles
              </Link>
            ),
          },
          {
            title: role.name,
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            initialValues={{
              name: role.name,
              description: role.description,
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={<strong>Name</strong>}
                  name="name"
                  rules={[
                    { required: true, message: "Please enter a role name!" },
                  ]}
                >
                  <Input placeholder="Role Name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={<strong>Description</strong>}
                  name="description"
                >
                  <Input placeholder="Role Description" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item className="mt-4 text-right">
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                size="middle"
              >
                Update Role
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}