import { HomeOutlined, PlusOutlined } from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { <PERSON>readcrumb, Button, Input } from "antd";
import { useState } from "react";
import z from "zod";
import { getStoriesQueryOptions } from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { StoryTableComponent } from "./-components/story-table-component";

const storySearchSchema = z.object({
  page: z.number().default(1),
  q: z.string().optional(),
});

export const Route = createFileRoute("/_home/home/<USER>/")({
  validateSearch: storySearchSchema,
  loaderDeps: ({search: {page, q}}) => ({page, q}),
  loader: async ({ deps: { page, q } }) => {
    await queryClient.ensureQueryData(getStoriesQueryOptions(page, q ?? ""));
  },
  component: RouteComponent,
});

function RouteComponent() {
  const { page, q } = Route.useSearch();
  const navigate = Route.useNavigate();
  const [searchText, setSearchText] = useState("");

  const [getStoriesQuery] = useSuspenseQueries({
    queries: [getStoriesQueryOptions(page, q ?? "")],
  });

  const stories = getStoriesQuery.data;

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: "Stories",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by name"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={() =>
              navigate({ search: (prev) => ({ ...prev, q: searchText }) })
            }
          />
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate({ to: "/home/<USER>/create" })}
        >
          Create
        </Button>
      </div>
      <StoryTableComponent stories={stories} />
    </div>
  );
}
