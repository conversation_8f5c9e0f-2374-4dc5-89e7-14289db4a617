import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import { SearchBar } from "src/components/search-bar";
import { CreateTag } from "./-components/create-tag";
import { TagTable } from "./-components/tag-table";

export const Route = createFileRoute("/_home/home/<USER>/")({
  component: RouteComponent,
});

function RouteComponent() {
  const [searchText, setSearchText] = useState("");
  return (
    <div className="p-6 space-y-4">
      <div className="flex items-center justify-between">
        <SearchBar searchText={searchText} setSearchText={setSearchText} />
        <CreateTag />
      </div>
      <TagTable searchText={searchText} />
    </div>
  );
}
