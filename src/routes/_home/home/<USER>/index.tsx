import { createFileRoute } from "@tanstack/react-router";
import {
  bankNameAndIDQueryOptions,
  banksDownTimeQueryOptions,
} from "~/lib/queries/queryKeys";
import { ColumnsComponent } from "./-components/columns";
import { useSuspenseQueries } from "@tanstack/react-query";
import { Button, Modal } from "antd";
import { Create } from "./-components/create";
import { useState } from "react";
import { PlusOutlined } from "@ant-design/icons";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute("/_home/home/<USER>/")({
  component: () => <RouteComponent />,
  loader: async () => {
    await Promise.all([
      queryClient.ensureQueryData({ ...banksDownTimeQueryOptions() }),
      queryClient.ensureQueryData({ ...bankNameAndIDQueryOptions() }),
    ]).catch((error) => {
      throw new Error(error.message);
    });
  },
});
function RouteComponent() {
  const [banksDownTimeQuery, banksNameAndIdQuery] = useSuspenseQueries({
    queries: [banksDownTimeQueryOptions(), bankNameAndIDQueryOptions()],
  });
  const banksDownTime = banksDownTimeQuery.data;
  const banksNameAndId = banksNameAndIdQuery.data;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <div className="p-6 space-y-4">
      <div className="justify-items-end">
        <Button
          onClick={() => setIsModalOpen(true)}
          type="primary"
          className="mb-2"
        >
          Add Bank
          <PlusOutlined />
        </Button>
        <Modal
          open={isModalOpen}
          onCancel={handleCancel}
          title="Create Bank Downtime"
          footer={null}
          width={650}
        >
          <Create data={banksNameAndId.bankDetail} onClose={handleCancel} />
        </Modal>
        <ColumnsComponent
          banksData={banksDownTime.BankDownTimesField}
          bankFieldsAndName={banksNameAndId.bankDetail}
        />
      </div>
    </div>
  );
}
