import {
  HomeOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { <PERSON>readcrumb, Button, Input } from "antd";
import { useState } from "react";
import z from "zod";
import { getBannersQueryOptions } from "~/lib/queries/queryKeys";
import { BannersTableComponent } from "./-components/banner-table-component";
import { queryClient } from "~/lib/utils/queryClient";

const bannerSearchSchema = z.object({
  page: z.number().default(1),
  q: z.string().optional(),
});

export const Route = createFileRoute("/_home/home/<USER>/")({
  validateSearch: bannerSearchSchema,
  loaderDeps: ({search: {page, q}}) => ({page, q}),
  loader: async ({ deps: { page, q } }) => {
    await queryClient.ensureQueryData(getBannersQueryOptions(page, q ?? ""));
  },
  component: RouteComponent,
});

function RouteComponent() {
  const { page, q } = Route.useSearch();
  const navigate = Route.useNavigate();
  const [searchText, setSearchText] = useState("");

  const [getBannersQuery] = useSuspenseQueries({
    queries: [getBannersQueryOptions(page, q ?? "")],
  });

  const banners = getBannersQuery.data;

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: "Banners",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by name"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={() =>
              navigate({ search: (prev) => ({ ...prev, q: searchText }) })
            }
          />
        </div>
        <div>
          <Button
            className="mr-2"
            icon={<QuestionCircleOutlined />}
            onClick={() => navigate({ to: "/home/<USER>/rules" })}
          >
            Rules
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate({ to: "/home/<USER>/create" })}
          >
            Create
          </Button>
        </div>
      </div>
      <BannersTableComponent banners={banners} />
    </div>
  );
}
