import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { Breadcrumb, Input, Table, Button, Tooltip, Tag, Modal, Form, Select, Switch, message, Tabs } from 'antd'
import { HomeOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import type { TableProps } from 'antd'
import type { DashboardUser, UserRole, Role, AssignRoleRequest, UpdateDashboardUserStatusRequest } from '~/lib/types/access-control'
import { getDashboardUsersQueryOptions, getRolesQueryOptions } from '~/lib/queries/queryKeys'
import { queryClient } from '~/lib/utils/queryClient'
import { useSuspenseQueries, useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { getUserRoles, assignRoleToUser, updateUserRoleStatus, removeRoleFromUser, updateDashboardUserStatus } from '~/lib/queries/access-control'
import { handleError } from '~/lib/utils/error'

export const Route = createFileRoute('/_home/home/<USER>/')({
  component: RouteComponent,
  loader: async () => {
    await Promise.all([
      queryClient.ensureQueryData(getDashboardUsersQueryOptions()),
      queryClient.ensureQueryData(getRolesQueryOptions()),
    ]);
  },
})

function RouteComponent() {
  const [searchText, setSearchText] = useState('')
  const [assignModalVisible, setAssignModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [selectedUser, setSelectedUser] = useState<DashboardUser | null>(null)
  const [selectedUserRole, setSelectedUserRole] = useState<UserRole | null>(null)
  const [activeTab, setActiveTab] = useState('active')
  const [form] = Form.useForm()
  const [editForm] = Form.useForm()
  const queryClient = useQueryClient()

  const [getDashboardUsersQuery, getRolesQuery] = useSuspenseQueries({
    queries: [getDashboardUsersQueryOptions(), getRolesQueryOptions()],
  })

  const dashboardUsers = getDashboardUsersQuery.data
  const roles = getRolesQuery.data

  const filteredData = dashboardUsers.filter((user) =>
    user.email.toLowerCase().includes(searchText.toLowerCase())
  )

  // Separate active and inactive users
  const activeUsers = filteredData.filter(user => user.isActive !== false)
  const inactiveUsers = filteredData.filter(user => user.isActive === false)

  // Get current tab data
  const currentTabData = activeTab === 'active' ? activeUsers : inactiveUsers

  // Add mutation for updating user status
  const { mutate: updateUserStatus } = useMutation({
    mutationFn: ({ email, request }: { email: string; request: UpdateDashboardUserStatusRequest }) =>
      updateDashboardUserStatus(email, request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getDashboardUsersQueryOptions().queryKey });
      message.success("User status updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update user status");
    },
  });

  // Add handler for status toggle
  const handleStatusToggle = (user: DashboardUser) => {
    Modal.confirm({
      title: "Confirm Status Change",
      content: `Are you sure you want to ${user.isActive ? "deactivate" : "activate"} ${user.email}?`,
      okText: "Yes, Change",
      cancelText: "Cancel",
      onOk: () => {
        updateUserStatus({
          email: user.email,
          request: { isActive: !user.isActive }
        });
      },
    });
  };

  const userRolesQueries = useQuery({
    queryKey: ['all-user-roles', filteredData.map(u => u.id)],
    queryFn: async () => {
      const results = await Promise.all(
        filteredData.map(async (user) => {
          try {
            const userRoles = await getUserRoles(user.id)
            return { userId: user.id, roles: userRoles }
          } catch {
            return { userId: user.id, roles: [] }
          }
        })
      )
      return results.reduce((acc, curr) => {
        acc[curr.userId] = curr.roles
        return acc
      }, {} as Record<string, UserRole[]>)
    },
    enabled: filteredData.length > 0,
  })

  const { mutate: assignRole } = useMutation({
    mutationFn: ({ userId, request }: { userId: string; request: AssignRoleRequest }) =>
      assignRoleToUser(userId, request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['all-user-roles'] });
      message.success("Role assigned successfully");
      setAssignModalVisible(false);
      form.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to assign role");
    },
  });

  const { mutate: updateRoleStatus } = useMutation({
    mutationFn: ({ userId, roleId, status }: { userId: string; roleId: string; status: boolean }) =>
      updateUserRoleStatus(userId, roleId, { status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['all-user-roles'] });
      message.success("Role status updated successfully");
      setEditModalVisible(false);
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update role status");
    },
  });

  const { mutate: removeRole } = useMutation({
    mutationFn: ({ userId, roleId }: { userId: string; roleId: string }) =>
      removeRoleFromUser(userId, roleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['all-user-roles'] });
      message.success("Role removed successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to remove role");
    },
  });

  const handleAssignRole = (user: DashboardUser) => {
    setSelectedUser(user);
    setAssignModalVisible(true);
  };

  const handleEditRole = (user: DashboardUser, userRole: UserRole) => {
    setSelectedUser(user);
    setSelectedUserRole(userRole);
    editForm.setFieldsValue({
      roleId: userRole.role.id,
      status: userRole.status,
    });
    setEditModalVisible(true);
  };

  const handleDeleteRole = (user: DashboardUser, userRole: UserRole) => {
    Modal.confirm({
      title: "Confirm Role Removal",
      content: `Are you sure you want to remove the role "${userRole.role.name}" from ${user.email}?`,
      okText: "Yes, Remove",
      cancelText: "Cancel",
      onOk: () => {
        removeRole({ userId: user.id, roleId: userRole.role.id });
      },
    });
  };

  const onAssignFinish = (values: { roleId: string; status: boolean }) => {
    if (selectedUser) {
      assignRole({
        userId: selectedUser.id,
        request: {
          roleId: values.roleId,
          status: values.status,
        },
      });
    }
  };

  const onEditFinish = (values: { status: boolean }) => {
    if (selectedUser && selectedUserRole) {
      updateRoleStatus({
        userId: selectedUser.id,
        roleId: selectedUserRole.role.id,
        status: values.status,
      });
    }
  };

  const columns: TableProps<DashboardUser>['columns'] = [
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: '25%',
    },
    {
      title: 'Status',
      key: 'status',
      width: '15%',
      render: (_, record) => (
        <Switch
          checked={record.isActive !== false}
          onChange={() => handleStatusToggle(record)}
          checkedChildren="Active"
          unCheckedChildren="Inactive"
        />
      ),
    },
    {
      title: 'Assigned Roles',
      key: 'roles',
      width: '40%',
      render: (_, record) => {
        const userRoles = userRolesQueries.data?.[record.id] || [];
        return (
          <div className="flex flex-wrap gap-2">
            {userRoles.length > 0 ? (
              userRoles.map((userRole) => (
                <div key={userRole.id} className="flex items-center gap-1 border rounded-md p-1 bg-gray-50">
                  <Tooltip title="Click to edit status">
                    <Tag
                      color={userRole.status ? 'green' : 'red'}
                      className="cursor-pointer m-0"
                      onClick={() => handleEditRole(record, userRole)}
                    >
                      <EditOutlined className="mr-1" />
                      {userRole.role.name} {!userRole.status && '(Inactive)'}
                    </Tag>
                  </Tooltip>
                  <Tooltip title={`Remove ${userRole.role.name} role`}>
                    <Button
                      danger
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      className="h-6 w-6 flex items-center justify-center"
                      onClick={() => handleDeleteRole(record, userRole)}
                    />
                  </Tooltip>
                </div>
              ))
            ) : (
              <span className="text-gray-400">No roles assigned</span>
            )}
          </div>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '20%',
      align: 'center',
      render: (_, record) => (
        <Tooltip title="Assign New Role">
          <Button
            type="primary"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => handleAssignRole(record)}
          >
            Assign Role
          </Button>
        </Tooltip>
      ),
    },
  ];

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: "Dashboard Users",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by email"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </div>
      </div>
      
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'active',
            label: `Active Users (${activeUsers.length})`,
            children: (
              <Table
                columns={columns}
                dataSource={currentTabData}
                rowKey="id"
                loading={userRolesQueries.isLoading}
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                }}
              />
            ),
          },
          {
            key: 'inactive',
            label: `Inactive Users (${inactiveUsers.length})`,
            children: (
              <Table
                columns={columns}
                dataSource={currentTabData}
                rowKey="id"
                loading={userRolesQueries.isLoading}
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                }}
              />
            ),
          },
        ]}
      />

      {/* Assign Role Modal */}
      <Modal
        title={`Assign Role to ${selectedUser?.email}`}
        open={assignModalVisible}
        onCancel={() => {
          setAssignModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onAssignFinish}
          initialValues={{ status: true }}
        >
          <Form.Item
            label="Role"
            name="roleId"
            rules={[{ required: true, message: "Please select a role!" }]}
          >
            <Select placeholder="Select a role" showSearch>
              {roles.map((role: Role) => (
                <Select.Option key={role.id} value={role.id}>
                  {role.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="Status"
            name="status"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          <Form.Item className="text-right mb-0">
            <Button
              type="primary"
              htmlType="submit"
              icon={<PlusOutlined />}
            >
              Assign Role
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Role Modal */}
      <Modal
        title={`Edit Role: ${selectedUserRole?.role.name} for ${selectedUser?.email}`}
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={onEditFinish}
        >
          <Form.Item
            label="Status"
            name="status"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          <Form.Item className="text-right mb-0">
            <Button
              type="primary"
              htmlType="submit"
              icon={<EditOutlined />}
            >
              Update Status
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
