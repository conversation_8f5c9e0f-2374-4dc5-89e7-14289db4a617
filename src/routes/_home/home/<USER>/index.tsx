import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Tabs } from 'antd'
import { BondCollectionTable, CreateBondCollection } from './-components/index'
import { SearchBar } from "src/components/search-bar";
import { useSuspenseQueries } from '@tanstack/react-query';
import { allBondCollectionsQueryOptions } from '~/lib/queries/queryKeys';
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute('/_home/home/<USER>/')({
  component: () => <RouteComponent />,
  loader: async () => {
    await queryClient.ensureQueryData({ ...allBondCollectionsQueryOptions() });
  },
})

function RouteComponent() {
  const [searchText, setSearchText] = useState('');

  const [allBondCollectionsQuery] = useSuspenseQueries({
    queries: [allBondCollectionsQueryOptions()],
  });

  const allBondCollections = allBondCollectionsQuery.data;

  const tabItems = [
    {
      key: 'active',
      label: 'Active',
      children: (
        <BondCollectionTable 
          searchText={searchText} 
          allBondCollections={allBondCollections}
          filterActive={true}
        />
      ),
    },
    {
      key: 'inactive',
      label: 'Inactive',
      children: (
        <BondCollectionTable 
          searchText={searchText} 
          allBondCollections={allBondCollections}
          filterActive={false}
        />
      ),
    },
  ];

  return (
    <div className="p-6 space-y-4">
      <div className="flex items-center justify-between">
        <SearchBar searchText={searchText} setSearchText={setSearchText} />
        <CreateBondCollection />
      </div>
      <Tabs 
        defaultActiveKey="active" 
        items={tabItems}
      />
    </div>
  );
}