import { HomeOutlined, PlusOutlined } from '@ant-design/icons';
import { useSuspenseQueries } from '@tanstack/react-query';
import { createFileRoute, Link } from '@tanstack/react-router'
import { Breadcrumb, Button, Input } from 'antd';
import { useState } from 'react';
import z from "zod";
import { getFdCollectionsQueryOptions } from '~/lib/queries/queryKeys';
import { CollectionsTableComponent } from './-components/collections-table-component';
import { queryClient } from "~/lib/utils/queryClient";

const pageSearchSchema = z.object({
  page: z.number().default(1),
  q: z.string().optional(),
});

export const Route = createFileRoute('/_home/home/<USER>/')({
  validateSearch: pageSearchSchema,
  loaderDeps: ({search: {page, q}}) => ({page, q}),
  loader: async ({ deps: { page, q } }) => {
    await queryClient.ensureQueryData(
      getFdCollectionsQueryOptions(page, q ?? "")
    );
  },
  component: RouteComponent,
})

function RouteComponent() {
  const { page, q } = Route.useSearch();
  const navigate = Route.useNavigate();
  const [searchText, setSearchText] = useState("");

  const [getFdCollectionsQuery] = useSuspenseQueries({
    queries: [getFdCollectionsQueryOptions(page, q ?? "")],
  });

  const fdCollections = getFdCollectionsQuery.data;

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: "Collections",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by name"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={() =>
              navigate({ search: (prev) => ({ ...prev, q: searchText }) })
            }
          />
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate({ to: "/home/<USER>/create" })}
        >
          Create
        </Button>
      </div>
      <CollectionsTableComponent fdCollections={fdCollections} />
    </div>
  );
}
