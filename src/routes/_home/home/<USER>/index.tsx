import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import { TableComponent } from "./-components/table-component";
import { SearchBar } from "src/components/search-bar";

export const Route = createFileRoute("/_home/home/<USER>/")({
  component: RouteComponent,
});

function RouteComponent() {
  const [searchText, setSearchText] = useState('');

  return (
    <div className="p-6 space-y-4">
      <SearchBar searchText={searchText} setSearchText={setSearchText} />
      <TableComponent searchText={searchText} />
    </div>
  );
}