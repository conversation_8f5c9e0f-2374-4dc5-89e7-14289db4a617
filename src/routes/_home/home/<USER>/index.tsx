import { HomeOutlined, PlusOutlined } from "@ant-design/icons";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Breadcrumb, Button, Input } from "antd";
import { PagesTableComponent } from "./-components/pages-table-component";
import z from "zod";
import { useSuspenseQueries } from "@tanstack/react-query";
import { getPagesQueryOptions } from "~/lib/queries/queryKeys";
import { useState } from "react";
import { queryClient } from "~/lib/utils/queryClient";

const pageSearchSchema = z.object({
  page: z.number().default(1),
  q: z.string().optional(),
});

export const Route = createFileRoute("/_home/home/<USER>/")({
  validateSearch: pageSearchSchema,
  loaderDeps: ({search: {page, q}}) => ({page, q}),
  loader: async ({ deps: { page, q } }) => {
    await queryClient.ensureQueryData(getPagesQueryOptions(page, q ?? ""));
  },
  component: RouteComponent,
});

function RouteComponent() {
  const { page, q } = Route.useSearch();
  const navigate = Route.useNavigate();
  const [searchText, setSearchText] = useState("");

  const [getPagesQuery] = useSuspenseQueries({
    queries: [getPagesQueryOptions(page, q ?? "")],
  });

  const pages = getPagesQuery.data;

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: "Pages",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by name"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={() =>
              navigate({ search: (prev) => ({ ...prev, q: searchText }) })
            }
          />
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate({ to: "/home/<USER>/create" })}
        >
          Create
        </Button>
      </div>
      <PagesTableComponent pages={pages} />
    </div>
  );
}
