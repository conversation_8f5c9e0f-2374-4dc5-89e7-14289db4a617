import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { Breadcrumb, Button, Input, Table, Tag, Modal, Form, Select, Tooltip, message } from 'antd'
import { HomeOutlined, PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons'
import type { TableProps } from 'antd'
import type { Role, RoleAccessResource, AccessResource, AssignAccessResourceRequest, CreateRoleRequest } from '~/lib/types/access-control'
import { getRolesQueryOptions, getAccessResourcesQueryOptions } from '~/lib/queries/queryKeys'
import { queryClient } from '~/lib/utils/queryClient'
import { useSuspenseQueries, useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { getRoleAccessResourcesByRole, assignAccessResourceToRole, removeAccessResourceFromRole, updateRole, createRole, deleteRole } from '~/lib/queries/access-control'
import { handleError } from '~/lib/utils/error'

export const Route = createFileRoute('/_home/home/<USER>/')({
  component: RouteComponent,
  loader: async () => {
    await Promise.all([
      queryClient.ensureQueryData(getRolesQueryOptions()),
      queryClient.ensureQueryData(getAccessResourcesQueryOptions()),
    ]);
  },
})

function RouteComponent() {
  const [searchText, setSearchText] = useState('')
  const [assignModalVisible, setAssignModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [form] = Form.useForm()
  const [editForm] = Form.useForm()
  const [createForm] = Form.useForm()
  const queryClient = useQueryClient()

  const [getRolesQuery, getAccessResourcesQuery] = useSuspenseQueries({
    queries: [getRolesQueryOptions(), getAccessResourcesQueryOptions()],
  })

  const roles = getRolesQuery.data
  const accessResources = getAccessResourcesQuery.data

  const filteredData = roles.filter((role) =>
    role.name.toLowerCase().includes(searchText.toLowerCase())
  )

  // Get role access resources for each role
  const roleAccessResourcesQueries = useQuery({
    queryKey: ['all-role-access-resources', filteredData.map(r => r.name)],
    queryFn: async () => {
      const results = await Promise.all(
        filteredData.map(async (role) => {
          try {
            const roleAccessResources = await getRoleAccessResourcesByRole(role.name)
            return { roleName: role.name, accessResources: roleAccessResources }
          } catch {
            return { roleName: role.name, accessResources: [] }
          }
        })
      )
      return results.reduce((acc, curr) => {
        acc[curr.roleName] = curr.accessResources
        return acc
      }, {} as Record<string, RoleAccessResource[]>)
    },
    enabled: filteredData.length > 0,
  })

  const { mutate: assignAccessResource } = useMutation({
    mutationFn: ({ roleName, request }: { roleName: string; request: AssignAccessResourceRequest }) =>
      assignAccessResourceToRole(roleName, request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['all-role-access-resources'] });
      message.success("Access resource assigned successfully");
      setAssignModalVisible(false);
      form.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to assign access resource");
    },
  });

  const { mutate: removeAccessResource } = useMutation({
    mutationFn: ({ roleName, accessResourceName }: { roleName: string; accessResourceName: string }) =>
      removeAccessResourceFromRole(roleName, accessResourceName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['all-role-access-resources'] });
      message.success("Access resource removed successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to remove access resource");
    },
  });

  const { mutate: updateRoleItem } = useMutation({
    mutationFn: updateRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      message.success("Role updated successfully!");
      setEditModalVisible(false);
      editForm.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update role");
    },
  });

  const { mutate: createRoleItem } = useMutation({
    mutationFn: createRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      message.success("Role created successfully!");
      setCreateModalVisible(false);
      createForm.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create role");
    },
  });

  const { mutate: deleteRoleItem } = useMutation({
    mutationFn: deleteRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      queryClient.invalidateQueries({ queryKey: ['all-role-access-resources'] });
      message.success("Role deleted successfully!");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete role");
    },
  });

  const handleAssignAccessResource = (role: Role) => {
    setSelectedRole(role);
    setAssignModalVisible(true);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    editForm.setFieldsValue({
      name: role.name,
      description: role.description,
    });
    setEditModalVisible(true);
  };

  const handleRemoveAccessResource = (role: Role, accessResourceName: string) => {
    Modal.confirm({
      title: "Confirm Access Resource Removal",
      content: `Are you sure you want to remove the access resource "${accessResourceName}" from role "${role.name}"?`,
      okText: "Yes, Remove",
      cancelText: "Cancel",
      onOk: () => {
        removeAccessResource({ roleName: role.name, accessResourceName });
      },
    });
  };

  const onAssignFinish = (values: { accessResourceName: string }) => {
    if (selectedRole) {
      assignAccessResource({
        roleName: selectedRole.name,
        request: { accessResourceName: values.accessResourceName }
      });
    }
  };

  const onEditFinish = (values: { name: string; description: string }) => {
    if (selectedRole) {
      updateRoleItem({
        ...values,
        id: selectedRole.id,
      });
    }
  };

  const onCreateFinish = (values: CreateRoleRequest) => {
    createRoleItem(values);
  };

  const handleDeleteRole = (role: Role) => {
    Modal.confirm({
      title: "Confirm Role Deletion",
      content: `Are you sure you want to delete the role "${role.name}"? This action cannot be undone.`,
      okText: "Yes, Delete",
      okType: "danger",
      cancelText: "Cancel",
      onOk: () => {
        deleteRoleItem(role.id);
      },
    });
  };

  const columns: TableProps<Role>['columns'] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
      render: (text, record) => (
        <span
          className="cursor-pointer text-blue-600 hover:text-blue-800"
          onClick={() => handleEditRole(record)}
        >
          {text}
        </span>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: '20%',
    },
    {
      title: 'Assigned Access Resources',
      key: 'accessResources',
      width: '45%',
      render: (_, record) => {
        const roleAccessResources = roleAccessResourcesQueries.data?.[record.name] || [];
        return (
          <div className="flex flex-wrap gap-2">
            {roleAccessResources.length > 0 ? (
              roleAccessResources.map((roleAccessResource) => (
                <div key={roleAccessResource.id} className="flex items-center gap-1 border rounded-md p-1 bg-gray-50">
                  <Tooltip title={`URL Mappings: ${roleAccessResource.accessResource.urlMappings}`}>
                    <Tag
                      color="blue"
                      className="m-0"
                    >
                      {roleAccessResource.accessResource.name}
                    </Tag>
                  </Tooltip>
                  <Tooltip title={`Remove ${roleAccessResource.accessResource.name} access resource`}>
                    <Button
                      danger
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      className="h-6 w-6 flex items-center justify-center"
                      onClick={() => handleRemoveAccessResource(record, roleAccessResource.accessResource.name)}
                    />
                  </Tooltip>
                </div>
              ))
            ) : (
              <span className="text-gray-400">No access resources assigned</span>
            )}
          </div>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '15%',
      align: 'center',
      render: (_, record) => (
        <div className="flex gap-2 justify-center">
          <Tooltip title="Assign Access Resource">
            <Button
              type="primary"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => handleAssignAccessResource(record)}
            />
          </Tooltip>
          <Tooltip title="Delete Role">
            <Button
              danger
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteRole(record)}
            />
          </Tooltip>
        </div>
      ),
    },
  ]

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: "Roles",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by name"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          Create Role
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={filteredData}
        rowKey="id"
        loading={roleAccessResourcesQueries.isLoading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />

      {/* Assign Access Resource Modal */}
      <Modal
        title={`Assign Access Resource to ${selectedRole?.name}`}
        open={assignModalVisible}
        onCancel={() => {
          setAssignModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onAssignFinish}
        >
          <Form.Item
            label="Access Resource"
            name="accessResourceName"
            rules={[{ required: true, message: "Please select an access resource!" }]}
          >
            <Select placeholder="Select an access resource" showSearch>
              {accessResources.map((resource: AccessResource) => (
                <Select.Option key={resource.name} value={resource.name}>
                  {resource.name} - {resource.urlMappings}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item className="text-right mb-0">
            <Button
              type="primary"
              htmlType="submit"
              icon={<PlusOutlined />}
            >
              Assign Access Resource
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Role Modal */}
      <Modal
        title={`Edit Role: ${selectedRole?.name}`}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={onEditFinish}
        >
          <Form.Item
            label="Name"
            name="name"
            rules={[{ required: true, message: "Please enter a role name!" }]}
          >
            <Input placeholder="Role Name" />
          </Form.Item>
          <Form.Item
            label="Description"
            name="description"
          >
            <Input placeholder="Role Description" />
          </Form.Item>
          <Form.Item className="text-right mb-0">
            <Button
              type="primary"
              htmlType="submit"
              icon={<EditOutlined />}
            >
              Update Role
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* Create Role Modal */}
      <Modal
        title="Create New Role"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={onCreateFinish}
        >
          <Form.Item
            label="Name"
            name="name"
            rules={[{ required: true, message: "Please enter a role name!" }]}
          >
            <Input placeholder="Role Name" />
          </Form.Item>
          <Form.Item
            label="Description"
            name="description"
          >
            <Input placeholder="Role Description" />
          </Form.Item>
          <Form.Item className="text-right mb-0">
            <Button
              type="primary"
              htmlType="submit"
              icon={<PlusOutlined />}
            >
              Create Role
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
