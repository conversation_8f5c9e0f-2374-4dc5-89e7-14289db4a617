import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { Breadcrumb, Button, Input, Modal, Form, message } from 'antd'
import { HomeOutlined, PlusOutlined, EditOutlined } from '@ant-design/icons'
import { AccessResourcesTableComponent } from './-components/access-resources-table-component'
import { getAccessResourcesQueryOptions, getRolesQueryOptions } from '~/lib/queries/queryKeys'
import { queryClient } from '~/lib/utils/queryClient'
import { useSuspenseQueries, useMutation, useQueryClient } from '@tanstack/react-query'
import { createAccessResource, updateAccessResource } from '~/lib/queries/access-control'
import { handleError } from '~/lib/utils/error'
import type { AccessResource, CreateAccessResourceRequest } from '~/lib/types/access-control'

export const Route = createFileRoute('/_home/home/<USER>/')({
  component: RouteComponent,
  loader: async () => {
    await Promise.all([
      queryClient.ensureQueryData(getAccessResourcesQueryOptions()),
      queryClient.ensureQueryData(getRolesQueryOptions()),
    ]);
  },
})

function RouteComponent() {
  const [searchText, setSearchText] = useState('')
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [selectedAccessResource, setSelectedAccessResource] = useState<AccessResource | null>(null)
  const [createForm] = Form.useForm()
  const [editForm] = Form.useForm()
  const queryClient = useQueryClient()

  const [getAccessResourcesQuery, getRolesQuery] = useSuspenseQueries({
    queries: [getAccessResourcesQueryOptions(), getRolesQueryOptions()],
  })

  const accessResources = getAccessResourcesQuery.data
  const roles = getRolesQuery.data

  const { mutate: createAccessResourceItem } = useMutation({
    mutationFn: createAccessResource,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["access-resources"] });
      message.success("Access resource created successfully!");
      setCreateModalVisible(false);
      createForm.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create access resource");
    },
  });

  const { mutate: updateAccessResourceItem } = useMutation({
    mutationFn: updateAccessResource,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["access-resources"] });
      message.success("Access resource updated successfully!");
      setEditModalVisible(false);
      editForm.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update access resource");
    },
  });

  const handleEditAccessResource = (accessResource: AccessResource) => {
    setSelectedAccessResource(accessResource);
    editForm.setFieldsValue({
      name: accessResource.name,
      urlMappings: accessResource.urlMappings,
    });
    setEditModalVisible(true);
  };

  const onCreateFinish = (values: CreateAccessResourceRequest) => {
    createAccessResourceItem(values);
  };

  const onEditFinish = (values: { name: string; urlMappings: string }) => {
    if (selectedAccessResource) {
      updateAccessResourceItem({
        ...values,
        id: selectedAccessResource.id,
      });
    }
  };

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: "Access Resources",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by name"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          Create
        </Button>
      </div>
      <AccessResourcesTableComponent 
        accessResources={accessResources} 
        searchText={searchText} 
        roles={roles}
        onEdit={handleEditAccessResource}
      />

      {/* Create Access Resource Modal */}
      <Modal
        title="Create New Access Resource"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={onCreateFinish}
        >
          <Form.Item
            label="Name"
            name="name"
            rules={[{ required: true, message: "Please enter a resource name!" }]}
          >
            <Input placeholder="Resource Name" />
          </Form.Item>
          <Form.Item
            label="URL Mappings"
            name="urlMappings"
            rules={[{ required: true, message: "Please enter URL mappings!" }]}
          >
            <Input.TextArea 
              placeholder="URL Mappings" 
              autoSize={{ minRows: 3, maxRows: 6 }}
            />
          </Form.Item>
          <Form.Item className="text-right mb-0">
            <Button
              type="primary"
              htmlType="submit"
              icon={<PlusOutlined />}
            >
              Create Access Resource
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Access Resource Modal */}
      <Modal
        title={`Edit Access Resource: ${selectedAccessResource?.name}`}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={onEditFinish}
        >
          <Form.Item
            label="Name"
            name="name"
            rules={[{ required: true, message: "Please enter a resource name!" }]}
          >
            <Input placeholder="Resource Name" />
          </Form.Item>
          <Form.Item
            label="URL Mappings"
            name="urlMappings"
            rules={[{ required: true, message: "Please enter URL mappings!" }]}
          >
            <Input.TextArea 
              placeholder="URL Mappings" 
              autoSize={{ minRows: 3, maxRows: 6 }}
            />
          </Form.Item>
          <Form.Item className="text-right mb-0">
            <Button
              type="primary"
              htmlType="submit"
              icon={<EditOutlined />}
            >
              Update Access Resource
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
