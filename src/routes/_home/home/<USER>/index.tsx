import { Tabs, type TabsProps } from "antd";
import { createFileRoute } from "@tanstack/react-router";
import { useSuspenseQueries } from "@tanstack/react-query";
import { useState } from "react";
import { SearchBar } from "src/components/search-bar";
import { allBondDetailsQueryOptions } from "~/lib/queries/queryKeys";
import { TabComponent } from "./-components/tab-component";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute("/_home/home/<USER>/")({
  loader: async () => {
    await Promise.all([
      queryClient.ensureQueryData(allBondDetailsQueryOptions()),
    ]);
  },

  component: RouteComponent,
});

function RouteComponent() {
  const [searchText, setSearchText] = useState("");

  const [allBondsQuery] = useSuspenseQueries({
    queries: [allBondDetailsQueryOptions()],
  });

  const allBonds = allBondsQuery.data.bondDetails;

  const activeBonds = allBonds.filter((bond) => bond.isActive);

  const tabItems: TabsProps["items"] = [
    {
      key: "active",
      label: "Active",
      children: (
        <TabComponent
          bonds={activeBonds}
          searchText={searchText}
          route={"active"}
        />
      ),
    },
  ];

  return (
    <div className="p-6 space-y-4">
      <SearchBar searchText={searchText} setSearchText={setSearchText} />
      <Tabs defaultActiveKey="active" items={tabItems} />
    </div>
  );
}
