import {
  ContainerOutlined,
  HomeOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import {
  useMutation,
  useQueryClient,
  useSuspenseQueries,
} from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Breadcrumb, Button, Card, Col, Form, Input, message, Row, Select } from "antd";
import type { UpdateRuleRequest } from "~/gen/proto-models/PersonalizationAdmin";
import { updateRule } from "~/lib/queries/personalization";
import {
  getBannersHeadersQueryOptions,
  getRuleQueryOptions,
} from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";
import {
  DragDropContext,
  Droppable,
  Draggable,
  type DropResult,
} from "@hello-pangea/dnd";

export const Route = createFileRoute("/_home/home/<USER>/rules/$rule-id/")({
  component: RouteComponent,
  loader: async ({ params }) => {
    await Promise.all([
      queryClient.ensureQueryData(getRuleQueryOptions(params["rule-id"])),
      queryClient.ensureQueryData(getBannersHeadersQueryOptions()),
    ]);
  },
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { "rule-id": ruleID } = Route.useParams();
  const [getRuleQuery, getBannersHeadersQuery] = useSuspenseQueries({
    queries: [getRuleQueryOptions(ruleID), getBannersHeadersQueryOptions()],
  });

  const { mutate: updateRuleItem } = useMutation({
    mutationFn: updateRule,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getRuleQueryOptions(ruleID).queryKey,
      });
      message.success("Rule updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update Rule");
    },
  });

  const handleUpdate = (values: UpdateRuleRequest) => {
    const payload = {
      ...values,
      id: ruleID,
      outputA: JSON.stringify(values.outputA),
      outputB: JSON.stringify(values.outputB),
    };

    updateRuleItem(payload);
  };
  const rule = getRuleQuery.data;
  const bannersHeaders = getBannersHeadersQuery.data;
  const handleDragEnd = (result: DropResult, fieldName: 'outputA' | 'outputB') => {
    if (!result.destination) return;

    const currentValues = form.getFieldValue(fieldName) || [];
    const items = Array.from(currentValues);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Force form to update and trigger re-render
    form.setFieldValue(fieldName, items);
    form.setFieldsValue({ [fieldName]: items });
  };

  const renderDraggableSelect = (fieldName: 'outputA' | 'outputB', label: string, required = false) => {
    return (
      <Form.Item
        name={fieldName}
        label={<strong>{label}</strong>}
        rules={required ? [{ required: true, message: `Please enter ${label.toLowerCase()}` }] : []}
      >
        <Form.Item name={fieldName} noStyle>
          <Select placeholder={`Select ${label.toLowerCase()}`} mode="multiple">
            {bannersHeaders?.banners.map((banner) => (
              <Select.Option value={banner} key={banner}>
                {banner}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item shouldUpdate={(prev, curr) => prev[fieldName] !== curr[fieldName]} noStyle>
          {({ getFieldValue }) => {
            const selectedValues = getFieldValue(fieldName) || [];
            return selectedValues.length > 0 ? (
              <div className="mt-2">
                <DragDropContext onDragEnd={(result) => handleDragEnd(result, fieldName)}>
                  <Droppable droppableId={`${fieldName}-list`}>
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="space-y-1"
                      >
                        {selectedValues.map((value: string, index: number) => (
                          <Draggable key={`${fieldName}-${value}`} draggableId={`${fieldName}-${value}`} index={index}>
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className={`p-2 bg-gray-50 border rounded text-sm cursor-move ${
                                  snapshot.isDragging ? "shadow-lg bg-blue-50" : ""
                                }`}
                              >
                                {index + 1}. {value}
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              </div>
            ) : null;
          }}
        </Form.Item>
      </Form.Item>
    );
  };
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <ContainerOutlined />
                <span>Banners</span>
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>/rules">
                <QuestionCircleOutlined />
                <span>Rules</span>
              </Link>
            ),
          },
          {
            title: rule.name,
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleUpdate}
            initialValues={{
              ...rule,
              outputA: JSON.parse(rule.outputA),
              outputB: JSON.parse(rule.outputB),
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label={<strong>Name</strong>}
                  rules={[
                    { required: true, message: "Please enter banner name" },
                  ]}
                >
                  <Input placeholder="Enter banner name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="page"
                  label={<strong>Page</strong>}
                  rules={[
                    {
                      required: true,
                      message: "Please enter page name",
                    },
                  ]}
                >
                  <Input placeholder="Enter page name" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="featureFlag"
                  label={<strong>Feature Flag</strong>}
                  rules={[
                    { required: true, message: "Please enter banner name" },
                  ]}
                >
                  <Input placeholder="Enter banner name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="priority"
                  label={<strong>Priority</strong>}
                  rules={[
                    {
                      required: true,
                      message: "Please enter page name",
                    },
                  ]}
                >
                  <Input placeholder="Enter page name" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="expression"
              label={<strong>Expression</strong>}
              rules={[{ required: true, message: "Please enter expression" }]}
            >
              <Input.TextArea
                placeholder="Enter expression"
                autoSize={{ maxRows: 20 }}
              />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                {renderDraggableSelect('outputA', 'Output A', true)}
              </Col>
              <Col span={12}>
                {renderDraggableSelect('outputB', 'Output B')}
              </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Update
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
