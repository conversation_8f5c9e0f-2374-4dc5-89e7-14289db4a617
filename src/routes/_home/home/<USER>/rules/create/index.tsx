import {
  ContainerOutlined,
  HomeOutlined,
  QuestionCircleOutlined,
} from "@ant-design/icons";
import {
  useMutation,
  useQueryClient,
  useSuspenseQueries,
} from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import {
  Breadcrumb,
  Button,
  Card,
  Col,
  Form,
  Input,
  message,
  Row,
  Select,
} from "antd";
import type { CreateRuleRequest } from "~/gen/proto-models/PersonalizationAdmin";
import { createRule } from "~/lib/queries/personalization";
import { getBannersHeadersQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute("/_home/home/<USER>/rules/create/")({
  component: RouteComponent,
  loader: async () => {
    await queryClient.ensureQueryData(getBannersHeadersQueryOptions());
  },
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();

  const [getBannersHeadersQuery] = useSuspenseQueries({
    queries: [getBannersHeadersQueryOptions()],
  });

  const bannersHeaders = getBannersHeadersQuery.data;

  const { mutate: createRuleItem } = useMutation({
    mutationFn: createRule,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["rules"],
      });
      form.resetFields();
      message.success("Rule created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create Rule");
    },
  });

  const handleCreation = (values: CreateRuleRequest) => {
    const payload = {
      ...values,
      outputA: JSON.stringify(values.outputA),
      outputB: JSON.stringify(values.outputB),
    };
    createRuleItem(payload);
  };
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <ContainerOutlined />
                <span>Banners</span>
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>/rules">
                <QuestionCircleOutlined />
                <span>Rules</span>
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form form={form} layout="vertical" onFinish={handleCreation}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label={<strong>Name</strong>}
                  rules={[
                    { required: true, message: "Please enter banner name" },
                  ]}
                >
                  <Input placeholder="Enter banner name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="page"
                  label={<strong>Page</strong>}
                  rules={[
                    {
                      required: true,
                      message: "Please enter page name",
                    },
                  ]}
                >
                  <Input placeholder="Enter page name" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="featureFlag"
                  label={<strong>Feature Flag</strong>}
                  rules={[
                    { required: true, message: "Please enter banner name" },
                  ]}
                >
                  <Input placeholder="Enter banner name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="priority"
                  label={<strong>Priority</strong>}
                  rules={[
                    {
                      required: true,
                      message: "Please enter page name",
                    },
                  ]}
                >
                  <Input placeholder="Enter page name" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="expression"
              label={<strong>Expression</strong>}
              rules={[{ required: true, message: "Please enter expression" }]}
            >
              <Input.TextArea
                placeholder="Enter expression"
                autoSize={{ maxRows: 20 }}
              />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="outputA"
                  label={<strong>Output A</strong>}
                  rules={[{ required: true, message: "Please enter output A" }]}
                >
                  <Select placeholder="Select output A" mode="multiple">
                    {bannersHeaders?.banners.map((banner) => (
                      <Select.Option value={banner} key={banner}>
                        {banner}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="outputB" label={<strong>Output B</strong>}>
                  <Select placeholder="Select output B" mode="multiple">
                    {bannersHeaders?.banners.map((banner) => (
                      <Select.Option value={banner} key={banner}>
                        {banner}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
