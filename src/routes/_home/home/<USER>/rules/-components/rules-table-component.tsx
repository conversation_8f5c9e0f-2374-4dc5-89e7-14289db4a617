import type { RulesResponse, RuleSummary } from "~/gen/proto-models/PersonalizationAdmin";
import { Route } from "..";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { getRulesQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { Button, message, Popconfirm, Table, Tag, Tooltip, type TableProps } from "antd";
import { Link } from "@tanstack/react-router";
import { DeleteOutlined } from "@ant-design/icons";
import { deleteRule } from "~/lib/queries/personalization";

interface RulesTableComponentProps {
  rules: RulesResponse;
}

export function RulesTableComponent({ rules }: RulesTableComponentProps) {
  const { page } = Route.useSearch();
  const navigate = Route.useNavigate();

  const queryClient = useQueryClient();

  const { mutate: deleteRuleItem } = useMutation({
    mutationFn: deleteRule,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getRulesQueryOptions(page, "").queryKey });
      message.success("Rule deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete rule");
    },
  });

  const columns: TableProps<RuleSummary>["columns"] = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "30%",
      render: (text, record) => (
        <Link
          to="/home/<USER>/rules/$rule-id"
          params={{ "rule-id": record.id }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: "Page",
      dataIndex: "page",
      key: "page",
      width: "10%",
    },
    {
      title: "Priority",
      dataIndex: "priority",
      key: "priority",
      width: "5%",
    },
    {
      title: "Output A",
      dataIndex: "outputA",
      key: "outputA",
      width: "10%",
      render: (outputA) => {
        const outputs = JSON.parse(outputA || "[]");
        return outputs.map((output: string) => (
          <Tag key={output} color="blue">
            {output}
          </Tag>
        ));
      },
    },
    {
      title: "Output B",
      dataIndex: "outputB",
      key: "outputB",
      width: "10%",
      render: (outputB) => {
        const outputs = JSON.parse(outputB || "[]");
        return outputs.map((output: string) => (
          <Tag key={output} color="green">
            {output}
          </Tag>
        ));
      },
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: "5%",
      align: "center",
      render: (_, record) => {
        return (
          <Popconfirm
            title="Are you sure to delete this rule?"
            onConfirm={() => deleteRuleItem(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <Table
      bordered
      columns={columns}
      dataSource={rules.data}
      rowKey="id"
      pagination={{
        current: page,
        pageSize: 8,
        onChange: (newPage) => {
          navigate({ search: (prev) => ({ ...prev, page: newPage }) });
        },
        total: rules.pagination?.hasNextPage ? page * 8 + 1 : page * 8,
      }}
    />
  );
}
