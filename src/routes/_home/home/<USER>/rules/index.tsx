import {
  ContainerOutlined,
  HomeOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Breadcrumb, Button, Input } from "antd";
import { useState } from "react";
import z from "zod";
import { getRulesQueryOptions } from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { RulesTableComponent } from "./-components/rules-table-component";

const ruleSearchSchema = z.object({
  page: z.number().default(1),
  q: z.string().optional(),
});

export const Route = createFileRoute("/_home/home/<USER>/rules/")({
  validateSearch: ruleSearchSchema,
  loaderDeps: ({search: {page, q}}) => ({page, q}),
  loader: async ({ deps: { page, q } }) => {
    await queryClient.ensureQueryData(getRulesQueryOptions(page, q ?? ""));
  },
  component: RouteComponent,
});

function RouteComponent() {
  const { page, q } = Route.useSearch();
  const navigate = Route.useNavigate();
  const [searchText, setSearchText] = useState("");

  const [getRulesQuery] = useSuspenseQueries({
    queries: [getRulesQueryOptions(page, q ?? "")],
  });

  const rules = getRulesQuery.data;

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <ContainerOutlined />
                <span>Banners</span>
              </Link>
            ),
          },
          {
            title: "Rules",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by name"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={() =>
              navigate({ search: (prev) => ({ ...prev, q: searchText }) })
            }
          />
        </div>
        <div>
          <Button
            className="mr-2"
            icon={<ContainerOutlined />}
            onClick={() => navigate({ to: "/home/<USER>" })}
          >
            Banners
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate({ to: "/home/<USER>/rules/create" })}
          >
            Create
          </Button>
        </div>
      </div>
      <RulesTableComponent rules={rules} />
    </div>
  );
}
