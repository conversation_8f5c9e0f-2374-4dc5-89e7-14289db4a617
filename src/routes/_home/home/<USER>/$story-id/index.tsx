import { HomeOutlined, ReadOutlined } from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { Breadcrumb } from "antd";
import { getStoryQueryOptions } from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { FormElement } from "./-components/form-element";

export const Route = createFileRoute("/_home/home/<USER>/$story-id/")({
  component: RouteComponent,
  loader: async ({ params }) => {
    await queryClient.ensureQueryData(getStoryQueryOptions(params["story-id"]))
  },
});

function RouteComponent() {
  const { "story-id": storyID } = Route.useParams();

  const [getStoryQuery] = useSuspenseQueries({
    queries: [getStoryQueryOptions(storyID)],
  });

  const story = getStoryQuery.data;
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4 mt-4">
        <Breadcrumb
          items={[
            {
              title: (
                <Link to="/home">
                  <HomeOutlined />
                </Link>
              ),
            },
            {
              title: (
                <Link to="/home/<USER>">
                  <ReadOutlined />
                  <span>Stories</span>
                </Link>
              ),
            },
            {
              title: story.name,
            },
          ]}
        />
      </div>
      <FormElement story={story} />
    </div>
  );
}
