import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  <PERSON>ton,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
} from "antd";
import dayjs from "dayjs";
import { useMemo, useState } from "react";
import type { StoryResponse } from "~/gen/proto-models/PersonalizationAdmin";
import { updateStory } from "~/lib/queries/personalization";
import { getStoryQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

export const FormElement = ({ story }: { story: StoryResponse }) => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const [watchContent, setWatchContent] = useState(story.content);

  const { url, cta } = useMemo(() => {
    try {
      const content = JSON.parse(watchContent || "{}");
      return { url: content.url || null, cta: content.cta?.url || null };
    } catch {
      return { url: null, cta: null };
    }
  }, [watchContent]);

  const { mutate: updateStoryItem } = useMutation({
    mutationFn: updateStory,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getStoryQueryOptions(story.id).queryKey,
      });
      message.success("Story updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update story");
    },
  });

  const handleUpdate = (values: StoryResponse) => {
    const payload = {
      ...values,
      id: story.id,
      startTime: values.startTime
        ? dayjs(values.startTime).format("YYYY-MM-DDTHH:mm:ss")
        : "",
      endTime: values.endTime
        ? dayjs(values.endTime).format("YYYY-MM-DDTHH:mm:ss")
        : "",
      content: JSON.stringify(JSON.parse(values.content)),
    };

    updateStoryItem(payload);
  };

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleUpdate}
        initialValues={{
          ...story,
          content: JSON.stringify(JSON.parse(story.content), null, 4),
          startTime: story.startTime ? dayjs(story.startTime) : null,
          endTime: story.endTime ? dayjs(story.endTime) : null,
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="page"
              label={<strong>Page</strong>}
              rules={[{ required: true, message: "Please enter page" }]}
            >
              <Input placeholder="Enter page" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="name"
              label={<strong>Name</strong>}
              rules={[{ required: true, message: "Please enter name" }]}
            >
              <Input placeholder="Enter name" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="featureFlag" label={<strong>Feature Flag</strong>}>
              <Input placeholder="Enter feature flag" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="variant" label={<strong>Variant</strong>}>
              <Input placeholder="Enter variant" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="startTime" label={<strong>Start Time</strong>}>
              <DatePicker
                className="w-full"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="endTime" label={<strong>End Time</strong>}>
              <DatePicker
                className="w-full"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="priority"
              label={<strong>Priority</strong>}
              rules={[{ required: true, message: "Please enter priority" }]}
            >
              <Input placeholder="Enter priority" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="isLikeable"
              label={<strong>Is Likeable</strong>}
              valuePropName="checked"
              initialValue={false}
            >
              <Checkbox />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="isShareable"
              label={<strong>Is Shareable</strong>}
              valuePropName="checked"
              initialValue={false}
            >
              <Checkbox />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="content"
              label={<strong>Content</strong>}
              rules={[{ required: true, message: "Please enter content" }]}
            >
              <Input.TextArea
                placeholder="Enter content"
                autoSize={{ maxRows: 20 }}
                onChange={(e) => setWatchContent(e.target.value)}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <div className="flex flex-col items-center justify-center h-full">
              {url && <img src={url} width={300} height={300} />}
              {cta && <img src={cta} width={300} height={300} />}
            </div>
          </Col>
        </Row>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            Update
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};
