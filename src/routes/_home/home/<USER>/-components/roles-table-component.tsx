import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Popconfirm, message } from 'antd'
import type { TableProps } from 'antd'
import { Link } from '@tanstack/react-router'
import { DeleteOutlined } from '@ant-design/icons'
import type { Role, AllRoles } from '~/lib/types/access-control'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { deleteRole } from '~/lib/queries/access-control'
import { handleError } from '~/lib/utils/error'

interface Props {
  roles: AllRoles
  searchText: string
}

export function RolesTableComponent({ roles, searchText }: Props) {
  const queryClient = useQueryClient();

  const filteredData = roles.filter((role) =>
    role.name.toLowerCase().includes(searchText.toLowerCase())
  )

  const { mutate: deleteRoleItem } = useMutation({
    mutationFn: deleteRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      message.success("Role deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete role");
    },
  });
  
  const handleDelete = (id: string) => {
    deleteRoleItem(id);
  }

  const columns: TableProps<Role>['columns'] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '40%',
      render: (text, record) => (
        <Link
          to="/home/<USER>/$role-id"
          params={{ 'role-id': record.id }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: '50%',
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '10%',
      align: 'center',
      render: (_, record) => (
        <Popconfirm
          title="Are you sure to delete this role?"
          onConfirm={() => handleDelete(record.id)}
          okText="Yes"
          cancelText="No"
        >
          <Tooltip title="Delete">
            <Button danger icon={<DeleteOutlined />} />
          </Tooltip>
        </Popconfirm>
      ),
    },
  ]

  return (
    <Table
      columns={columns}
      dataSource={filteredData}
      rowKey="id"
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
      }}
    />
  )
}
