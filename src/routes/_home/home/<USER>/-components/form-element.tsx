import React, { useState } from "react";
import {
  Form,
  Input,
  Button,
  Row,
  Col,
  Typography,
  Select,
  message,
  InputNumber,
  Switch,
  Modal,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useForm } from "antd/es/form/Form";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createCollection,
  getAllCollectionExpressions,
} from "~/lib/queries/bonds-collection";
import { CreateBondCollectionRequest } from "~/gen/proto-models/BrokingCollection";
import {
  CollectionType,
  DisplayType,
} from "~/gen/proto-models/BrokingCollection";
import { handleError } from "~/lib/utils/error";

const { TextArea } = Input;

const { Title } = Typography;

const FormElement: React.FC<{
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ setModalOpen }) => {
  const [form] = useForm<CreateBondCollectionRequest>();
  const queryClient = useQueryClient();
  const [isManual, setIsManual] = useState(true);
  const [expressionName, setExpressionName] = useState<string | null>(null);
  const [isExpressionModalOpen, setExpressionModalOpen] = useState(false);

  const { data: expressionsData } = useQuery({
    queryKey: ["all-collection-expressions"],
    queryFn: getAllCollectionExpressions,
  });

  const handleExpressionChange = (expressionName: string) => {
    setExpressionName(expressionName);
    const selectedExpression = expressionsData?.collectionExpressions.find(
      (expr) => expr.name === expressionName
    );

    if (selectedExpression) {
      form.setFieldsValue({
        preFilterCriteria: selectedExpression.preFilterCriteria,
        postFilterCriteria: selectedExpression.postFilterCriteria,
        sortCriteria: selectedExpression.sortCriteria,
      });
    }
  };

  const expressions = expressionsData?.collectionExpressions.map(
    (expression) => ({
      label: expression.name,
      value: expression.name,
    })
  );

  const { mutate } = useMutation({
    mutationFn: createCollection,
    onSuccess: () => {
      message.success("Collection created successfully!");
      queryClient.invalidateQueries({ queryKey: ["collections"] });
      setModalOpen(false);
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create collection");
    },
  });

  const onFinish = (values: CreateBondCollectionRequest) => {
    const protoValues = CreateBondCollectionRequest.create(values);
    mutate(protoValues);
  };

  return (
    <>
      <div className="text-center text-gray-700 mb-6">
        <Title level={3}>Create Collection</Title>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        size="middle"
        initialValues={{
          displayType: DisplayType.DEFAULT,
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={<strong>Name</strong>}
              name="name"
              rules={[
                { required: true, message: "Please enter a collection name!" },
              ]}
            >
              <Input placeholder="Collection Name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={<strong>Title</strong>}
              name="title"
              rules={[
                { required: true, message: "Please enter a collection title!" },
              ]}
            >
              <Input placeholder="Collection Title" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label={<strong>Description</strong>} name="description">
          <Input.TextArea
            rows={2}
            placeholder="Collection Description"
            showCount
            maxLength={500}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label={<strong>Icon URL</strong>}
              name="iconUrl"
              rules={[{ type: "url", message: "Please enter a valid URL!" }]}
            >
              <Input placeholder="Icon URL" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={<strong>Collection Type</strong>}
              name="collectionType"
              rules={[
                { required: true, message: "Please select a collection type!" },
              ]}
            >
              <Select
                placeholder="Select Collection Type"
                onSelect={(value) => {
                  form.resetFields([
                    "preFilterCriteria",
                    "postFilterCriteria",
                    "sortCriteria",
                  ]);
                  setIsManual(value === CollectionType.MANUAL);
                }}
              >
                <Select.Option value={CollectionType.MANUAL}>
                  Manual
                </Select.Option>
                <Select.Option value={CollectionType.EXPRESSION}>
                  Expression
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Button
              type="default"
              onClick={() => setExpressionModalOpen(true)}
              className="mb-4 text-left"
              disabled={isManual}
            >
              Choose Expression
            </Button>
            <Modal
              open={isExpressionModalOpen}
              onCancel={() => setExpressionModalOpen(false)}
              title="Choose Expression"
              footer={null}
              destroyOnHidden
            >
              <Select
                options={expressions}
                onChange={handleExpressionChange}
                placeholder="Select expression"
                className="w-full"
                value={expressionName}
              />
            </Modal>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label={<strong>Sort Criteria</strong>}
              name="sortCriteria"
            >
              <Input placeholder="Sort Criteria" disabled={isManual} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label={<strong>Pre Filter Criteria</strong>}
              name="preFilterCriteria"
            >
              <TextArea placeholder="Pre Filter" disabled={isManual} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label={<strong>Post Filter Criteria</strong>}
              name="postFilterCriteria"
            >
              <TextArea placeholder="Post Filter" disabled={isManual} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label={<strong>Display Type</strong>} name="displayType">
              <Select placeholder="Select Display Type">
                <Select.Option value={DisplayType.DEFAULT}>
                  Default
                </Select.Option>
                <Select.Option value={DisplayType.MINIMUM_INVESTMENT}>
                  Minimum Investment
                </Select.Option>
                <Select.Option value={DisplayType.SHORT_TERM}>
                  Short Term
                </Select.Option>
                <Select.Option value={DisplayType.SHORT_TERM_XIRR}>
                  Short Term XIRR
                </Select.Option>
                <Select.Option value={DisplayType.SELLING_OUT_SOON}>
                  Selling Out Soon
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={<strong>Collection Item Limit</strong>}
              name="collectionItemLimit"
            >
              <InputNumber
                min={0}
                placeholder="Collection Item Limit"
                className="w-full"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="isActive" label={<strong>Is Active</strong>}>
              <Switch checkedChildren="Active" unCheckedChildren="In-Active" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item className="mt-4 text-right">
          <Button
            type="primary"
            htmlType="submit"
            icon={<PlusOutlined />}
            size="middle"
          >
            Create Collection
          </Button>
        </Form.Item>
      </Form>
    </>
  );
};

export default FormElement;
