import React from "react";
import {
  Form,
  Input,
  But<PERSON>,
  Row,
  Col,
  message,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useForm } from "antd/es/form/Form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createTag } from "~/lib/queries/bond-catalog";
import { handleError } from "~/lib/utils/error";

interface CreateTagRequest {
  name: string;
  color: string;
  bgColor: string;
  shimmerColor: string;
}

export function FormElement({ setModalOpen }: { setModalOpen: React.Dispatch<React.SetStateAction<boolean>> }) {
  const [form] = useForm<CreateTagRequest>();
  const queryClient = useQueryClient();

  const { mutate: createTagElement } = useMutation({
    mutationFn: async (tagData: CreateTagRequest) => {
      return createTag(tagData);
    },
    onSuccess: () => {
      message.success("Tag created successfully!");
      queryClient.invalidateQueries({ queryKey: ["all-tags"] });
      setModalOpen(false);
      form.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create tag");
    },
  });

  const onFinish = (values: CreateTagRequest) => {
    createTagElement(values);
  };

  return (
    <>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
      >
        <Form.Item
          label={<strong>Name</strong>}
          name="name"
          rules={[
            { required: true, message: "Please enter a tag name!" },
          ]}
        >
          <Input placeholder="Tag Name" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              label={<strong>Color</strong>}
              name="color"
              rules={[
                { required: true, message: "Please enter a color!" },
              ]}
            >
              <Input type="color" placeholder="#000000" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={<strong>Background Color</strong>}
              name="bgColor"
              rules={[
                { required: true, message: "Please enter a background color!" },
              ]}
            >
              <Input type="color" placeholder="#ffffff" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={<strong>Shimmer Color</strong>}
              name="shimmerColor"
              rules={[
                { required: true, message: "Please enter a shimmer color!" },
              ]}
            >
              <Input type="color" placeholder="#cccccc" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item className="mt-4 text-right">
          <Button
            type="primary"
            htmlType="submit"
            icon={<PlusOutlined />}
          >
            Create Tag
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}
