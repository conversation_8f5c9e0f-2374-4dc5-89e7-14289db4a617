import { useState } from 'react';
import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import ModalComponent from './modal-component';

const CreateBondCollection: React.FC = () => {
  const [isModalOpen, setModalOpen] = useState(false);
  const showModal = () => setModalOpen(true);

  return (
    <>
      <Button type="primary" icon={<PlusOutlined />} onClick={showModal}>
        Create
      </Button>
      <ModalComponent modalState={{ isModalOpen, setModalOpen }} />
    </>
  );
}

export default CreateBondCollection;