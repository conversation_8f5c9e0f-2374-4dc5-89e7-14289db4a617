import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Route } from "..";
import type { CollectionEntity, CollectionsResponseProto } from "~/gen/proto-models/CollectionV2";
import { Button, message, Popconfirm, Table, Tag, Tooltip, type TableProps } from "antd";
import { Link } from "@tanstack/react-router";
import { DeleteOutlined } from "@ant-design/icons";
import { deleteFdCollection } from "~/lib/queries/fd-collections";
import { handleError } from "~/lib/utils/error";
import { getFdCollectionsQueryOptions } from "~/lib/queries/queryKeys";

interface CollectionsTableComponentProps {
  fdCollections: CollectionsResponseProto;
}

export function CollectionsTableComponent({ fdCollections }: CollectionsTableComponentProps) {
  const { page } = Route.useSearch();
  const navigate = Route.useNavigate();

  const queryClient = useQueryClient();

  const { mutate: deleteCollection } = useMutation({
    mutationFn: deleteFdCollection,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getFdCollectionsQueryOptions(page, "").queryKey });
      message.success("Collection deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete collection");
    },
  });

  const columns: TableProps<CollectionEntity>["columns"] = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "60%",
      render: (text, record) => (
        <Link
          to="/home/<USER>/$fd-collection-id"
          params={{ "fd-collection-id": record.id }}
        >
          {text}
        </Link>
      ),
    },
    {
        title: "Pick Highest Interest Rate",
        dataIndex: "isPickHighestInterestRate",
        key: "isPickHighestInterestRate",
        width: "10%",
        align: "center",
        render: (text) => <div>{text ? <Tag color="green">Yes</Tag> : <Tag color="red">No</Tag>}</div>,
    },
    {
        title: "Sorted by Interest Rate",
        dataIndex: "isSortByInterestRate",
        key: "isSortByInterestRate",
        width: "10%",
        align: "center",
        render: (text) => <div>{text ? <Tag color="green">Yes</Tag> : <Tag color="red">No</Tag>}</div>,
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: "10%",
      align: "center",
      render: (_, record) => {
        return (
          <Popconfirm
            title="Are you sure to delete this collection?"
            onConfirm={() => deleteCollection(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <Table
      bordered
      columns={columns}
      dataSource={fdCollections.collections}
      rowKey="id"
      pagination={{
        current: page,
        showSizeChanger: true,
        showQuickJumper: true,
        onChange: (newPage) => {
          navigate({ search: (prev) => ({ ...prev, page: newPage }) });
        },
        total: fdCollections.pagination?.hasNextPage ? page * 8 + 1 : page * 8,
      }}
    />
  );
}
