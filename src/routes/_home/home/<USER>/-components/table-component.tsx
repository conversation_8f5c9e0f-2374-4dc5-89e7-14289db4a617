import { Link } from "@tanstack/react-router";
import { Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import type { BondIssuingInstitution } from "~/gen/proto-models/Catalog";
import { useMemo } from "react";
import QueryRenderer from "~/components/functional/query-renderer";
import { useQuery } from "@tanstack/react-query";
import { getAllISINS } from "~/lib/queries/bond-catalog";

interface Props {
  searchText: string;
}

export function TableComponent({ searchText }: Props) {
  const query = useQuery({
    queryKey: ["all-isins"],
    queryFn: () => getAllISINS(),
  });

  const columns: ColumnsType<BondIssuingInstitution> = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (text, record) => (
        <Link
          to="/home/<USER>/$isin-id"
          params={{ "isin-id": record.id }}
          search={{ isinID: record.id }}
        >
          {text}
        </Link>
      ),
    },
  ];

  return (
    <QueryRenderer query={query}>
      {(data) => {
        const filteredData = useMemo(() => {
          if (!data?.bondIssuingInstitutions) return [];
          return data.bondIssuingInstitutions.filter((institution) =>
            institution.name.toLowerCase().includes(searchText.toLowerCase())
          );
        }, [data, searchText]);
        return (
          <Table
            columns={columns}
            dataSource={filteredData}
            rowKey="id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
             }}
          />
        );
      }}
    </QueryRenderer>
  );
}