import {
  <PERSON><PERSON>,
  <PERSON>,
  DatePicker,
  message,
  Modal,
  Popconfirm,
  Row,
  Table,
  Tag,
  Tooltip,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import { Link } from "@tanstack/react-router";
import { useMemo, useState } from "react";
import dayjs from "dayjs";
import {
  repaymentFrequencyToJSON,
  type AllBond,
} from "~/gen/proto-models/Catalog";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateRecordDate, verifyRecordDate } from "~/lib/queries/bond-catalog";
import { handleError } from "~/lib/utils/error";
import { CheckOutlined, EditOutlined } from "@ant-design/icons";

type Props = {
  bonds: AllBond[];
  searchText: string;
  route: string;
};

export function TableComponent({ bonds, searchText }: Props) {
  const queryClient = useQueryClient();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newRecordDate, setNewRecordDate] = useState("");
  const [cashflowScheduleId, setCashflowScheduleId] = useState("");

  const filteredData = useMemo(() => {
    if (!bonds) return [];
    return bonds.filter(
      (bond) =>
        bond.isinCode.toLowerCase().includes(searchText.toLowerCase()) ||
        bond.name.toLowerCase().includes(searchText.toLowerCase())
    );
  }, [bonds, searchText]);

  const { mutate: verifyRecordDateItem } = useMutation({
    mutationFn: verifyRecordDate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["bond-cashflow-schedule"],
      });
      queryClient.invalidateQueries({
        queryKey: ["all-bond-details"],
      });
      message.success("Record date verified successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to verify record date");
    },
  });

  const { mutate: updateRecordDateItem } = useMutation({
    mutationFn: updateRecordDate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["bond-cashflow-schedule"],
      });
      queryClient.invalidateQueries({
        queryKey: ["all-bond-details"],
      });
      message.success("Record date updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update record date");
    },
  });

  const handleVerifyRecordDate = (
    bondDetailId: string,
    cashflowScheduleId: string
  ) => {
    verifyRecordDateItem({
      bondDetailId,
      cashflowScheduleId,
    });
  };

  const handleOk = () => {
    const formattedDate = newRecordDate
      ? dayjs(newRecordDate).format("YYYY-MM-DD")
      : "";
    updateRecordDateItem({ cashflowScheduleId, recordDate: formattedDate });
    setIsModalOpen(false);
    setNewRecordDate("");
    setCashflowScheduleId("");
  };

  const handleEditClick = (recordDate: string, scheduleId: string) => {
    setNewRecordDate(recordDate);
    setCashflowScheduleId(scheduleId);
    setIsModalOpen(true);
  };

  const columns: ColumnsType<AllBond> = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (text, record) => (
        <Link
          to={"/home/<USER>/active/$bond-id"}
          params={{ "bond-id": record.id }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: "ISIN Code",
      dataIndex: "isinCode",
      key: "isinCode",
      width: "10%",
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: "Maturity Date",
      dataIndex: "maturityDate",
      key: "maturityDate",
      width: "10%",
      render: (text) => <Tag color="grey">{text}</Tag>,
    },
    {
      title: "Record Date",
      dataIndex: "recordDate",
      key: "recordDate",
      width: "15%",
      align: "center",
      render: (_, record) => {
        if (record.recordDate === "") {
          return;
        }
        return (
          <Row gutter={16}>
            <Col span={12}>
              <Tag color="grey">{record.recordDate}</Tag>
            </Col>
            <Col span={12}>
              <Button
                icon={<EditOutlined />}
                size="small"
                onClick={() =>
                  handleEditClick(
                    record.recordDate,
                    record.bondCashflowScheduleId
                  )
                }
              />
            </Col>
          </Row>
        );
      },
    },
    {
      title: "Record Date Verified",
      dataIndex: "isRecordDateVerified",
      key: "isRecordDateVerified",
      width: "10%",
      render: (_, record) => {
        if (record.recordDate === "") {
          return;
        }
        return record.isRecordDateVerified ? (
          <Tag color="green">Yes</Tag>
        ) : (
          <>
            <Tag color="red">No</Tag>
            <Popconfirm
              title="Are you sure you want to verify this record date?"
              okText="Yes"
              cancelText="No"
              onConfirm={() =>
                handleVerifyRecordDate(record.id, record.bondCashflowScheduleId)
              }
            >
              <Tooltip title="Verify Record Date">
                <Button
                  icon={<CheckOutlined />}
                  size="small"
                  type="primary"
                  className="!bg-green-500 !border-green-500 hover:!bg-green-600 hover:!border-green-600"
                />
              </Tooltip>
            </Popconfirm>
          </>
        );
      },
    },
    {
      title: "Coupon Rate",
      dataIndex: "couponRate",
      key: "couponRate",
      width: "5%",
      render: (text) => <Tag>{text}</Tag>,
    },
    {
      title: "Coupon Frequency",
      dataIndex: "couponFrequency",
      key: "couponFrequency",
      width: "10%",
      render: (couponFrequency) => {
        return (
          <Tag color="red">{repaymentFrequencyToJSON(couponFrequency)}</Tag>
        );
      },
    },
    {
      title: "Per User Purchase Limit",
      dataIndex: "perUserPurchaseLimit",
      key: "perUserPurchaseLimit",
      width: "5%",
      render: (text) => <Tag>{text}</Tag>,
    },
    {
      title: "Include In Collection",
      dataIndex: "includeInCollection",
      key: "includeInCollection",
      width: "5%",
      render: (includeInCollection) =>
        includeInCollection ? (
          <Tag color="green">Yes</Tag>
        ) : (
          <Tag color="red">No</Tag>
        ),
    },
  ];

  return (
    <>
      <Table
        columns={columns}
        dataSource={filteredData}
        rowKey="id"
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />

      <Modal
        title="Edit Record Date"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsModalOpen(false)}>
            Cancel
          </Button>,
          <Popconfirm
            key="confirm"
            title="Are you sure you want to update this record date?"
            okText="Yes"
            cancelText="No"
            onConfirm={handleOk}
          >
            <Button type="primary">Update</Button>
          </Popconfirm>,
        ]}
      >
        <DatePicker
          placeholder="Select record date"
          value={newRecordDate ? dayjs(newRecordDate) : null}
          onChange={(date) => setNewRecordDate(date ? date.toISOString() : "")}
          format="YYYY-MM-DD"
          className="w-full"
        />
      </Modal>
    </>
  );
}
