import type { BannersResponse, BannerSummary } from "~/gen/proto-models/PersonalizationAdmin";
import { Route } from "..";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteBanner } from "~/lib/queries/personalization";
import { getBannersQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { Button, message, Popconfirm, Table, Tooltip, type TableProps } from "antd";
import { Link } from "@tanstack/react-router";
import { DeleteOutlined } from "@ant-design/icons";

interface BannersTableComponentProps {
  banners: BannersResponse;
}

export function BannersTableComponent({ banners }: BannersTableComponentProps) {
  const { page } = Route.useSearch();
  const navigate = Route.useNavigate();

  const queryClient = useQueryClient();

  const { mutate: deleteBannerItem } = useMutation({
    mutationFn: deleteBanner,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getBannersQueryOptions(page, "").queryKey });
      message.success("Banner deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete banner");
    },
  });

  const columns: TableProps<BannerSummary>["columns"] = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "90%",
      render: (text, record) => (
        <Link
          to="/home/<USER>/$banner-id"
          params={{ "banner-id": record.id }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: "10%",
      align: "center",
      render: (_, record) => {
        return (
          <Popconfirm
            title="Are you sure to delete this banner?"
            onConfirm={() => deleteBannerItem(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <Table
      bordered
      columns={columns}
      dataSource={banners.data}
      rowKey="id"
      pagination={{
        current: page,
        pageSize: 8,
        onChange: (newPage) => {
          navigate({ search: (prev) => ({ ...prev, page: newPage }) });
        },
        total: banners.pagination?.hasNextPage ? page * 8 + 1 : page * 8,
      }}
    />
  );
}
