import { Modal } from "antd";
import FormElement from "./form-element";

interface ModalComponentProps {
  modalState: {
    isModalOpen: boolean;
    setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  };
}

const ModalComponent: React.FC<ModalComponentProps> = ({ modalState }) => {
  const { isModalOpen, setModalOpen } = modalState;

  const handleCancel = () => setModalOpen(false);

  return (
    <Modal
      open={isModalOpen}
      onCancel={handleCancel}
      width="40%"
      footer={null}
      destroyOnHidden
    >
      <FormElement setModalOpen = {setModalOpen}/>
    </Modal>
  );
};

export default ModalComponent;