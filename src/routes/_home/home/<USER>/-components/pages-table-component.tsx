import { DeleteOutlined } from "@ant-design/icons";
import { But<PERSON>, message, Popconfirm, Table, Tooltip, type TableProps } from "antd";
import type { PagesResponse, PageSummary } from "~/gen/proto-models/PersonalizationAdmin";
import { Route } from "..";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deletePage } from "~/lib/queries/personalization";
import { handleError } from "~/lib/utils/error";
import { Link } from "@tanstack/react-router";

interface PagesTableComponentProps {
  pages: PagesResponse;
}

export function PagesTableComponent({ pages }: PagesTableComponentProps) {
  const { page } = Route.useSearch();
  const navigate = Route.useNavigate();

  const queryClient = useQueryClient();

  const { mutate: deletePageItem } = useMutation({
    mutationFn: deletePage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      message.success("Page deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete page");
    },
  });

  const columns: TableProps<PageSummary>["columns"] = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "90%",
      render: (text, record) => (
        <Link
          to="/home/<USER>/$page-id"
          params={{ "page-id": record.id }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: "10%",
      align: "center",
      render: (_, record) => {
        return (
          <Popconfirm
            title="Are you sure to delete this page?"
            onConfirm={() => deletePageItem(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <Table
      bordered
      columns={columns}
      dataSource={pages.data}
      rowKey="id"
      pagination={{
        current: page,
        showSizeChanger: true,
        showQuickJumper: true,
        onChange: (newPage) => {
          navigate({ search: (prev) => ({ ...prev, page: newPage }) });
        },
        total: pages.pagination?.hasNextPage ? page * 8 + 1 : page * 8,
      }}
    />
  );
}
