import { Tabs, type TabsProps } from "antd";
import type { AllBond } from "~/gen/proto-models/Catalog";
import { TableComponent } from "./table-component";

export function TabComponent({bonds, searchText, route}: { bonds: AllBond[]; searchText: string; route: string; }) {

    const liveBonds = bonds.filter((bond)=> bond.investabilityStatus === 1 && !bond.isSoldOut);
    const soldOutBonds = bonds.filter((bond)=> bond.investabilityStatus === 2 || bond.isSoldOut);
    const comingSoonBonds = bonds.filter((bond)=> bond.investabilityStatus === 3 && !bond.isSoldOut);
    const inactiveBonds = bonds.filter((bond)=> bond.investabilityStatus === 4 && !bond.isSoldOut);

  const tabItems: TabsProps["items"] = [
    {
      key: "live",
      label: "Live",
      children: (
        <TableComponent
          bonds={liveBonds}
          searchText={searchText}
          route={route}
        />
      ),
    },
    {
      key: "sold_out",
      label: "Sold Out",
      children: (
        <TableComponent
          bonds={soldOutBonds}
          searchText={searchText}
          route={route}
        />
      ),
    },
    {
      key: "comming_soon",
      label: "Comming Soon",
      children: (
        <TableComponent
          bonds={comingSoonBonds}
          searchText={searchText}
          route={route}
        />
      ),
    },
    {
      key: "inactive",
      label: "In-Active",
      children: (
        <TableComponent
          bonds={inactiveBonds}
          searchText={searchText}
          route={route}
        />
      ),
    },
  ];

  return <Tabs defaultActiveKey="live" items={tabItems} />;
}
