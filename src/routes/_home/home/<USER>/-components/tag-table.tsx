import { useQuery } from "@tanstack/react-query";
import { Table, type TableProps } from "antd";
import { useMemo } from "react";
import QueryRenderer from "~/components/functional/query-renderer";
import { getAllTags } from "~/lib/queries/bond-catalog";
import type { Tag } from "~/gen/proto-models/Catalog";
import { Link } from "@tanstack/react-router";

export function TagTable({ searchText }: { searchText: string }) {
  const query = useQuery({
    queryKey: ["all-tags"],
    queryFn: getAllTags,
  });

  const columns: TableProps<Tag>["columns"] = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (_, record) => (
        <Link to="/home/<USER>/$tag-id" params={{ "tag-id": record.id }}>
          {record.name}
        </Link>
      ),
    },
    {
      title: "Color",
      dataIndex: "color",
      key: "color",
    },
    {
      title: "Background Color",
      dataIndex: "bgColor",
      key: "bgColor",
    },
    {
      title: "Shimmer Color",
      dataIndex: "shimmerColor",
      key: "shimmerColor",
    },
  ];

  return (
    <QueryRenderer query={query}>
      {(data) => {
        const filteredData = useMemo(() => {
          if (!data.tag) return [];
          return data.tag.filter((tag) =>
            tag.name.toLowerCase().includes(searchText.toLowerCase())
          );
        }, [data, searchText]);

        return (
          <Table
            bordered
            columns={columns}
            dataSource={filteredData}
            rowKey="id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
            }}
          />
        );
      }}
    </QueryRenderer>
  );
}
