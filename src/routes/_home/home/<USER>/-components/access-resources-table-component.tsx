import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Popconfirm, message } from 'antd'
import type { TableProps } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import type { AccessResource, AllAccessResources, Role } from '~/lib/types/access-control'
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query'
import { deleteAccessResource, getAccessResourceRoles } from '~/lib/queries/access-control'
import { handleError } from '~/lib/utils/error'

interface Props {
  accessResources: AllAccessResources
  searchText: string
  roles: Role[]
  onEdit: (accessResource: AccessResource) => void
}

export function AccessResourcesTableComponent({ accessResources, searchText, onEdit }: Props) {
  const queryClient = useQueryClient();

  const filteredData = accessResources.filter((resource) =>
    resource.name.toLowerCase().includes(searchText.toLowerCase())
  )

  // Get roles for each access resource
  const accessResourceRolesQueries = useQuery({
    queryKey: ['all-access-resource-roles', filteredData.map(r => r.name)],
    queryFn: async () => {
      const results = await Promise.all(
        filteredData.map(async (accessResource) => {
          try {
            const accessResourceRoles = await getAccessResourceRoles(accessResource.name)
            return { accessResourceName: accessResource.name, roles: accessResourceRoles }
          } catch {
            return { accessResourceName: accessResource.name, roles: [] }
          }
        })
      )
      return results.reduce((acc, curr) => {
        acc[curr.accessResourceName] = curr.roles
        return acc
      }, {} as Record<string, any[]>)
    },
    enabled: filteredData.length > 0,
  })

  const { mutate: deleteAccessResourceItem } = useMutation({
    mutationFn: deleteAccessResource,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["access-resources"] });
      queryClient.invalidateQueries({ queryKey: ['all-access-resource-roles'] });
      message.success("Access resource deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete access resource");
    },
  });
  
  const handleDelete = (id: string) => {
    deleteAccessResourceItem(id);
  }

  const columns: TableProps<AccessResource>['columns'] = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: '25%',
      render: (text, record) => (
        <span
          className="cursor-pointer text-blue-600 hover:text-blue-800"
          onClick={() => onEdit(record)}
        >
          {text}
        </span>
      ),
    },
    {
      title: 'URL Mappings',
      dataIndex: 'urlMappings',
      key: 'urlMappings',
      width: '30%',
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '10%',
      align: 'center',
      render: (_, record) => (
        <Popconfirm
          title="Are you sure to delete this access resource?"
          onConfirm={() => handleDelete(record.id)}
          okText="Yes"
          cancelText="No"
        >
          <Tooltip title="Delete">
            <Button danger size="small" icon={<DeleteOutlined />} />
          </Tooltip>
        </Popconfirm>
      ),
    },
  ]

  return (
    <Table
      columns={columns}
      dataSource={filteredData}
      rowKey="id"
      loading={accessResourceRolesQueries.isLoading}
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
      }}
    />
  )
}
