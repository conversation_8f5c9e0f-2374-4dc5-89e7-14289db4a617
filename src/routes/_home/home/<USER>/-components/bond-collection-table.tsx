import React, { useMemo } from 'react';
import { Table, Button, Space, Popconfirm, message, Tooltip, Tag } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Link } from '@tanstack/react-router';
import type { ColumnsType } from 'antd/es/table';
import { deleteBondCollection } from '~/lib/queries/bonds-collection';
import { CollectionType, type AllCollectionsResponse, type AllCollectionsResponse_Collection } from '~/gen/proto-models/BrokingCollection';
import { handleError } from '~/lib/utils/error';

interface BondCollectionTableProps {
  searchText: string;
  allBondCollections: AllCollectionsResponse;
  filterActive?: boolean;
}

const BondCollectionTable: React.FC<BondCollectionTableProps> = ({ 
  searchText, 
  allBondCollections, 
  filterActive 
}) => {
  const queryClient = useQueryClient();

  const { mutate: deleteCollection } = useMutation({
    mutationFn: deleteBondCollection,
    onSuccess: () => {
      message.success('Collection deleted');
      queryClient.invalidateQueries({ queryKey: ['collections'] });
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete collection");
    },
  });

  const handleDelete = (id: string) => {
    deleteCollection(id);
  };

  const columns: ColumnsType<AllCollectionsResponse_Collection> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Link to="/home/<USER>/edit/$bond-collection-id" params={{ "bond-collection-id": record.id }}>{text}</Link>
      ),
    },
    {
      title: 'Collection Type',
      dataIndex: 'collectionType',
      key: 'collectionType',
      render: (collectionType) => collectionType === CollectionType.MANUAL ? <Tag color="red">Manual</Tag> : <Tag color="blue">Expression</Tag>,
    },
    {
      title: 'Active',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => isActive ? <Tag color="green">Active</Tag> : <Tag color="red">Inactive</Tag>,
    },
    {
      title: 'Action',
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <Space size="middle">
          <Popconfirm
            title="Are you sure to delete this collection?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const filteredData = useMemo(() => {
    if (!allBondCollections?.collections) return [];
    return allBondCollections.collections
      .filter((item) => {
        const matchesSearch = item.name.toLowerCase().includes(searchText.toLowerCase());
        const matchesStatus = filterActive === undefined || item.isActive === filterActive;
        return matchesSearch && matchesStatus;
      })
      .map((item) => ({ ...item, key: item.id }));
  }, [allBondCollections, searchText, filterActive]);

  return (
    <>
      <Table<AllCollectionsResponse_Collection>
        bordered
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
         }}
        columns={columns}
        dataSource={filteredData}
      />
    </>
  );
};

export default BondCollectionTable;
