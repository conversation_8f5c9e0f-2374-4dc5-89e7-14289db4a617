import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Button,
  Checkbox,
  Form,
  DatePicker,
  Input,
  Select,
  Alert,
  Row,
  Col,
} from "antd";
import dayjs from "dayjs";

import {
  BankDownTime,
  DownTimeApplicability,
  WidgetConfiguration_ImpactedWidget,
  widgetConfiguration_ImpactedWidgetToJSON,
  WidgetConfiguration_PositionType,
  widgetConfiguration_PositionTypeToJSON,
} from "~/gen/proto-models/BankDownTime";
import { addOrUpdateBankDownTime } from "~/lib/queries/bankDownTime";
import {
  bankDownTimeQueryOptions,
  banksDownTimeQueryOptions,
} from "~/lib/queries/queryKeys";
import { Loader } from "~/components/loader";
import { handleError } from "~/lib/utils/error";

interface WidgetPositionFormData {
  impacted_widget: string;
  positionType: string;
  position?: number;
}

type FieldType = {
  id: string;
  created_by?: string;
  updated_by?: string;
  is_recurring?: boolean;
  bank_id?: string;
  applicability?: string;
  down_time_start?: number;
  down_time_end?: number;
  widget_configuration?: string;
};
interface BankNameAndId {
  bankId: string;
  bankName: string;
}
interface EditProps {
  id: string;
  BanksNameAndId: BankNameAndId[];
  onClose?: () => void;
}

export function Edit({ id, BanksNameAndId, onClose }: EditProps) {
  const [form] = Form.useForm();
  const { Option } = Select;
  const queryClient = useQueryClient();
  const bankDownTimeData = useQuery(bankDownTimeQueryOptions(id));

  const bankUpdate = useMutation({
    mutationFn: addOrUpdateBankDownTime,
    onSuccess: (data) => {
      onClose?.();
      queryClient.invalidateQueries({
        queryKey: bankDownTimeQueryOptions(data.id).queryKey,
      });
      queryClient.invalidateQueries({
        queryKey: banksDownTimeQueryOptions().queryKey,
      });
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update bank downtime");
    },
  });

  if (bankDownTimeData.isPending) {
    return <Loader />;
  }

  const handleSubmit = async () => {
    const widgetPositions = form.getFieldValue("widgetPositions");
    const transformedWidgetPositions = widgetPositions.map(
      (item: WidgetPositionFormData) => ({
        impactedWidget:
          WidgetConfiguration_ImpactedWidget[
            item.impacted_widget.toUpperCase() as keyof typeof WidgetConfiguration_ImpactedWidget
          ],
        positionType:
          WidgetConfiguration_PositionType[
            item.positionType.toUpperCase() as keyof typeof WidgetConfiguration_PositionType
          ],
        position: parseInt(item.position?.toString() || "0") || 0,
      })
    );
    const selectedBankName = form.getFieldValue("bank_name");
    const selectedBankId = BanksNameAndId.find(
      (bank) => bank.bankName === selectedBankName
    )?.bankId;

    const AddOrUpdatebankDownTime = BankDownTime.create({
      id: form.getFieldValue("id"),
      bankId: selectedBankId,
      applicability:
        DownTimeApplicability[
          form
            .getFieldValue("applicability")
            .toUpperCase() as keyof typeof DownTimeApplicability
        ],
      isRecurring: form.getFieldValue("is_recurring"),
      widgetConfiguration:
        transformedWidgetPositions.length > 0
          ? {
              widgetPositions: transformedWidgetPositions,
            }
          : undefined,
      downTimeStart: form.getFieldValue("down_time_start")?.valueOf(),
      downTimeEnd: form.getFieldValue("down_time_end")?.valueOf(),
    });
    bankUpdate.mutate(AddOrUpdatebankDownTime);
  };
  return (
    <div>
      <Form form={form} layout="vertical">
        <Form.Item
          name="bank_name"
          label="Banks"
          rules={[
            {
              required: true,
              type: "string",
            },
          ]}
          initialValue={bankDownTimeData.data?.bankDownTime?.bankName}
        >
          <Select placeholder="Please select Bank">
            {BanksNameAndId.map((item) => (
              <Option value={item.bankName} key={item.bankName}>
                {item.bankName}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item<FieldType>
          name="id"
          hidden
          layout="vertical"
          initialValue={bankDownTimeData.data?.bankDownTime?.id}
        >
          <Input></Input>
        </Form.Item>
        <Row gutter={8}>
          <Col span={8}>
            <Form.Item
              name="applicability"
              label="Applicability"
              rules={[{ required: true }]}
              initialValue={
                bankDownTimeData.data?.bankDownTime?.applicability
                  ? DownTimeApplicability[
                      bankDownTimeData.data?.bankDownTime?.applicability
                    ]
                  : undefined
              }
            >
              <Select allowClear>
                <Option value="ntb_only">Ntb only</Option>
                <Option value="etb_only">Etb Only</Option>
                <Option value="both">Both</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item<FieldType>
              label="Date Time Start"
              name="down_time_start"
              rules={[
                { required: true, message: "Please select date and time!" },
              ]}
              initialValue={
                bankDownTimeData.data?.bankDownTime?.downTimeStart
                  ? dayjs(bankDownTimeData.data?.bankDownTime?.downTimeStart)
                  : ""
              }
            >
              <DatePicker showTime format="YYYY-MM-DD HH:mm A" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item<FieldType>
              label="Down Time End"
              name="down_time_end"
              rules={[
                { required: true, message: "Please select end date and time!" },
              ]}
              initialValue={
                bankDownTimeData.data?.bankDownTime?.downTimeEnd
                  ? dayjs(bankDownTimeData.data?.bankDownTime?.downTimeEnd)
                  : ""
              }
            >
              <DatePicker showTime format="YYYY-MM-DD HH:mm A" />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item<FieldType>
          name="is_recurring"
          valuePropName="checked"
          label={null}
          layout="vertical"
          initialValue={bankDownTimeData.data?.bankDownTime?.isRecurring}
        >
          <Checkbox>Recurring</Checkbox>
        </Form.Item>
        <Form.List
          name="widgetPositions"
          initialValue={bankDownTimeData.data?.bankDownTime?.widgetConfiguration?.widgetPositions?.map(
            (position) => ({
              impacted_widget: widgetConfiguration_ImpactedWidgetToJSON(
                position.impactedWidget
              ),
              positionType: widgetConfiguration_PositionTypeToJSON(
                position.positionType
              ),
              position: position.position,
            })
          )}
        >
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Form.Item
                  key={key}
                  noStyle
                  shouldUpdate={(prevValues, currentValues) => {
                    const prevPositionType =
                      prevValues?.widgetPositions?.[name]?.positionType;
                    const currentPositionType =
                      currentValues?.widgetPositions?.[name]?.positionType;
                    return prevPositionType !== currentPositionType;
                  }}
                >
                  {({ getFieldValue }) => {
                    const positionType = getFieldValue([
                      "widgetPositions",
                      name,
                      "positionType",
                    ]);
                    const showPositionField =
                      positionType === "SPECIFIC" ||
                      positionType === "specific";

                    return (
                      <div>
                        <Row gutter={8} align="bottom">
                          <Col span={showPositionField ? 7 : 10}>
                            <Form.Item
                              {...restField}
                              name={[name, "impacted_widget"]}
                              label="Impacted Widget"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select an impacted widget",
                                },
                              ]}
                            >
                              <Select placeholder="Widget Configuration">
                                <Option value="carousel">CAROUSEL</Option>
                                <Option value="collections">COLLECTIONS</Option>
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={showPositionField ? 7 : 11}>
                            <Form.Item
                              {...restField}
                              name={[name, "positionType"]}
                              label="Position Type"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select a position type",
                                },
                              ]}
                            >
                              <Select placeholder="Position Type">
                                <Option value="last">Last</Option>
                                <Option value="disappear">Disappear</Option>
                                <Option value="specific">Specific</Option>
                              </Select>
                            </Form.Item>
                          </Col>
                          {showPositionField && (
                            <Col span={7}>
                              <Form.Item
                                {...restField}
                                name={[name, "position"]}
                                label="Position"
                                rules={[
                                  {
                                    required: true,
                                    message: "Please enter a position",
                                  },
                                ]}
                                normalize={(value) => {
                                  if (value) {
                                    return parseInt(value, 10) ?? 0;
                                  }
                                  return value;
                                }}
                              >
                                <Input
                                  type="number"
                                  placeholder="Enter position"
                                  min={0}
                                />
                              </Form.Item>
                            </Col>
                          )}
                          <Col
                            span={3}
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <MinusCircleOutlined
                              onClick={() => remove(name)}
                              style={{ marginBottom: "24px" }}
                            />
                          </Col>
                        </Row>
                      </div>
                    );
                  }}
                </Form.Item>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  disabled={fields.length >= 2}
                  block
                  icon={<PlusOutlined />}
                >
                  Add Widget Position
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>
        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue }) => {
            const widgetPositions = getFieldValue("widgetPositions") || [];
            return widgetPositions.length > 2 ? (
              <Alert
                message="You cannot add more than 2 widget positions"
                type="warning"
                showIcon
                closable
              />
            ) : null;
          }}
        </Form.Item>
        <Button type="primary" htmlType="submit" onClick={handleSubmit}>
          Submit
        </Button>
      </Form>
    </div>
  );
}
