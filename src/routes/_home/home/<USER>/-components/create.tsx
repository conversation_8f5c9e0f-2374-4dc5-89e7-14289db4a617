import {
  Button,
  Checkbox,
  Form,
  Input,
  Select,
  DatePicker,
  Alert,
  Row,
  Col,
} from "antd";
import {
  BankDownTime,
  DownTimeApplicability,
} from "~/gen/proto-models/BankDownTime";
import { addOrUpdateBankDownTime } from "~/lib/queries/bankDownTime";
import { banksDownTimeQueryOptions } from "~/lib/queries/queryKeys";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface WidgetPositionFormData {
  impacted_widget: string;
  positionType: string;
  position?: string;
}
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import {
  WidgetConfiguration_ImpactedWidget,
  WidgetConfiguration_PositionType,
} from "~/gen/proto-models/BankDownTime";
import { handleError } from "~/lib/utils/error";

type FieldType = {
  created_by?: string;
  updated_by?: string;
  is_recurring?: boolean;
  bank_id?: string;
  applicability?: string;
  down_time_start?: number;
  down_time_end?: number;
  widgetPositions?: Array<{
    impacted_widget: string;
    positionType: string;
    position: string;
  }>;
};

interface BankNameAndId {
  bankId: string;
  bankName: string;
}

interface AppProps {
  data: BankNameAndId[];
  onClose?: () => void;
}

export function Create({ data, onClose }: AppProps) {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const bankCreate = useMutation({
    mutationFn: addOrUpdateBankDownTime,
    onSuccess: () => {
      onClose?.();
      queryClient.invalidateQueries({
        queryKey: banksDownTimeQueryOptions().queryKey,
      });
      form.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create bank downtime");
    },
  });
  const handleSubmit = async () => {
    const widgetPositions = form.getFieldValue("widgetPositions");
    const transformedWidgetPositions = widgetPositions.map(
      (item: WidgetPositionFormData) => ({
        impactedWidget:
          WidgetConfiguration_ImpactedWidget[
            item.impacted_widget.toUpperCase() as keyof typeof WidgetConfiguration_ImpactedWidget
          ],
        positionType:
          WidgetConfiguration_PositionType[
            item.positionType.toUpperCase() as keyof typeof WidgetConfiguration_PositionType
          ],
        position: parseInt(item.position || "0") || 0,
      })
    );
    const selectedBankName = form.getFieldValue("bank_name");
    const selectedBankId = data?.find(
      (bank) => bank.bankName === selectedBankName
    )?.bankId;
    const AddOrUpdatebankDownTime = BankDownTime.create({
      bankId: selectedBankId,
      applicability:
        DownTimeApplicability[
          form
            .getFieldValue("applicability")
            .toUpperCase() as keyof typeof DownTimeApplicability
        ],
      isRecurring: form.getFieldValue("is_recurring"),
      widgetConfiguration:
        transformedWidgetPositions.length > 0
          ? {
              widgetPositions: transformedWidgetPositions,
            }
          : undefined,
      downTimeStart: form.getFieldValue("down_time_start")?.valueOf(),
      downTimeEnd: form.getFieldValue("down_time_end")?.valueOf(),
    });
    bankCreate.mutate(AddOrUpdatebankDownTime);
  };

  const { Option } = Select;
  return (
    <div>
      <Form form={form} layout="vertical">
        <Form.Item
          name="bank_name"
          label="Banks"
          rules={[
            {
              required: true,
              message: "Please select your Bank Name",
            },
          ]}
        >
          <Select placeholder="Please select bank name">
            {data.map((item) => (
              <Option value={item.bankName} key={item.bankName}>
                {item.bankName}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Row gutter={8}>
          <Col span={8}>
            <Form.Item
              name="applicability"
              label="Applicability"
              rules={[{ required: true }]}
            >
              <Select allowClear>
                <Option value="ntb_only">Ntb only</Option>
                <Option value="etb_only">Etb only</Option>
                <Option value="both">Both</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item<FieldType>
              label="Date & Time"
              name="down_time_start"
              rules={[
                { required: true, message: "Please select date and time!" },
              ]}
            >
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm A"
                name="down_time_start"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item<FieldType>
              label="End Date & Time"
              name="down_time_end"
              rules={[
                { required: true, message: "Please select end date and time!" },
              ]}
            >
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm A"
                name="down_time_end"
              />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item<FieldType>
          name="is_recurring"
          valuePropName="checked"
          label={null}
        >
          <Checkbox>Recurring</Checkbox>
        </Form.Item>
        <Form.List name="widgetPositions">
          {(fields, { add, remove }) => (
            <div>
              {fields.map(({ key, name, ...restField }) => (
                <Form.Item
                  key={key}
                  noStyle
                  shouldUpdate={(prevValues, currentValues) => {
                    const prevPositionType =
                      prevValues?.widgetPositions?.[name]?.positionType;
                    const currentPositionType =
                      currentValues?.widgetPositions?.[name]?.positionType;
                    return prevPositionType !== currentPositionType;
                  }}
                  className="d-flex align-items-center"
                >
                  {({ getFieldValue }) => {
                    const positionType = getFieldValue([
                      "widgetPositions",
                      name,
                      "positionType",
                    ]);
                    const showPositionField = positionType === "specific";

                    return (
                      <>
                        <Row gutter={8} align="bottom">
                          <Col span={showPositionField ? 7 : 10}>
                            <Form.Item
                              {...restField}
                              name={[name, "impacted_widget"]}
                              label="Impacted Widget"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select an impacted widget",
                                },
                              ]}
                            >
                              <Select placeholder="Widget Configuration">
                                <Option value="carousel">Carousel</Option>
                                <Option value="collections">Collections</Option>
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={showPositionField ? 7 : 11}>
                            <Form.Item
                              {...restField}
                              name={[name, "positionType"]}
                              label="Position Type"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select a position type",
                                },
                              ]}
                            >
                              <Select placeholder="Position Type">
                                <Option value="last">Last</Option>
                                <Option value="disappear">Disappear</Option>
                                <Option value="specific">Specific</Option>
                              </Select>
                            </Form.Item>
                          </Col>
                          {showPositionField && (
                            <Col span={7}>
                              <Form.Item
                                {...restField}
                                name={[name, "position"]}
                                label="Position"
                                rules={[
                                  {
                                    required: true,
                                    message: "Please enter a position",
                                  },
                                ]}
                                normalize={(value) => {
                                  if (value) {
                                    return parseInt(value, 10) ?? 0;
                                  }
                                  return value;
                                }}
                              >
                                <Input
                                  type="number"
                                  placeholder="Enter position"
                                  min={0}
                                />
                              </Form.Item>
                            </Col>
                          )}
                          <Col span={3} className="flex">
                            <MinusCircleOutlined onClick={() => remove(name)} />
                          </Col>
                        </Row>
                      </>
                    );
                  }}
                </Form.Item>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  disabled={fields.length >= 2}
                  block
                  icon={<PlusOutlined />}
                >
                  Add Widget Position
                </Button>
              </Form.Item>
            </div>
          )}
        </Form.List>
        <Form.Item>
          {({ getFieldValue }) => {
            const widgetPositions = getFieldValue("widgetPositions");
            return widgetPositions.length > 2 ? (
              <Alert
                message="You cannot add more than 2 widget positions"
                type="warning"
                showIcon
                closable
              />
            ) : null;
          }}
        </Form.Item>
        <Button type="primary" htmlType="submit" onClick={handleSubmit}>
          Submit
        </Button>
      </Form>
    </div>
  );
}
