import React, { useState } from "react";
import { Space, Table, type TableColumnsType, Modal } from "antd";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import {
  BankDetail,
  DownTimeApplicability,
  BankDownTimesField,
} from "~/gen/proto-models/BankDownTime";
import { deleteBankDownTime } from "~/lib/queries/bankDownTime";
import { Edit } from "./edit";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import dayjs from "dayjs";
import { banksDownTimeQueryOptions} from "~/lib/queries/queryKeys";
interface DataType {
  key: React.Key;
  id: string;
  bankId: string;
  applicability: DownTimeApplicability;
  downTimeStart: number;
  downTimeEnd: number;
  isRecurring: boolean;
  bankName: string;
}

interface ColumnsComponentProps {
  banksData: BankDownTimesField[];
  bankFieldsAndName: BankDetail[];
}

export function ColumnsComponent({
  banksData,
  bankFieldsAndName,
}: ColumnsComponentProps) {
  const tableData: DataType[] = banksData.map((item) => ({
    key: item.id,
    id: item.id,
    bankId: item.bankId,
    applicability: item.applicability,
    downTimeStart: item.downTimeStart,
    downTimeEnd: item.downTimeEnd,
    isRecurring: item.isRecurring,
    bankName: item.bankName,
  }));

  const banks: string[] = banksData.map((item) => item.bankName);
  const uniqueBanks = Array.from(new Set(banks));
  const queryClient = useQueryClient();
  const [editingRecordId, setEditingRecordId] = useState<string | null>(null);
  const deleteBankDownTimeResponse = useMutation({
    mutationFn:deleteBankDownTime,
    onSuccess: () => {
      queryClient.invalidateQueries({queryKey:banksDownTimeQueryOptions().queryKey});
    },
  });
  const columns: TableColumnsType<DataType> = [
    {
      title: "Bank Name",
      dataIndex: "bankName",
      key: "bankName",
      width: "20%",
      filters: uniqueBanks.map((item) => {
        return {
          text: item,
          value: item,
        };
      }),
      filterMode: "tree",
      filterSearch: true,
      onFilter: (value, record) => record.bankName.includes(value as string),
    },
    {
      title: "Applicability",
      dataIndex: "applicability",
      key: "applicability",
      render: (applicability) => DownTimeApplicability[applicability],
      width: "20%",
    },
    {
      title: "Start Time",
      dataIndex: "downTimeStart",
      key: "downTimeStart",
      render: (timestamp: number) =>
        dayjs(timestamp).format("YYYY-MM-DD hh:mm A"),
      width: "20%",
    },
    {
      title: "End Time",
      dataIndex: "downTimeEnd",
      key: "downTimeEnd",
      render: (timestamp: number) =>
        dayjs(timestamp).format("YYYY-MM-DD hh:mm A"),
      width: "20%",
    },
    {
      title: "Recurring",
      dataIndex: "isRecurring",
      key: "isRecurring",
      render: (isRecurring: boolean) => (isRecurring ? "Yes" : "No"),
      width: "20%",
    },
    {
      title: "Action",
      key: "icon",
      width: "30%",
      render: (_: any, record: DataType) => (
        <Space size="middle">
          <button
            onClick={() => {
              setEditingRecordId(record.id);
            }}
          >
            <EditOutlined />
          </button>
          <button
            onClick={() => {
              Modal.confirm({
                title: "Are you sure you want to delete this item?",
                content: "This action cannot be undone.",
                okText: "Delete",
                okType: "danger",
                cancelText: "Cancel",
                onOk: () => deleteBankDownTimeResponse.mutate(record.id),
              });
            }}
          >
            <DeleteOutlined />
          </button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ width: "100%" }}>
      <Table<DataType>
        dataSource={tableData}
        bordered={true}
        columns={columns}
        pagination={false}
      />
      {editingRecordId && (
        <Modal
          open={true}
          onCancel={() => setEditingRecordId(null)}
          title="Edit Bank Downtime"
          footer={null}
          width={650}
        >
          <Edit
            id={editingRecordId}
            BanksNameAndId={bankFieldsAndName}
            onClose={() => setEditingRecordId(null)}
          />
        </Modal>
      )}
    </div>
  );
}
