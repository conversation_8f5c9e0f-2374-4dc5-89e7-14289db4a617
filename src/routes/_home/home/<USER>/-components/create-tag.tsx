import { PlusOutlined } from "@ant-design/icons";
import { But<PERSON> } from "antd";
import { useState } from "react";
import { ModalComponent } from "./modal-component";

export function CreateTag() {
  const [isModalOpen, setModalOpen] = useState(false);
  const showModal = () => setModalOpen(true);

  return (
    <>
      <Button type="primary" icon={<PlusOutlined />} onClick={showModal}>
        Create
      </Button>
      <ModalComponent modalState={{ isModalOpen, setModalOpen }} />
    </>
  );
}
