import { DeleteOutlined } from "@ant-design/icons";
import { Button, message, Popconfirm, Table, Tooltip, type TableProps } from "antd";
import type { FramesResponse, FrameSummary } from "~/gen/proto-models/PersonalizationAdmin";
import { Route } from "..";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteFrame } from "~/lib/queries/personalization";
import { handleError } from "~/lib/utils/error";
import { Link } from "@tanstack/react-router";
import { getFramesQueryOptions } from "~/lib/queries/queryKeys";

interface FramesTableComponentProps {
  frames: FramesResponse;
}

export function FramesTableComponent({ frames }: FramesTableComponentProps) {
  const { page } = Route.useSearch();
  const navigate = Route.useNavigate();

  const queryClient = useQueryClient();

  const { mutate: deleteFrameItem } = useMutation({
    mutationFn: deleteFrame,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getFramesQueryOptions(page, "").queryKey });
      message.success("Frame deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete frame");
    },
  });

  const columns: TableProps<FrameSummary>["columns"] = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "90%",
      render: (text, record) => (
        <Link
          to="/home/<USER>/$frame-id"
          params={{ "frame-id": record.id }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: "10%",
      align: "center",
      render: (_, record) => {
        return (
          <Popconfirm
            title="Are you sure to delete this frame?"
            onConfirm={() => deleteFrameItem(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <Table
      bordered
      columns={columns}
      dataSource={frames.data}
      rowKey="id"
      pagination={{
        current: page,
        showSizeChanger: true,
        showQuickJumper: true,
        onChange: (newPage) => {
          navigate({ search: (prev) => ({ ...prev, page: newPage }) });
        },
        total: frames.pagination?.hasNextPage ? page * 8 + 1 : page * 8,
      }}
    />
  );
}
