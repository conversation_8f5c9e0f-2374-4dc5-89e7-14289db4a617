import type {
  StoriesResponse,
  StorySummary,
} from "~/gen/proto-models/PersonalizationAdmin";
import { Route } from "..";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteStory } from "~/lib/queries/personalization";
import { getStoriesQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { Button, message, Popconfirm, Table, Tag, Tooltip, type TableProps } from "antd";
import { Link } from "@tanstack/react-router";
import { DeleteOutlined } from "@ant-design/icons";

interface StoryTableComponentProps {
  stories: StoriesResponse;
}

export function StoryTableComponent({ stories }: StoryTableComponentProps) {
  const { page } = Route.useSearch();
  const navigate = Route.useNavigate();

  const queryClient = useQueryClient();

  const { mutate: deleteStoryItem } = useMutation({
    mutationFn: deleteStory,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getStoriesQueryOptions(page, "").queryKey,
      });
      message.success("Story deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete story");
    },
  });

  const columns: TableProps<StorySummary>["columns"] = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (text, record) => (
        <Link to="/home/<USER>/$story-id" params={{ "story-id": record.id }}>
          {text}
        </Link>
      ),
    },
    {
      title: "Page",
      dataIndex: "page",
      key: "page",
      width: "10%",
      render: (text) => <Tag color="green">{text}</Tag>,
    },
    {
      title: "Start Time",
      dataIndex: "startTime",
      key: "startTime",
      width: "20%",
      render: (text) => {
        if (!text) {
          return (
            <Tag color="red">
              Not Set
            </Tag>
          );
        }
        const startTime = new Date(text);
        return <div>{startTime.toLocaleString()}</div>;
      },
    },
    {
      title: "End Time",
      dataIndex: "endTime",
      key: "endTime",
      width: "20%",
      render: (text) => {
        if (!text) {
          return (
            <Tag color="red">
              Not Set
            </Tag>
          );
        }
        const endTime = new Date(text);
        return <div>{endTime.toLocaleString()}</div>;
      },
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: "10%",
      align: "center",
      render: (_, record) => {
        return (
          <Popconfirm
            title="Are you sure to delete this story?"
            onConfirm={() => deleteStoryItem(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <Table
      bordered
      columns={columns}
      dataSource={stories.data}
      rowKey="id"
      pagination={{
        current: page,
        pageSize: 8,
        onChange: (newPage) => {
          navigate({ search: (prev) => ({ ...prev, page: newPage }) });
        },
        total: stories.pagination?.hasNextPage ? page * 8 + 1 : page * 8,
      }}
    />
  );
}
