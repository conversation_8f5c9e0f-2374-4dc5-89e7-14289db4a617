import { HomeOutlined } from "@ant-design/icons";
import { useMutation, useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
} from "antd";
import { useState, useEffect } from "react";
import type { UpdateCollectionItemRequestProto } from "~/gen/proto-models/CollectionV2";
import { updateFdCollectionItem } from "~/lib/queries/fd-collections";
import {
  getBankTenureListQueryOptions,
  getFdCollectionDataQueryOptions,
  getFdCollectionItemDataQueryOptions,
} from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  "/_home/home/<USER>/$fd-collection-id/$collection-item-id/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await Promise.all([
      queryClient.ensureQueryData(
        getFdCollectionItemDataQueryOptions(params["collection-item-id"])
      ),
      queryClient.ensureQueryData(
        getFdCollectionDataQueryOptions(params["fd-collection-id"])
      ),
      queryClient.ensureQueryData(getBankTenureListQueryOptions()),
    ]);
  },
});

function RouteComponent() {
  const [form] = Form.useForm();
  const {
    "collection-item-id": collectionItemID,
    "fd-collection-id": fdCollectionID,
  } = Route.useParams();
  const [
    getFdCollectionItemDataQuery,
    getFdCollectionDataQuery,
    getBankTenureListQuery,
  ] = useSuspenseQueries({
    queries: [
      getFdCollectionItemDataQueryOptions(collectionItemID),
      getFdCollectionDataQueryOptions(fdCollectionID),
      getBankTenureListQueryOptions(),
    ],
  });

  const collectionItem = getFdCollectionItemDataQuery.data.collectionItem;
  const fdCollection = getFdCollectionDataQuery.data.collection;
  const bankTenureList = getBankTenureListQuery.data.bankTenureList;

  const [tenure, setTenure] =
    useState<{ minTenureInDays: number; maxTenureInDays: number }[]>();

  const { mutate: updateFdCollection } = useMutation({
    mutationFn: updateFdCollectionItem,
    onSuccess: () => {
      message.success("FD Collection Item updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update FD Collection Item");
    },
  });

  useEffect(() => {
    if (collectionItem?.bankId) {
      const bank = bankTenureList.find((item) => item.bankId === collectionItem.bankId);
      if (bank) {
        setTenure(
          bank.tenures.map((item) => ({
            minTenureInDays: item.minTenureInDays,
            maxTenureInDays: item.maxTenureInDays,
          }))
        );
      }
    }
  }, [collectionItem?.bankId, bankTenureList]);

  const options = bankTenureList.map((item) => ({
    label: item.bankName,
    value: item.bankId,
  }));

  const handleChange = (bankId: string) => {
    const bank = bankTenureList.find((item) => item.bankId === bankId);
    setTenure(
      bank?.tenures.map((item) => ({
        minTenureInDays: item.minTenureInDays,
        maxTenureInDays: item.maxTenureInDays,
      })) || []
    );
  };

  const handleUpdate = (values: any) => {
    const payload: UpdateCollectionItemRequestProto = {
      id: collectionItemID,
      collectionId: fdCollectionID,
      bankId: values.bankId,
      minTenureInDays: parseInt(values.tenure.split("-")[0]),
      maxTenureInDays: parseInt(values.tenure.split("-")[1]),
      priority: values.priority,
      displayTitle: values.displayTitle,
      redirectDeeplink: {
        path: values.path,
        pathType: values.pathType,
      },
      tagConfig: {
        name: values.name,
        iconUrl: values.iconUrl,
        color: values.color,
        bgColor: values.bgColor,
        type: values.type,
      },
    };

    updateFdCollection(payload);
  };
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <span>FD Collections</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to="/home/<USER>/$fd-collection-id"
                params={{ "fd-collection-id": fdCollectionID }}
              >
                <span>{fdCollection!.name}</span>
              </Link>
            ),
          },
          {
            title: collectionItem!.bankName,
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form form={form} layout="vertical" initialValues={{
            ...collectionItem,
            tenure: `${collectionItem?.minTenureInDays}-${collectionItem?.maxTenureInDays}`
          }} onFinish={handleUpdate}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="bankId" label={<strong>Bank</strong>}>
                  <Select
                    placeholder="Select bank"
                    options={options}
                    onChange={(bankId) => handleChange(bankId)}
                    showSearch
                    optionFilterProp="label"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="tenure" label={<strong>Tenure</strong>}>
                  <Select placeholder="Select tenure">
                    {tenure?.map((item, index) => (
                      <Select.Option
                        value={`${item.minTenureInDays}-${item.maxTenureInDays}`}
                        key={index}
                      >
                        {item.minTenureInDays} - {item.maxTenureInDays} days
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="priority" label={<strong>Priority</strong>}>
                  <InputNumber
                    min={0}
                    placeholder="Enter priority"
                    className="w-full"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="displayTitle"
                  label={<strong>Display Title</strong>}
                >
                  <Input placeholder="Enter display title" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="path" label={<strong>Deeplink Path</strong>}>
                  <Input placeholder="Enter deeplink path" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="pathType"
                  label={<strong>Deeplink Path Type</strong>}
                >
                  <Input placeholder="Enter deeplink path type" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="name" label={<strong>Tag Name</strong>}>
                  <Input placeholder="Enter Tag name" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="iconUrl" label={<strong>Tag Icon URL</strong>}>
                  <Input placeholder="Enter Tag icon url" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="color" label={<strong>Tag Color</strong>}>
                  <Input type="color" placeholder="Enter Tag color" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="bgColor"
                  label={<strong>Tag Background Color</strong>}
                >
                  <Input
                    type="color"
                    placeholder="Enter Tag background color"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="type" label={<strong>Tag Type</strong>}>
                  <Input placeholder="Enter Tag type" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Update
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
