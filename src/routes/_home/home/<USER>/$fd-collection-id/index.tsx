import { HomeOutlined } from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Breadcrumb, Col, Row } from "antd";
import { getFdCollectionDataQueryOptions } from "~/lib/queries/queryKeys";
import { CollectionFormComponent } from "./-components/collection-form-component";
import { CollectionItemsComponent } from "./-components/collection-items-component";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  "/_home/home/<USER>/$fd-collection-id/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await queryClient.ensureQueryData(getFdCollectionDataQueryOptions(params["fd-collection-id"]))
  },
});

function RouteComponent() {
  const { "fd-collection-id": fdCollectionID } = Route.useParams();
  const [getFdCollectionDataQuery] = useSuspenseQueries({
    queries: [getFdCollectionDataQueryOptions(fdCollectionID)],
  });
  const fdCollection = getFdCollectionDataQuery.data.collection;
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <span>FD Collections</span>
              </Link>
            ),
          },
          {
            title: fdCollection!.name,
          },
        ]}
      />
      <Row gutter={16}>
        <Col span={14}>
          <CollectionFormComponent fdCollection={fdCollection!} />
        </Col>
        <Col span={10}>
          <CollectionItemsComponent />
        </Col>
      </Row>
    </div>
  );
}
