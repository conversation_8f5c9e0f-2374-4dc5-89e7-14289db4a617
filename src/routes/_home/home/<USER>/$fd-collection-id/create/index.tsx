import { HomeOutlined } from "@ant-design/icons";
import {
  useMutation,
  useQueryClient,
  useSuspenseQueries,
} from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import {
  Breadcrumb,
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
} from "antd";
import { useState } from "react";
import type { CreateCollectionItemRequestProto } from "~/gen/proto-models/CollectionV2";
import { createFdCollectionItem } from "~/lib/queries/fd-collections";
import {
  getBankTenureListQueryOptions,
  getFdCollectionDataQueryOptions,
} from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  "/_home/home/<USER>/$fd-collection-id/create/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await Promise.all([
      queryClient.ensureQueryData(getBankTenureListQueryOptions()),
      queryClient.ensureQueryData(
        getFdCollectionDataQueryOptions(params["fd-collection-id"])
      ),
    ]);
  },
});

function RouteComponent() {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { "fd-collection-id": fdCollectionID } = Route.useParams();
  const [tenure, setTenure] =
    useState<{ minTenureInDays: number; maxTenureInDays: number }[]>();
  const [getBankTenureListQuery, getFdCollectionDataQuery] = useSuspenseQueries(
    {
      queries: [
        getBankTenureListQueryOptions(),
        getFdCollectionDataQueryOptions(fdCollectionID),
      ],
    }
  );
  const bankTenureList = getBankTenureListQuery.data.bankTenureList;
  const fdCollection = getFdCollectionDataQuery.data.collection;

  const { mutate: createFdCollection } = useMutation({
    mutationFn: createFdCollectionItem,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getFdCollectionDataQueryOptions(fdCollectionID).queryKey,
      });
      message.success("FD Collection Item created successfully");
      form.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create FD Collection Item");
    },
  });

  const options = bankTenureList.map((item) => ({
    label: item.bankName,
    value: item.bankId,
  }));

  const handleChange = (bankId: string) => {
    const bank = bankTenureList.find((item) => item.bankId === bankId);
    setTenure(
      bank?.tenures.map((item) => ({
        minTenureInDays: item.minTenureInDays,
        maxTenureInDays: item.maxTenureInDays,
      }))
    );
  };

  const handleCreation = (values: any) => {
    const payload: CreateCollectionItemRequestProto = {
      collectionId: fdCollectionID,
      bankId: values.bankId,
      minTenureInDays: parseInt(values.tenure.split("-")[0]),
      maxTenureInDays: parseInt(values.tenure.split("-")[1]),
      priority: values.priority,
      displayTitle: values.displayTitle,
      redirectDeeplink: {
        path: values.path,
        pathType: values.pathType,
      },
      tagConfig: {
        name: values.name,
        iconUrl: values.iconUrl,
        color: values.color,
        bgColor: values.bgColor,
        type: values.type,
      },
    };

    createFdCollection(payload);
  };
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <span>FD Collections</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to="/home/<USER>/$fd-collection-id"
                params={{ "fd-collection-id": fdCollectionID }}
              >
                <span>{fdCollection!.name}</span>
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form form={form} layout="vertical" onFinish={handleCreation}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="bankId" label={<strong>Bank</strong>}>
                  <Select
                    placeholder="Select bank"
                    options={options}
                    onChange={(bankId) => handleChange(bankId)}
                    showSearch
                    optionFilterProp="label"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="tenure" label={<strong>Tenure</strong>}>
                  <Select placeholder="Select tenure">
                    {tenure?.map((item, index) => (
                      <Select.Option
                        value={`${item.minTenureInDays}-${item.maxTenureInDays}`}
                        key={index}
                      >
                        {item.minTenureInDays} - {item.maxTenureInDays} days
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="priority" label={<strong>Priority</strong>}>
                  <InputNumber
                    min={0}
                    placeholder="Enter priority"
                    className="w-full"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="displayTitle"
                  label={<strong>Display Title</strong>}
                >
                  <Input placeholder="Enter display title" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="path" label={<strong>Deeplink Path</strong>}>
                  <Input placeholder="Enter deeplink path" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="pathType"
                  label={<strong>Deeplink Path Type</strong>}
                >
                  <Input placeholder="Enter deeplink path type" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="name" label={<strong>Tag Name</strong>}>
                  <Input placeholder="Enter Tag name" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="iconUrl" label={<strong>Tag Icon URL</strong>}>
                  <Input placeholder="Enter Tag icon url" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="color" label={<strong>Tag Color</strong>}>
                  <Input type="color" placeholder="Enter Tag color" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="bgColor"
                  label={<strong>Tag Background Color</strong>}
                >
                  <Input
                    type="color"
                    placeholder="Enter Tag background color"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="type" label={<strong>Tag Type</strong>}>
                  <Input placeholder="Enter Tag type" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
