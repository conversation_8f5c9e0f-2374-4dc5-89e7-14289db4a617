import {
  DragDropContext,
  Droppable,
  Draggable,
  type DropResult,
} from "@hello-pangea/dnd";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate, useParams } from "@tanstack/react-router";
import { Button, Card, Space, Popconfirm, message } from "antd";
import { EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { getFdCollectionItemsQueryOptions } from "~/lib/queries/queryKeys";
import { useState, useEffect } from "react";
import { deleteFdCollectionItem, bulkUpdateFdItemPriority } from "~/lib/queries/fd-collections";
import { handleError } from "~/lib/utils/error";

export function CollectionItemsComponent() {
  const navigate = useNavigate();
  const { "fd-collection-id": fdCollectionId } = useParams({ from: "/_home/home/<USER>/$fd-collection-id/" });
  const [sortedItems, setSortedItems] = useState<any[]>([]);

  const queryClient = useQueryClient();

  const query = useQuery(getFdCollectionItemsQueryOptions(fdCollectionId));

  const { mutate: deleteFdCollection } = useMutation({
    mutationFn: deleteFdCollectionItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getFdCollectionItemsQueryOptions(fdCollectionId).queryKey });
      message.success("FD Collection Item deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete FD Collection Item");
    },
  });

  const { mutate: updatePriorities } = useMutation({
    mutationFn: bulkUpdateFdItemPriority,
    onSuccess: () => {
      message.success("Item priorities saved successfully.");
      queryClient.invalidateQueries({ queryKey: getFdCollectionItemsQueryOptions(fdCollectionId).queryKey });
    },
    onError: (error: Error) => {
      handleError(error, "Failed to save item priorities");
    },
  });

  useEffect(() => {
    if (query.data?.collectionItems) {
      const sorted = [...query.data.collectionItems].sort((a, b) => (a.priority || 0) - (b.priority || 0));
      setSortedItems(sorted);
    }
  }, [query.data]);

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(sortedItems);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const updatedItems = items.map((item, index) => ({
      ...item,
      priority: index + 1,
    }));

    setSortedItems(updatedItems);
  };

  const handleEdit = (collectionItemId: string) => {
    navigate({ to: `/home/<USER>/${fdCollectionId}/${collectionItemId}` });
  };

  const handleDelete = (collectionItemId: string) => {
    deleteFdCollection(collectionItemId);
  };

  const handleSaveOrder = () => {
    const payload = {
      items: sortedItems.map((item) => ({
        id: item.id,
        newPriority: item.priority,
      })),
    };
    updatePriorities(payload);
  };
  
  return (
    <div className="mt-4">
      <Card
        title="Collection Items"
        extra={
          <div className="flex gap-2">
            <Button type="primary" onClick={() => navigate({ to: `/home/<USER>/${fdCollectionId}/create` })}>Add Item</Button>
            <Button onClick={handleSaveOrder}>Save Order</Button>
          </div>
        }
      >
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="fd-collection-items">
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className="space-y-2"
                    >
                      {sortedItems.map((item, index) => (
                        <Draggable key={item.id} draggableId={item.id} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={`p-4 bg-white border rounded-lg shadow-sm hover:shadow-md transition-shadow ${
                                snapshot.isDragging ? "shadow-lg" : ""
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-3 mb-2">
                                    <span className="text-sm text-gray-500">
                                      #{item.priority || index + 1}
                                    </span>
                                    <h4 className="font-medium">{item.displayTitle}</h4>
                                  </div>
                                  <div className="flex items-center gap-4 text-sm text-gray-600">
                                    <span><strong>Bank:</strong> {item.bankName}</span>
                                    <span><strong>Tenure:</strong> {item.minTenureInDays} - {item.maxTenureInDays} days</span>
                                  </div>
                                </div>
                                <Space>
                                  <Button
                                    icon={<EditOutlined />}
                                    size="small"
                                    onClick={() => handleEdit(item.id)}
                                  />
                                  <Popconfirm
                                    title="Are you sure you want to delete this item?"
                                    onConfirm={() => handleDelete(item.id)}
                                    okText="Delete"
                                    okType="danger"
                                    cancelText="Cancel"
                                  >
                                    <Button
                                      icon={<DeleteOutlined />}
                                      size="small"
                                      danger
                                    />
                                  </Popconfirm>
                                </Space>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
      </Card>
    </div>
  );
}
