import { useMutation, useQueryClient, useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { Button, Card, Col, Form, Input, message, Row } from "antd";
import type { Tag } from "~/gen/proto-models/Catalog";
import { updateTag } from "~/lib/queries/bond-catalog";
import { getTagQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute("/_home/home/<USER>/$tag-id/")({
  component: RouteComponent,
  loader: async ({ params }) => {
    await queryClient.ensureQueryData(getTagQueryOptions(params["tag-id"]));
  },
});

function RouteComponent() {
  const { "tag-id": tagID } = Route.useParams();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const [getTagQuery] = useSuspenseQueries({
    queries: [getTagQueryOptions(tagID)],
  });

  const tag = getTagQuery.data;

  const { mutate: updateTagItem } = useMutation({
    mutationFn: updateTag,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getTagQueryOptions(tagID).queryKey });
      message.success("Tag updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update tag");
    },
  });

  const handleUpdate = (values: Tag) => {
    const payload = {
      ...values,
      id: tag.id,
    };

    updateTagItem(payload);
  };

  return (
  <div className="p-6">
    <Card>
        <Form form={form} layout="vertical" initialValues={tag} onFinish={handleUpdate}>
            <Form.Item name="name" label={<strong>Name</strong>}>
              <Input />
            </Form.Item>
            <Row gutter={16}>
                <Col span={8}>
                    <Form.Item name="color" label={<strong>Color</strong>}>
                        <Input type="color"/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item name="bgColor" label={<strong>Background Color</strong>}>
                        <Input type="color"/>
                    </Form.Item>
                </Col>
                <Col span={8}>
                    <Form.Item name="shimmerColor" label={<strong>Shimmer Color</strong>}>
                        <Input type="color"/>
                    </Form.Item>
                </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Update
              </Button>
            </Form.Item>
        </Form>
    </Card>
  </div>
  );
}
