import { ContainerOutlined, HomeOutlined } from "@ant-design/icons";
import {
  useMutation,
  useQueryClient,
  useSuspenseQueries,
} from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import {
  <PERSON>readcrumb,
  Button,
  Card,
  Col,
  Form,
  Input,
  message,
  Row,
  Select,
} from "antd";
import { useMemo, useState } from "react";
import {
  BannerResponse,
  BannerType,
  bannerTypeFromJSON,
} from "~/gen/proto-models/PersonalizationAdmin";
import { updateBanner } from "~/lib/queries/personalization";
import {
  bankNameAndIDQueryOptions,
  getBannerQueryOptions,
} from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute("/_home/home/<USER>/$banner-id/")({
  component: RouteComponent,
  loader: async ({ params }) => {
    await Promise.all([
      queryClient.ensureQueryData(getBannerQueryOptions(params["banner-id"])),
      queryClient.ensureQueryData(bankNameAndIDQueryOptions()),
    ]);
  },
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const { "banner-id": bannerID } = Route.useParams();
  const [form] = Form.useForm();
  const [getBannerQuery, getAllBanksQuery] = useSuspenseQueries({
    queries: [getBannerQueryOptions(bannerID), bankNameAndIDQueryOptions()],
  });

  const banner = getBannerQuery.data;
  const allBanks = getAllBanksQuery.data;

  const [watchContent, setWatchContent] = useState(banner.content);

  const { clippedURL, stackedURL } = useMemo(() => {
    try {
      const content = JSON.parse(watchContent || "{}");
      return {
        clippedURL: content.clippedUrl || null,
        stackedURL: content.stackedUrl || null,
      };
    } catch {
      return { clippedURL: null, stackedURL: null };
    }
  }, [watchContent]);

  const { mutate: updateBannerItem } = useMutation({
    mutationFn: updateBanner,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getBannerQueryOptions(bannerID).queryKey,
      });
      message.success("Banner created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create banner");
    },
  });

  const [bannerType, setBannerType] = useState<BannerType>(
    bannerTypeFromJSON(banner.bannerType)
  );

  const handleUpdate = (values: BannerResponse) => {
    const payload = {
      ...values,
      id: banner.id,
      content: JSON.stringify(JSON.parse(values.content)),
    };

    updateBannerItem(payload);
  };

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <ContainerOutlined />
                <span>Banners</span>
              </Link>
            ),
          },
          {
            title: banner.name,
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleUpdate}
            initialValues={{
              ...banner,
              bannerType: banner.bannerType,
              entityIdentifier: banner.entityIdentifier,
              content: JSON.stringify(JSON.parse(banner.content), null, 4),
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label={<strong>Name</strong>}
                  rules={[
                    { required: true, message: "Please enter banner name" },
                  ]}
                >
                  <Input placeholder="Enter banner name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="groupName"
                  label={<strong>Group Name</strong>}
                  rules={[
                    {
                      required: true,
                      message: "Please enter banner group name",
                    },
                  ]}
                >
                  <Input placeholder="Enter banner group name" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="bannerType"
              label={<strong>Banner Type</strong>}
              rules={[{ required: true, message: "Please select banner type" }]}
            >
              <Select
                placeholder="Select banner type"
                onChange={(value) => setBannerType(value)}
              >
                <Select.Option value={BannerType.BANK}>Bank</Select.Option>
                <Select.Option value={BannerType.GENERAL}>
                  General
                </Select.Option>
                <Select.Option value={BannerType.BOND}>Bond</Select.Option>
              </Select>
            </Form.Item>

            {bannerType === BannerType.BANK && (
              <Form.Item
                name="entityIdentifier"
                label={<strong>Bank</strong>}
                rules={[{ required: true, message: "Please select bank" }]}
              >
                <Select placeholder="Select bank">
                  {allBanks?.bankDetail.map((item) => (
                    <Select.Option value={item.fsi} key={item.bankId}>
                      {item.bankName}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            )}

            {bannerType === BannerType.BOND && (
              <Form.Item
                name="entityIdentifier"
                label={<strong>Bond</strong>}
                rules={[{ required: true, message: "Please enter bond identifier" }]}
              >
                <Input placeholder="Enter bond identifier" />
              </Form.Item>
            )}
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="content"
                  label={<strong>Content</strong>}
                  rules={[
                    { required: true, message: "Please enter banner content" },
                  ]}
                >
                  <Input.TextArea
                    placeholder="Enter banner content"
                    autoSize={{ maxRows: 20 }}
                    onChange={(e) => setWatchContent(e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <div className="flex flex-col items-center justify-center h-full">
                  {clippedURL && (
                    <img src={clippedURL} width={300} height={300} />
                  )}
                  {stackedURL && (
                    <img src={stackedURL} width={300} height={300} />
                  )}
                </div>
              </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Update
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
