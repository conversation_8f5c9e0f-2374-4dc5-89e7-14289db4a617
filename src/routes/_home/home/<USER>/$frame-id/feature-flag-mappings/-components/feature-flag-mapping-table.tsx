import { DeleteOutlined } from "@ant-design/icons";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import {
  Button,
  message,
  Popconfirm,
  Table,
  Tooltip,
  type TableProps,
} from "antd";
import { useMemo } from "react";
import {
  ReferenceType,
  type FeatureFlagMappingsResponse,
  type FeatureFlagMappingSummary,
  type FrameResponse,
} from "~/gen/proto-models/PersonalizationAdmin";
import { deleteFeatureFlagMapping } from "~/lib/queries/personalization";
import { getFeatureFlagMappingsQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

interface FeatureFlagMappingTableComponentProps {
  frame: FrameResponse;
  featureFlagMappings: FeatureFlagMappingsResponse;
  searchText: string;
}

export function FeatureFlagMappingTableComponent({
  frame,
  featureFlagMappings,
  searchText,
}: FeatureFlagMappingTableComponentProps) {
  const queryClient = useQueryClient();

  const { mutate: deleteFeatureFlagMappingItem } = useMutation({
    mutationFn: deleteFeatureFlagMapping,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getFeatureFlagMappingsQueryOptions(
          frame.id,
          ReferenceType.FRAME
        ).queryKey,
      });
      message.success("Feature Flag Mapping deleted successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete feature flag mapping");
    },
  });

  const filteredData = useMemo(() => {
    if (!featureFlagMappings?.featureFlagMappings) return [];
    return featureFlagMappings.featureFlagMappings.filter(
      (item) =>
        item.featureFlag.toLowerCase().includes(searchText.toLowerCase()) ||
        item.widgetId.toLowerCase().includes(searchText.toLowerCase())
    );
  }, [featureFlagMappings, searchText]);

  const columns: TableProps<FeatureFlagMappingSummary>["columns"] = [
    {
      title: "Name",
      dataIndex: "featureFlag",
      key: "featureFlag",
      width: "50%",
      render: (text, record) => (
        <Link
          to="/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id"
          params={{
            "frame-id": frame.id,
            "feature-flag-mapping-id": record.id,
          }}
        >
          {text}
        </Link>
      ),
    },
    {
      title: "Widget ID",
      dataIndex: "widgetId",
      key: "widgetId",
      width: "20%",
    },
    {
      title: "Variant",
      dataIndex: "variant",
      key: "variant",
      width: "20%",
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: "10%",
      align: "center",
      render: (_, record) => {
        return (
          <Popconfirm
            title="Are you sure to delete this Feature Flag Mapping?"
            onConfirm={() => deleteFeatureFlagMappingItem(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        );
      },
    },
  ];
  return (
    <Table
      bordered
      columns={columns}
      dataSource={filteredData}
      rowKey="id"
      pagination={{
        showSizeChanger: true,
        showQuickJumper: true,
      }}
    />
  );
}
