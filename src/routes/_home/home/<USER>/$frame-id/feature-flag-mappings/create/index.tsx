import {
  BlockOutlined,
  BorderOutlined,
  FlagOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import {
  useMutation,
  useQueryClient,
  useSuspenseQueries,
} from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Breadcrumb, Button, Card, Form, Input, message } from "antd";
import {
  ReferenceType,
  type CreateFeatureFlagMappingRequest,
} from "~/gen/proto-models/PersonalizationAdmin";
import { createFeatureFlagMapping } from "~/lib/queries/personalization";
import {
  getFeatureFlagMappingsQueryOptions,
  getFrameQueryOptions,
} from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  "/_home/home/<USER>/$frame-id/feature-flag-mappings/create/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await queryClient.ensureQueryData(getFrameQueryOptions(params["frame-id"]))
  },
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { "frame-id": frameID } = Route.useParams();

  const [getFrameQuery] = useSuspenseQueries({
    queries: [getFrameQueryOptions(frameID)],
  });

  const { mutate: createFeatureFlagMappingItem } = useMutation({
    mutationFn: createFeatureFlagMapping,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getFeatureFlagMappingsQueryOptions(
          frameID,
          ReferenceType.FRAME
        ).queryKey,
      });
      form.resetFields();
      message.success("Feature Flag Mapping created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create feature flag mapping");
    },
  });

  const handleCreate = (values: CreateFeatureFlagMappingRequest) => {
    const payload = {
      featureFlag: values.featureFlag,
      widgetId: values.widgetId,
      typeId: frameID,
      variant: values.variant,
      referenceType: ReferenceType.FRAME,
    };

    createFeatureFlagMappingItem(payload);
  };

  const frame = getFrameQuery.data;
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <BlockOutlined />
                <span>Frames</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to={`/home/<USER>/$frame-id`}
                params={{ "frame-id": frameID }}
              >
                <BorderOutlined />
                <span>{frame.name}</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to={`/home/<USER>/$frame-id/feature-flag-mappings`}
                params={{ "frame-id": frameID }}
              >
                <FlagOutlined />
                <span>Feature Flag Mappings</span>
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-6">
        <Card>
          <Form form={form} layout="vertical" onFinish={handleCreate}>
            <Form.Item name="featureFlag" label={<strong>Feature Flag</strong>}>
              <Input />
            </Form.Item>
            <Form.Item name="widgetId" label={<strong>Widget ID</strong>}>
              <Input />
            </Form.Item>
            <Form.Item name="variant" label={<strong>Variant</strong>}>
              <Input />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
