import {
  BlockOutlined,
  BorderOutlined,
  HomeOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { <PERSON><PERSON>crumb, Button, Input } from "antd";
import { useState } from "react";
import { ReferenceType } from "~/gen/proto-models/PersonalizationAdmin";
import {
  getFeatureFlagMappingsQueryOptions,
  getFrameQueryOptions,
} from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { FeatureFlagMappingTableComponent } from "./-components/feature-flag-mapping-table";

export const Route = createFileRoute(
  "/_home/home/<USER>/$frame-id/feature-flag-mappings/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await Promise.all([
      queryClient.ensureQueryData(getFrameQueryOptions(params["frame-id"])),
      queryClient.ensureQueryData(
        getFeatureFlagMappingsQueryOptions(
          params["frame-id"],
          ReferenceType.FRAME
        )
      ),
    ]);
  },
});

function RouteComponent() {
  const { "frame-id": frameID } = Route.useParams();

  const navigate = Route.useNavigate();

  const [searchText, setSearchText] = useState("");

  const [getFrameQuery, getFeatureFlagMappingsQuery] = useSuspenseQueries({
    queries: [
      getFrameQueryOptions(frameID),
      getFeatureFlagMappingsQueryOptions(frameID, ReferenceType.FRAME),
    ],
  });

  const frame = getFrameQuery.data;
  const featureFlagMappings = getFeatureFlagMappingsQuery.data;

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <BlockOutlined />
                <span>Frames</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to={"/home/<USER>/$frame-id"}
                params={{ "frame-id": frameID }}
              >
                <BorderOutlined />
                <span>{frame.name}</span>
              </Link>
            ),
          },
          {
            title: "Feature Flag Mappings",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by name"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() =>
            navigate({
              to: "/home/<USER>/$frame-id/feature-flag-mappings/create",
            })
          }
        >
          Create
        </Button>
      </div>
      <FeatureFlagMappingTableComponent
        frame={frame}
        featureFlagMappings={featureFlagMappings}
        searchText={searchText}
      />
    </div>
  );
}
