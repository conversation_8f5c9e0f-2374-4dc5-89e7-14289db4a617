import {
  BlockOutlined,
  BorderOutlined,
  FlagOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Breadcrumb } from "antd";
import {
  getFeatureFlagMappingQueryOptions,
  getFrameQueryOptions,
} from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { FormElement } from "./-components/form-element";

export const Route = createFileRoute(
  "/_home/home/<USER>/$frame-id/feature-flag-mappings/$feature-flag-mapping-id/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await Promise.all([
      queryClient.ensureQueryData(getFrameQueryOptions(params["frame-id"])),
      queryClient.ensureQueryData(
        getFeatureFlagMappingQueryOptions(params["feature-flag-mapping-id"])
      ),
    ]);
  },
});

function RouteComponent() {
  const {
    "feature-flag-mapping-id": featureFlagMappingID,
    "frame-id": frameID,
  } = Route.useParams();

  const [getFeatureFlagMappingQuery, getFrameQuery] = useSuspenseQueries({
    queries: [
      getFeatureFlagMappingQueryOptions(featureFlagMappingID),
      getFrameQueryOptions(frameID),
    ],
  });

  const featureFlagMapping = getFeatureFlagMappingQuery.data;
  const frame = getFrameQuery.data;
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <BlockOutlined />
                <span>Frames</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to={`/home/<USER>/$frame-id`}
                params={{ "frame-id": frameID }}
              >
                <BorderOutlined />
                <span>{frame.name}</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to={`/home/<USER>/$frame-id/feature-flag-mappings`}
                params={{ "frame-id": frameID }}
              >
                <FlagOutlined />
                <span>Feature Flag Mappings</span>
              </Link>
            ),
          },
          {
            title: featureFlagMapping.featureFlag,
          },
        ]}
      />
      <div className="mt-6">
        <FormElement
          featureFlagMapping={featureFlagMapping}
          featureFlagMappingID={featureFlagMappingID}
          frameID={frameID}
        />
      </div>
    </div>
  );
}
