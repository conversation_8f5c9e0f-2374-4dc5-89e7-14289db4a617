import { useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>ton, Card, Form, Input, message } from "antd";
import type { FrameResponse } from "~/gen/proto-models/PersonalizationAdmin";
import { updateFrame } from "~/lib/queries/personalization";
import { getFrameQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

export const FormElement = ({ frame }: { frame: FrameResponse }) => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const { mutate: updateFrameItem } = useMutation({
    mutationFn: updateFrame,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getFrameQueryOptions(frame.id).queryKey,
      });
      message.success("Frame updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update frame");
    },
  });

  const handleUpdate = (values: FrameResponse) => {
    const payload = {
      ...values,
      id: frame.id,
      content: JSON.stringify(JSON.parse(values.content)),
      config: JSON.stringify(JSON.parse(values.config)),
    };

    updateFrameItem(payload);
  };

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleUpdate}
        initialValues={{
          ...frame,
          content: JSON.stringify(JSON.parse(frame.content), null, 4),
          config: JSON.stringify(JSON.parse(frame.config), null, 4),
        }}
      >
        <Form.Item
          name="name"
          label={<strong>Name</strong>}
          rules={[{ required: true, message: "Please enter Frame name" }]}
        >
          <Input placeholder="Enter Frame name" />
        </Form.Item>
        <Form.Item
          name="content"
          label={<strong>Content</strong>}
          rules={[{ required: true, message: "Please enter frame content" }]}
        >
          <Input.TextArea
            placeholder="Enter frame content"
            autoSize={{ maxRows: 20 }}
          />
        </Form.Item>
        <Form.Item
          name="config"
          label={<strong>Config</strong>}
          rules={[{ required: true, message: "Please enter frame config" }]}
        >
          <Input.TextArea
            placeholder="Enter frame config"
            autoSize={{ maxRows: 10 }}
          />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            Update
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};
