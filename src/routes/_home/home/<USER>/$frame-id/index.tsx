import { BlockOutlined, HomeOutlined } from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { Breadcrumb, Button } from "antd";
import { getFrameQueryOptions } from "~/lib/queries/queryKeys";
import { FormElement } from "./-components/form-element";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute("/_home/home/<USER>/$frame-id/")({
  component: RouteComponent,
  loader: async ({ params }) => {
    await queryClient.ensureQueryData(getFrameQueryOptions(params["frame-id"]))
  },
});

function RouteComponent() {
  const { "frame-id": frameID } = Route.useParams();

  const navigate = Route.useNavigate();

  const [getFrameQuery] = useSuspenseQueries({
    queries: [getFrameQueryOptions(frameID)],
  });

  const frame = getFrameQuery.data;
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4 mt-4">
        <Breadcrumb
          items={[
            {
              title: (
                <Link to="/home">
                  <HomeOutlined />
                </Link>
              ),
            },
            {
              title: (
                <Link to="/home/<USER>">
                  <BlockOutlined />
                  <span>Frames</span>
                </Link>
              ),
            },
            {
              title: frame.name,
            },
          ]}
        />
        <Button
          type="primary"
          onClick={() =>
            navigate({ to: "/home/<USER>/$frame-id/feature-flag-mappings" })
          }
        >
          Feature Flag Mappings
        </Button>
      </div>
      <FormElement frame={frame} />
    </div>
  );
}
