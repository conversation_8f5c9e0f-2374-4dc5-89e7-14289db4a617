import { useMutation, useQueryClient, useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { Button, Col, Form, Input, message, Row, Select, Switch } from "antd";
import {
    bondMediaItemParentTypeFromJSON,
MediaItemDashboard,
MediaItemUpdateRequest,
mediaTypeFromJSON,
screenTypeFromJSON,
} from "~/gen/proto-models/Catalog";
import { updateMediaItem } from "~/lib/queries/bond-catalog";
import { getMediaItemByIdQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  "/_home/home/<USER>/active/$bond-id/media-items/$media-item-id/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    const mediaItemID = params["media-item-id"];
    await queryClient.ensureQueryData(
      getMediaItemByIdQueryOptions(mediaItemID)
    );
  },
});

function RouteComponent() {
  const { "media-item-id": mediaItemID } = Route.useParams();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [mediaItemQuery] = useSuspenseQueries({
    queries: [getMediaItemByIdQueryOptions(mediaItemID)],
  });

  const mediaItemData = mediaItemQuery.data;

  const { mutate: updateMedia } = useMutation({
    mutationFn: updateMediaItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["media-items"] });
      message.success("Media Item updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update media item");
    },
  });

  const handleUpdate = (values: MediaItemDashboard) => {
    const payload: MediaItemUpdateRequest = {
      id: mediaItemID,
      parentType: bondMediaItemParentTypeFromJSON(mediaItemData.mediaItem!.parentType),
      section: values.section,
      mediaType: mediaTypeFromJSON(values.mediaType),
      mediaUrl: values.url,
      screenType: screenTypeFromJSON(values.screenType),
      redirectDeeplink: values.redirectDeeplink,
      isActive: values.isActive,
    };
    
    updateMedia(payload);
  };

  return (
    <div className="p-6">
      <Form
        form={form}
        layout="vertical"
        initialValues={mediaItemData.mediaItem}
        onFinish={handleUpdate}
      >
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="section" label={<strong>Section</strong>}>
              <Input placeholder="Enter section name" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="mediaType" label={<strong>Media Type</strong>}>
              <Select placeholder="Select media type">
                <Select.Option value="IMAGE">Image</Select.Option>
                <Select.Option value="VIDEO">Video</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="url" label={<strong>URL</strong>}>
              <Input placeholder="Enter URL" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="screenType" label={<strong>Screen Type</strong>}>
              <Select placeholder="Select screen type">
                <Select.Option value="MOBILE">Mobile</Select.Option>
                <Select.Option value="DESKTOP">Desktop</Select.Option>
                <Select.Option value="TABLET">Tablet</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="redirectDeeplink"
              label={<strong>Redirect Deeplink</strong>}
            >
              <Input placeholder="Enter redirect deeplink" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="isActive"
              label={<strong>Active</strong>}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            Update
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
