import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Button, Card, Col, Divider, Image, Row, Typography } from "antd";
import { mediaTypeToJSON, screenTypeToJSON } from "~/gen/proto-models/Catalog";
import { mediaItemQueryOptions } from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";

const { Text } = Typography;

export const Route = createFileRoute(
  "/_home/home/<USER>/active/$bond-id/media-items/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    const bondID = params["bond-id"];
    await queryClient.ensureQueryData(
      mediaItemQueryOptions({ id: bondID, parentType: "BOND_DETAIL" })
    );
  },
});

function RouteComponent() {
  const { "bond-id": bondID } = Route.useParams();
  const navigate = Route.useNavigate();
  const [mediaItemQuery] = useSuspenseQueries({
    queries: [mediaItemQueryOptions({ id: bondID, parentType: "BOND_DETAIL" })],
  });

  const mediaItems = mediaItemQuery.data;

  return (
    <div>
      <Button type="primary" onClick={() => navigate({ to: "create" })}>
        Add Media Item
      </Button>
      <div className="mt-4">
        <Row gutter={16}>
          {mediaItems.mediaItems.map((item, index) => (
            <Col span={12} key={item.id}>
              <Card key={item.id} title={`Media Item ${index + 1}`} extra={
                <Button type="primary" onClick={() => navigate({ to: `$media-item-id`, params: { "media-item-id": item.id } })}>Edit</Button>
              }>
                <div className="text-center">
                  <Image width={200} src={item.url} />
                </div>
                  <div className="mt-4">
                    <Row gutter={16} key={item.id}>
                        <Col span={12}>
                          <Text strong>Section: </Text>
                          {item.section}
                        </Col>
                        <Col span={12}>
                          <Text strong>Media Type: </Text>
                          {mediaTypeToJSON(item.mediaType)}
                        </Col>
                        <Divider />
                        <Col span={12}>
                          <Text strong>Screen Type: </Text>
                          {screenTypeToJSON(item.screenType)}
                        </Col>
                        <Col span={12}>
                          <Text strong>Redirect Deeplink: </Text>
                          <Text 
                            ellipsis 
                            className="max-w-full" 
                            title={item.redirectDeeplink ? item.redirectDeeplink : "N/A"}
                          >
                            {item.redirectDeeplink ? <Link to={item.redirectDeeplink}>{item.redirectDeeplink}</Link>: "N/A"}
                          </Text>
                        </Col>
                        <Divider />
                        <Col span={12}>
                          <Text strong>Active: </Text>
                          {item.isActive ? <span className="text-green-500">Yes</span> : <span className="text-red-500">No</span>}
                        </Col>
                    </Row>
                  </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </div>
  );
}
