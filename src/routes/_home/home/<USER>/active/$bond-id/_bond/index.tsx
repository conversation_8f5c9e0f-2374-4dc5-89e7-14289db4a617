import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { bondDetailByIdQueryOptions } from "~/lib/queries/queryKeys";
import { FormComponent } from "../-components/form-component";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  "/_home/home/<USER>/active/$bond-id/_bond/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    const bondID = params["bond-id"];
    await queryClient.ensureQueryData(bondDetailByIdQueryOptions(bondID));
  },
});

function RouteComponent() {
  const { "bond-id": bondID } = Route.useParams();

  const [bondDetailQuery] = useSuspenseQueries({
    queries: [bondDetailByIdQueryOptions(bondID)],
  });

  const bond = bondDetailQuery.data;
  return (
    <div>
      <FormComponent bond={bond} />
    </div>
  );
}
