import { CheckOutlined } from "@ant-design/icons";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Button, message, Modal, Popconfirm, Table, Tooltip } from "antd";
import type { ColumnsType } from "antd/es/table";
import QueryRenderer from "~/components/functional/query-renderer";
import {
  getBondCashflowSchedule,
  verifyRecordDate,
} from "~/lib/queries/bond-catalog";
import { handleError } from "~/lib/utils/error";

interface Props {
  bondID: string;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

interface BondCashflowSchedule {
  id: string;
  date: string;
  interestPaymentAmount: number;
  principalRepaymentAmount: number;
  recordDate: string;
  type: number;
  faceValue: number;
  isActive: boolean;
  maturityAmount: number;
  isRecordDateVerified: boolean;
}

export function CashflowScheduleModal({
  bondID,
  isModalOpen,
  setIsModalOpen,
}: Props) {
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ["bond-cashflow-schedule", bondID],
    queryFn: () => getBondCashflowSchedule(bondID),
  });

  const { mutate: verifyRecordDateItem } = useMutation({
    mutationFn: verifyRecordDate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["bond-cashflow-schedule", bondID],
      });
      message.success("Record date verified successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to verify record date");
    },
  });

  const handleVerifyRecordDate = (
    bondDetailId: string,
    cashflowScheduleId: string
  ) => {
    verifyRecordDateItem({
      bondDetailId,
      cashflowScheduleId,
    });
  };

  return (
    <>
      <Modal
        title="Cashflow Schedule"
        open={isModalOpen}
        onOk={() => setIsModalOpen(false)}
        onCancel={() => setIsModalOpen(false)}
        width="60%"
        footer={null}
        destroyOnHidden
      >
        <QueryRenderer query={query}>
          {(data) => {
            const sortedData = [...data.bondCashflowSchedule].sort(
              (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
            );

            const columns: ColumnsType<BondCashflowSchedule> = [
              {
                title: "Date",
                dataIndex: "date",
                key: "date",
                width: 120,
                fixed: "left",
              },
              {
                title: "Principal",
                dataIndex: "principalRepaymentAmount",
                key: "principalRepaymentAmount",
                width: 120,
              },
              {
                title: "Interest",
                dataIndex: "interestPaymentAmount",
                key: "interestPaymentAmount",
                width: 120,
              },
              {
                title: "Record Date",
                dataIndex: "recordDate",
                key: "recordDate",
                width: 120,
              },
              {
                title: "Repayment Type",
                dataIndex: "type",
                key: "type",
                width: 120,
                render: (type: number) => {
                  switch (type) {
                    case 1:
                      return "Interest Payment";
                    case 2:
                      return "Principal Repayment";
                    case 3:
                      return "Principal Repayment and Interest Payment";
                    default:
                      return "Unknown";
                  }
                },
              },
              {
                title: "Face Value",
                dataIndex: "faceValue",
                key: "faceValue",
                width: 120,
              },
              {
                title: "Status",
                dataIndex: "isActive",
                key: "isActive",
                width: 120,
                render: (isActive: boolean) =>
                  isActive ? (
                    <span className="text-green-500">Yes</span>
                  ) : (
                    <span className="text-red-500">No</span>
                  ),
              },
              {
                title: "Maturity Amount",
                dataIndex: "maturityAmount",
                key: "maturityAmount",
                width: 120,
              },
              {
                title: "Record Date Verified",
                dataIndex: "isRecordDateVerified",
                key: "isRecordDateVerified",
                width: 120,
                render: (_, record, index) => {
                  const firstUnverifiedIndex = sortedData?.findIndex(
                    (item) => !item.isRecordDateVerified
                  );
                  const isFirstUnverified =
                    !record.isRecordDateVerified &&
                    index === firstUnverifiedIndex;

                  return record.isRecordDateVerified ? (
                    <span className="text-green-500">Yes</span>
                  ) : (
                    <>
                      <span className="text-red-500 mr-2">No</span>
                      {isFirstUnverified && (
                        <Popconfirm
                          title="Are you sure you want to verify this record date?"
                          okText="Yes"
                          cancelText="No"
                          onConfirm={() =>
                            handleVerifyRecordDate(bondID, record.id)
                          }
                        >
                          <Tooltip title="Verify Record Date"
                          >
                            <Button icon={<CheckOutlined />} size="small" />
                          </Tooltip>
                        </Popconfirm>
                      )}
                    </>
                  );
                },
              },
            ];

            return (
              <Table
                bordered
                rowKey="id"
                columns={columns}
                dataSource={sortedData}
                scroll={{ y: 400, x: 500 }}
                pagination={false}
                size="small"
              />
            );
          }}
        </QueryRenderer>
      </Modal>
    </>
  );
}
