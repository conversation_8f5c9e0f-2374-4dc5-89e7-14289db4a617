import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Switch,
} from "antd";
import dayjs from "dayjs";
import {
  BondDetailsUpdateRequest,
  BondType,
  bondTypeFromJSON,
  InvestabilityStatus,
  investabilityStatusFromJSON,
  PartyDetail,
  RepaymentFrequency,
  repaymentFrequencyFromJSON,
  type BondDetailsDashboard,
} from "~/gen/proto-models/Catalog";
import { updateBondDetails } from "~/lib/queries/bond-catalog";
import { bondDetailByIdQueryOptions, partyDetailsQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

const investabilityOptions = [
  { label: "Live", value: InvestabilityStatus.LIVE },
  { label: "Sold Out", value: InvestabilityStatus.SOLD_OUT },
  { label: "Coming Soon", value: InvestabilityStatus.COMING_SOON },
  { label: "Inactive", value: InvestabilityStatus.INACTIVE },
];

const repaymentOptions = [
  { label: "Monthly", value: RepaymentFrequency.MONTHLY },
  { label: "Quarterly", value: RepaymentFrequency.QUARTERLY },
  { label: "Half Yearly", value: RepaymentFrequency.HALF_YEARLY },
  { label: "Yearly", value: RepaymentFrequency.YEARLY },
  { label: "Cumulative", value: RepaymentFrequency.CUMULATIVE },
];

const bondTypeOptions = [
  { label: "T-Bill", value: BondType.T_BILL },
  { label: "Govt Securities", value: BondType.GOVT_SECURITIES },
  { label: "State Development Loan", value: BondType.STATE_DEVELOPMENT_LOAN },
  { label: "Municipal Bonds", value: BondType.MUNICIPAL_BONDS },
  { label: "Corporate Bonds", value: BondType.CORPORATE_BONDS },
];

export function FormComponent({ bond }: { bond: BondDetailsDashboard }) {
  const [form] = Form.useForm();

  const queryClient = useQueryClient();

  const query = useQuery(partyDetailsQueryOptions());

  const partyOptions = query.data?.partyDetails.map((item: PartyDetail) => {
    return { label: item.name, value: item.id };
  });

  const { mutate: updateBondMutation } = useMutation({
    mutationFn: updateBondDetails,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: bondDetailByIdQueryOptions(bond.id).queryKey,
      });
      message.success("Bond updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update bond");
    },
  });

  const handleUpdate = (values: BondDetailsUpdateRequest) => {
    const payload: BondDetailsUpdateRequest = {
      ...values,
      id: bond.id,
      isinCode: bond.isinCode,
      name: bond.name,
      couponFrequency: repaymentFrequencyFromJSON(values.couponFrequency),
      principalRepaymentFrequency: repaymentFrequencyFromJSON(
        values.principalRepaymentFrequency
      ),
      bondType: bondTypeFromJSON(values.bondType),
      investabilityStatus: investabilityStatusFromJSON(values.investabilityStatus),
      maturityDate: values.maturityDate ? dayjs(values.maturityDate).format("YYYY-MM-DD") : undefined,
      issueDate: values.issueDate ? dayjs(values.issueDate).format("YYYY-MM-DD") : undefined,
      ratingDate: values.ratingDate ? dayjs(values.ratingDate).format("YYYY-MM-DD") : undefined,
      dailySellLimit: values.dailySellLimit ? "-1" : "0",
      buyerPartyId: values.buyerPartyId ? values.buyerPartyId : undefined,
    };

    updateBondMutation(payload);
  };
  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        ...bond,
        maturityDate: bond.maturityDate ? dayjs(bond.maturityDate) : null,
        ratingDate: bond.ratingDate === "-" ? null : dayjs(bond.ratingDate),
        issueDate: bond.issueDate ? dayjs(bond.issueDate) : null,
        buyerPartyId: bond.buyerPartyId ? bond.buyerPartyId : null,
        dailySellLimit: bond.dailySellLimit === "-1",
      }}
      onFinish={handleUpdate}
    >
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label={<strong>Issuer</strong>} name="issuer">
            <Link
              to="/home/<USER>/$isin-id"
              params={{ "isin-id": bond.bondIssuingInstitutionId }}
              search={{ isinID: bond.bondIssuingInstitutionId }}
            >
              {bond.issuer}
            </Link>
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Display Title</strong>} name="displayTitle">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Type</strong>} name="type">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Maturity Date</strong>} name="maturityDate">
            <DatePicker className="w-full" format="YYYY-MM-DD" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item
            label={<strong>Investability Status</strong>}
            name="investabilityStatus"
          >
            <Select options={investabilityOptions} />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Coupon Frequency</strong>}
            name="couponFrequency"
          >
            <Select options={repaymentOptions} />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Principal Repayment Frequency</strong>}
            name="principalRepaymentFrequency"
          >
            <Select options={repaymentOptions} />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Bond Type</strong>} name="bondType">
            <Select options={bondTypeOptions} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
            <Form.Item label={<strong>Coupon Rate</strong>} name="couponRate">
              <Input className="w-full" />
            </Form.Item>
        </Col>
        <Col span={6}>
            <Form.Item
              label={<strong>Per User Purchase Limit</strong>}
              name="perUserPurchaseLimit"
            >
              <Input className="w-full" />
            </Form.Item>
        </Col>
        <Col span={6}>
            <Form.Item label={<strong>Min Lot Size</strong>} name="minLotSize">
              <Input className="w-full" />
            </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Buyer Party ID</strong>} name="buyerPartyId">
            <Select placeholder="Select Buyer Party" options={partyOptions} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
            <Form.Item label={<strong>Issue Size</strong>} name="issueSize">
              <Input className="w-full" />
            </Form.Item>
        </Col>
        <Col span={6}>
            <Form.Item label={<strong>Issue Price</strong>} name="issuePrice">
              <Input className="w-full" />
            </Form.Item>
        </Col>
        <Col span={6}>
            <Form.Item
              label={<strong>Face Value</strong>}
              name="issueFaceValue"
            >
              <Input className="w-full" />
            </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Principal Desc</strong>}
            name="principalRepaymentFrequencyDesc"
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label={<strong>Rating</strong>} name="rating">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Rating Agency</strong>} name="ratingAgency">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Rating URL</strong>}
            name="ratingSupportingUrl"
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Rating Date</strong>} name="ratingDate">
            <DatePicker className="w-full" format="YYYY-MM-DD" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label={<strong>Issue Mode</strong>} name="issueMode">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Nature</strong>} name="nature">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Seniority</strong>} name="seniority">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Coupon Type</strong>} name="couponType">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label={<strong>Yield Type</strong>} name="typeOfYield">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Information Memorandum</strong>}
            name="informationMemorandum"
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Debenture Trustee</strong>}
            name="debuntureTrustee"
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Put/Call</strong>} name="putCall">
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label={<strong>Background Color</strong>} name="bgColor">
            <Input type="color" />
          </Form.Item>
        </Col>
        <Col span={6}>
            <Form.Item label={<strong>Sell Spread</strong>} name="sellSpread">
              <Input className="w-full" />
            </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Nature of Bond</strong>} name="natureOfBond">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Issue Date</strong>} name="issueDate">
            <DatePicker className="w-full" format="YYYY-MM-DD" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item
            label={<strong>Block Trade</strong>}
            name="blockTrade"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Active</strong>}
            name="isActive"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Include in Collection</strong>}
            name="includeInCollection"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>T+0 Settlement Supported</strong>}
            name="isTZeroSettlementSupported"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Include in Search</strong>}
            name="includeInSearch"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Sell Allowed</strong>}
            name="dailySellLimit"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item>
        <Button className="mt-4" type="primary" htmlType="submit">
          Update
        </Button>
      </Form.Item>
    </Form>
  );
}
