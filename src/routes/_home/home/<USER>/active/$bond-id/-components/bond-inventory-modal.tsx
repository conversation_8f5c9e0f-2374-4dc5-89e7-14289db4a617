import { Modal, Descriptions, type DescriptionsProps } from "antd";
import { useQuery } from "@tanstack/react-query";
import { bondInventoryQueryOptions } from "~/lib/queries/queryKeys";
import { inventoryAlarmStatusFromJSON, inventoryAlarmStatusToJSON } from "~/gen/proto-models/Catalog";

interface BondInventoryModalProps {
  offeringId: string | null;
  visible: boolean;
  onClose: () => void;
}

export function BondInventoryModal({ offeringId, visible, onClose }: BondInventoryModalProps) {
  const bondInventoryQuery = useQuery(
    bondInventoryQueryOptions(offeringId ?? "")
  );

  const items: DescriptionsProps["items"] = bondInventoryQuery.data
    ? [
        {
          key: "1",
          label: <strong>Total Count</strong>,
          children: bondInventoryQuery.data?.totalCount,
          span: 2,
        },
        {
          key: "2",
          label: <strong>Order Limit</strong>,
          children: bondInventoryQuery.data?.orderLimit,
        },
        {
          key: "3",
          label: <strong>Alarm Status</strong>,
          children: inventoryAlarmStatusToJSON(
            inventoryAlarmStatusFromJSON(bondInventoryQuery.data?.alarmStatus)
          ),
        },
      ]
    : [];

  return (
    <Modal
      title="Bond Inventory"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnHidden
    >
      {bondInventoryQuery.isLoading ? (
        <p>Loading...</p>
      ) : bondInventoryQuery.isError ? (
        <p>Error loading inventory details.</p>
      ) : (
        <Descriptions column={2} size="middle" bordered items={items} />
      )}
    </Modal>
  );
}
