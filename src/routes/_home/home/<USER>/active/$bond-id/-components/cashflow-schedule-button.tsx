import { Button } from "antd";
import { CashflowScheduleModal } from "./cashflow-schedule-modal";
import { useState } from "react";

export function CashflowScheduleButton({bondID, buttonType, buttonSize}: {bondID: string, buttonType?: "primary" | "link", buttonSize?: "large" | "small" | "middle"}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  return (
    <>
        <Button type={buttonType || "primary"} onClick={() => setIsModalOpen(true)} size={buttonSize || "middle"}>
            Cashflow Schedule
        </Button>
        <CashflowScheduleModal bondID={bondID} isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} />
    </>
  );
}
