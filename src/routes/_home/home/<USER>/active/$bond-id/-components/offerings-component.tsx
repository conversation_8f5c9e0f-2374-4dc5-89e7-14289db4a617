import { BookOutlined, EditOutlined } from "@ant-design/icons";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Button, Switch, Table, type TableProps, Modal, message, InputNumber } from "antd";
import { useState } from "react";
import { partyDetailsQueryOptions } from "~/lib/queries/queryKeys";
import { BondInventoryModal } from "./bond-inventory-modal";
import type {
  BondDetailsDashboard,
  BondOfferingDetail,
  BondOfferingDetailResponse,
  PartyDetail,
  UpdateBondOfferingYieldRequest,
  UpdateOfferingActiveStatusRequest,
} from "~/gen/proto-models/Catalog";
import { updateBondOfferingYield, updateOfferingActiveStatus } from "~/lib/queries/bond-catalog";
import { handleError } from "~/lib/utils/error";

interface DataType {
  id: string;
  party_name: string;
  is_active: boolean;
  yeild: number;
}

export function OfferingsComponent({
  bondOfferings,
  bond
}: {
  bondOfferings: BondOfferingDetailResponse;
  bond: BondDetailsDashboard;
}) {
  const partyDetails = useQuery(partyDetailsQueryOptions());
  const [selectedOfferingId, setSelectedOfferingId] = useState<string | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingYield, setEditingYield] = useState<{ id: string; value: number } | null>(null);

  const queryClient = useQueryClient();

  const updateOfferingStatus = useMutation({
    mutationFn: async (data: UpdateOfferingActiveStatusRequest) => {
      return updateOfferingActiveStatus(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["bond-offering-detail"],
      });
      message.success("Offering updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update offering");
    },
  });

  const updateOfferingYield = useMutation({
    mutationFn: async (data: UpdateBondOfferingYieldRequest) => {
      return updateBondOfferingYield(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["bond-offering-detail"],
      });
      message.success("Yield updated successfully");
      setEditingYield(null);
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update yield, check for Record date verification");
    },
  });

  const handleYieldUpdate = (offeringId: string, currentYield: number) => {
    setEditingYield({ id: offeringId, value: currentYield });
  };

  const handleYieldSave = () => {
    if (!editingYield) return;

    if (editingYield.value <= 0) {
      message.error("Yield must be greater than 0");
      return;
    }

    updateOfferingYield.mutate({
      bondOfferingId: editingYield.id,
      yield: editingYield.value,
    });
  };

  const handleInventoryClick = (offeringId: string) => {
    setSelectedOfferingId(offeringId);
    setModalVisible(true);
  };

  const handleSwitchChange = (checked: boolean, record: DataType) => {
    Modal.confirm({
      title: "Confirm Status Change",
      content: `Are you sure you want to ${checked ? 'activate' : 'deactivate'} this offering?`,
      okText: "Yes, Change",
      cancelText: "Cancel",
      onOk: () => {
        updateOfferingStatus.mutate({
          id: record.id,
          isActive: checked,
        });
      },
    });
  };

  const dataSource: DataType[] = bondOfferings.bondOfferingDetails.map(
    (item: BondOfferingDetail) => {
      const party = partyDetails.data?.partyDetails.find(
        (p: PartyDetail) => p.id === item.partyId
      );
      return {
        id: item.id,
        party_name: party?.name || "Unknown",
        is_active: item.isActive,
        yeild: item.yield,
      };
    }
  );

  const columns: TableProps<DataType>["columns"] = [
    {
      title: "Party Name",
      dataIndex: "party_name",
      key: "party_name",
      width: "25%",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "20%",
      render: (_, record) => (
        <Switch
          checkedChildren="Active"
          unCheckedChildren="In-Active"
          checked={record.is_active}
          onChange={(checked) => handleSwitchChange(checked, record)}
        />
      ),
    },
    {
      title: "Yield",
      dataIndex: "yeild",
      key: "yeild",
      width: "25%",
      render: (value, record) => (
        <div className="flex items-center gap-2">
          {editingYield?.id === record.id ? (
            <div className="flex items-center gap-2">
              <InputNumber
                value={editingYield.value}
                onChange={(val) => setEditingYield(prev => prev ? { ...prev, value: val || 0 } : null)}
                step={0.01}
                precision={2}
                size="small"
                className="w-20"
              />
              <Button size="small" type="primary" onClick={handleYieldSave}>
                Save
              </Button>
              <Button size="small" onClick={() => setEditingYield(null)}>
                Cancel
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <span className="font-semibold text-green-600">{value}%</span>
              {bond.bondType === 5 && (
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleYieldUpdate(record.id, value)}
                  title="Edit Yield"
                />
              )}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Actions",
      key: "action",
      width: "15%",
      align: "center",
      render: (_, record) => (
        <Button
          type="text"
          icon={<BookOutlined />}
          onClick={() => handleInventoryClick(record.id)}
          title="View Inventory"
        />
      ),
    },
  ];

  return (
    <>
      <Table<DataType>
        columns={columns}
        dataSource={dataSource}
        rowKey="id"
        pagination={false}
      />

      <BondInventoryModal
        offeringId={selectedOfferingId}
        visible={modalVisible}
        onClose={() => {
          setModalVisible(false);
          setSelectedOfferingId(null);
        }}
      />
    </>
  );
}
