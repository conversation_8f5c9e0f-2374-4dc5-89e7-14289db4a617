import { createFileRoute, Outlet } from "@tanstack/react-router";
import {
  Tag,
  Card,
  Tabs,
  type TabsProps,
} from "antd";
import { useSuspenseQueries } from "@tanstack/react-query";
import { bondDetailByIdQueryOptions } from "~/lib/queries/queryKeys";
import { CashflowScheduleButton } from "./-components/cashflow-schedule-button";
import { BondType, bondTypeFromJSON } from "~/gen/proto-models/Catalog";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  "/_home/home/<USER>/active/$bond-id"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    const bondID = params["bond-id"];
    await queryClient.ensureQueryData(bondDetailByIdQueryOptions(bondID));
  },
});

function RouteComponent() {
  const { "bond-id": bondID } = Route.useParams();

  const navigate = Route.useNavigate();

  const [bondDetailQuery] = useSuspenseQueries({
    queries: [bondDetailByIdQueryOptions(bondID)],
  });

  const handleTabClick = (key: string) => {
    if (key === "1") {
      navigate({
        to: "/home/<USER>/active/$bond-id",
        params: { "bond-id": bondID },
      });
    }

    if (key === "2") {
      navigate({
        to: "/home/<USER>/active/$bond-id/offerings",
        params: { "bond-id": bondID },
      });
    }

    if(key === "3"){
      navigate({
        to: "/home/<USER>/active/$bond-id/media-items",
        params: { "bond-id": bondID },
      });
    }
  };

  const bond = bondDetailQuery.data;

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Bond Info",
    },
    {
      key: "2",
      label: "Offerings",
    },
    {
      key: "3",
      label: "Bond Media Items",
    },
  ];

  return (
    <div className="p-6 space-y-4">
      <Card>
        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <strong>Bond : </strong>
            <Tag color="blue">{bond.name}</Tag>
            <strong>ISIN : </strong>
            <Tag color="green">{bond.isinCode}</Tag>
          </div>
          <div>
            {bondTypeFromJSON(bond.bondType) === BondType.CORPORATE_BONDS && (
              <CashflowScheduleButton bondID={bondID} />)}
          </div>
        </div>
        <div className="mt-4">
          <Tabs
            defaultActiveKey="1"
            items={items}
            onTabClick={(key) => {
              handleTabClick(key);
            }}
          />
        </div>
        <Outlet />
      </Card>
    </div>
  );
}
