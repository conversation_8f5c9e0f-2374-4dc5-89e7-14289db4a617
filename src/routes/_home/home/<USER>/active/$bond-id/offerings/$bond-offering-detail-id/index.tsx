import { useSuspenseQueries } from '@tanstack/react-query';
import { createFileRoute } from '@tanstack/react-router'
import { Collapse, type CollapseProps } from 'antd'
import { bondDetailByIdQueryOptions, bondOfferingDetailQueryOptions } from '~/lib/queries/queryKeys';
import { OfferingComponent } from './-components/offering-component';
import { InventoryComponent } from './-components/inventory-component';
import { PricingComponent } from './-components/pricing-component';
import { BondType, bondTypeFromJSON } from '~/gen/proto-models/Catalog';
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  '/_home/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id/',
)({
  component: RouteComponent,
  loader: async({ params }) => {
    const bondID = params["bond-id"]
    await Promise.all([
      queryClient.ensureQueryData(bondOfferingDetailQueryOptions(bondID)),
      queryClient.ensureQueryData(bondDetailByIdQueryOptions(bondID)),
    ]);
  },
})

function RouteComponent() {
  const { "bond-id": bondID } = Route.useParams();
  const { "bond-offering-detail-id": bondOfferingDetailID } = Route.useParams();

  const [bondOfferingDetailQuery, bondDetailQuery] = useSuspenseQueries({
    queries: [bondOfferingDetailQueryOptions(bondID), bondDetailByIdQueryOptions(bondID)],
  });

  const bondOfferingDetail = bondOfferingDetailQuery.data;
  const bond = bondDetailQuery.data;

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: <strong>Inventory</strong>,
      children: <InventoryComponent bondOfferingId={bondOfferingDetailID} />,
    },
    {
      key: '2',
      label: <strong>Offering Details</strong>,
      children: <OfferingComponent bondOfferingDetail={bondOfferingDetail.bondOfferingDetails.find((item) => item.id === bondOfferingDetailID)!} isCorporate={bondTypeFromJSON(bond.bondType) === BondType.CORPORATE_BONDS ? true : false} />,
    },
    {
      key: '3',
      label: <strong>Pricing</strong>,
      children: <PricingComponent bondOfferingId={bondOfferingDetailID} />,
    },
  ]
  return (
      <Collapse
        collapsible="header"
        items={items}
        key={bondOfferingDetailID}
        defaultActiveKey={['1', '2', '3']}
      />
  )
}
