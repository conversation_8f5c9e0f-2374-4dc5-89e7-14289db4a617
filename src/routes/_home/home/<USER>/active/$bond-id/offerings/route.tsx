
import {
  useQueryClient,
  useMutation,
  useSuspenseQueries,
} from "@tanstack/react-query";
import { createFileRoute, Outlet } from "@tanstack/react-router";
import { Col, message, Modal, Row, Switch, Table, type TableProps } from "antd";
import { useEffect, useState } from "react";
import type {
  BondOfferingDetail,
  PartyDetail,
} from "~/gen/proto-models/Catalog";
import { updateOfferingActiveStatus } from "~/lib/queries/bond-catalog";
import {
  bondOfferingDetailQueryOptions,
  partyDetailsQueryOptions,
} from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  "/_home/home/<USER>/active/$bond-id/offerings"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    const bondID = params["bond-id"];
    await Promise.all([
      queryClient.ensureQueryData(bondOfferingDetailQueryOptions(bondID)),
      queryClient.ensureQueryData(partyDetailsQueryOptions()),
    ]);
  },
});

interface OfferingTable {
  id: string;
  party_name: string;
  is_active: boolean;
}

function RouteComponent() {
  const { "bond-id": bondID } = Route.useParams();
  const queryClient = useQueryClient();
  const navigate = Route.useNavigate();

  const [bondOfferingDetailQuery, partyDetailsQuery] = useSuspenseQueries({
    queries: [
      bondOfferingDetailQueryOptions(bondID),
      partyDetailsQueryOptions(),
    ],
  });

  const { mutate: updateOfferingStatus } = useMutation({
    mutationFn: updateOfferingActiveStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: bondOfferingDetailQueryOptions(bondID).queryKey,
      });
      message.success("Offering updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update offering");
    },
  });

  const handleStatusUpdate = (checked: boolean, record: OfferingTable) => {
    Modal.confirm({
      title: "Confirm Status Change",
      content: `Are you sure you want to ${checked ? "activate" : "deactivate"} this offering?`,
      okText: "Yes, Change",
      cancelText: "Cancel",
      onOk: () => {
        updateOfferingStatus({
          id: record.id,
          isActive: checked,
        });
      },
    });
  };

  const bondOfferingDetail = bondOfferingDetailQuery.data;
  const partyDetails = partyDetailsQuery.data;

  const dataSource: OfferingTable[] =
    bondOfferingDetail.bondOfferingDetails.map((item: BondOfferingDetail) => {
      const party = partyDetails.partyDetails.find(
        (p: PartyDetail) => p.id === item.partyId
      );
      return {
        id: item.id,
        party_name: party?.name || "Unknown",
        is_active: item.isActive,
      };
    });

  const [selectedKey, setSelectedKey] = useState<string[]>([]);

  useEffect(()=>{
    if (dataSource.length > 0) {
      const firstId = dataSource[0].id;
      setSelectedKey([firstId]);
      navigate({
        to: "/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id",
        params: {
          "bond-id": bondID,
          "bond-offering-detail-id": firstId,
        },
      });
    }
  },[])

  const columns: TableProps<OfferingTable>["columns"] = [
    {
      title: "Party Name",
      dataIndex: "party_name",
      key: "party_name",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      render: (_, record) => (
        <Switch
          checkedChildren="Active"
          unCheckedChildren="In-Active"
          checked={record.is_active}
          onChange={(checked) => handleStatusUpdate(checked, record)}
        />
      ),
    },
  ];

  return (
    <Row gutter={16}>
      <Col span={8}>
        <Table
          bordered
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          pagination={false}
          rowSelection={{
            type: "radio",
            columnTitle: "Select",
            selectedRowKeys: selectedKey,
            onSelect: (record) => {
              setSelectedKey([record.id]);
              navigate({
                to: "/home/<USER>/active/$bond-id/offerings/$bond-offering-detail-id",
                params: {
                  "bond-id": bondID,
                  "bond-offering-detail-id": record.id,
                },
              })
            },
          }}
        />
      </Col>
      <Col span={16} >
        <Outlet />
      </Col>
    </Row>
  );
}
