import { useQuery } from "@tanstack/react-query";
import { Table, Tag, type TableProps } from "antd";
import QueryRenderer from "~/components/functional/query-renderer";
import type { BondPrice } from "~/gen/proto-models/Catalog";
import { getBondPricesQueryOptions } from "~/lib/queries/queryKeys";

export function PricingComponent({ bondOfferingId }: { bondOfferingId: string }) {
  const query = useQuery(getBondPricesQueryOptions(bondOfferingId));
  const column: TableProps<BondPrice>['columns'] = [
    {
      title: "Settlement Date",
      dataIndex: "settlementDate",
      key: "settlementDate",
      render: (text) => {
        return (
            <Tag color="blue">{text}</Tag>
        );
      },
    },
    {
      title: "Clean Price",
      dataIndex: "cleanPrice",
      key: "cleanPrice",
    },
    {
      title: "Dirty Price",
      dataIndex: "dirtyPrice",
      key: "dirtyPrice",
    },
    {
      title: "Face Value",
      dataIndex: "faceValue",
      key: "faceValue",
      render: (text) => {
        return (
            <div className="text-green-500">{text}</div>
        );
      },
    },
    {
      title: "Accrued Interest",
      dataIndex: "accruedInterest",
      key: "accruedInterest",
    },
    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      render: (isActive: boolean) =>
        isActive ? (
          <Tag color="green">Active</Tag>
        ) : (
          <Tag color="red">Inactive</Tag>
        ),
    },
  ];
  return (
    <QueryRenderer query={query}>
        {(data) => {
          return (
            <Table
              bordered
              columns={column}
              dataSource={data.bondPrices}
              rowKey="id"
              pagination={false}
              scroll={{ y: "500px" }}
            />
          );
        }}
    </QueryRenderer>
  );
}
