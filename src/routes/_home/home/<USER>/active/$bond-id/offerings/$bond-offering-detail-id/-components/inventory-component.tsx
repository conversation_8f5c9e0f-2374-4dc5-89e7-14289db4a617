import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Switch,
} from "antd";
import dayjs from "dayjs";
import { useEffect } from "react";
import QueryRenderer from "~/components/functional/query-renderer";
import {
  InventoryAlarmStatus,
  inventoryAlarmStatusToJSON,
  UpdateBondInventoryRequest,
} from "~/gen/proto-models/Catalog";
import { updateBondInventory } from "~/lib/queries/bond-catalog";
import { bondInventoryQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

const alarmStatusOptions = [
  { label: "Normal", value: InventoryAlarmStatus.NORMAL },
  { label: "Level 1", value: InventoryAlarmStatus.LEVEL_1 },
];

export function InventoryComponent({
  bondOfferingId,
}: {
  bondOfferingId: string;
}) {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const bondInventoryQuery = useQuery(
    bondInventoryQueryOptions(bondOfferingId)
  );

  const { mutate: updateBondInventoryItem } = useMutation({
    mutationFn: updateBondInventory,
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ["bond-inventory", bondOfferingId],
      });
      message.success("Inventory updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update inventory");
    },
  });

  const handleUpdate = (values: UpdateBondInventoryRequest) => {
    const payload: UpdateBondInventoryRequest = {
      ...values,
      bondOfferingId,
      validFrom: values.validFrom
        ? dayjs(values.validFrom).format("YYYY-MM-DDTHH:mm:ss.SSS")
        : "",
      validTill: values.validTill
        ? dayjs(values.validTill).format("YYYY-MM-DDTHH:mm:ss.SSS")
        : "",
    };

    updateBondInventoryItem(payload);
  };
  return (
    <QueryRenderer query={bondInventoryQuery}>
      {(data) => {
        useEffect(() => {
          form.setFieldsValue({
            totalCount: data?.totalCount ? data.totalCount : null,
            count: data?.count ? data.count : null,
            totalCountDifference: "",
          });
        }, [data]);

        return (
          <Form
            form={form}
            layout="vertical"
            key={data?.totalCount}
            initialValues={{
              count: data?.count ? data.count : null,
              maxCount: data?.maxCount ? data.maxCount : null,
              totalCount: data?.totalCount ? data.totalCount : null,
              orderLimit: data?.orderLimit ? data.orderLimit : null,
              alarmStatus: inventoryAlarmStatusToJSON(data?.alarmStatus!),
              validFrom: data?.validFrom ? dayjs(data.validFrom) : null,
              validTill: data?.validTill ? dayjs(data.validTill) : null,
              isActive: data?.isActive,
            }}

            onFinish={handleUpdate}
          >
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  label={<strong>Total Count Inc/Dec</strong>}
                  name="totalCountDifference"
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={<strong>Count</strong>} name="count">
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={<strong>Max Count</strong>} name="maxCount">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={<strong>Valid From</strong>} name="validFrom">
                  <DatePicker />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  label={<strong>Total Count</strong>}
                  name="totalCount"
                >
                  <Input disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={<strong>Order Limit</strong>}
                  name="orderLimit"
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={<strong>Alarm Status</strong>}
                  name="alarmStatus"
                >
                  <Select options={alarmStatusOptions} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label={<strong>Valid Till</strong>} name="validTill">
                  <DatePicker />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item
                  label={<strong>Active</strong>}
                  name="isActive"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Update
              </Button>
            </Form.Item>
          </Form>
        );
      }}
    </QueryRenderer>
  );
}
