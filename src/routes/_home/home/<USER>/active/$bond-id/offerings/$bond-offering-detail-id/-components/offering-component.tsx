import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  <PERSON>ton,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Switch,
} from "antd";
import dayjs from "dayjs";
import type {
  BondOfferingDetail,
  BondOfferingUpdateRequest,
} from "~/gen/proto-models/Catalog";
import { updateBondOffering } from "~/lib/queries/bond-catalog";
import { bondOfferingDetailQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

interface OfferingComponentProps {
  bondOfferingDetail: BondOfferingDetail;
  isCorporate: boolean;
}

export function OfferingComponent({
  bondOfferingDetail,
  isCorporate,
}: OfferingComponentProps) {
  const [form] = Form.useForm();

  const queryClient = useQueryClient();

  const { mutate: updateOffering } = useMutation({
    mutationFn: updateBondOffering,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: bondOfferingDetailQueryOptions(
          bondOfferingDetail!.bondDetailId
        ).queryKey,
      });
      message.success("Offering updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update offering");
    },
  });

  const handleUpdate = (values: BondOfferingDetail) => {
    const payload: BondOfferingUpdateRequest = {
      bondOfferingId: bondOfferingDetail!.id,
      yield: values.yield,
      minLotSize: values.minLotSize?.toString(),
      repeatUserDefaultQuantity: values.repeatUserDefaultQuantity?.toString(),
      dealType: values.dealType,
      bidOffer: values.bidOffer,
      bondSettlementType: values.bondSettlementType?.toString(),
      expiryTime: values.expiryTime
        ? dayjs(values.expiryTime).toISOString()
        : values.expiryTime,
      newUserDefaultQuantity: values.newUserDefaultQuantity?.toString(),
      isTZeroSettlementSupported: values.isTZeroSettlementSupported,
    };

    updateOffering(payload);
  };

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        ...bondOfferingDetail,
        expiryTime: bondOfferingDetail?.expiryTime
          ? dayjs(bondOfferingDetail.expiryTime)
          : null,
        repeatUserDefaultQuantity: bondOfferingDetail?.repeatUserDefaultQuantity
          ? bondOfferingDetail.repeatUserDefaultQuantity
          : null,
        newUserDefaultQuantity: bondOfferingDetail?.newUserDefaultQuantity
          ? bondOfferingDetail.newUserDefaultQuantity
          : null,
        bidOffer: bondOfferingDetail?.bidOffer
          ? bondOfferingDetail.bidOffer
          : null,
        dealType: bondOfferingDetail?.dealType
          ? bondOfferingDetail.dealType
          : null,
        bondSettlementType: bondOfferingDetail?.bondSettlementType
          ? bondOfferingDetail.bondSettlementType
          : null,
        yield: bondOfferingDetail?.yield ? bondOfferingDetail.yield : null,
      }}
      onFinish={handleUpdate}
    >
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label={<strong>Deal Type</strong>} name="dealType">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Bid Offer</strong>} name="bidOffer">
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label={<strong>Min Lot Size</strong>} name="minLotSize">
            <Input className="w-full" />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Bond Settlement Type</strong>}
            name="bondSettlementType"
          >
            <Input className="w-full" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label={<strong>Expiry Time</strong>} name="expiryTime">
            <DatePicker className="w-full" />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>Repeat User DQ</strong>}
            name="repeatUserDefaultQuantity"
          >
            <Input className="w-full" />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            label={<strong>New User DQ</strong>}
            name="newUserDefaultQuantity"
          >
            <Input className="w-full" />
          </Form.Item>
        </Col>
        {isCorporate && (
          <Col span={6}>
            <Form.Item label={<strong>Yield</strong>} name="yield">
              <InputNumber className="w-full" />
            </Form.Item>
          </Col>
        )}
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item
            label={<strong>T + 0 Settlement Supported</strong>}
            name="isTZeroSettlementSupported"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item>
        <Button className="mt-4" type="primary" htmlType="submit">
          Update
        </Button>
      </Form.Item>
    </Form>
  );
}
