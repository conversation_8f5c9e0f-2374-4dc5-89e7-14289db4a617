import { createFile<PERSON>out<PERSON>, <PERSON> } from '@tanstack/react-router'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Form, Input, Button, Card, Breadcrumb, Row, Col, message } from 'antd'
import { HomeOutlined, PlusOutlined } from '@ant-design/icons'
import { useForm } from 'antd/es/form/Form'
import { handleError } from '~/lib/utils/error'
import type { CreateAccessResourceRequest } from '~/lib/types/access-control'
import { createAccessResource } from '~/lib/queries/access-control'

export const Route = createFileRoute('/_home/home/<USER>/create/')({
  component: RouteComponent,
})

function RouteComponent() {
  const [form] = useForm()
  const queryClient = useQueryClient()

  const { mutate: createAccessResourceItem } = useMutation({
    mutationFn: createAccessResource,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["access-resources"] })
      message.success("Access resource created successfully!")
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create access resource")
    },
  })

  const onFinish = (values: CreateAccessResourceRequest) => {
    createAccessResourceItem(values)
  }

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                Access Resources
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={<strong>Name</strong>}
                  name="name"
                  rules={[
                    { required: true, message: "Please enter a resource name!" },
                  ]}
                >
                  <Input placeholder="Resource Name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={<strong>URL Mappings</strong>}
                  name="urlMappings"
                  rules={[
                    { required: true, message: "Please enter URL mappings!" },
                  ]}
                >
                  <Input placeholder="URL Mappings" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item className="mt-4 text-right">
              <Button
                type="primary"
                htmlType="submit"
                icon={<PlusOutlined />}
                size="middle"
              >
                Create Access Resource
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  )
}
