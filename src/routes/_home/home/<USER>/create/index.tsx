import { CopyOutlined, HomeOutlined } from "@ant-design/icons";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import {
  Breadcrumb,
  Button,
  Card,
  Checkbox,
  Col,
  Form,
  Input,
  message,
  Row,
} from "antd";
import type { CreatePageRequest } from "~/gen/proto-models/PersonalizationAdmin";
import { createPage } from "~/lib/queries/personalization";
import { handleError } from "~/lib/utils/error";

export const Route = createFileRoute("/_home/home/<USER>/create/")({
  component: RouteComponent,
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();

  const { mutate: createPageItem } = useMutation({
    mutationFn: createPage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      form.resetFields();
      message.success("Page created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create page");
    },
  });

  const handleCreation = (values: CreatePageRequest) => {
    createPageItem(values);
  };
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <CopyOutlined />
                <span>Pages</span>
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form form={form} layout="vertical" onFinish={handleCreation}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label={<strong>Name</strong>}
                  rules={[
                    { required: true, message: "Please enter page name" },
                  ]}
                >
                  <Input placeholder="Enter page name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="path"
                  label={<strong>Path</strong>}
                  rules={[
                    { required: true, message: "Please enter page path" },
                  ]}
                >
                  <Input placeholder="Enter page path" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="featureFlag"
                  label={<strong>Feature Flag</strong>}
                >
                  <Input placeholder="Enter feature flag" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="variant" label={<strong>Variant</strong>}>
                  <Input placeholder="Enter variant" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="content"
              label={<strong>Content</strong>}
              rules={[{ required: true, message: "Please enter page content" }]}
            >
              <Input.TextArea placeholder="Enter page content" autoSize={{ maxRows: 20 }}/>
            </Form.Item>
            <Form.Item
              name="config"
              label={<strong>Config</strong>}
              rules={[{ required: true, message: "Please enter page config" }]}
            >
              <Input.TextArea placeholder="Enter page config" autoSize={{ maxRows: 20 }} />
            </Form.Item>
            <Form.Item name="isPublic" valuePropName="checked">
              <Checkbox>
                <strong>Is Public</strong>
              </Checkbox>
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
