import { HomeOutlined } from "@ant-design/icons";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import {
  Breadcrumb,
  Button,
  Card,
  Checkbox,
  Col,
  Form,
  Image,
  Input,
  message,
  Row,
} from "antd";
import { useState } from "react";
import type { CreateCollectionRequestProto } from "~/gen/proto-models/CollectionV2";
import { createFdCollection } from "~/lib/queries/fd-collections";
import { handleError } from "~/lib/utils/error";

export const Route = createFileRoute("/_home/home/<USER>/create/")({
  component: RouteComponent,
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();

  const [icon, setIcon] = useState<string | undefined>(undefined);

  const { mutate: createFdCollectionItem } = useMutation({
    mutationFn: createFdCollection,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["fd-collections"] });
      message.success("FD Collection created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create FD Collection");
    },
  });

  const handleCreation = (values: CreateCollectionRequestProto) => {
    createFdCollectionItem(values);
  };
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <span>FD Collections</span>
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form form={form} layout="vertical" onFinish={handleCreation}>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="name"
                  label={<strong>Name</strong>}
                  rules={[
                    { required: true, message: "Please enter collection name" },
                  ]}
                >
                  <Input placeholder="Enter collection name" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="title"
                  label={<strong>Title</strong>}
                  rules={[
                    { required: true, message: "Please enter collection title" },
                  ]}
                >
                  <Input placeholder="Enter collection title" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="description"
                  label={<strong>Description</strong>}
                >
                  <Input placeholder="Enter collection description" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="shortTitle"
                  label={<strong>Short Title</strong>}
                >
                  <Input placeholder="Enter collection short title" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="shortDescription"
                  label={<strong>Short Description</strong>}
                >
                  <Input placeholder="Enter collection short description" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="backgroundColor"
                  label={<strong>Background Color</strong>}
                >
                  <Input type="color" placeholder="Enter collection background color" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="excludeInactiveBanks" valuePropName="checked">
                  <Checkbox>
                    <strong>Exclude Inactive Banks</strong>
                  </Checkbox>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="isSortByInterestRate" valuePropName="checked">
                  <Checkbox>
                    <strong>Sort by Interest Rate</strong>
                  </Checkbox>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="isPickHighestInterestRate"
                  valuePropName="checked"
                >
                  <Checkbox>
                    <strong>Pick Highest Interest Rate</strong>
                  </Checkbox>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={18}>
                <Form.Item name="iconUrl" label={<strong>Icon URL</strong>}>
                  <Input
                    placeholder="Enter collection icon url"
                    onChange={(e) => setIcon(e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Image width={100} src={icon} />
              </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
