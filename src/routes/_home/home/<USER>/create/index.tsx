import { ContainerOutlined, HomeOutlined } from "@ant-design/icons";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  Button,
  Card,
  Col,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
} from "antd";
import { useMemo, useState } from "react";
import QueryRenderer from "~/components/functional/query-renderer";
import {
  BannerType,
  type CreateBannerRequest,
} from "~/gen/proto-models/PersonalizationAdmin";
import { createBanner } from "~/lib/queries/personalization";
import { bankNameAndIDQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

export const Route = createFileRoute("/_home/home/<USER>/create/")({
  component: RouteComponent,
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();

  const [bannerType, setBannerType] = useState<BannerType | null>(null);

  const [previewContent, setPreviewContent] = useState(false);

  const { clippedURL, stackedURL } = useMemo(() => {
    if (!previewContent) return { clippedURL: null, stackedURL: null };

    const value = form.getFieldsValue(["content"]);
    const content = JSON.parse(value.content || "{}");
    return {
      clippedURL: content.clippedUrl || null,
      stackedURL: content.stackedUrl || null,
    };
  }, [previewContent, form]);

  const query = useQuery(bankNameAndIDQueryOptions());

  const { mutate: createBannerItem } = useMutation({
    mutationFn: createBanner,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["banners"],
      });
      form.resetFields();
      message.success("Frame created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create frame");
    },
  });

  const handlePreview = () => {
    setPreviewContent(!previewContent);
  };

  const handleCreation = (values: CreateBannerRequest) => {
    createBannerItem(values);
  };
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <ContainerOutlined />
                <span>Banners</span>
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form form={form} layout="vertical" onFinish={handleCreation}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label={<strong>Name</strong>}
                  rules={[
                    { required: true, message: "Please enter banner name" },
                  ]}
                >
                  <Input placeholder="Enter banner name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="groupName"
                  label={<strong>Group Name</strong>}
                  rules={[
                    {
                      required: true,
                      message: "Please enter banner group name",
                    },
                  ]}
                >
                  <Input placeholder="Enter banner group name" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="bannerType"
              label={<strong>Banner Type</strong>}
              rules={[{ required: true, message: "Please select banner type" }]}
            >
              <Select
                placeholder="Select banner type"
                onChange={(value) => setBannerType(value)}
              >
                <Select.Option value={BannerType.BANK}>Bank</Select.Option>
                <Select.Option value={BannerType.GENERAL}>
                  General
                </Select.Option>
                <Select.Option value={BannerType.BOND}>Bond</Select.Option>
              </Select>
            </Form.Item>

            {bannerType === BannerType.BANK && (
              <QueryRenderer query={query}>
                {(data) => {
                  return (
                    <Form.Item
                      name="entityIdentifier"
                      label={<strong>Bank</strong>}
                      rules={[
                        { required: true, message: "Please select bank" },
                      ]}
                    >
                      <Select placeholder="Select bank">
                        {data?.bankDetail.map((item) => (
                          <Select.Option value={item.fsi} key={item.bankId}>
                            {item.bankName}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  );
                }}
              </QueryRenderer>
            )}

            <Row gutter={16}>
              <Col span={20}>
                <Form.Item
                  name="content"
                  label={<strong>Content</strong>}
                  rules={[
                    { required: true, message: "Please enter banner content" },
                  ]}
                >
                  <Input.TextArea
                    placeholder="Enter banner content"
                    autoSize={{ maxRows: 20 }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Button className="mt-7" type="primary" onClick={handlePreview}>
                  Preview
                </Button>
                <Modal
                  open={previewContent}
                  onCancel={() => handlePreview()}
                  title="Preview"
                  footer={null}
                  width="30%"
                >
                  <div className="flex flex-col items-center justify-center">
                    {clippedURL && (
                      <img src={clippedURL} width={300} height={300} />
                    )}
                    {stackedURL && (
                      <img src={stackedURL} width={300} height={300} />
                    )}
                  </div>
                </Modal>
              </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
