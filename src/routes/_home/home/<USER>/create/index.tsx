import { BlockOutlined, HomeOutlined } from "@ant-design/icons";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Breadcrumb, Button, Card, Form, Input, message } from "antd";
import type { CreateFrameRequest } from "~/gen/proto-models/PersonalizationAdmin";
import { createFrame } from "~/lib/queries/personalization";
import { getFramesQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

export const Route = createFileRoute("/_home/home/<USER>/create/")({
  component: RouteComponent,
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();

  const { mutate: createFrameItem } = useMutation({
    mutationFn: createFrame,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getFramesQueryOptions(1, "").queryKey,
      });
      form.resetFields();
      message.success("Frame created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create frame");
    },
  });

  const handleCreation = (values: CreateFrameRequest) => {
    createFrameItem(values);
  };
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <BlockOutlined />
                <span>Frames</span>
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form form={form} layout="vertical" onFinish={handleCreation}>
            <Form.Item
              name="name"
              label={<strong>Name</strong>}
              rules={[{ required: true, message: "Please enter frame name" }]}
            >
              <Input placeholder="Enter frame name" />
            </Form.Item>
            <Form.Item
              name="content"
              label={<strong>Content</strong>}
              rules={[
                { required: true, message: "Please enter frame content" },
              ]}
            >
              <Input.TextArea placeholder="Enter frame content" />
            </Form.Item>
            <Form.Item
              name="config"
              label={<strong>Config</strong>}
              rules={[{ required: true, message: "Please enter frame config" }]}
            >
              <Input.TextArea placeholder="Enter frame config" autoSize={{ maxRows: 20 }} />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
