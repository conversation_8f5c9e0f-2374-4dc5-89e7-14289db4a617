import { HomeOutlined, ReadOutlined } from "@ant-design/icons";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import {
  <PERSON><PERSON><PERSON>rumb,
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Row,
} from "antd";
import dayjs from "dayjs";
import { useMemo, useState } from "react";
import type { CreateStoryRequest } from "~/gen/proto-models/PersonalizationAdmin";
import { createStory } from "~/lib/queries/personalization";
import { getStoriesQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

export const Route = createFileRoute("/_home/home/<USER>/create/")({
  component: RouteComponent,
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();

  const [previewContent, setPreviewContent] = useState(false);

  const { url, cta } = useMemo(() => {
    if (!previewContent) return { url: null, cta: null };

    const value = form.getFieldsValue(["content"]);
    const content = JSON.parse(value.content || "{}");
    return { url: content.url || null, cta: content.cta?.url || null };
  }, [previewContent, form]);

  const { mutate: createStoryItem } = useMutation({
    mutationFn: createStory,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: getStoriesQueryOptions(1, "").queryKey,
      });
      form.resetFields();
      message.success("Story created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create story");
    },
  });

  const handleCreation = (values: CreateStoryRequest) => {
    const payload = {
      ...values,
      startTime: values.startTime
        ? dayjs(values.startTime).format("YYYY-MM-DDTHH:mm:ss")
        : "",
      endTime: values.endTime
        ? dayjs(values.endTime).format("YYYY-MM-DDTHH:mm:ss")
        : "",
      content: JSON.stringify(JSON.parse(values.content)),
    };

    createStoryItem(payload);
  };

  const handlePreview = () => {
    setPreviewContent(!previewContent);
  };

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <ReadOutlined />
                <span>Stories</span>
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form form={form} layout="vertical" onFinish={handleCreation}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="page"
                  label={<strong>Page</strong>}
                  rules={[{ required: true, message: "Please enter page" }]}
                >
                  <Input placeholder="Enter page" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label={<strong>Name</strong>}
                  rules={[{ required: true, message: "Please enter name" }]}
                >
                  <Input placeholder="Enter name" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="featureFlag"
                  label={<strong>Feature Flag</strong>}
                >
                  <Input placeholder="Enter feature flag" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="variant" label={<strong>Variant</strong>}>
                  <Input placeholder="Enter variant" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="startTime" label={<strong>Start Time</strong>}>
                  <DatePicker
                    className="w-full"
                    showTime
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="endTime" label={<strong>End Time</strong>}>
                  <DatePicker
                    className="w-full"
                    showTime
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="priority"
                  label={<strong>Priority</strong>}
                  rules={[{ required: true, message: "Please enter priority" }]}
                >
                  <Input placeholder="Enter priority" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="isLikeable"
                  label={<strong>Is Likeable</strong>}
                  valuePropName="checked"
                  initialValue={false}
                >
                  <Checkbox />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="isShareable"
                  label={<strong>Is Shareable</strong>}
                  valuePropName="checked"
                  initialValue={false}
                >
                  <Checkbox />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={20}>
                <Form.Item
                  name="content"
                  label={<strong>Content</strong>}
                  rules={[{ required: true, message: "Please enter content" }]}
                >
                  <Input.TextArea
                    placeholder="Enter content"
                    autoSize={{ maxRows: 20 }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Button className="mt-7" type="primary" onClick={handlePreview}>
                  Preview
                </Button>
                <Modal
                  open={previewContent}
                  onCancel={() => handlePreview()}
                  title="Preview"
                  footer={null}
                  width="30%"
                >
                  <div className="flex flex-col items-center justify-center">
                    {url && <img src={url!} width={300} height={300} />}
                    {cta && <img src={cta!} width={300} height={300} />}
                  </div>
                </Modal>
              </Col>
            </Row>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
