import { createFile<PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { Form, Input, But<PERSON>, Card, Breadcrumb, Row, Col, message } from "antd";
import { HomeOutlined, SaveOutlined } from "@ant-design/icons";
import { useForm } from "antd/es/form/Form";
import { handleError } from "~/lib/utils/error";
import type { AccessResource } from "~/lib/types/access-control";
import { getAccessResourceQueryOptions } from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { updateAccessResource } from "~/lib/queries/access-control";

export const Route = createFileRoute(
  "/_home/home/<USER>/$resource-id/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await queryClient.ensureQueryData(
      getAccessResourceQueryOptions(params["resource-id"])
    );
  },
});

function RouteComponent() {
  const { "resource-id": resourceId } = Route.useParams();
  const [form] = useForm();
  const queryClient = useQueryClient();

  const { data: accessResource } = useSuspenseQuery(
    getAccessResourceQueryOptions(resourceId)
  );

  const { mutate: updateAccessResourceItem } = useMutation({
    mutationFn: updateAccessResource,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["access-resources"] });
      message.success("Access resource updated successfully!");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update access resource");
    },
  });

  const onFinish = (values: AccessResource) => {
    const payload = {
      ...values,
      id: resourceId,
    };
    updateAccessResourceItem(payload);
  };

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: <Link to="/home/<USER>">Access Resources</Link>,
          },
          {
            title: accessResource.name,
          },
        ]}
      />
      <div className="mt-4">
        <Card>
          <Form
            form={form}
            layout="vertical"
            initialValues={accessResource}
            onFinish={onFinish}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={<strong>Name</strong>}
                  name="name"
                  rules={[
                    {
                      required: true,
                      message: "Please enter a resource name!",
                    },
                  ]}
                >
                  <Input placeholder="Resource Name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={<strong>URL Mappings</strong>}
                  name="urlMappings"
                  rules={[
                    { required: true, message: "Please enter URL mappings!" },
                  ]}
                >
                  <Input.TextArea
                    placeholder="URL Mappings"
                    autoSize={{ minRows: 3, maxRows: 6 }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item className="mt-4 text-right">
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                size="middle"
              >
                Update Access Resource
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
