import { useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>ton, Card, Checkbox, Col, Form, Input, message, Row } from "antd";
import type { PageResponse } from "~/gen/proto-models/PersonalizationAdmin";
import { updatePage } from "~/lib/queries/personalization";
import { getPageQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

export const FormElement = ({ page }: { page: PageResponse }) => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const { mutate: updatePageItem } = useMutation({
    mutationFn: updatePage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getPageQueryOptions(page.id).queryKey });
      message.success("Page updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update page");
    },
  });

  const handleUpdate = (values: PageResponse) => {
    const payload = {
      ...values,
      id: page.id,
      content: JSON.stringify(JSON.parse(values.content)),
      config: JSON.stringify(JSON.parse(values.config)),
    };

    updatePageItem(payload);
  };
  
  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleUpdate}
        initialValues={{
            ...page,
            content : JSON.stringify(JSON.parse(page.content), null, 4),
            config : JSON.stringify(JSON.parse(page.config), null, 4),
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label={<strong>Name</strong>}
              rules={[{ required: true, message: "Please enter page name" }]}
            >
              <Input placeholder="Enter page name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="path"
              label={<strong>Path</strong>}
              rules={[{ required: true, message: "Please enter page path" }]}
            >
              <Input placeholder="Enter page path" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="featureFlag" label={<strong>Feature Flag</strong>}>
              <Input placeholder="Enter feature flag" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="variant" label={<strong>Variant</strong>}>
              <Input placeholder="Enter variant" />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name="content"
          label={<strong>Content</strong>}
          rules={[{ required: true, message: "Please enter page content" }]}
        >
          <Input.TextArea placeholder="Enter page content" autoSize={{ maxRows: 20 }} />
        </Form.Item>
        <Form.Item
          name="config"
          label={<strong>Config</strong>}
          rules={[{ required: true, message: "Please enter page config" }]}
        >
          <Input.TextArea placeholder="Enter page config" autoSize={{ maxRows: 10 }}/>
        </Form.Item>
        <Form.Item name="isPublic" valuePropName="checked">
          <Checkbox>
            <strong>Is Public</strong>
          </Checkbox>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            Update
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};
