import { CopyOutlined, HomeOutlined } from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { Breadcrumb, Button } from "antd";
import { getPageQueryOptions } from "~/lib/queries/queryKeys";
import { FormElement } from "./-components/form-element";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute("/_home/home/<USER>/$page-id/")({
  component: RouteComponent,
  loader: async ({ params }) => {
    await queryClient.ensureQueryData(getPageQueryOptions(params["page-id"]))
  },
});

function RouteComponent() {
  const { "page-id": pageID } = Route.useParams();

  const navigate = Route.useNavigate();

  const [getPageQuery] = useSuspenseQueries({
    queries: [getPageQueryOptions(pageID)],
  });

  const page = getPageQuery.data;
  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4 mt-4">
        <Breadcrumb
          items={[
            {
              title: (
                <Link to="/home">
                  <HomeOutlined />
                </Link>
              ),
            },
            {
              title: (
                <Link to="/home/<USER>">
                  <CopyOutlined />
                  <span>Pages</span>
                </Link>
              ),
            },
            {
              title: page.name,
            },
          ]}
        />
        <Button
          type="primary"
          onClick={() =>
            navigate({ to: "/home/<USER>/$page-id/feature-flag-mappings" })
          }
        >
          Feature Flag Mappings
        </Button>
      </div>
      <FormElement page={page} />
    </div>
  );
}
