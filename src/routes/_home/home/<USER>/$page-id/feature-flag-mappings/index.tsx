import {
  CopyOutlined,
  FileOutlined,
  HomeOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { <PERSON><PERSON><PERSON><PERSON>b, Button, Input } from "antd";
import {
  getFeatureFlagMappingsQueryOptions,
  getPageQueryOptions,
} from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { FeatureFlagMappingTableComponent } from "./-components/feature-flag-mapping-table-component";
import { ReferenceType } from "~/gen/proto-models/PersonalizationAdmin";
import { useState } from "react";

export const Route = createFileRoute(
  "/_home/home/<USER>/$page-id/feature-flag-mappings/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await Promise.all([
      queryClient.ensureQueryData(getPageQueryOptions(params["page-id"])),
      queryClient.ensureQueryData(
        getFeatureFlagMappingsQueryOptions(
          params["page-id"],
          ReferenceType.PAGE
        )
      ),
    ]);
  },
});

function RouteComponent() {
  const { "page-id": pageID } = Route.useParams();

  const navigate = Route.useNavigate();

  const [searchText, setSearchText] = useState("");

  const [getPageQuery, getFeatureFlagMappingsQuery] = useSuspenseQueries({
    queries: [
      getPageQueryOptions(pageID),
      getFeatureFlagMappingsQueryOptions(pageID, ReferenceType.PAGE),
    ],
  });

  const page = getPageQuery.data;
  const featureFlagMappings = getFeatureFlagMappingsQuery.data;

  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <CopyOutlined />
                <span>Pages</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to={`/home/<USER>/$page-id`}
                params={{ "page-id": pageID }}
              >
                <FileOutlined />
                <span>{page.name}</span>
              </Link>
            ),
          },
          {
            title: "Feature Flag Mappings",
          },
        ]}
      />
      <div className="flex items-center justify-between mb-4 mt-4">
        <div className="w-80">
          <Input.Search
            placeholder="Search by name"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() =>
            navigate({
              to: "/home/<USER>/$page-id/feature-flag-mappings/create",
            })
          }
        >
          Create
        </Button>
      </div>
      <FeatureFlagMappingTableComponent
        page={page}
        featureFlagMappings={featureFlagMappings}
        searchText={searchText}
      />
    </div>
  );
}
