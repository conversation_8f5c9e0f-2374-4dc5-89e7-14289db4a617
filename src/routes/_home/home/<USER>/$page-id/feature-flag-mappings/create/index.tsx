import {
  CopyOutlined,
  FileOutlined,
  FlagOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import {
  useMutation,
  useQueryClient,
  useSuspenseQueries,
} from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Breadcrumb, Button, Card, Form, Input, message } from "antd";
import {
  ReferenceType,
  type CreateFeatureFlagMappingRequest,
} from "~/gen/proto-models/PersonalizationAdmin";
import { createFeatureFlagMapping } from "~/lib/queries/personalization";
import { getPageQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute(
  "/_home/home/<USER>/$page-id/feature-flag-mappings/create/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await queryClient.ensureQueryData(getPageQueryOptions(params["page-id"]))
  },
});

function RouteComponent() {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { "page-id": pageID } = Route.useParams();

  const [getPageQuery] = useSuspenseQueries({
    queries: [getPageQueryOptions(pageID)],
  });

  const { mutate: createFeatureFlagMappingItem } = useMutation({
    mutationFn: createFeatureFlagMapping,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["feature-flag-mappings"] });
      form.resetFields();
      message.success("Feature Flag Mapping created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create feature flag mapping");
    },
  });

  const handleCreate = (values: CreateFeatureFlagMappingRequest) => {
    const payload = {
      featureFlag: values.featureFlag,
      widgetId: values.widgetId,
      typeId: pageID,
      variant: values.variant,
      referenceType: ReferenceType.PAGE,
    };

    createFeatureFlagMappingItem(payload);
  };

  const page = getPageQuery.data;
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <CopyOutlined />
                <span>Pages</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to={`/home/<USER>/$page-id`}
                params={{ "page-id": pageID }}
              >
                <FileOutlined />
                <span>{page.name}</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to={`/home/<USER>/$page-id/feature-flag-mappings`}
                params={{ "page-id": pageID }}
              >
                <FlagOutlined />
                <span>Feature Flag Mappings</span>
              </Link>
            ),
          },
          {
            title: "Create",
          },
        ]}
      />
      <div className="mt-6">
        <Card>
          <Form form={form} layout="vertical" onFinish={handleCreate}>
            <Form.Item name="featureFlag" label={<strong>Feature Flag</strong>}>
              <Input placeholder="Enter feature flag" />
            </Form.Item>
            <Form.Item name="widgetId" label={<strong>Widget ID</strong>}>
              <Input placeholder="Enter widget id" />
            </Form.Item>
            <Form.Item name="variant" label={<strong>Variant</strong>}>
              <Input placeholder="Enter variant" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </div>
  );
}
