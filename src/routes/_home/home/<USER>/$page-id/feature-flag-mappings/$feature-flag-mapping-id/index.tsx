import {
  CopyOutlined,
  FileOutlined,
  FlagOutlined,
  HomeOutlined,
} from "@ant-design/icons";
import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";
import { Breadcrumb } from "antd";
import {
  getFeatureFlagMappingQueryOptions,
  getPageQueryOptions,
} from "~/lib/queries/queryKeys";
import { queryClient } from "~/lib/utils/queryClient";
import { FormElement } from "./-components/form-element";

export const Route = createFileRoute(
  "/_home/home/<USER>/$page-id/feature-flag-mappings/$feature-flag-mapping-id/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await Promise.all([
      queryClient.ensureQueryData(getPageQueryOptions(params["page-id"])),
      queryClient.ensureQueryData(
        getFeatureFlagMappingQueryOptions(params["feature-flag-mapping-id"])
      ),
    ]);
  },
});

function RouteComponent() {
  const { "feature-flag-mapping-id": featureFlagMappingID, "page-id": pageID } =
    Route.useParams();

  const [getFeatureFlagMappingQuery, getPageQuery] = useSuspenseQueries({
    queries: [
      getFeatureFlagMappingQueryOptions(featureFlagMappingID),
      getPageQueryOptions(pageID),
    ],
  });

  const featureFlagMapping = getFeatureFlagMappingQuery.data;
  const page = getPageQuery.data;
  return (
    <div className="p-6">
      <Breadcrumb
        items={[
          {
            title: (
              <Link to="/home">
                <HomeOutlined />
              </Link>
            ),
          },
          {
            title: (
              <Link to="/home/<USER>">
                <CopyOutlined />
                <span>Pages</span>
              </Link>
            ),
          },
          {
            title: (
              <Link to={`/home/<USER>/$page-id`} params={{ "page-id": pageID }}>
                <FileOutlined />
                <span>{page.name}</span>
              </Link>
            ),
          },
          {
            title: (
              <Link
                to={`/home/<USER>/$page-id/feature-flag-mappings`}
                params={{ "page-id": pageID }}
              >
                <FlagOutlined />
                <span>Feature Flag Mappings</span>
              </Link>
            ),
          },
          {
            title: featureFlagMapping.featureFlag,
          },
        ]}
      />
      <div className="mt-6">
        <FormElement
          featureFlagMapping={featureFlagMapping}
          featureFlagMappingID={featureFlagMappingID}
          pageID={pageID}
        />
      </div>
    </div>
  );
}
