import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button, Card, Form, Input, message } from "antd";
import type { FeatureFlagMappingResponse } from "~/gen/proto-models/PersonalizationAdmin";
import { updateFeatureFlagMapping } from "~/lib/queries/personalization";
import { getFeatureFlagMappingQueryOptions } from "~/lib/queries/queryKeys";
import { handleError } from "~/lib/utils/error";

interface FormElementProps {
  featureFlagMapping: FeatureFlagMappingResponse;
  featureFlagMappingID: string;
  pageID: string;
}

export const FormElement = ({ featureFlagMapping, featureFlagMappingID, pageID }: FormElementProps) => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const { mutate: updateFeatureFlagMappingItem } = useMutation({
    mutationFn: updateFeatureFlagMapping,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getFeatureFlagMappingQueryOptions(featureFlagMappingID).queryKey });
      message.success("Feature Flag Mapping updated successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update feature flag mapping");
    },
  });

  const handleUpdate = (values: FeatureFlagMappingResponse) => {
    const payload = {
      id: featureFlagMappingID,
      featureFlag: values.featureFlag,
      widgetId: values.widgetId,
      variant: values.variant,
      pageId: pageID,
    };

    updateFeatureFlagMappingItem(payload);
  };
  return (
    <Card>
        <Form
          form={form}
          layout="vertical"
          initialValues={featureFlagMapping}
          onFinish={handleUpdate}
        >
            <Form.Item name="featureFlag" label={<strong>Feature Flag</strong>}>
              <Input placeholder="Enter feature flag"/>
            </Form.Item>
            <Form.Item name="widgetId" label={<strong>Widget ID</strong>}>
              <Input placeholder="Enter widget id"/>
            </Form.Item>
            <Form.Item name="variant" label={<strong>Variant</strong>}>
              <Input placeholder="Enter variant"/>
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                Update
              </Button>
            </Form.Item>
        </Form>
    </Card>
  );
};
