import { useSuspenseQueries } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import z from "zod";
import { bondIssuingInstitutionQueryOptions, mediaItemQueryOptions } from "~/lib/queries/queryKeys";
import { ViewComponent } from "./-components/view-component";
import { MediaEditComponent } from "./-components/media-edit-component";
import { queryClient } from "~/lib/utils/queryClient";

export const Route = createFileRoute("/_home/home/<USER>/$isin-id/")({
  validateSearch: z.object({
    isinID: z.string(),
  }),
  loaderDeps: ({ search }) => ({ isinID: search.isinID }),
  loader: async ({ deps: { isinID } }) => {
    await Promise.all([
      queryClient.ensureQueryData(
        bondIssuingInstitutionQueryOptions(isinID)
      ),
      queryClient.ensureQueryData(
        mediaItemQueryOptions({ id: isinID, parentType: "BOND_ISSUING_INSTITUTION"})
      )
    ]);
  },
  component: RouteComponent,
});

function RouteComponent() {
    const { isinID } = Route.useSearch();
    const [bondIssuingInstitutionQuery, mediaItemQuery] = useSuspenseQueries({
    queries: [bondIssuingInstitutionQueryOptions(isinID), mediaItemQueryOptions({ id: isinID, parentType: "BOND_ISSUING_INSTITUTION"})],
  });

  const bondIssuingInstitution = bondIssuingInstitutionQuery.data;
  const mediaItems = mediaItemQuery.data;

  return (
    <div className="p-6 space-y-4">
      <ViewComponent bondIssuingInstitution={bondIssuingInstitution} />
      <MediaEditComponent mediaItems={mediaItems} />
    </div>
  );
}
