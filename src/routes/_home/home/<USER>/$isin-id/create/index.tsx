import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { Button, Card, Col, Form, Input, message, Row, Select } from "antd";
import { BondMediaItemParentType, CreateMediaItemRequest, mediaItem_MediaTypeFromJSON, mediaItem_ScreenTypeFromJSON } from "~/gen/proto-models/Catalog";
import { createMediaItem } from "~/lib/queries/bond-catalog";
import { handleError } from "~/lib/utils/error";

export const Route = createFileRoute(
  "/_home/home/<USER>/$isin-id/create/"
)({
  component: RouteComponent,
});

function RouteComponent() {
  const [form] = Form.useForm();
  const { "isin-id": isinId } = Route.useParams();
  const queryClient = useQueryClient();

  const { mutate: createMedia } = useMutation({
    mutationFn: createMediaItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["media-items"] });
      form.resetFields();
      message.success("Media Item created successfully");
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create media item");
    },
  });

  const handleCreation = (values: CreateMediaItemRequest) => {
    const payload: CreateMediaItemRequest = {
      parentId: isinId,
      parentType: BondMediaItemParentType.BOND_ISSUING_INSTITUTION,
      section: values.section,
      mediaType: mediaItem_MediaTypeFromJSON(values.mediaType),
      mediaUrl: values.mediaUrl,
      screenType: mediaItem_ScreenTypeFromJSON(values.screenType),
      redirectDeeplink: values.redirectDeeplink,
    };
    createMedia(payload);
  };

  return (
    <div className="p-6">
      <Card>
        <Form form={form} layout="vertical" onFinish={handleCreation}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="section" label={<strong>Section</strong>}>
                <Input placeholder="Enter section name" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="mediaType" label={<strong>Media Type</strong>}>
                <Select placeholder="Select media type">
                  <Select.Option value="IMAGE">Image</Select.Option>
                  <Select.Option value="VIDEO">Video</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="mediaUrl" label={<strong>URL</strong>}>
                <Input placeholder="Enter URL" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="screenType" label={<strong>Screen Type</strong>}>
                <Select placeholder="Select screen type">
                  <Select.Option value="MOBILE">Mobile</Select.Option>
                  <Select.Option value="DESKTOP">Desktop</Select.Option>
                  <Select.Option value="TABLET">Tablet</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="redirectDeeplink"
                label={<strong>Redirect Deeplink</strong>}
              >
                <Input placeholder="Enter redirect deeplink" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              Create
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}
