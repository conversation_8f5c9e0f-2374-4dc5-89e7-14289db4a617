import {
  Descriptions,
  Card,
  Image,
  Typography,
  Space,
  Tag,
  Button,
  Form,
  Input,
  message,
} from "antd";
import { BondIssuingInstitutionResponse, BondIssuingInstitutionUpdateRequest } from "~/gen/proto-models/Catalog";
import dayjs from "dayjs";
import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateIssuingInstitution } from "~/lib/queries/bond-catalog";
import { handleError } from "~/lib/utils/error";

const { Text, Link } = Typography;
const { TextArea } = Input;

interface ViewComponentProps {
  bondIssuingInstitution: BondIssuingInstitutionResponse;
}

interface FormValues {
  name: string;
  description: string;
  sector: string;
  websiteUrl: string;
  logoUrl: string;
  color: string;
  financialSnapshotUrl: string;
  coverImageUrl: string;
  gridCoverImageUrl: string;
}

export function ViewComponent({ bondIssuingInstitution }: ViewComponentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [form] = Form.useForm();
  const [currentData, setCurrentData] = useState(bondIssuingInstitution);
  const queryClient = useQueryClient();

  const { mutate, isPending } = useMutation({
    mutationFn: updateIssuingInstitution,
    onSuccess: async (variables) => {
      message.success("Institution updated successfully");
      setCurrentData(prev => ({ ...prev, ...variables }));
      queryClient.invalidateQueries({
        queryKey: ['bond-issuing-institution', bondIssuingInstitution.id],
      });

      setIsEditing(false);
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update media item");
    },
  });

  const onFinish = (values: FormValues) => {
    const payload: BondIssuingInstitutionUpdateRequest = {
      id: currentData.id,
      ...values,
    };
    mutate(payload);
  };

  const editForm = (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        name: currentData.name,
        description: currentData.description,
        sector: currentData.sector,
        websiteUrl: currentData.websiteUrl,
        logoUrl: currentData.logoUrl,
        color: currentData.color,
        financialSnapshotUrl: currentData.financialSnapshotUrl,
        coverImageUrl: currentData.coverImageUrl,
        gridCoverImageUrl: currentData.gridCoverImageUrl,
      }}
      onFinish={onFinish}
    >
      <Form.Item label="Name" name="name" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      <Form.Item label="Description" name="description">
        <TextArea rows={3} />
      </Form.Item>
      <Form.Item label="Sector" name="sector" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      <Form.Item label="Website URL" name="websiteUrl">
        <Input />
      </Form.Item>
      <Form.Item label="Logo URL" name="logoUrl">
        <Input />
      </Form.Item>
      <Form.Item label="Color" name="color">
        <Input />
      </Form.Item>
      <Form.Item label="Financial Snapshot URL" name="financialSnapshotUrl">
        <Input />
      </Form.Item>
      <Form.Item label="Cover Image URL" name="coverImageUrl">
        <Input />
      </Form.Item>
      <Form.Item label="Grid Cover Image URL" name="gridCoverImageUrl">
        <Input />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" loading={isPending}>
            Save
          </Button>
          <Button onClick={() => setIsEditing(false)}>Cancel</Button>
        </Space>
      </Form.Item>
    </Form>
  );

  const viewItems = [
    {
      key: "1",
      label: "Institution Name",
      children: <Text strong>{currentData.name}</Text>,
      span: 2,
    },
    {
      key: "2",
      label: "Slug",
      children: currentData.slug,
    },
    {
      key: "3",
      label: "Sector",
      children: <Tag color="blue">{currentData.sector}</Tag>,
    },
    {
      key: "4",
      label: "CIN",
      children: currentData.cin,
      span: 2,
    },
    {
      key: "5",
      label: "Description",
      children: currentData.description,
      span: 3,
    },
    {
      key: "6",
      label: "Website",
      children: currentData.websiteUrl ? (
        <Link href={currentData.websiteUrl} target="_blank">
          {currentData.websiteUrl}
        </Link>
      ) : (
        "N/A"
      ),
      span: 2,
    },
    {
      key: "7",
      label: "Created At",
      children: dayjs(currentData.createdAt).format("DD/MM/YYYY HH:mm"),
    },
    {
      key: "8",
      label: "Updated At",
      children: dayjs(currentData.updatedAt).format("DD/MM/YYYY HH:mm"),
    },
    {
      key: "9",
      label: "Color",
      children: currentData.color ? (
        <div className="flex items-center gap-2">
          {currentData.color}
        </div>
      ) : "N/A",
    },
    {
      key: "10",
      label: "Financial Snapshot",
      children: currentData.financialSnapshotUrl ? (
        <Link href={currentData.financialSnapshotUrl} target="_blank">
          View Document
        </Link>
      ) : "N/A",
    },
    {
      key: "11",
      label: "Cover Image",
      children: currentData.coverImageUrl ? (
        <Link href={currentData.coverImageUrl} target="_blank">
          View Image
        </Link>
      ) : "N/A",
    },
    {
      key: "12",
      label: "Grid Cover Image",
      children: currentData.gridCoverImageUrl ? (
        <Link href={currentData.gridCoverImageUrl} target="_blank">
          View Image
        </Link>
      ) : "N/A",
    },
  ];

  return (
    <Card
      title="Institution Details"
      className="mb-6"
      extra={
        !isEditing && (
          <Button type="primary" onClick={() => setIsEditing(true)}>
            Edit
          </Button>
        )
      }
    >
      <Space direction="vertical" size="large" className="w-full">
        {currentData.logoUrl && (
          <div className="text-center">
            <Image
              src={currentData.logoUrl}
              alt="Institution Logo"
              className="max-h-25"
            />
          </div>
        )}
        {isEditing ? editForm : <Descriptions bordered column={3} size="middle" items={viewItems} />}
      </Space>
    </Card>
  );
}
