import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Space,
  Typography,
  Divider,
  message,
  Row,
  Col,
  Switch,
} from "antd";
import { EditOutlined, SaveOutlined } from "@ant-design/icons";
import { useState } from "react";
import {
  MediaItemResponse,
  MediaItemDashboard,
  MediaType,
  ScreenType,
  BondMediaItemParentType,
} from "~/gen/proto-models/Catalog";
import { updateMediaItem } from "~/lib/queries/bond-catalog";
import { handleError } from "~/lib/utils/error";
import { Route } from "..";
import { Link } from "@tanstack/react-router";

const { Text } = Typography;
const { Option } = Select;

interface MediaEditComponentProps {
  mediaItems: MediaItemResponse;
}

interface MediaFormData {
  section: string;
  mediaType: MediaType;
  url: string;
  screenType: ScreenType;
  redirectDeeplink?: string;
  isActive: boolean;
}

export function MediaEditComponent({ mediaItems }: MediaEditComponentProps) {
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const navigate = Route.useNavigate();

  const updateMediaMutation = useMutation({
    mutationFn: async (data: {
      id: string;
      formData: MediaFormData;
      parentType: BondMediaItemParentType;
    }) => {
      const mediaItemUpdateRequest = {
        id: data.id,
        parentType: data.parentType,
        section: data.formData.section,
        mediaType: data.formData.mediaType,
        mediaUrl: data.formData.url,
        screenType: data.formData.screenType,
        redirectDeeplink: data.formData.redirectDeeplink || "",
        isActive: data.formData.isActive,
      };
      return updateMediaItem(mediaItemUpdateRequest);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["media-items"],
      });
      message.success("Media item updated successfully");
      setEditingItemId(null);
      form.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update media item");
    },
  });

  const handleEdit = (item: MediaItemDashboard) => {
    setEditingItemId(item.id);
    form.setFieldsValue({
      section: item.section,
      mediaType: item.mediaType,
      url: item.url,
      screenType: item.screenType,
      redirectDeeplink: item.redirectDeeplink,
      isActive: item.isActive,
    });
  };

  const handleSave = (item: MediaItemDashboard) => {
    form.validateFields().then((values) => {
      updateMediaMutation.mutate({
        id: item.id,
        formData: values,
        parentType: item.parentType,
      });
    });
  };

  const handleCancel = () => {
    setEditingItemId(null);
    form.resetFields();
  };

  const getMediaTypeLabel = (type: MediaType) => {
    switch (type) {
      case MediaType.IMAGE:
        return "Image";
      case MediaType.VIDEO:
        return "Video";
      default:
        return "Unknown";
    }
  };

  const getScreenTypeLabel = (type: ScreenType) => {
    switch (type) {
      case ScreenType.MOBILE:
        return "Mobile";
      case ScreenType.DESKTOP:
        return "Desktop";
      case ScreenType.TABLET:
        return "Tablet";
      case ScreenType.SCREEN_TYPE_UNKNOWN:
        return "Unknown";
      default:
        return "Unknown";
    }
  };

  return (
    <Card className="mt-6">
      <div className="mb-4 flex justify-between items-center">
        <Typography.Title level={4} className="mb-0">
          Media Items Management
        </Typography.Title>
        <Button type="primary" onClick={() => navigate({ to: "create" })}>
          Add Media Item
        </Button>
      </div>
      <Divider className="mb-4" />
      {mediaItems.mediaItems.length === 0 ? (
        <Text type="secondary">No media items found</Text>
      ) : (
        <Space direction="vertical" size="large" className="w-full">
          {mediaItems.mediaItems.map((item, index) => (
            <Card
              key={item.id}
              size="small"
              title={`Media Item ${index + 1}`}
              extra={
                editingItemId === item.id ? (
                  <Space>
                    <Button size="small" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <Button
                      type="primary"
                      size="small"
                      icon={<SaveOutlined />}
                      loading={updateMediaMutation.isPending}
                      onClick={() => handleSave(item)}
                    >
                      Save
                    </Button>
                  </Space>
                ) : (
                  <Button
                    type="primary"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEdit(item)}
                  >
                    Edit
                  </Button>
                )
              }
            >
              {editingItemId === item.id ? (
                <Form form={form} layout="vertical">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="Section"
                        name="section"
                        rules={[
                          { required: true, message: "Section is required" },
                        ]}
                      >
                        <Input placeholder="Enter section name" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Media Type"
                        name="mediaType"
                        rules={[
                          { required: true, message: "Media type is required" },
                        ]}
                      >
                        <Select placeholder="Select media type">
                          <Option value={MediaType.IMAGE}>Image</Option>
                          <Option value={MediaType.VIDEO}>Video</Option>
                        </Select>
                      </Form.Item>
                    </Col>

                    <Col span={20}>
                      <Form.Item
                        label="URL"
                        name="url"
                        rules={[
                          { required: true, message: "URL is required" },
                          { type: "url", message: "Please enter a valid URL" },
                        ]}
                      >
                        <Input placeholder="https://example.com/media.jpg" />
                      </Form.Item>
                    </Col>

                    <Col span={4}>
                      <Form.Item
                        label="Active"
                        name="isActive"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Screen Type"
                        name="screenType"
                        rules={[
                          {
                            required: true,
                            message: "Screen type is required",
                          },
                        ]}
                      >
                        <Select placeholder="Select screen type">
                          <Option value={ScreenType.SCREEN_TYPE_UNKNOWN}>
                            Unknown
                          </Option>
                          <Option value={ScreenType.MOBILE}>Mobile</Option>
                          <Option value={ScreenType.DESKTOP}>Desktop</Option>
                          <Option value={ScreenType.TABLET}>Tablet</Option>
                        </Select>
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Redirect Deeplink"
                        name="redirectDeeplink"
                      >
                        <Input placeholder="Enter redirect deeplink" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              ) : (
                <Space direction="vertical" size="small" className="w-full">
                  <Row gutter={16}>
                    <Col span={6}>
                      <Text strong>Section:</Text>
                      <br />
                      <Text>{item.section}</Text>
                    </Col>
                    <Col span={6}>
                      <Text strong>Media Type:</Text>
                      <br />
                      <Text>{getMediaTypeLabel(item.mediaType)}</Text>
                    </Col>
                    <Col span={6}>
                      <Text strong>Screen Type:</Text>
                      <br />
                      <Text>{getScreenTypeLabel(item.screenType)}</Text>
                    </Col>
                    <Col span={6}>
                      <Text strong>Active:</Text>
                      <br />
                      <Text>{item.isActive ? <div className="text-green-500">Yes</div> : <div className="text-red-500">No</div>}</Text>
                    </Col>
                  </Row>

                  <Divider className="my-2" />

                  <Row gutter={16}>
                    <Col span={12}>
                      <Text strong>URL:</Text>
                      <br />
                      <Text ellipsis className="max-w-full">
                        <Link to={item.url}>{item.url}</Link>
                      </Text>
                    </Col>
                    <Col span={12}>
                      <Text strong>Redirect Deeplink:</Text>
                      <br />
                      <Text ellipsis className="max-w-full">
                        {item.redirectDeeplink || "N/A"}
                      </Text>
                    </Col>
                  </Row>
                </Space>
              )}
            </Card>
          ))}
        </Space>
      )}
    </Card>
  );
}
