import { createFileRoute } from "@tanstack/react-router";
import {
  useMutation,
  useQueryClient,
  useSuspenseQueries,
} from "@tanstack/react-query";
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  message,
  Switch,
  Select,
  Card,
  Space,
  Modal,
} from "antd";
import { useState } from "react";
import { CreateBondModal, SortableBondTable } from "./-components/index";
import { updateBondCollection } from "~/lib/queries/bonds-collection";
import {
  CollectionType,
  UpdateBondCollectionRequest,
  DisplayType,
} from "~/gen/proto-models/BrokingCollection";
import {
  getAllCollectionExpressionsQueryOptions,
  getAllISINsQueryOptions,
  getBondCollectionByIdQueryOptions,
} from "~/lib/queries/queryKeys";
import { CollectionItemTagging } from "./-components/collection-item-tagging";
import { handleError } from "~/lib/utils/error";
import { queryClient } from "~/lib/utils/queryClient";

const { TextArea } = Input;

export const Route = createFileRoute(
  "/_home/home/<USER>/edit/$bond-collection-id/"
)({
  component: RouteComponent,
  loader: async ({ params }) => {
    await Promise.all([
      queryClient.ensureQueryData(
        getBondCollectionByIdQueryOptions(params["bond-collection-id"])
      ),
      queryClient.ensureQueryData(getAllISINsQueryOptions()),
      queryClient.ensureQueryData(getAllCollectionExpressionsQueryOptions()),
    ]).catch((error) => {
      throw new Error(error.message);
    });
  },
});

function RouteComponent() {
  const { "bond-collection-id": bondCollectionID } = Route.useParams();
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const [isModalOpen, setModalOpen] = useState(false);
  const [expressionName, setExpressionName] = useState<string | null>(null);

  const [
    getBondCollectionByIdQuery,
    getAllISINsQuery,
    getAllCollectionExpressionsQuery,
  ] = useSuspenseQueries({
    queries: [
      getBondCollectionByIdQueryOptions(bondCollectionID),
      getAllISINsQueryOptions(),
      getAllCollectionExpressionsQueryOptions(),
    ],
  });

  const expressions =
    getAllCollectionExpressionsQuery.data?.collectionExpressions.map(
      (expression) => ({
        label: expression.name,
        value: expression.name,
      })
    );

  const handleExpressionChange = (expressionName: string) => {
    setExpressionName(expressionName);
    const selectedExpression =
      getAllCollectionExpressionsQuery.data?.collectionExpressions.find(
        (expr) => expr.name === expressionName
      );

    if (selectedExpression) {
      form.setFieldsValue({
        preFilterCriteria: selectedExpression.preFilterCriteria,
        postFilterCriteria: selectedExpression.postFilterCriteria,
        sortCriteria: selectedExpression.sortCriteria,
      });
    }
  };

  const collectionData = getBondCollectionByIdQuery.data;
  const allISINsData = getAllISINsQuery.data;

  const [isManual, setIsManual] = useState(
    collectionData.collectionType === CollectionType.MANUAL
  );

  const { mutate, isPending: isUpdating } = useMutation({
    mutationFn: (payload: UpdateBondCollectionRequest) => {
      return updateBondCollection(payload);
    },
    onSuccess: () => {
      message.success("Updated successfully");
      queryClient.invalidateQueries({
        queryKey: ["collections", bondCollectionID],
      });
    },
    onError: (error: Error) => {
      handleError(error, "Failed to update collection");
    },
  });

  const handleToggle = (checked: boolean) => {
    setIsManual(checked);
  };

  return (
    <div className="p-6">
      <Row gutter={32}>
        <Col span={14}>
          <Card>
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                ...collectionData,
                collectionItemLimit:
                  collectionData.collectionItemLimit === 0
                    ? undefined
                    : collectionData.collectionItemLimit,
                excludedIsinList: collectionData.excludedIsin
                  ? collectionData.excludedIsin?.split(",")
                  : [],
              }}
              onFinish={(values) => {
                const excludedIsin = values.excludedIsinList?.join(",") || "";

                mutate({
                  id: bondCollectionID,
                  name: values.name || "",
                  title: values.title || "",
                  description: values.description || "",
                  iconUrl: values.iconUrl || "",
                  displayType: values.displayType || DisplayType.DEFAULT,
                  isActive: values.isActive ?? false,
                  preFilterCriteria: values.preFilterCriteria || "",
                  postFilterCriteria: values.postFilterCriteria || "",
                  sortCriteria: values.sortCriteria || "",
                  excludedIsin,
                  includeSoldOut: false,
                  collectionType: isManual
                    ? CollectionType.MANUAL
                    : CollectionType.EXPRESSION,
                  collectionItemLimit: values.collectionItemLimit || 100,
                });
              }}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="name"
                    label="Collection Name"
                    rules={[{ required: true }]}
                  >
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="title"
                    label="Display Title"
                    rules={[{ required: true }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item name="description" label="Description">
                <Input.TextArea rows={3} />
              </Form.Item>

              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item name="displayType" label="Display Type">
                    <Select placeholder="Select type">
                      <Select.Option value={DisplayType.DEFAULT}>
                        Default
                      </Select.Option>
                      <Select.Option value={DisplayType.MINIMUM_INVESTMENT}>
                        Minimum Investment
                      </Select.Option>
                      <Select.Option value={DisplayType.SHORT_TERM}>
                        Short Term
                      </Select.Option>
                      <Select.Option value={DisplayType.SHORT_TERM_XIRR}>
                        Short Term XIRR
                      </Select.Option>
                      <Select.Option value={DisplayType.SELLING_OUT_SOON}>
                        Selling Out Soon
                      </Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="iconUrl" label="Icon URL">
                    <Input placeholder="https://..." />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="collectionItemLimit"
                    label="Item Limit"
                    rules={[{ required: true }]}
                  >
                    <InputNumber
                      className="w-full"
                      min={0}
                      placeholder="No limit"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24} className="mb-6">
                <Col span={12}>
                  <Form.Item label="Collection Type">
                    <Switch
                      checked={isManual}
                      checkedChildren="Manual"
                      unCheckedChildren="Expression"
                      onChange={handleToggle}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="isActive"
                    label="Status"
                    valuePropName="checked"
                  >
                    <Switch
                      checkedChildren="Active"
                      unCheckedChildren="Inactive"
                    />
                  </Form.Item>
                </Col>
              </Row>

              {!isManual && (
                <>
                  <Row gutter={16}>
                    <Col span={24}>
                      <Button type="default" onClick={() => setModalOpen(true)} className="mb-4 text-left" >
                        Choose Expression
                      </Button>
                      <Modal
                        open={isModalOpen}
                        onCancel={() => setModalOpen(false)}
                        title="Choose Expression"
                        footer={null}
                        destroyOnHidden
                      >
                        <Select
                          options={expressions}
                          onChange={handleExpressionChange}
                          placeholder="Select expression"
                          className="w-full"
                          value={expressionName}
                        />
                      </Modal>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={24}>
                      <Form.Item label="Sort Criteria" name="sortCriteria">
                        <Input placeholder="Sort Criteria" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={24}>
                      <Form.Item name="preFilterCriteria" label="Pre Filter">
                        <TextArea placeholder="Enter criteria" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={24}>
                      <Form.Item name="postFilterCriteria" label="Post Filter">
                        <TextArea placeholder="Enter criteria" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item name="excludedIsinList" label="Excluded Bonds">
                    <Select
                      mode="multiple"
                      placeholder="Select bonds to exclude"
                      options={allISINsData?.data.map((bond) => ({
                        label: `${bond.displayTitle} (${bond.isinCode})`,
                        value: bond.isinCode,
                      }))}
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label as string)
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                    />
                  </Form.Item>
                </>
              )}
              <Space>
                <Button type="primary" htmlType="submit" loading={isUpdating}>
                  Update Collection
                </Button>
              </Space>

              <Form.Item name="collectionType" hidden>
                <Input />
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {isManual ? (
          <Col span={10}>
            <Card>
              <SortableBondTable
                key={collectionData?.id}
                collectionId={bondCollectionID}
                onAddBond={() => setModalOpen(true)}
              />
            </Card>

            <CreateBondModal
              collectionId={bondCollectionID}
              visible={isModalOpen}
              onClose={() => setModalOpen(false)}
            />
          </Col>
        ) : (
          <Col span={10}>
            <CollectionItemTagging bondCollectionID={bondCollectionID} />
          </Col>
        )}
      </Row>
    </div>
  );
}
