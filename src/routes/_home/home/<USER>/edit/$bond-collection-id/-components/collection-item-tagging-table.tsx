import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Table, Button, Popconfirm, message, Space } from "antd";
import { useMemo } from "react";
import { DeleteOutlined } from "@ant-design/icons";
import QueryRenderer from "~/components/functional/query-renderer";
import { deleteTagCollectionItem } from "~/lib/queries/bond-catalog";
import { handleError } from "~/lib/utils/error";
import { getAllTagCollectionItemQueryOptions } from "~/lib/queries/queryKeys";

export function TagCollectionItemTable({ bondCollectionID }: { bondCollectionID: string }) {
  const queryClient = useQueryClient();
  
  const query = useQuery(getAllTagCollectionItemQueryOptions());

  const { mutate: deleteItem } = useMutation({
    mutationFn: async (id: string) => {
      return deleteTagCollectionItem(id);
    },
    onSuccess: () => {
      message.success("Tag collection item deleted successfully");
      queryClient.invalidateQueries({ queryKey: getAllTagCollectionItemQueryOptions().queryKey });
    },
    onError: (error: Error) => {
      handleError(error, "Failed to delete item");
    },
  });

  const handleDelete = (id: string) => {
    deleteItem(id);
  };

  const columns = [
    {
      title: "Tag",
      dataIndex: "tagName",
      key: "tagName",
    },
    {
      title: "ISIN Code",
      dataIndex: "collectionItemIsinCode",
      key: "collectionItemIsinCode",
    },
    {
      title: "Actions",
      key: "actions",
      align: "center" as const,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Popconfirm
            title="Are you sure you want to delete this item?"
            onConfirm={() => handleDelete(record.id)}
            okText="Delete"
            okType="danger"
            cancelText="Cancel"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <QueryRenderer query={query}>
      {(data) => {
        const filteredData = useMemo(() => {
          if (!data.tagCollectionItem) return [];
          return data.tagCollectionItem.filter((tagCollectionItem) =>
            tagCollectionItem.collectionId === bondCollectionID
          );
        }, [data]);

        return (
          <Table
            bordered
            columns={columns}
            dataSource={filteredData}
            rowKey="id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
             }}
          />
        );
      }}
    </QueryRenderer>
  );
}
