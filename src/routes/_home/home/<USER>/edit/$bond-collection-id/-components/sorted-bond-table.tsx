import React, { useEffect, useState } from "react";
import {
  DragDropContext,
  Droppable,
  Draggable,
  type DropResult,
} from "@hello-pangea/dnd";
import {
  Button,
  Space,
  message,
  Modal,
  Form,
  Input,
  InputNumber,
  Row,
  Col,
  Popconfirm,
  Spin,
  Alert,
  Typography,
  Select,
  Tag,
  Switch,
} from "antd";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { EditOutlined, DeleteOutlined } from "@ant-design/icons";

import type {
  BondCollectionItemResponse,
  GetBondsCollectionItemResponse,
} from "~/gen/proto-models/BrokingCollection";

import {
  deleteBondFromCollection,
  fetchBondsByCollectionId,
  bulkUpdateBondItemPriority,
  fetchAllISINs,
  createBondInCollection,
} from "~/lib/queries/bonds-collection";
import {
  getAllTagCollectionItem,
  getAllTags,
  deleteTagCollectionItem,
  createTagCollectionItem,
} from "~/lib/queries/bond-catalog";

const { Title: Header } = Typography;

interface BondCollectionTableProps {
  collectionId: string;
  onAddBond: () => void;
}

interface CreateBondFormValues {
  displayTitle: string;
  isinCode: string;
  priority: number;
  isActive: boolean;
  sellingPoint?: string;
  buttonCta?: string;
  struckenYield?: number;
  showTag?: boolean;
  isDynamicTag?: boolean;
  tagName?: string;
  iconUrl?: string;
  textColor?: string;
  backgroundColor?: string;
  tagType?: string;
  tagId: string;
}

const BondCollectionTable: React.FC<BondCollectionTableProps> = ({
  collectionId,
  onAddBond,
}) => {
  const [sortedBonds, setSortedBonds] = useState<BondCollectionItemResponse[]>(
    []
  );
  const [editingBond, setEditingBond] =
    useState<BondCollectionItemResponse | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const [isTagged, setIsTagged] = useState(false);

  const query = useQuery({
    queryKey: ["all-tag-collection-items"],
    queryFn: getAllTagCollectionItem,
  });

  const { data: tagsData } = useQuery({
    queryKey: ["all-tags"],
    queryFn: getAllTags,
  });

  const { data, isLoading, isError } = useQuery<GetBondsCollectionItemResponse>(
    {
      queryKey: ["bonds", collectionId],
      queryFn: () => fetchBondsByCollectionId(collectionId),
    }
  );

  const { data: allISINsData, isLoading: isLoadingISINs } = useQuery({
    queryKey: ["all-isins"],
    queryFn: fetchAllISINs,
  });

  const { mutate: updatePriorities, isPending: isSaving } = useMutation({
    mutationFn: bulkUpdateBondItemPriority,
    onSuccess: () => {
      message.success("Bond priorities saved successfully.");
      queryClient.invalidateQueries({ queryKey: ["bonds", collectionId] });
    },
    onError: () => message.error("Failed to save bond priorities."),
  });

  const { mutate: updateBond, isPending: isUpdating } = useMutation({
    mutationFn: createBondInCollection,
    onSuccess: () => {
      message.success("Bond updated successfully.");
      setEditingBond(null);
      queryClient.invalidateQueries({ queryKey: ["bonds", collectionId] });
    },
    onError: () => message.error("Failed to update bond."),
  });

  const { mutate: deleteBond } = useMutation({
    mutationFn: deleteBondFromCollection,
    onSuccess: () => {
      message.success("Bond deleted.");
      queryClient.invalidateQueries({ queryKey: ["bonds", collectionId] });
    },
    onError: () => message.error("Failed to delete bond."),
  });

  const { mutate: createTagItem } = useMutation({
    mutationFn: createTagCollectionItem,
    onSuccess: () => {
      message.success("Tag created successfully.");
      queryClient.invalidateQueries({ queryKey: ["all-tag-collection-items"] });
      queryClient.invalidateQueries({ queryKey: ["bonds", collectionId] });
    },
    onError: () => message.error("Failed to create tag."),
  });

  const { mutate: deleteTagItem } = useMutation({
    mutationFn: deleteTagCollectionItem,
    onSuccess: () => {
      message.success("Tag deleted successfully.");
      queryClient.invalidateQueries({ queryKey: ["all-tag-collection-items"] });
      queryClient.invalidateQueries({ queryKey: ["bonds", collectionId] });
    },
    onError: () => message.error("Failed to delete tag."),
  });

  useEffect(() => {
    if (data?.data) {
      const sorted = [...data.data].sort((a, b) => a.priority - b.priority);
      setSortedBonds(sorted);
    }
  }, [data]);

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(sortedBonds);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const updatedItems = items.map((item, index) => ({
      ...item,
      priority: index + 1,
    }));

    setSortedBonds(updatedItems);
  };

  const handleSaveOrder = () => {
    const payload = {
      items: sortedBonds.map((bond) => ({
        id: bond.id,
        newPriority: bond.priority,
      })),
    };
    updatePriorities(payload);
  };

  const tagColor = (bondISIN: string) => {
    const tag = query.data?.tagCollectionItem.find(
      (tag) => tag.collectionItemIsinCode === bondISIN && tag.collectionId === collectionId
    );
    return tagsData?.tag.find((tagData) => tagData.id === tag?.tagId)?.color;
  };

  const tagName = (bondISIN: string) => {
    const tag = query.data?.tagCollectionItem.find(
      (tag) => tag.collectionItemIsinCode === bondISIN && tag.collectionId === collectionId
    );
    return tagsData?.tag.find((tagData) => tagData.id === tag?.tagId)?.name;
  };

  const openEditModal = (bond: BondCollectionItemResponse) => {
    setEditingBond(bond);

    if (
      query.data?.tagCollectionItem.find(
        (tag) =>
          tag.collectionItemIsinCode === bond.isinCode &&
          tag.collectionId === collectionId
      )
    ) {
      setIsTagged(true);
    } else {
      setIsTagged(false);
    }

    const tag = query.data?.tagCollectionItem.find(
      (tag) =>
        tag.collectionItemIsinCode === bond.isinCode &&
        tag.collectionId === collectionId
    );

    form.setFieldsValue({
      displayTitle: bond.displayTitle || "",
      isinCode: bond.isinCode || "",
      isActive: bond.isActive ?? false,
      priority: bond.priority ?? 1,
      sellingPoint: bond.sellingPoint ?? "",
      buttonCta: bond.buttonCta ?? "",
      struckenYield: bond.struckenYield ?? 0,
      showTag: bond.showTag ?? false,
      isDynamicTag: bond.isDynamicTag ?? false,
      tagName: bond.tagConfig?.name ?? "",
      backgroundColor: bond.tagConfig?.bgColor ?? "",
      textColor: bond.tagConfig?.color ?? "",
      iconUrl: bond.tagConfig?.iconUrl ?? "",
      tagType: bond.tagConfig?.type ?? "",
      tagId: tag?.tagId ?? "",
    });
  };

  const handleDelete = () => {
    const tag = query.data?.tagCollectionItem.find(
      (tag) =>
        tag.collectionItemIsinCode === editingBond?.isinCode &&
        tag.collectionId === collectionId
    );

    if (tag?.id) {
      deleteTagItem(tag.id);
      setIsTagged(false);
    }
  };

  const handleEditFinish = (values: CreateBondFormValues) => {
    if (!editingBond) return;

    updateBond({
      id: editingBond.id,
      displayTitle: values.displayTitle,
      isinCode: values.isinCode,
      priority: values.priority,
      isActive: values.isActive,
      collectionId,
      sellingPoint: values.sellingPoint || "",
      buttonCta: values.buttonCta || "",
      struckenYield: values.struckenYield || 0,
      showTag: values.showTag || false,
      isDynamicTag: values.isDynamicTag || false,
      createdAt: "",
      updatedAt: "",
    });

    if (values.tagId && !isTagged) {
      createTagItem({
        tagId: values.tagId,
        collectionId,
        collectionItemIsinCode: values.isinCode,
      });
    }
  };

  if (isLoading) return <Spin size="large" />;
  if (isError) return <Alert type="error" message="Failed to load bonds." />;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Header level={4}>Bonds ({sortedBonds.length})</Header>
        <Space>
          <Button type="primary" onClick={onAddBond}>
            Add Bond
          </Button>
          <Button onClick={handleSaveOrder} loading={isSaving}>
            Save Order
          </Button>
        </Space>
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="bond-table">
          {(provided) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className="space-y-2"
            >
              {sortedBonds.map((bond, index) => (
                <Draggable key={bond.id} draggableId={bond.id} index={index}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      className={`p-4 bg-white border rounded-lg shadow-sm hover:shadow-md transition-shadow ${
                        snapshot.isDragging ? "shadow-lg" : ""
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <span className="text-sm text-gray-500">
                              #{bond.priority}
                            </span>
                            <h4 className="font-medium">{bond.displayTitle}</h4>
                            <Tag color={bond.isActive ? "green" : "red"}>
                              {bond.isActive ? "Active" : "Inactive"}
                            </Tag>
                            {tagColor(bond.isinCode) && (
                              <Tag color={tagColor(bond.isinCode)}>{tagName(bond.isinCode)}</Tag>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {bond.isinCode}
                          </p>
                          {bond.sellingPoint && (
                            <p className="text-sm text-gray-500 mt-1">
                              {bond.sellingPoint}
                            </p>
                          )}
                        </div>
                        <Space>
                          <Button
                            icon={<EditOutlined />}
                            size="small"
                            onClick={() => openEditModal(bond)}
                          />
                          <Popconfirm
                            title="Delete this bond?"
                            onConfirm={() => deleteBond(bond.id)}
                          >
                            <Button
                              icon={<DeleteOutlined />}
                              size="small"
                              danger
                            />
                          </Popconfirm>
                        </Space>
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      <Modal
        open={!!editingBond}
        onCancel={() => setEditingBond(null)}
        title="Edit Bond"
        footer={null}
        destroyOnHidden
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleEditFinish}>
          <div className="space-y-4">
            <Form.Item
              name="displayTitle"
              label="Display Title"
              rules={[{ required: true }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="isinCode"
              label="ISIN Code"
              rules={[{ required: true }]}
            >
              <Select
                loading={isLoadingISINs}
                showSearch
                placeholder="Select ISIN"
                options={allISINsData?.data.map((bond) => ({
                  label: `${bond.displayTitle} (${bond.isinCode})`,
                  value: bond.isinCode,
                }))}
              />
            </Form.Item>

            <Form.Item name="sellingPoint" label="Selling Point">
              <Input placeholder="Enter selling point" />
            </Form.Item>

            <Form.Item name="buttonCta" label="Button CTA">
              <Input placeholder="Enter button CTA text" />
            </Form.Item>
            <Row gutter={16} align="bottom">
              <Col span={20}>
                <Form.Item label="Tag" name="tagId">
                  <Select
                    placeholder="Select a tag"
                    showSearch
                    optionFilterProp="label"
                    options={tagsData?.tag.map((tag) => ({
                      label: tag.name,
                      value: tag.id,
                    }))}
                    disabled={isTagged}
                  />
                </Form.Item>
              </Col>
              <Col span={2}>
                <Form.Item>
                  <Popconfirm title="Delete this Tag?" onConfirm={handleDelete}>
                    <Button
                      icon={<DeleteOutlined />}
                      size="middle"
                      danger
                      disabled={!isTagged}
                    />
                  </Popconfirm>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="isActive"
                  label="Status"
                  valuePropName="checked"
                >
                  <Switch
                    checkedChildren="Active"
                    unCheckedChildren="Inactive"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="priority" label="Priority">
                  <InputNumber min={1} className="w-full" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="struckenYield" label="Stricken Yield">
                  <InputNumber
                    min={0}
                    step={0.01}
                    placeholder="0.00"
                    className="w-full"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="showTag"
                  label="Show Tag"
                  valuePropName="checked"
                >
                  <Switch disabled />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="isDynamicTag"
                  label="Dynamic Tag"
                  valuePropName="checked"
                >
                  <Switch disabled />
                </Form.Item>
              </Col>
            </Row>
          </div>
          <div className="flex justify-end gap-2 mt-4">
            <Button onClick={() => setEditingBond(null)}>Cancel</Button>
            <Button type="primary" htmlType="submit" loading={isUpdating}>
              Update Bond
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default BondCollectionTable;
