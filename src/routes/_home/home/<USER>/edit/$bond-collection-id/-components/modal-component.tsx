import { Modal } from "antd";
import { FormElement } from "./form-element";

interface modalState {
    isModalOpen: boolean;
    setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};


export function ModalComponent({ modalState, bondCollectionID }: { modalState: modalState; bondCollectionID: string }) {
  const { isModalOpen, setModalOpen } = modalState;

  const handleCancel = () => setModalOpen(false);

  return (
    <Modal
      title="Create Tag Collection Item"
      open={isModalOpen}
      onCancel={handleCancel}
      width="40%"
      footer={null}
      destroyOnHidden
    >
      <FormElement setModalOpen={setModalOpen} bondCollectionID={bondCollectionID} />
    </Modal>
  );
}
