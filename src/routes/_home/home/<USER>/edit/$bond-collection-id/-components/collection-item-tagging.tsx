import { CreateTagCollectionItem } from "./create-collection-item-tagging";
import { TagCollectionItemTable } from "./collection-item-tagging-table";

export function CollectionItemTagging({
  bondCollectionID,
}: {
  bondCollectionID: string;
}) {
  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <CreateTagCollectionItem bondCollectionID={bondCollectionID} />
      </div>
      <TagCollectionItemTable
        bondCollectionID={bondCollectionID}
      />
    </div>
  );
}
