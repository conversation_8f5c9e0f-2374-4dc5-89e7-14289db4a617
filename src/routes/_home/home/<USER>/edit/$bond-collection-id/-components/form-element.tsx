import React from "react";
import {
  Form,
  Button,
  message,
  Select,
} from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useForm } from "antd/es/form/Form";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createTagCollectionItem } from "~/lib/queries/bond-catalog";
import { CreateTagCollectionItemRequest } from "~/gen/proto-models/Catalog";
import { getAllTags } from "~/lib/queries/bond-catalog";
import { fetchAllISINs } from "~/lib/queries/bonds-collection";
import { handleError } from "~/lib/utils/error";

interface FormElementProps {
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  bondCollectionID: string;
}

export function FormElement({ setModalOpen, bondCollectionID }: FormElementProps) {
  const [form] = useForm<CreateTagCollectionItemRequest>();
  const queryClient = useQueryClient();

  const { data: tagsData } = useQuery({
    queryKey: ["all-tags"],
    queryFn: getAllTags,
  });

  const { data: allISINsData } = useQuery({
    queryKey: ["all-isins"],
    queryFn: fetchAllISINs,
  });

  const { mutate: createTagCollectionItemElement } = useMutation({
    mutationFn: async (data: CreateTagCollectionItemRequest) => {
      return createTagCollectionItem(data);
    },
    onSuccess: () => {
      message.success("Tag Collection Item created successfully!");
      queryClient.invalidateQueries({ queryKey: ["all-tag-collection-items"] });
      setModalOpen(false);
      form.resetFields();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create tag collection item");
    },
  });

  const onFinish = (values: CreateTagCollectionItemRequest) => {
    const payload: CreateTagCollectionItemRequest = {
      tagId: values.tagId,
      collectionId: bondCollectionID,
      collectionItemIsinCode: values.collectionItemIsinCode,
    };
    
    createTagCollectionItemElement(payload);
  };

  return (
    <>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        size="middle"
      >
        <Form.Item
          label={<strong>Tag</strong>}
          name="tagId"
          rules={[
            { required: true, message: "Please select a tag!" },
          ]}
        >
          <Select
            placeholder="Select a tag"
            showSearch
            optionFilterProp="label"
            options={tagsData?.tag.map((tag) => ({
              label: tag.name,
              value: tag.id,
            }))}
          />
        </Form.Item>

        <Form.Item
          label={<strong>ISIN Code</strong>}
          name="collectionItemIsinCode"
          rules={[
            { required: true, message: "Please enter ISIN code!" },
          ]}
        >
          <Select
            placeholder="Select an ISIN Code"
            showSearch
            optionFilterProp="label"
            options={allISINsData?.data.map((bond) => ({
              label: bond.isinCode,
              value: bond.isinCode,
            }))}
          />
        </Form.Item>

        <Form.Item className="mt-4 text-right">
          <Button
            type="primary"
            htmlType="submit"
            icon={<PlusOutlined />}
            size="middle"
          >
            Create Tag Collection Item
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}

