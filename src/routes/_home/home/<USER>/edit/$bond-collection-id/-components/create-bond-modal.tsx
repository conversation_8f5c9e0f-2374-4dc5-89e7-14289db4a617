import React from "react";
import {
  Modal,
  Form,
  Input,
  InputN<PERSON>ber,
  Button,
  message,
  Row,
  Col,
  Select,
  Typography,
  Switch,
} from "antd";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import type {
  CreateAndUpdateBondCollectionItemRequest,
} from "~/gen/proto-models/BrokingCollection";

import {
  createBondInCollection,
  fetchAllISINs,
} from "~/lib/queries/bonds-collection";
import { handleError } from "~/lib/utils/error";

const { Title } = Typography;

interface Props {
  visible: boolean;
  onClose: () => void;
  collectionId: string;
}

interface CreateBondFormValues {
  displayTitle: string;
  isinCode: string;
  priority?: number;
  isActive?: boolean;
  sellingPoint?: string;
  buttonCta?: string;
  struckenYield?: number;
  showTag?: boolean;
  isDynamicTag?: boolean;
}

const CreateBondModal: React.FC<Props> = ({
  visible,
  onClose,
  collectionId,
}) => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  const { data: allISINsData, isLoading: isLoadingISINs } = useQuery({
    queryKey: ["all-isins"],
    queryFn: fetchAllISINs,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (bond: CreateAndUpdateBondCollectionItemRequest) =>
      createBondInCollection(bond),
    onSuccess: () => {
      message.success("Bond added successfully");
      queryClient.invalidateQueries({ queryKey: ["bonds", collectionId] });
      form.resetFields();
      onClose();
    },
    onError: (error: Error) => {
      handleError(error, "Failed to create bond");
    },
  });

  const onFinish = (values: CreateBondFormValues) => {

    const payload: CreateAndUpdateBondCollectionItemRequest = {
      id: "",
      displayTitle: values.displayTitle,
      isinCode: values.isinCode,
      priority: values.priority ?? 1,
      isActive: values.isActive ?? true,
      collectionId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      sellingPoint: values.sellingPoint || "",
      buttonCta: values.buttonCta || "",
      struckenYield: values.struckenYield ?? 0,
      showTag: values.showTag ?? true,
      isDynamicTag: values.isDynamicTag ?? false,
    };

    mutate(payload);
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      open={visible}
      onCancel={handleCancel}
      title={
        <Title level={4} className="mb-0">
          Add New Bond
        </Title>
      }
      footer={null}
      destroyOnHidden
      width={600}
    >
      <Form form={form} layout="vertical" onFinish={onFinish} className="mt-4">
        <div className="space-y-4">
          <Form.Item
            name="displayTitle"
            label="Display Title"
            rules={[{ required: true, message: "Please enter display title" }]}
          >
            <Input placeholder="Enter display title" />
          </Form.Item>

          <Form.Item
            name="isinCode"
            label="ISIN Code"
            rules={[{ required: true, message: "Please select ISIN code" }]}
          >
            <Select
              loading={isLoadingISINs}
              showSearch
              placeholder="Select an ISIN"
              optionFilterProp="label"
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={allISINsData?.data.map((bond) => ({
                label: `${bond.displayTitle} (${bond.isinCode})`,
                value: bond.isinCode,
              }))}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="isActive"
                label="Status"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="priority" label="Priority" initialValue={1}>
                <InputNumber
                  min={1}
                  placeholder="Priority"
                  className="w-full"
                />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
          <Button onClick={handleCancel}>Cancel</Button>
          <Button type="primary" htmlType="submit" loading={isPending}>
            Create Bond
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default CreateBondModal;
