import {
  Outlet,
  createRootRouteWithContext,
  useNavigate,
} from "@tanstack/react-router";
import { useAuth, type AuthContext } from "../auth";
import { QueryClientProvider } from "@tanstack/react-query";
import { request } from "../lib/requests/proto-api";
import { ConfigProvider } from "antd";
import { queryClient } from "../lib/utils/queryClient";

interface MyRouterContext {
  auth: AuthContext;
}

function RootComponent() {
  const auth = useAuth();
  const navigate = useNavigate();
  if (!auth.isAuthenticated && location.pathname !== "/auth/login") {
    navigate({ to: "/auth/login" });
  }

  return (
    <ConfigProvider
      theme={{
        components: {
          InputNumber: {
            controlWidth: undefined,
          },
        },
      }}
    >
      <QueryClientProvider client={queryClient}>
        <Outlet />
      </QueryClientProvider>
    </ConfigProvider>
  );
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  component: RootComponent,
  loader: async () => {
    try {
      await request({
        method: "GET",
        url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/auth/validate`,
      });
    } catch (error) {
      console.log("Token validation failed");
    }
  },
  notFoundComponent: () => (
    <div className="flex items-center justify-center w-screen h-full">
      404: Page Not Found
    </div>
  ),
});
