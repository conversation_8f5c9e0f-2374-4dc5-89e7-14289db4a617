import { createFileRoute, useNavigate } from "@tanstack/react-router";
import {
  GoogleLogin,
  GoogleOAuthProvider,
  type CredentialResponse,
} from "@react-oauth/google";
import stableMoneyIcon from "~/assets/logos/stable-money.webp";
import { useAuth } from "~/auth";
import { notification } from "antd";
import { useMutation } from "@tanstack/react-query";
import { authenticateDashboardUser } from "~/lib/queries/admin";

const googleClientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
const googleClientSecret = import.meta.env.VITE_GOOGLE_CLIENT_SECRET;

export const Route = createFileRoute("/auth/login/")({
  component: RouteComponent,
});

function RouteComponent() {
  const auth = useAuth();
  const navigate = useNavigate();
  if (auth.isAuthenticated) {
    navigate({ to: "/home" });
  }
  const { mutate: login } = useMutation({
    mutationFn: (credential: string | undefined) =>
      authenticateDashboardUser({ accessToken: credential! }),
    onSuccess: (data) => {
      auth.login(data.token);
      navigate({ to: "/home" });
    },
    onError: (error) => {
      notification.error({
        message: "Login Failed: " + error.message,
        description: "Please try ",
      });
    },
  });

  const handleSuccessGoogleLogin = (credentialResponse: CredentialResponse) => {
    login(credentialResponse.credential);
  };
  return (
    <GoogleOAuthProvider clientId={googleClientId} key={googleClientSecret}>
      <div className="flex flex-col items-center justify-center gap-5 w-screen h-screen">
        <div>
          <img src={stableMoneyIcon} alt="StableMoney" className="h-20" />
        </div>
        <div className="text-3xl font-bold">Control Hub</div>
        <div>
          <GoogleLogin
            useOneTap
            ux_mode="popup"
            onSuccess={handleSuccessGoogleLogin}
            onError={() => {
              notification.error({
                message: "Login Failed",
                description: "Please try again",
              });
            }}
          />
        </div>
      </div>
    </GoogleOAuthProvider>
  );
}