import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useAuth } from "~/auth";
export const Route = createFileRoute("/")({
  component: RouteComponent,
});

function RouteComponent() {
  const auth = useAuth();
  const navigate = useNavigate();

  const handleNavigate = (to: string) => {
    navigate({ to });
  };

  if (auth.isAuthenticated) {
    handleNavigate("/home" );
  } else {
    handleNavigate("/auth/login");
  }
  return null;
}
