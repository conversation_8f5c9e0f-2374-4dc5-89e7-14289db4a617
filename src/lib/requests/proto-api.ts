import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { getStoredAuthToken, logout } from "~lib/utils/auth";

type RequestOptions<T> = {
  method: string;
  url: string;
  responseDecoder?: (input: BinaryReader | Uint8Array, length?: number) => T;
  body?: BinaryWriter;
  signal?: AbortSignal;
};

export function request<T>(
  options: RequestOptions<T> & {
    responseDecoder: NonNullable<RequestOptions<T>["responseDecoder"]>;
  }
): Promise<T>;
export function request(
  options: Omit<RequestOptions<never>, "responseDecoder">
): Promise<void>;

export async function request<T>({
  method,
  url,
  body,
  responseDecoder,
  signal,
}: RequestOptions<T>) {
  const authToken = getStoredAuthToken();
  const headers: Record<string, string> = {
    Accept: "application/x-protobuf",
    "Content-Type": "application/x-protobuf",
    "X-App-Version-Code": "728",
    "X-Client-Type": "WEB",
  };
  if (authToken) {
    headers["Authorization"] = `Bearer ${authToken}`;
  }
  const fetchResponse = await fetch(url, {
    method,
    signal,
    headers: headers,
    body: body?.finish(),
  });

  if (fetchResponse.status === 401) {
    logout();
  }

  if(fetchResponse.status === 403) {
    throw new Error("Forbidden : You don't have permission to access this resource. Ask for permission.");
  }

  if (fetchResponse.status > 299) {
    throw fetchResponse;
  }

  if (!responseDecoder) {
    return;
  }

  const arrayBuffer = await fetchResponse.arrayBuffer();
  return responseDecoder(new Uint8Array(arrayBuffer));
}
