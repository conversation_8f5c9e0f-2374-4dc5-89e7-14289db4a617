import { getStoredAuthToken, logout } from "~lib/utils/auth";

interface RequestOption {
  method: string;
  url: string;
  body?: Object | null;
  signal?: AbortSignal;
}

export async function restRequest<T>({
  method,
  url,
  body,
  signal,
}: RequestOption): Promise<T> {
  const authToken = getStoredAuthToken();
  const headers: Record<string, string> = {
    Accept: "application/json",
    "Content-Type": "application/json",
    "X-App-Version-Code": "728",
    "X-Client-Type": "WEB",
  };

  if (authToken) {
    headers["Authorization"] = `Bearer ${authToken}`;
  }
  const fetchResponse = await fetch(url, {
    method,
    signal,
    headers: headers,
    body: body ? JSON.stringify(body) : null,
  });

  if (fetchResponse.status === 401) {
    logout();
  }

  if(fetchResponse.status === 403) {
    throw new Error("Forbidden : You don't have permission to access this resource. Ask for permission.");
  }

  if (fetchResponse.status > 299) {
    throw fetchResponse;
  }

  if (fetchResponse.status === 204) {
    return null as T;
  }

  return (await fetchResponse.json()) as T;
}
