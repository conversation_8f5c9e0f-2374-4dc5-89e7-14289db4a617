import { getStoredAuthToken, logout } from "~lib/utils/auth";

interface RequestOption {
  url: string;
  body: Object;
  signal?: AbortSignal;
}

export async function jsonRequest<T>({
  url,
  body,
  signal,
}: RequestOption): Promise<T> {
  const authToken = getStoredAuthToken();
  const headers: Record<string, string> = {
    Accept: "application/json",
    "Content-Type": "application/json",
    "X-App-Version-Code": "728",
    "X-Client-Type": "WEB",
  };

  if (authToken) {
    headers["Authorization"] = `Bearer ${authToken}`;
  }
  const fetchResponse = await fetch(url, {
    method: "POST",
    signal,
    headers: headers,
    body: JSON.stringify(body),
  });

  if (fetchResponse.status === 401) {
    logout();
  }

  if(fetchResponse.status === 403) {
    throw new Error("Forbidden : You don't have permission to access this resource. Ask for permission.");
  }

  if (fetchResponse.status > 299) {
    throw fetchResponse;
  }

  return (await fetchResponse.json()) as T;
}
