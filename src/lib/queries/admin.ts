import { DashboardUserCredentialRequest, DashboardUserCredentialResponse } from "~/gen/proto-models/DashboardAuth";
import { request } from "~lib/requests/proto-api";

const adminBaseUrl = import.meta.env.VITE_ADMIN_BASE_URL;

export const authenticateDashboardUser = (
  dashboardUserCredentialRequest: DashboardUserCredentialRequest
): Promise<DashboardUserCredentialResponse> => {
  return request({
    method: "POST",
    url: `${adminBaseUrl}/v1/auth/google`,
    body: DashboardUserCredentialRequest.encode(dashboardUserCredentialRequest),
    responseDecoder: DashboardUserCredentialResponse.decode,
  });
};

