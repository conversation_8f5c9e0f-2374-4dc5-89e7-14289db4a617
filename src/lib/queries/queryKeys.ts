import { keepPreviousData, queryOptions } from "@tanstack/react-query";
import {
  getBanksDownTime,
  getBanksNameAndId,
  getBankTime,
} from "./bankDownTime";
import {
  fetchAllISINs,
  fetchBondCollectionById,
  fetchBondsByCollectionId,
  getAllBondCollections,
  getAllCollectionExpressions,
} from "./bonds-collection";
import {
  getBanner,
  getBanners,
  getBannersHeaders,
  getFeatureFlagMapping,
  getFeatureFlagMappings,
  getFrame,
  getFrames,
  getPage,
  getPages,
  getRule,
  getRules,
  getStories,
  getStory,
} from "./personalization.ts";
import type { ReferenceType } from "~/gen/proto-models/PersonalizationAdmin";
import {
  getAllActiveBonds,
  getAllBonds,
  getAllInActiveBonds,
  getAllMediaItems,
  getAllTagCollectionItem,
  getAllTags,
  getBondCashflowSchedule,
  getBondDetailById,
  getBondInventory,
  getBondIssuingInstitution,
  getBondOfferingDetail,
  getBondPrices,
  getMediaItemById,
  getPartyDetails,
  getTag,
} from "./bond-catalog";
import { getBankTenureList, getFdCollectionData, getFdCollectionItemData, getFdCollectionItems, getFdCollections } from "./fd-collections.ts";
import { getAccessResource, getAccessResources, getCurrentUserRoles, getDashboardUsers, getRole, getRoleAccessResourcesByRole, getRoles, getUserRoles } from "./access-control.ts";

export const banksDownTimeQueryOptions = () => {
  return queryOptions({
    queryKey: ["banks-detail"],
    queryFn: getBanksDownTime,
  });
};

export const bankNameAndIDQueryOptions = () => {
  return queryOptions({
    queryKey: ["banks-name-id"],
    queryFn: getBanksNameAndId,
  });
};

export const bankDownTimeQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["bank-down-time", id],
    queryFn: () => getBankTime(id),
  });
};

export const allBondDetailsQueryOptions = () => {
  return queryOptions({
    queryKey: ["all-bond-details"],
    queryFn: getAllBonds,
  });
};

export const activeBondCatalogListQueryOptions = (req: any) => {
  return queryOptions({
    queryKey: ["active-bond-catalog", req],
    queryFn: () => getAllActiveBonds(req),
    placeholderData: keepPreviousData,
  });
};

export const inActiveBondCatalogListQueryOptions = (req: any) => {
  return queryOptions({
    queryKey: ["inactive-bond-catalog", req],
    queryFn: () => getAllInActiveBonds(req),
    placeholderData: keepPreviousData,
  });
};

export const bondIssuingInstitutionQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["bond-issuing-institution", id],
    queryFn: () => getBondIssuingInstitution(id),
  });
};

export const bondOfferingDetailQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["bond-offering-detail", id],
    queryFn: () => getBondOfferingDetail(id),
  });
};

export const bondInventoryQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["bond-inventory", id],
    queryFn: () => getBondInventory(id),
  });
};

export const mediaItemQueryOptions = (req: any) => {
  return queryOptions({
    queryKey: ["media-item", req],
    queryFn: () => getAllMediaItems(req),
  });
};

export const partyDetailsQueryOptions = () => {
  return queryOptions({
    queryKey: ["party-details"],
    queryFn: getPartyDetails,
  });
};

export const allBondCollectionsQueryOptions = () => {
  return queryOptions({
    queryKey: ["collections"],
    queryFn: getAllBondCollections,
  });
};

export const getBondCollectionByIdQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["collections", id],
    queryFn: () => fetchBondCollectionById(id),
  });
};

export const getBondsByCollectionIdQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["collections", id, "bonds"],
    queryFn: () => fetchBondsByCollectionId(id),
  });
};

export const getAllISINsQueryOptions = () => {
  return queryOptions({
    queryKey: ["all-isins"],
    queryFn: fetchAllISINs,
  });
};

export const bondDetailByIdQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["bond-detail", id],
    queryFn: () => getBondDetailById(id),
  });
};

export const bondCashflowScheduleQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["bond-cashflow-schedule", id],
    queryFn: () => getBondCashflowSchedule(id),
  });
};

export const getPagesQueryOptions = (page: number, q: string) => {
  return queryOptions({
    queryKey: ["pages", page, q],
    queryFn: () =>
      getPages({ pagination: { page, size: 8 }, includeDeleted: false, q: q }),
  });
};

export const getPageQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["page", id],
    queryFn: () => getPage(id),
  });
};

export const getFeatureFlagMappingsQueryOptions = (
  id: string,
  referenceType: ReferenceType
) => {
  return queryOptions({
    queryKey: ["feature-flag-mappings", id, referenceType],
    queryFn: () => getFeatureFlagMappings({ typeId: id, referenceType }),
  });
};

export const getFeatureFlagMappingQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["feature-flag-mapping", id],
    queryFn: () => getFeatureFlagMapping(id),
  });
};

export const getFramesQueryOptions = (page: number, q: string) => {
  return queryOptions({
    queryKey: ["frames", page, q],
    queryFn: () =>
      getFrames({ pagination: { page, size: 8 }, includeDeleted: false, q: q }),
  });
};

export const getFrameQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["frame", id],
    queryFn: () => getFrame(id),
  });
};

export const getBannersQueryOptions = (page: number, q: string) => {
  return queryOptions({
    queryKey: ["banners", page, q],
    queryFn: () =>
      getBanners({
        pagination: { page, size: 8 },
        includeDeleted: false,
        q: q,
      }),
  });
};

export const getStoriesQueryOptions = (page: number, q: string) => {
  return queryOptions({
    queryKey: ["stories", page, q],
    queryFn: () =>
      getStories({
        pagination: { page, size: 8 },
        includeDeleted: false,
        q: q,
      }),
  });
};

export const getStoryQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["story", id],
    queryFn: () => getStory(id),
  });
};

export const getRulesQueryOptions = (page: number, q: string) => {
  return queryOptions({
    queryKey: ["rules", page, q],
    queryFn: () =>
      getRules({ pagination: { page, size: 8 }, includeDeleted: false, q: q }),
  });
};

export const getBannersHeadersQueryOptions = () => {
  return queryOptions({
    queryKey: ["banners-headers"],
    queryFn: getBannersHeaders,
  });
};

export const getRuleQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["rule", id],
    queryFn: () => getRule(id),
  });
};

export const getBannerQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["banner", id],
    queryFn: () => getBanner(id),
  });
};

export const allTagsQueryOptions = () => {
  return queryOptions({
    queryKey: ["all-tags"],
    queryFn: getAllTags,
  });
};

export const getAllTagCollectionItemQueryOptions = () => {
  return queryOptions({
    queryKey: ["all-tag-collection-items"],
    queryFn: getAllTagCollectionItem,
  });
};

export const getTagQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["tag", id],
    queryFn: () => getTag(id),
  });
};
export const getBondPricesQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["bond-prices", id],
    queryFn: () => getBondPrices(id),
  });
};

export const getMediaItemByIdQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["media-item", id],
    queryFn: () => getMediaItemById(id),
  });
};

export const getAllCollectionExpressionsQueryOptions = () => {
  return queryOptions({
    queryKey: ["all-collection-expressions"],
    queryFn: getAllCollectionExpressions,
  });
};
export const getFdCollectionsQueryOptions = (page: number, q: string) => {
  return queryOptions({
    queryKey: ["fd-collections", page, q],
    queryFn: () => getFdCollections({ pagination: { page, size: 8 }, q: q }),
  });
};

export const getFdCollectionDataQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["fd-collection", id],
    queryFn: () => getFdCollectionData(id),
  });
};

export const getBankTenureListQueryOptions = () => {
  return queryOptions({
    queryKey: ["bank-tenure-list"],
    queryFn: getBankTenureList,
  });
};

export const getFdCollectionItemsQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["fd-collection-items", id],
    queryFn: () => getFdCollectionItems(id),
  });
};

export const getFdCollectionItemDataQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["fd-collection-item", id],
    queryFn: () => getFdCollectionItemData(id),
  });
};

// Access Resources Query Keys

export const getAccessResourcesQueryOptions = () => {
  return queryOptions({
    queryKey: ["access-resources"],
    queryFn: () => getAccessResources(),
  });
};

export const getAccessResourceQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["access-resource", id],
    queryFn: () => getAccessResource(id),
  });
};

// Roles Query Keys

export const getRolesQueryOptions = () => {
  return queryOptions({
    queryKey: ["roles"],
    queryFn: () => getRoles(),
  });
};

export const getRoleQueryOptions = (id: string) => {
  return queryOptions({
    queryKey: ["role", id],
    queryFn: () => getRole(id),
  });
};

// Dashboard Users Query Keys

export const getDashboardUsersQueryOptions = () => {
  return queryOptions({
    queryKey: ["dashboard-users"],
    queryFn: () => getDashboardUsers(),
  });
};

// Role Access Resources Query Keys

export const getRoleAccessResourcesByRoleQueryOptions = (roleName: string) => ({
  queryKey: ["role-access-resources", roleName],
  queryFn: () => getRoleAccessResourcesByRole(roleName),
});

// User Roles Query Keys
export const getUserRolesQueryOptions = (userId: string) => ({
  queryKey: ["user-roles", userId],
  queryFn: () => getUserRoles(userId),
});

export const getCurrentUserRolesQueryOptions = () => ({
  queryKey: ["current-user-roles"],
  queryFn: () => getCurrentUserRoles(),
});
