import type { BannerResponse, BannersHeadingResponse, BannersRequest, BannersResponse, CreateBannerRequest, CreateFeatureFlagMappingRequest, CreateFrameRequest, CreatePageRequest, CreateRuleRequest, CreateStoryRequest, DeleteBannerResponse, DeleteFeatureFlagMappingResponse, DeleteFrameResponse, DeletePageResponse, DeleteRuleResponse, DeleteStoryResponse, FeatureFlagMappingResponse, FeatureFlagMappingsRequest, FeatureFlagMappingsResponse, FrameResponse, FramesRequest, FramesResponse, PageResponse, PagesRequest, PagesResponse, RuleResponse, RulesRequest, RulesResponse, StatusResponse, StoriesRequest, StoriesResponse, StoryResponse, UpdateBannerRequest, UpdateFeatureFlagMappingRequest, UpdateFrameRequest, UpdatePageRequest, UpdateRuleRequest, UpdateStoryRequest } from "~/gen/proto-models/PersonalizationAdmin";
import { jsonRequest } from "../requests/json-api";

const adminBaseUrl = import.meta.env.VITE_ADMIN_BASE_URL;

export const getPages = (pagesRequest: PagesRequest): Promise<PagesResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getPages`,
    body: pagesRequest,
  });
};

export const deletePage = (id: string): Promise<DeletePageResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/deletePage`,
    body: { id },
  });
}

export const createPage = (createPageRequest: CreatePageRequest): Promise<PageResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/createPage`,
    body: createPageRequest,
  });
}

export const getPage = (id: string): Promise<PageResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getPage`,
    body: { id },
  });
}

export const updatePage = (updatePageRequest: UpdatePageRequest): Promise<PageResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/updatePage`,
    body: updatePageRequest,
  });
}

export const getFeatureFlagMappings = (featureFlagMappingsRequest: FeatureFlagMappingsRequest): Promise<FeatureFlagMappingsResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getFeatureFlagMappings`,
    body: featureFlagMappingsRequest,
  });
}

export const deleteFeatureFlagMapping = (id: string): Promise<DeleteFeatureFlagMappingResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/deleteFeatureFlagMapping`,
    body: { id },
  });
}

export const getFeatureFlagMapping = (id: string): Promise<FeatureFlagMappingResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getFeatureFlagMapping`,
    body: { id },
  });
}

export const updateFeatureFlagMapping = (updateFeatureFlagMappingRequest: UpdateFeatureFlagMappingRequest): Promise<StatusResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/updateFeatureFlagMapping`,
    body: updateFeatureFlagMappingRequest,
  });
}

export const createFeatureFlagMapping = (createFeatureFlagMappingRequest: CreateFeatureFlagMappingRequest): Promise<StatusResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/createFeatureFlagMapping`,
    body: createFeatureFlagMappingRequest,
  });
}

export const getFrames = (framesRequest: FramesRequest): Promise<FramesResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getFrames`,
    body: framesRequest,
  });
}

export const deleteFrame = (id: string): Promise<DeleteFrameResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/deleteFrame`,
    body: { id },
  });
}

export const createFrame = (createFrameRequest: CreateFrameRequest): Promise<FrameResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/createFrame`,
    body: createFrameRequest,
  });
}

export const getFrame = (id: string): Promise<FrameResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getFrame`,
    body: { id },
  });
}

export const updateFrame = (updateFrameRequest: UpdateFrameRequest): Promise<FrameResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/updateFrame`,
    body: updateFrameRequest,
  });
}

export const getBanners = (bannersRequest: BannersRequest): Promise<BannersResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getBanners`,
    body: bannersRequest,
  });
}

export const deleteBanner = (id: string): Promise<DeleteBannerResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/deleteBanner`,
    body: { id },
  });
}

export const createBanner = (createBannerRequest: CreateBannerRequest): Promise<BannerResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/createBanner`,
    body: createBannerRequest,
  });
}

export const getBanner = (id: string): Promise<BannerResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getBanner`,
    body: { id },
  });
}

export const updateBanner = (updateBannerRequest: UpdateBannerRequest): Promise<BannerResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/updateBanner`,
    body: updateBannerRequest,
  });
}

export const getStories = (storiesRequest: StoriesRequest): Promise<StoriesResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getStories`,
    body: storiesRequest,
  });
}

export const deleteStory = (id: string): Promise<DeleteStoryResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/deleteStory`,
    body: { id },
  });
}

export const createStory = (createStoryRequest: CreateStoryRequest): Promise<StoryResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/createStory`,
    body: createStoryRequest,
  });
}

export const getStory = (id: string): Promise<StoryResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getStory`,
    body: { id },
  });
}

export const updateStory = (updateStoryRequest: UpdateStoryRequest): Promise<StoryResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/updateStory`,
    body: updateStoryRequest,
  });
}

export const getRules = (rulesRequest: RulesRequest): Promise<RulesResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getRules`,
    body: rulesRequest,
  });
}

export const deleteRule = (id: string): Promise<DeleteRuleResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/deleteRule`,
    body: { id },
  });
}

export const createRule = (createRuleRequest: CreateRuleRequest): Promise<RuleResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/createRule`,
    body: createRuleRequest,
  });
}

export const getBannersHeaders = (): Promise<BannersHeadingResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getBannersHeaders`,
    body: {},
  });
}

export const getRule = (id: string): Promise<RuleResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/getRule`,
    body: { id },
  });
}

export const updateRule = (updateRuleRequest: UpdateRuleRequest): Promise<RuleResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/PersonalizationAdminService/updateRule`,
    body: updateRuleRequest,
  });
}
