import type { BankTenureResponse, BulkUpdateFdItemPriorityRequest, BulkUpdateFdItemPriorityResponse, CollectionItemResponseProto, CollectionItemsResponseProto, CollectionResponseProto, CollectionsRequestProto, CollectionsResponseProto, CreateCollectionItemRequestProto, CreateCollectionRequestProto, StatusResponseProto, UpdateCollectionItemRequestProto, UpdateCollectionRequestProto } from "~/gen/proto-models/CollectionV2";
import { jsonRequest } from "../requests/json-api";

export const getFdCollections = (collectionsRequest: CollectionsRequestProto): Promise<CollectionsResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/getFdCollections`,
    body: collectionsRequest,
  });
};

export const deleteFdCollection = (id: string): Promise<StatusResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/deleteFdCollection`,
    body: { id },
  });
};

export const createFdCollection = (createFdCollectionRequest: CreateCollectionRequestProto): Promise<StatusResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/CreateFdCollection`,
    body: createFdCollectionRequest,
  });
};

export const getFdCollectionData = (id: string): Promise<CollectionResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/getFdCollectionData`,
    body: { id },
  });
};

export const updateFdCollection = (updateFdCollectionRequest: UpdateCollectionRequestProto): Promise<StatusResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/updateFdCollection`,
    body: updateFdCollectionRequest,
  });
};

export const getBankTenureList = (): Promise<BankTenureResponse> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/getBankTenureList`,
    body: {},
  });
};

export const createFdCollectionItem = (createFdCollectionItemRequest: CreateCollectionItemRequestProto): Promise<StatusResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/createFdCollectionItem`,
    body: createFdCollectionItemRequest,
  });
};

export const getFdCollectionItems = (collectionId: string): Promise<CollectionItemsResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/getFdCollectionItems`,
    body: { collectionId },
  });
};

export const deleteFdCollectionItem = (id: string): Promise<StatusResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/deleteFdCollectionItem`,
    body: { id },
  });
};

export const getFdCollectionItemData = (id: string): Promise<CollectionItemResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/getFdCollectionItemData`,
    body: { id },
  });
};

export const updateFdCollectionItem = (updateFdCollectionItemRequest: UpdateCollectionItemRequestProto): Promise<StatusResponseProto> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/updateFdCollectionItem`,
    body: updateFdCollectionItemRequest,
  });
};

export const bulkUpdateFdItemPriority = (bulkUpdateFdItemPriorityRequest: BulkUpdateFdItemPriorityRequest): Promise<BulkUpdateFdItemPriorityResponse> => {
  return jsonRequest({
    url: `${import.meta.env.VITE_ADMIN_BASE_URL}/v1/grpc/FdCollectionService/bulkUpdateFdItemPriority`,
    body: bulkUpdateFdItemPriorityRequest,
  });
};
