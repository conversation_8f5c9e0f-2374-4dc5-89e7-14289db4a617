import { restRequest } from "../requests/rest-api";
import type { AccessResource, AllAccessResources, AllDashboardUsers, AllRoleAccessResources, AllRoles, CreateAccessResourceRequest, CreateRoleRequest, Role, AssignAccessResourceRequest, RoleAccessResource, AssignRoleRequest, UpdateUserRoleRequest, UserRole, AllUserRoles, CurrentUserRolesResponse, UpdateDashboardUserStatusRequest, DashboardUser } from "../types/access-control";

const adminBaseUrl = import.meta.env.VITE_ADMIN_BASE_URL;

// Access Resource Endpoints

export const getAccessResources = (): Promise<AllAccessResources> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/access-resources`,
  });
};

export const deleteAccessResource = (id: string): Promise<void> => {
  return restRequest({
    method: "DELETE",
    url: `${adminBaseUrl}/v1/access-control/access-resources/${id}`,
  });
};

export const createAccessResource = (
  createAccessResourceRequest: CreateAccessResourceRequest
) => {
  return restRequest({
    method: "POST",
    url: `${adminBaseUrl}/v1/access-control/access-resources`,
    body: createAccessResourceRequest,
  });
};

export const getAccessResource = (id: string): Promise<AccessResource> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/access-resources/${id}`,
  });
};

export const updateAccessResource = (accessResource: AccessResource) => {
  return restRequest({
    method: "PUT",
    url: `${adminBaseUrl}/v1/access-control/access-resources/${accessResource.id}`,
    body: {
      name: accessResource.name,
      urlMappings: accessResource.urlMappings,
    },
  });
};

// Role Endpoints

export const createRole = (createRoleRequest: CreateRoleRequest) => {
  return restRequest({
    method: "POST",
    url: `${adminBaseUrl}/v1/access-control/roles`,
    body: createRoleRequest,
  });
};

export const getRoles = (): Promise<AllRoles> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/roles`,
  });
};

export const getRole = (id: string): Promise<Role> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/roles/${id}`,
  });
};

export const updateRole = (role: Role) => {
  return restRequest({
    method: "PUT",
    url: `${adminBaseUrl}/v1/access-control/roles/${role.id}`,
    body: {
      name: role.name,
      description: role.description,
    },
  });
};

export const deleteRole = (id: string): Promise<void> => {
  return restRequest({
    method: "DELETE",
    url: `${adminBaseUrl}/v1/access-control/roles/${id}`,
  });
};

// Dashboard User Endpoints

export const getDashboardUsers = (): Promise<AllDashboardUsers> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/dashboard-users`,
  });
};

export const updateDashboardUserStatus = (
  email: string,
  request: UpdateDashboardUserStatusRequest
): Promise<DashboardUser> => {
  return restRequest({
    method: "PUT",
    url: `${adminBaseUrl}/v1/access-control/dashboard-users/${email}/status`,
    body: request,
  });
};

// Role Access Resource Endpoints

export const getRoleAccessResources = (): Promise<AllRoleAccessResources> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/role-access-resources`,
  });
};

export const assignAccessResourceToRole = (
  roleName: string,
  request: AssignAccessResourceRequest
): Promise<RoleAccessResource> => {
  return restRequest({
    method: "POST",
    url: `${adminBaseUrl}/v1/access-control/roles/${roleName}/access-resources`,
    body: request,
  });
};

export const removeAccessResourceFromRole = (
  roleName: string,
  accessResourceName: string
): Promise<void> => {
  return restRequest({
    method: "DELETE",
    url: `${adminBaseUrl}/v1/access-control/roles/${roleName}/access-resources/${accessResourceName}`,
  });
};

export const getRoleAccessResourcesByRole = (
  roleName: string
): Promise<AllRoleAccessResources> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/roles/${roleName}/access-resources`,
  });
};

export const getAccessResourceRoles = (
  accessResourceName: string
): Promise<AllRoleAccessResources> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/access-resources/${accessResourceName}/roles`,
  });
};

// User Role Endpoints
export const assignRoleToUser = (
  userId: string,
  request: AssignRoleRequest
): Promise<UserRole> => {
  return restRequest({
    method: "POST",
    url: `${adminBaseUrl}/v1/access-control/users/${userId}/roles`,
    body: request,
  });
};

export const updateUserRoleStatus = (
  userId: string,
  roleId: string,
  request: UpdateUserRoleRequest
): Promise<UserRole> => {
  return restRequest({
    method: "PUT",
    url: `${adminBaseUrl}/v1/access-control/users/${userId}/roles/${roleId}`,
    body: request,
  });
};

export const removeRoleFromUser = (
  userId: string,
  roleId: string
): Promise<void> => {
  return restRequest({
    method: "DELETE",
    url: `${adminBaseUrl}/v1/access-control/users/${userId}/roles/${roleId}`,
  });
};

export const getUserRoles = (
  userId: string
): Promise<AllUserRoles> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/users/${userId}/roles`,
  });
};

export const getCurrentUserRoles = (): Promise<CurrentUserRolesResponse> => {
  return restRequest({
    method: "GET",
    url: `${adminBaseUrl}/v1/access-control/current-user/roles`,
  });
};
