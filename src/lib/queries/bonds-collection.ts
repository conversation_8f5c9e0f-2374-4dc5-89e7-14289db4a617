import { request } from "~lib/requests/proto-api";
import { AllCollectionsResponse, BondCollectionResponse, BondsCollectionItemStatusResponse, BulkUpdateBondItemPriorityRequest, BulkUpdateBondItemPriorityResponse, CreateAndUpdateBondCollectionItemRequest, CreateBondCollectionRequest, CreateBondCollectionResponse, DeleteBondCollectionResponse, GetAllCollectionExpressionsResponse, GetBondDetailsResponse, GetBondsCollectionItemResponse, UpdateBondCollectionRequest, UpdateBondCollectionResponse, UpdateBondItemPriorityRequest } from '~/gen/proto-models/BrokingCollection';
import { jsonRequest } from "../requests/json-api";
const adminBaseUrl = import.meta.env.VITE_ADMIN_BASE_URL;

export const getAllBondCollections = (): Promise<AllCollectionsResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/v1/bond-collections`,
    responseDecoder: AllCollectionsResponse.decode,
  });
};

export const createCollection = (createbondCollectionRequest: CreateBondCollectionRequest): Promise<CreateBondCollectionResponse> => {
  return request({
    method: "POST",
    url: `${adminBaseUrl}/v1/bond-collections`,
    body: CreateBondCollectionRequest.encode(createbondCollectionRequest),
    responseDecoder: CreateBondCollectionResponse.decode,
  });
};

export const deleteBondCollection = (id: string): Promise<DeleteBondCollectionResponse> => {
  return request({
    method: "DELETE",
    url: `${adminBaseUrl}/v1/bond-collections?id=${id}`,
    responseDecoder: DeleteBondCollectionResponse.decode,
  });
};

export const fetchBondCollectionById = (id: string): Promise<BondCollectionResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/v1/bond-collections/collection?id=${id}`,
    responseDecoder: BondCollectionResponse.decode,
  });
};

export const fetchBondsByCollectionId = (id: string): Promise<GetBondsCollectionItemResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/v1/bond-collections/collection/bonds?id=${id}`,
    responseDecoder: GetBondsCollectionItemResponse.decode,
  });
};

export const updateBondCollection = (updateBondCollectionRequest: UpdateBondCollectionRequest): Promise<UpdateBondCollectionResponse> => {
  return request({
    method: "PUT",
    url: `${adminBaseUrl}/v1/bond-collections/collection/update`,
    body: UpdateBondCollectionRequest.encode(updateBondCollectionRequest),
    responseDecoder: UpdateBondCollectionResponse.decode,
  });
};

export const createBondInCollection = (createAndUpdateBondCollectionItemRequest: CreateAndUpdateBondCollectionItemRequest): Promise<BondsCollectionItemStatusResponse> => {
  return request({
    method: "POST",
    url: `${adminBaseUrl}/v1/bond-collections/collection/bonds`,
    body: CreateAndUpdateBondCollectionItemRequest.encode(createAndUpdateBondCollectionItemRequest),
    responseDecoder: BondsCollectionItemStatusResponse.decode,
  });
};

export const updateBondPriorityInCollection = (updateBondItemPriorityRequest: UpdateBondItemPriorityRequest): Promise<any> => {
  return request({
    method: "PUT",
    url: `${adminBaseUrl}/v1/bond-collections/collection/bonds/priority`,
    body: UpdateBondItemPriorityRequest.encode(updateBondItemPriorityRequest),
    responseDecoder: BondsCollectionItemStatusResponse.decode,
  });
};

export const fetchAllISINs = (): Promise<GetBondDetailsResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/v1/bond-collections/bonds`,
    responseDecoder: GetBondDetailsResponse.decode,
  });
};

export const deleteBondFromCollection = (id: String): Promise<any> => {
  return request({
    method: "DELETE",
    url: `${adminBaseUrl}/v1/bond-collections/collection/bonds/delete?id=${id}`,
  });
};

export const bulkUpdateBondItemPriority = (bulkUpdateBondItemPriorityRequest : BulkUpdateBondItemPriorityRequest): Promise<BulkUpdateBondItemPriorityResponse> => {
  return request({
    method: "PUT",
    url: `${adminBaseUrl}/v1/bond-collections/collection/bonds/updatePriority`,
    body: BulkUpdateBondItemPriorityRequest.encode(bulkUpdateBondItemPriorityRequest),
    responseDecoder: BulkUpdateBondItemPriorityResponse.decode,
  });
};

export const getAllCollectionExpressions = (): Promise<GetAllCollectionExpressionsResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/BrokingCollectionAdminService/GetAllCollectionExpressions`,
    body: {},
  });
};



