import {
  BanksDownTimeResponse,
  DeleteBankDownTimeRequest,
  BankDownTimeResponse,
  BankDownTime,
  AllBanksDetailsResponse,
} from "~/gen/proto-models/BankDownTime";
import { Empty } from "~/gen/proto-models/google/protobuf/empty";
import { request } from "~lib/requests/proto-api";

const adminBaseUrl = import.meta.env.VITE_ADMIN_BASE_URL;
export const getBanksDownTime = (): Promise<BanksDownTimeResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/v1/bank/banks-down-time`,
    responseDecoder: BanksDownTimeResponse.decode,
  });
};

export const deleteBankDownTime = (id: string): Promise<Empty> => {
  const deleteRequest: DeleteBankDownTimeRequest = { id };
  return request({
    method: "DELETE",
    url: `${adminBaseUrl}/v1/bank/delete-bank-down-time`,
    body: DeleteBankDownTimeRequest.encode(deleteRequest),
    responseDecoder: Empty.decode,
  });
};

export const addOrUpdateBankDownTime = (
  bankDownTime: BankDownTime
): Promise<BankDownTime> => {
  return request({
    method: "POST",
    url: `${adminBaseUrl}/v1/bank/add-update-bank-down-time`,
    body: BankDownTime.encode(bankDownTime),
    responseDecoder: BankDownTime.decode,
  });
};

export const getBankTime = (id: string): Promise<BankDownTimeResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/v1/bank/bank-down-time?id=${id}`,
    responseDecoder: BankDownTimeResponse.decode,
  });
};

export const getBanksNameAndId = (): Promise<AllBanksDetailsResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/v1/bank/banks-id-name`,
    responseDecoder: AllBanksDetailsResponse.decode,
  });
};
