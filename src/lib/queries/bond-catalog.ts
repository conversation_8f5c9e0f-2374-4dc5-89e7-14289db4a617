import { AllBondDetailsResponse, AllISINSResponse, BondDetailsDashboard, BondDetailsUpdateRequest, BondDetailsUpdateResponse, BondInventoryResponse, BondIssuingInstitutionResponse, BondIssuingInstitutionUpdateRequest, BondIssuingInstitutionUpdateResponse, BondOfferingDetailResponse, BondOfferingUpdateRequest, BondOfferingUpdateResponse, CreateMediaItemRequest, CreateMediaItemResponse, CreateTagCollectionItemRequest, CreateTagCollectionItemResponse, CreateTagRequest, CreateTagResponse, DeleteTagCollectionItemResponse, GetAllBondsResponse, GetAllTagCollectionItemResponse, GetAllTagsResponse, GetBondCashflowScheduleResponse, GetBondPricesResponse, GetMediaItemResponse, MediaItemResponse, MediaItemUpdateRequest, MediaItemUpdateResponse, PartyDetailsResponse, Tag, UpdateBondInventoryRequest, UpdateBondInventoryResponse, UpdateBondOfferingYieldRequest, UpdateBondOfferingYieldResponse, UpdateOfferingActiveStatusRequest, UpdateOfferingActiveStatusResponse, UpdateRecordDateRequest, UpdateRecordDateResponse, UpdateTagResponse, VerifyRecordDateRequest, VerifyRecordDateResponse } from "~/gen/proto-models/Catalog";
import { request } from "~lib/requests/proto-api";
import { jsonRequest } from "../requests/json-api";
const adminBaseUrl = import.meta.env.VITE_ADMIN_BASE_URL;

interface GetAllBondsRequest {
  page: number;
  size: number;
  searchString: string;
}

export const getAllBonds = (): Promise<AllBondDetailsResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/all-bonds`,
    responseDecoder: AllBondDetailsResponse.decode,
  });
};

export const getAllActiveBonds = (req: GetAllBondsRequest): Promise<GetAllBondsResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/active?page=${req.page}&size=${req.size}`,
    responseDecoder: GetAllBondsResponse.decode,
  });
};

export const getAllInActiveBonds = (req: GetAllBondsRequest): Promise<GetAllBondsResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/inactive?page=${req.page}&size=${req.size}`,
    responseDecoder: GetAllBondsResponse.decode,
  });
};

export const getBondIssuingInstitution = (id: string): Promise<BondIssuingInstitutionResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/bond-issuing-institution?id=${id}`,
    responseDecoder: BondIssuingInstitutionResponse.decode,
  });
};

export const getBondOfferingDetail = (id: string): Promise<BondOfferingDetailResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/bond-offering-detail?id=${id}`,
    responseDecoder: BondOfferingDetailResponse.decode,
  });
};

export const getBondInventory = (id: string): Promise<BondInventoryResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/bond-inventory?id=${id}`,
    responseDecoder: BondInventoryResponse.decode,
  });
};

export const updateBondDetails = (bondDetailsUpdateRequest: BondDetailsUpdateRequest): Promise<BondDetailsUpdateResponse> => {
  return request({
    method: "PUT",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/update`,
    body: BondDetailsUpdateRequest.encode(bondDetailsUpdateRequest),
    responseDecoder: BondDetailsUpdateResponse.decode,
  });
};

export const getAllISINS = (): Promise<AllISINSResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/all-isins`,
    responseDecoder: AllISINSResponse.decode,
  });
};

export const getAllMediaItems = (req: {id: string, parentType: string}): Promise<MediaItemResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/media-item?id=${req.id}&parentType=${req.parentType}`,
    responseDecoder: MediaItemResponse.decode,
  });
};

export const updateMediaItem = (mediaItemUpdateRequest: MediaItemUpdateRequest): Promise<MediaItemUpdateResponse> => {
  return request({
    method: "PUT",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/media-item/update`,
    body: MediaItemUpdateRequest.encode(mediaItemUpdateRequest),
    responseDecoder: MediaItemUpdateResponse.decode,
  });
};

export const getPartyDetails = (): Promise<PartyDetailsResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/party-details`,
    responseDecoder: PartyDetailsResponse.decode,
  });
};

export const updateIssuingInstitution = (bondIssuingInstitutionUpdateRequest: BondIssuingInstitutionUpdateRequest): Promise<BondIssuingInstitutionUpdateResponse> => {
  return request({
    method: "PUT",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/bond-issuing-institution`,
    body: BondIssuingInstitutionUpdateRequest.encode(bondIssuingInstitutionUpdateRequest),
    responseDecoder: BondIssuingInstitutionUpdateResponse.decode,
  });
};

export const getBondDetailById = (id: string): Promise<BondDetailsDashboard> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/getBondDetailById`,
    body: { id },
  });
};

export const updateOfferingActiveStatus = (updateOfferingActiveStatusRequest: UpdateOfferingActiveStatusRequest): Promise<UpdateOfferingActiveStatusResponse> => {
  return request({
    method: "PUT",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/bond-offering-detail/update-offering-active-status`,
    body: UpdateOfferingActiveStatusRequest.encode(updateOfferingActiveStatusRequest),
    responseDecoder: UpdateOfferingActiveStatusResponse.decode,
  });
};

export const updateBondOfferingYield = (updateBondOfferingYieldRequest: UpdateBondOfferingYieldRequest): Promise<UpdateBondOfferingYieldResponse> => {
  return request({
    method: "PUT",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/bond-offering-detail/update-yield`,
    body: UpdateBondOfferingYieldRequest.encode(updateBondOfferingYieldRequest),
    responseDecoder: UpdateBondOfferingYieldResponse.decode,
  });
};

export const getBondCashflowSchedule = (id: string): Promise<GetBondCashflowScheduleResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/bond-cashflow-schedule?id=${id}`,
    responseDecoder: GetBondCashflowScheduleResponse.decode,
  });
};

export const createTag = (createTagRequest: CreateTagRequest): Promise<CreateTagResponse> => {
  return request({
    method: "POST",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/create-tag`,
    body: CreateTagRequest.encode(createTagRequest),
    responseDecoder: CreateTagResponse.decode,
  });
};

export const getAllTags = (): Promise<GetAllTagsResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/tags`,
    responseDecoder: GetAllTagsResponse.decode,
  });
};

export const createTagCollectionItem = (createTagCollectionItemRequest: CreateTagCollectionItemRequest): Promise<CreateTagCollectionItemResponse> => {
  return request({
    method: "POST",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/tag-collection-item`,
    body: CreateTagCollectionItemRequest.encode(createTagCollectionItemRequest),
    responseDecoder: CreateTagCollectionItemResponse.decode,
  });
};

export const getAllTagCollectionItem = (): Promise<GetAllTagCollectionItemResponse> => {
  return request({
    method: "GET",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/tag-collection-item`,
    responseDecoder: GetAllTagCollectionItemResponse.decode,
  });
};

export const deleteTagCollectionItem = (id: string): Promise<DeleteTagCollectionItemResponse> => {
  return request({
    method: "DELETE",
    url: `${adminBaseUrl}/admin/v1/bonds-catalog/tag-collection-item?id=${id}`,
    responseDecoder: DeleteTagCollectionItemResponse.decode,
  });
};

export const updateTag = (tag: Tag
): Promise<UpdateTagResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/updateTag`,
    body: tag,
  });
};

export const getTag = (tagId: string): Promise<Tag> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/getTag`,
    body: { tagId },
  });
};

export const updateBondOffering = (bondOfferingUpdateRequest: BondOfferingUpdateRequest
): Promise<BondOfferingUpdateResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/updateBondOffering`,
    body: bondOfferingUpdateRequest,
  });
};

export const getBondPrices = (bondOfferingId: string): Promise<GetBondPricesResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/getBondPrices`,
    body: { bondOfferingId },
  });
};

export const updateBondInventory = (updateBondInventoryRequest: UpdateBondInventoryRequest
): Promise<UpdateBondInventoryResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/updateBondInventory`,
    body: updateBondInventoryRequest,
  });
};

export const createMediaItem = (createMediaItemRequest: CreateMediaItemRequest
): Promise<CreateMediaItemResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/createMediaItem`,
    body: createMediaItemRequest,
  });
};

export const verifyRecordDate = (verifyRecordDateRequest: VerifyRecordDateRequest
): Promise<VerifyRecordDateResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/verifyRecordDate`,
    body: verifyRecordDateRequest,
  });
};

export const getMediaItemById = (mediaItemId: string): Promise<GetMediaItemResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/getMediaItemById`,
    body: { mediaItemId },
  });
};

export const updateRecordDate = (updateRecordDateRequest: UpdateRecordDateRequest
): Promise<UpdateRecordDateResponse> => {
  return jsonRequest({
    url: `${adminBaseUrl}/v1/grpc/CatalogService/updateRecordDate`,
    body: updateRecordDateRequest,
  });
};

