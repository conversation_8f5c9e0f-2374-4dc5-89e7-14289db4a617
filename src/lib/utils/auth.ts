export function getStoredAuthToken(key: string = "auth_token") {
  return localStorage.getItem(key);
}

export function setStoredAuthToken(
  authToken: string | null,
  key: string = "auth_token"
) {
  if (authToken) {
    localStorage.setItem(key, authToken);
  } else {
    localStorage.removeItem(key);
  }
}

export function logout() {
  localStorage.clear();
  window.location.href = "/auth/login";
}
