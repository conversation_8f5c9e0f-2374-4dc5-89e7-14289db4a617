// Access Resource Interfaces

export interface AccessResource {
  id: string
  name: string
  urlMappings: string
}

export type AllAccessResources = AccessResource[]

export interface CreateAccessResourceRequest {
  name: string
  urlMappings: string
}

export interface UpdateAccessResourceRequest {
  name: string
  urlMappings: string
}

// Role Interfaces

export interface Role {
  id: string
  name: string
  description?: string
}

export type AllRoles = Role[]

export interface CreateRoleRequest {
  name: string
  description?: string
}

export interface UpdateRoleRequest {
  name?: string
  description?: string
}

// Dashboard User Interface

export interface DashboardUser {
  id: string
  email: string
  isActive: boolean
}

export type AllDashboardUsers = DashboardUser[]

export interface UpdateDashboardUserStatusRequest {
  isActive: boolean
}

// Role Access Resource Interfaces

export interface AssignAccessResourceRequest {
  accessResourceName: string
}

export interface RoleAccessResource {
  id: string
  role: Role
  accessResource: AccessResource
}

export type AllRoleAccessResources = RoleAccessResource[]

// User Role Interfaces
export interface AssignRoleRequest {
  roleId: string
  status?: boolean
}

export interface UpdateUserRoleRequest {
  status: boolean
}

export interface UserRole {
  id: string
  user: DashboardUser
  role: Role
  status: boolean
}

export type AllUserRoles = UserRole[]

export interface CurrentUserRolesResponse {
  userId: string
  roles: RoleInfo[]
}

export interface RoleInfo {
  id: string
  name: string
  description?: string
}

