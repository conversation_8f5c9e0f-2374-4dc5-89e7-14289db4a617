{
    "$schema": "node_modules/wrangler/config-schema.json",
    "name": "control-hub",
    "assets": {
        "directory": "./dist",
        "not_found_handling": "single-page-application"
    },
    "compatibility_flags": [
        "nodejs_compat"
    ],
    "compatibility_date": "2025-06-04",
    "observability": {
        "enabled": true
    },
    "env": {
        "production": {
            "routes": [
                {
                    "pattern": "controlhub.stablemoney.in",
                    "custom_domain": true
                }
            ],
        },
        "staging": {
            "routes": [
                {
                    "pattern": "controlhub-staging.stablemoney.in",
                    "custom_domain": true
                }
            ],
        }
    }
}